# PhotonRender .gitignore
# Professional C++/Ruby project gitignore

# Build directories
build/
build-*/
out/
cmake-build-*/

# Compiled Object files
*.slo
*.lo
*.o
*.obj

# Precompiled Headers
*.gch
*.pch

# Compiled Dynamic libraries
*.so
*.dylib
*.dll

# Fortran module files
*.mod
*.smod

# Compiled Static libraries
*.lai
*.la
*.a
*.lib

# Executables
*.exe
*.out
*.app

# CMake
CMakeCache.txt
CMakeFiles/
CMakeScripts/
install_manifest.txt
compile_commands.json
CTestTestfile.cmake
_deps/

# Visual Studio
.vs/
*.vcxproj.user
*.vcxproj.filters
*.sln.docstates
*.suo
*.user
*.userosscache
*.sln.docstates

# Visual Studio Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# Xcode
*.xcodeproj/
*.xcworkspace/
DerivedData/
*.hmap
*.ipa

# Ruby
*.gem
*.rbc
/.config
/coverage/
/InstalledFiles
/pkg/
/spec/reports/
/spec/examples.txt
/test/tmp/
/test/version_tmp/
/tmp/

# Bundler
.bundle/
vendor/bundle/
lib/bundler/man/

# RubyMine
.idea/

# YARD artifacts
.yardoc
_yardoc
doc/

# Python (for scripts)
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/

# Jupyter Notebook
.ipynb_checkpoints

# Node.js (for web UI)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Logs
*.log
logs/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
*.swp
*.swo
*~

# Package files
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# Test results
test_results/
test_output/
benchmark_results/
*.xml
*.json.test

# Render outputs
renders/
output/
*.png
*.jpg
*.jpeg
*.exr
*.hdr

# Third party downloads
third_party/downloads/
third_party/build/

# Documentation generated
docs/html/
docs/latex/
docs/api/

# IDE specific
*.sublime-project
*.sublime-workspace

# Backup files
*.bak
*.backup
*.old

# Local configuration
local_config.json
.env.local

# Distribution
dist/
*.rbz

# CUDA
*.ptx
*.cubin

# Profiling
*.prof
*.nvprof

# Conan
conandata.yml
conaninfo.txt
conanbuildinfo.*

# vcpkg
vcpkg_installed/

# CLion
cmake-build-*/

# Qt Creator
CMakeLists.txt.user*

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Vim
[._]*.s[a-v][a-z]
[._]*.sw[a-p]
[._]s[a-rt-v][a-z]
[._]ss[a-gi-z]
[._]sw[a-p]

# Session
Session.vim

# Temporary
.netrwhist

# Auto-generated tag files
tags

# Persistent undo
[._]*.un~

# ============================================================================
# PHOTONRENDER REPOSITORY OPTIMIZATION RULES
# ============================================================================

# VS Code Local History extension files (MAJOR CLEANUP)
.history/
**/.history/

# Backup and temporary files
*.backup
backup*.txt
backup*.md
*_backup.*
*_temp.*
*_tmp.*
*_draft.*
*_copy.*
*_old.*

# Test output directories (keep clean)
qa_results/
qa_demo_results/
regression_demo_results/
performance_demo_results/
automated_results/
test_qa_output/
custom_output/

# Generated documentation drafts
*_draft_report.*
*_temp_report.*

# External dependencies (auto-downloaded, except OIDN)
external/embree/
external/tbb/
external/eigen/
external/gtest/
# Keep external/oidn/ (manually installed)

# Build artifacts
*.pdb
*.ilk
*.exp
*.idb
*.ipdb
*.pgc
*.pgd
*.rsp
*.sbr
*.tlb
*.tli
*.tlh
*.tmp_proj
*.vspscc
*.vssscc
.builds
*.pidb
*.svclog
*.scc

# Temporary development files
PhotonRender_*_Fix__*.md
*_temp_*.md
temp_*.txt

# Keep only essential documentation
# All task completion reports are kept
# All phase reports are kept
# Technical guides are kept
