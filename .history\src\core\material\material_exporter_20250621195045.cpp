// src/core/material/material_exporter.cpp
// PhotonRender - Material Export System Implementation
// Implementazione sistema di export materiali

#include "material_exporter.hpp"
#include "material.hpp"
#include <filesystem>
#include <fstream>
#include <sstream>
#include <iomanip>
#include <chrono>
#include <algorithm>
#include <iostream>

namespace photon {

// ExportResult implementation
std::string ExportResult::getSummary() const {
    std::ostringstream oss;
    
    oss << "Export Summary:\n";
    oss << "Status: " << (success ? "SUCCESS" : "FAILED") << "\n";
    oss << "Materials Exported: " << materialCount << "\n";
    oss << "Textures Exported: " << textureCount << "\n";
    oss << "Files Created: " << exportedFiles.size() << "\n";
    oss << "Export Time: " << std::fixed << std::setprecision(2) << exportTime << "s\n";
    
    if (!outputPath.empty()) {
        oss << "Output Path: " << outputPath << "\n";
    }
    
    if (hasWarnings()) {
        oss << "Warnings: " << warnings.size() << "\n";
    }
    
    if (hasErrors()) {
        oss << "Errors: " << errors.size() << "\n";
    }
    
    return oss.str();
}

// MaterialExporter implementation
MaterialExporter::MaterialExporter() {
    // Initialize exporter
}

MaterialExporter::~MaterialExporter() {
    // Cleanup
}

ExportResult MaterialExporter::exportMaterial(const std::shared_ptr<Material>& material, 
                                             const ExportOptions& options) {
    auto startTime = std::chrono::high_resolution_clock::now();
    
    ExportResult result;
    result.outputPath = options.outputPath;
    
    if (!material) {
        result.errors.push_back("Material is null");
        return result;
    }
    
    if (!validateExportOptions(options)) {
        result.errors.push_back("Invalid export options");
        return result;
    }
    
    updateProgress(0.0f, "Starting material export...");
    
    try {
        // Convert material to export data
        MaterialExportData exportData = convertMaterialToExportData(material);
        std::vector<MaterialExportData> materials = { exportData };
        
        updateProgress(0.2f, "Converting material data...");
        
        // Create output directory
        std::filesystem::path outputDir = std::filesystem::path(options.outputPath).parent_path();
        if (!createOutputDirectory(outputDir.string())) {
            result.errors.push_back("Failed to create output directory: " + outputDir.string());
            return result;
        }
        
        updateProgress(0.4f, "Creating output directory...");
        
        // Export based on format
        switch (options.format) {
            case ExportFormat::MTL:
                result = exportToMTL(materials, options);
                break;
            case ExportFormat::GLTF:
                result = exportToGLTF(materials, options);
                break;
            case ExportFormat::JSON:
                result = exportToJSON(materials, options);
                break;
            case ExportFormat::OBJ_MTL:
                result = exportToOBJMTL(materials, options);
                break;
            default:
                result.errors.push_back("Unsupported export format");
                return result;
        }
        
        updateProgress(0.8f, "Exporting material file...");
        
        // Export textures if requested
        if (options.includeTextures && result.success) {
            auto textureFiles = exportTextures(materials, options);
            result.exportedFiles.insert(result.exportedFiles.end(), 
                                       textureFiles.begin(), textureFiles.end());
            result.textureCount = textureFiles.size();
        }
        
        updateProgress(1.0f, "Export completed");
        
        result.materialCount = 1;
        
    } catch (const std::exception& e) {
        result.success = false;
        result.errors.push_back("Export exception: " + std::string(e.what()));
    }
    
    // Calculate export time
    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
    result.exportTime = duration.count() / 1000.0;
    
    // Update statistics
    m_totalExports++;
    m_totalExportTime += result.exportTime;
    if (result.success) {
        m_successfulExports++;
    } else {
        m_failedExports++;
    }
    
    return result;
}

ExportResult MaterialExporter::exportMaterials(const std::vector<std::shared_ptr<Material>>& materials, 
                                              const ExportOptions& options) {
    auto startTime = std::chrono::high_resolution_clock::now();
    
    ExportResult result;
    result.outputPath = options.outputPath;
    
    if (materials.empty()) {
        result.errors.push_back("No materials to export");
        return result;
    }
    
    if (!validateExportOptions(options)) {
        result.errors.push_back("Invalid export options");
        return result;
    }
    
    updateProgress(0.0f, "Starting materials export...");
    
    try {
        // Convert materials to export data
        std::vector<MaterialExportData> exportData;
        for (size_t i = 0; i < materials.size(); ++i) {
            if (materials[i]) {
                exportData.push_back(convertMaterialToExportData(materials[i]));
                updateProgress(0.1f + (0.3f * i / materials.size()), 
                              "Converting material " + std::to_string(i + 1) + "...");
            } else {
                result.warnings.push_back("Skipping null material at index " + std::to_string(i));
            }
        }
        
        if (exportData.empty()) {
            result.errors.push_back("No valid materials to export");
            return result;
        }
        
        // Create output directory
        std::filesystem::path outputDir = std::filesystem::path(options.outputPath).parent_path();
        if (!createOutputDirectory(outputDir.string())) {
            result.errors.push_back("Failed to create output directory: " + outputDir.string());
            return result;
        }
        
        updateProgress(0.4f, "Creating output directory...");
        
        // Export based on format
        switch (options.format) {
            case ExportFormat::MTL:
                result = exportToMTL(exportData, options);
                break;
            case ExportFormat::GLTF:
                result = exportToGLTF(exportData, options);
                break;
            case ExportFormat::JSON:
                result = exportToJSON(exportData, options);
                break;
            case ExportFormat::OBJ_MTL:
                result = exportToOBJMTL(exportData, options);
                break;
            default:
                result.errors.push_back("Unsupported export format");
                return result;
        }
        
        updateProgress(0.8f, "Exporting materials file...");
        
        // Export textures if requested
        if (options.includeTextures && result.success) {
            auto textureFiles = exportTextures(exportData, options);
            result.exportedFiles.insert(result.exportedFiles.end(), 
                                       textureFiles.begin(), textureFiles.end());
            result.textureCount = textureFiles.size();
        }
        
        updateProgress(1.0f, "Export completed");
        
        result.materialCount = exportData.size();
        
    } catch (const std::exception& e) {
        result.success = false;
        result.errors.push_back("Export exception: " + std::string(e.what()));
    }
    
    // Calculate export time
    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
    result.exportTime = duration.count() / 1000.0;
    
    // Update statistics
    m_totalExports++;
    m_totalExportTime += result.exportTime;
    if (result.success) {
        m_successfulExports++;
    } else {
        m_failedExports++;
    }
    
    return result;
}

ExportResult MaterialExporter::exportToMTL(const std::vector<MaterialExportData>& materials, 
                                          const ExportOptions& options) {
    ExportResult result;
    result.outputPath = options.outputPath;
    
    try {
        if (writeMTLFile(materials, options.outputPath, options)) {
            result.success = true;
            result.exportedFiles.push_back(options.outputPath);
        } else {
            result.errors.push_back("Failed to write MTL file");
        }
    } catch (const std::exception& e) {
        result.errors.push_back("MTL export error: " + std::string(e.what()));
    }
    
    return result;
}

ExportResult MaterialExporter::exportToGLTF(const std::vector<MaterialExportData>& materials, 
                                           const ExportOptions& options) {
    ExportResult result;
    result.outputPath = options.outputPath;
    
    try {
        if (writeGLTFFile(materials, options.outputPath, options)) {
            result.success = true;
            result.exportedFiles.push_back(options.outputPath);
        } else {
            result.errors.push_back("Failed to write glTF file");
        }
    } catch (const std::exception& e) {
        result.errors.push_back("glTF export error: " + std::string(e.what()));
    }
    
    return result;
}

ExportResult MaterialExporter::exportToJSON(const std::vector<MaterialExportData>& materials, 
                                           const ExportOptions& options) {
    ExportResult result;
    result.outputPath = options.outputPath;
    
    try {
        if (writeJSONFile(materials, options.outputPath, options)) {
            result.success = true;
            result.exportedFiles.push_back(options.outputPath);
        } else {
            result.errors.push_back("Failed to write JSON file");
        }
    } catch (const std::exception& e) {
        result.errors.push_back("JSON export error: " + std::string(e.what()));
    }
    
    return result;
}

MaterialExportData MaterialExporter::convertMaterialToExportData(const std::shared_ptr<Material>& material) {
    MaterialExportData data;
    
    if (!material) {
        return data;
    }
    
    // Basic material info
    data.name = material->getName();
    data.id = generateMaterialId(data.name);
    
    // Try to get Disney BRDF parameters
    auto pbrMaterial = std::dynamic_pointer_cast<PBRMaterial>(material);
    if (pbrMaterial) {
        data.brdfParams = pbrMaterial->getDisneyParams();
        
        // Get texture assignments
        // TODO: Interface with texture system to get actual texture paths
        // For now, use placeholder paths
        data.diffuseTexture = "textures/" + data.name + "_diffuse.png";
        data.normalTexture = "textures/" + data.name + "_normal.png";
        data.roughnessTexture = "textures/" + data.name + "_roughness.png";
        data.metallicTexture = "textures/" + data.name + "_metallic.png";
    }
    
    // Add metadata
    data.metadata["exportTime"] = std::to_string(std::time(nullptr));
    data.metadata["exporter"] = "PhotonRender";
    data.metadata["version"] = "1.0";
    
    return data;
}

std::vector<ExportFormat> MaterialExporter::getSupportedFormats() const {
    return {
        ExportFormat::MTL,
        ExportFormat::GLTF,
        ExportFormat::JSON,
        ExportFormat::OBJ_MTL
    };
}

std::string MaterialExporter::getFormatName(ExportFormat format) const {
    switch (format) {
        case ExportFormat::MTL: return "Wavefront MTL";
        case ExportFormat::GLTF: return "glTF 2.0";
        case ExportFormat::JSON: return "PhotonRender JSON";
        case ExportFormat::OBJ_MTL: return "OBJ + MTL";
        case ExportFormat::BLENDER: return "Blender";
        case ExportFormat::MAYA: return "Maya";
        case ExportFormat::MAX: return "3ds Max";
        case ExportFormat::SUBSTANCE: return "Substance Designer";
        case ExportFormat::UNREAL: return "Unreal Engine";
        case ExportFormat::UNITY: return "Unity";
        default: return "Unknown";
    }
}

std::string MaterialExporter::getFormatExtension(ExportFormat format) const {
    switch (format) {
        case ExportFormat::MTL: return ".mtl";
        case ExportFormat::GLTF: return ".gltf";
        case ExportFormat::JSON: return ".json";
        case ExportFormat::OBJ_MTL: return ".obj";
        default: return ".txt";
    }
}

void MaterialExporter::setProgressCallback(std::function<void(float, const std::string&)> callback) {
    m_progressCallback = callback;
}

std::string MaterialExporter::getExportStats() const {
    std::ostringstream oss;
    
    oss << "Material Export Statistics:\n";
    oss << "Total Exports: " << m_totalExports << "\n";
    oss << "Successful: " << m_successfulExports << "\n";
    oss << "Failed: " << m_failedExports << "\n";
    
    if (m_totalExports > 0) {
        float successRate = (static_cast<float>(m_successfulExports) / m_totalExports) * 100.0f;
        oss << "Success Rate: " << std::fixed << std::setprecision(1) << successRate << "%\n";
        
        double avgTime = m_totalExportTime / m_totalExports;
        oss << "Average Export Time: " << std::fixed << std::setprecision(2) << avgTime << "s\n";
    }
    
    oss << "Total Export Time: " << std::fixed << std::setprecision(2) << m_totalExportTime << "s";
    
    return oss.str();
}

} // namespace photon
