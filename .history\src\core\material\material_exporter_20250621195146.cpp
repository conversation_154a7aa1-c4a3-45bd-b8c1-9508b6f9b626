// src/core/material/material_exporter.cpp
// PhotonRender - Material Export System Implementation
// Implementazione sistema di export materiali

#include "material_exporter.hpp"
#include "material.hpp"
#include <filesystem>
#include <fstream>
#include <sstream>
#include <iomanip>
#include <chrono>
#include <algorithm>
#include <iostream>

namespace photon {

// ExportResult implementation
std::string ExportResult::getSummary() const {
    std::ostringstream oss;
    
    oss << "Export Summary:\n";
    oss << "Status: " << (success ? "SUCCESS" : "FAILED") << "\n";
    oss << "Materials Exported: " << materialCount << "\n";
    oss << "Textures Exported: " << textureCount << "\n";
    oss << "Files Created: " << exportedFiles.size() << "\n";
    oss << "Export Time: " << std::fixed << std::setprecision(2) << exportTime << "s\n";
    
    if (!outputPath.empty()) {
        oss << "Output Path: " << outputPath << "\n";
    }
    
    if (hasWarnings()) {
        oss << "Warnings: " << warnings.size() << "\n";
    }
    
    if (hasErrors()) {
        oss << "Errors: " << errors.size() << "\n";
    }
    
    return oss.str();
}

// MaterialExporter implementation
MaterialExporter::MaterialExporter() {
    // Initialize exporter
}

MaterialExporter::~MaterialExporter() {
    // Cleanup
}

ExportResult MaterialExporter::exportMaterial(const std::shared_ptr<Material>& material, 
                                             const ExportOptions& options) {
    auto startTime = std::chrono::high_resolution_clock::now();
    
    ExportResult result;
    result.outputPath = options.outputPath;
    
    if (!material) {
        result.errors.push_back("Material is null");
        return result;
    }
    
    if (!validateExportOptions(options)) {
        result.errors.push_back("Invalid export options");
        return result;
    }
    
    updateProgress(0.0f, "Starting material export...");
    
    try {
        // Convert material to export data
        MaterialExportData exportData = convertMaterialToExportData(material);
        std::vector<MaterialExportData> materials = { exportData };
        
        updateProgress(0.2f, "Converting material data...");
        
        // Create output directory
        std::filesystem::path outputDir = std::filesystem::path(options.outputPath).parent_path();
        if (!createOutputDirectory(outputDir.string())) {
            result.errors.push_back("Failed to create output directory: " + outputDir.string());
            return result;
        }
        
        updateProgress(0.4f, "Creating output directory...");
        
        // Export based on format
        switch (options.format) {
            case ExportFormat::MTL:
                result = exportToMTL(materials, options);
                break;
            case ExportFormat::GLTF:
                result = exportToGLTF(materials, options);
                break;
            case ExportFormat::JSON:
                result = exportToJSON(materials, options);
                break;
            case ExportFormat::OBJ_MTL:
                result = exportToOBJMTL(materials, options);
                break;
            default:
                result.errors.push_back("Unsupported export format");
                return result;
        }
        
        updateProgress(0.8f, "Exporting material file...");
        
        // Export textures if requested
        if (options.includeTextures && result.success) {
            auto textureFiles = exportTextures(materials, options);
            result.exportedFiles.insert(result.exportedFiles.end(), 
                                       textureFiles.begin(), textureFiles.end());
            result.textureCount = textureFiles.size();
        }
        
        updateProgress(1.0f, "Export completed");
        
        result.materialCount = 1;
        
    } catch (const std::exception& e) {
        result.success = false;
        result.errors.push_back("Export exception: " + std::string(e.what()));
    }
    
    // Calculate export time
    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
    result.exportTime = duration.count() / 1000.0;
    
    // Update statistics
    m_totalExports++;
    m_totalExportTime += result.exportTime;
    if (result.success) {
        m_successfulExports++;
    } else {
        m_failedExports++;
    }
    
    return result;
}

ExportResult MaterialExporter::exportMaterials(const std::vector<std::shared_ptr<Material>>& materials, 
                                              const ExportOptions& options) {
    auto startTime = std::chrono::high_resolution_clock::now();
    
    ExportResult result;
    result.outputPath = options.outputPath;
    
    if (materials.empty()) {
        result.errors.push_back("No materials to export");
        return result;
    }
    
    if (!validateExportOptions(options)) {
        result.errors.push_back("Invalid export options");
        return result;
    }
    
    updateProgress(0.0f, "Starting materials export...");
    
    try {
        // Convert materials to export data
        std::vector<MaterialExportData> exportData;
        for (size_t i = 0; i < materials.size(); ++i) {
            if (materials[i]) {
                exportData.push_back(convertMaterialToExportData(materials[i]));
                updateProgress(0.1f + (0.3f * i / materials.size()), 
                              "Converting material " + std::to_string(i + 1) + "...");
            } else {
                result.warnings.push_back("Skipping null material at index " + std::to_string(i));
            }
        }
        
        if (exportData.empty()) {
            result.errors.push_back("No valid materials to export");
            return result;
        }
        
        // Create output directory
        std::filesystem::path outputDir = std::filesystem::path(options.outputPath).parent_path();
        if (!createOutputDirectory(outputDir.string())) {
            result.errors.push_back("Failed to create output directory: " + outputDir.string());
            return result;
        }
        
        updateProgress(0.4f, "Creating output directory...");
        
        // Export based on format
        switch (options.format) {
            case ExportFormat::MTL:
                result = exportToMTL(exportData, options);
                break;
            case ExportFormat::GLTF:
                result = exportToGLTF(exportData, options);
                break;
            case ExportFormat::JSON:
                result = exportToJSON(exportData, options);
                break;
            case ExportFormat::OBJ_MTL:
                result = exportToOBJMTL(exportData, options);
                break;
            default:
                result.errors.push_back("Unsupported export format");
                return result;
        }
        
        updateProgress(0.8f, "Exporting materials file...");
        
        // Export textures if requested
        if (options.includeTextures && result.success) {
            auto textureFiles = exportTextures(exportData, options);
            result.exportedFiles.insert(result.exportedFiles.end(), 
                                       textureFiles.begin(), textureFiles.end());
            result.textureCount = textureFiles.size();
        }
        
        updateProgress(1.0f, "Export completed");
        
        result.materialCount = exportData.size();
        
    } catch (const std::exception& e) {
        result.success = false;
        result.errors.push_back("Export exception: " + std::string(e.what()));
    }
    
    // Calculate export time
    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
    result.exportTime = duration.count() / 1000.0;
    
    // Update statistics
    m_totalExports++;
    m_totalExportTime += result.exportTime;
    if (result.success) {
        m_successfulExports++;
    } else {
        m_failedExports++;
    }
    
    return result;
}

ExportResult MaterialExporter::exportToMTL(const std::vector<MaterialExportData>& materials, 
                                          const ExportOptions& options) {
    ExportResult result;
    result.outputPath = options.outputPath;
    
    try {
        if (writeMTLFile(materials, options.outputPath, options)) {
            result.success = true;
            result.exportedFiles.push_back(options.outputPath);
        } else {
            result.errors.push_back("Failed to write MTL file");
        }
    } catch (const std::exception& e) {
        result.errors.push_back("MTL export error: " + std::string(e.what()));
    }
    
    return result;
}

ExportResult MaterialExporter::exportToGLTF(const std::vector<MaterialExportData>& materials, 
                                           const ExportOptions& options) {
    ExportResult result;
    result.outputPath = options.outputPath;
    
    try {
        if (writeGLTFFile(materials, options.outputPath, options)) {
            result.success = true;
            result.exportedFiles.push_back(options.outputPath);
        } else {
            result.errors.push_back("Failed to write glTF file");
        }
    } catch (const std::exception& e) {
        result.errors.push_back("glTF export error: " + std::string(e.what()));
    }
    
    return result;
}

ExportResult MaterialExporter::exportToJSON(const std::vector<MaterialExportData>& materials, 
                                           const ExportOptions& options) {
    ExportResult result;
    result.outputPath = options.outputPath;
    
    try {
        if (writeJSONFile(materials, options.outputPath, options)) {
            result.success = true;
            result.exportedFiles.push_back(options.outputPath);
        } else {
            result.errors.push_back("Failed to write JSON file");
        }
    } catch (const std::exception& e) {
        result.errors.push_back("JSON export error: " + std::string(e.what()));
    }
    
    return result;
}

MaterialExportData MaterialExporter::convertMaterialToExportData(const std::shared_ptr<Material>& material) {
    MaterialExportData data;
    
    if (!material) {
        return data;
    }
    
    // Basic material info
    data.name = material->getName();
    data.id = generateMaterialId(data.name);
    
    // Try to get Disney BRDF parameters
    auto pbrMaterial = std::dynamic_pointer_cast<PBRMaterial>(material);
    if (pbrMaterial) {
        data.brdfParams = pbrMaterial->getDisneyParams();
        
        // Get texture assignments
        // TODO: Interface with texture system to get actual texture paths
        // For now, use placeholder paths
        data.diffuseTexture = "textures/" + data.name + "_diffuse.png";
        data.normalTexture = "textures/" + data.name + "_normal.png";
        data.roughnessTexture = "textures/" + data.name + "_roughness.png";
        data.metallicTexture = "textures/" + data.name + "_metallic.png";
    }
    
    // Add metadata
    data.metadata["exportTime"] = std::to_string(std::time(nullptr));
    data.metadata["exporter"] = "PhotonRender";
    data.metadata["version"] = "1.0";
    
    return data;
}

std::vector<ExportFormat> MaterialExporter::getSupportedFormats() const {
    return {
        ExportFormat::MTL,
        ExportFormat::GLTF,
        ExportFormat::JSON,
        ExportFormat::OBJ_MTL
    };
}

std::string MaterialExporter::getFormatName(ExportFormat format) const {
    switch (format) {
        case ExportFormat::MTL: return "Wavefront MTL";
        case ExportFormat::GLTF: return "glTF 2.0";
        case ExportFormat::JSON: return "PhotonRender JSON";
        case ExportFormat::OBJ_MTL: return "OBJ + MTL";
        case ExportFormat::BLENDER: return "Blender";
        case ExportFormat::MAYA: return "Maya";
        case ExportFormat::MAX: return "3ds Max";
        case ExportFormat::SUBSTANCE: return "Substance Designer";
        case ExportFormat::UNREAL: return "Unreal Engine";
        case ExportFormat::UNITY: return "Unity";
        default: return "Unknown";
    }
}

std::string MaterialExporter::getFormatExtension(ExportFormat format) const {
    switch (format) {
        case ExportFormat::MTL: return ".mtl";
        case ExportFormat::GLTF: return ".gltf";
        case ExportFormat::JSON: return ".json";
        case ExportFormat::OBJ_MTL: return ".obj";
        default: return ".txt";
    }
}

void MaterialExporter::setProgressCallback(std::function<void(float, const std::string&)> callback) {
    m_progressCallback = callback;
}

std::string MaterialExporter::getExportStats() const {
    std::ostringstream oss;
    
    oss << "Material Export Statistics:\n";
    oss << "Total Exports: " << m_totalExports << "\n";
    oss << "Successful: " << m_successfulExports << "\n";
    oss << "Failed: " << m_failedExports << "\n";
    
    if (m_totalExports > 0) {
        float successRate = (static_cast<float>(m_successfulExports) / m_totalExports) * 100.0f;
        oss << "Success Rate: " << std::fixed << std::setprecision(1) << successRate << "%\n";
        
        double avgTime = m_totalExportTime / m_totalExports;
        oss << "Average Export Time: " << std::fixed << std::setprecision(2) << avgTime << "s\n";
    }
    
    oss << "Total Export Time: " << std::fixed << std::setprecision(2) << m_totalExportTime << "s";
    
    return oss.str();
}

bool MaterialExporter::createOutputDirectory(const std::string& path) {
    try {
        if (!path.empty() && !std::filesystem::exists(path)) {
            return std::filesystem::create_directories(path);
        }
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error creating directory: " << e.what() << std::endl;
        return false;
    }
}

std::vector<std::string> MaterialExporter::exportTextures(const std::vector<MaterialExportData>& materials,
                                                         const ExportOptions& options) {
    std::vector<std::string> exportedTextures;

    if (!options.includeTextures) {
        return exportedTextures;
    }

    // Create textures subdirectory
    std::filesystem::path outputDir = std::filesystem::path(options.outputPath).parent_path();
    std::filesystem::path textureDir = outputDir / options.textureSubdirectory;

    if (!createOutputDirectory(textureDir.string())) {
        return exportedTextures;
    }

    for (const auto& material : materials) {
        // Export each texture type
        std::vector<std::string> textureTypes = {
            material.diffuseTexture,
            material.normalTexture,
            material.roughnessTexture,
            material.metallicTexture,
            material.specularTexture,
            material.emissionTexture,
            material.opacityTexture,
            material.displacementTexture,
            material.ambientOcclusionTexture,
            material.subsurfaceTexture,
            material.clearcoatTexture,
            material.clearcoatNormalTexture,
            material.anisotropyTexture,
            material.sheenTexture,
            material.environmentTexture
        };

        for (const auto& texturePath : textureTypes) {
            if (!texturePath.empty() && std::filesystem::exists(texturePath)) {
                std::filesystem::path sourceFile(texturePath);
                std::filesystem::path targetFile = textureDir / sourceFile.filename();

                if (copyTextureFile(texturePath, targetFile.string(), options)) {
                    exportedTextures.push_back(targetFile.string());
                }
            }
        }
    }

    return exportedTextures;
}

bool MaterialExporter::copyTextureFile(const std::string& sourcePath,
                                      const std::string& targetPath,
                                      const ExportOptions& options) {
    try {
        // Copy file
        std::filesystem::copy_file(sourcePath, targetPath,
                                  std::filesystem::copy_options::overwrite_existing);

        // Optimize if requested
        if (options.optimizeTextures) {
            optimizeTexture(targetPath, options);
        }

        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error copying texture: " << e.what() << std::endl;
        return false;
    }
}

bool MaterialExporter::optimizeTexture(const std::string& texturePath,
                                      const ExportOptions& options) {
    // TODO: Implement texture optimization
    // For now, just return true
    return true;
}

bool MaterialExporter::validateExportOptions(const ExportOptions& options) {
    if (options.outputPath.empty()) {
        return false;
    }

    if (options.maxTextureSize <= 0) {
        return false;
    }

    if (options.textureQuality < 0.0f || options.textureQuality > 1.0f) {
        return false;
    }

    return true;
}

void MaterialExporter::updateProgress(float progress, const std::string& message) {
    if (m_progressCallback) {
        m_progressCallback(progress, message);
    }
}

std::string MaterialExporter::generateMaterialId(const std::string& materialName) {
    // Generate a simple ID based on material name
    std::string id = sanitizeFilename(materialName);
    if (id.empty()) {
        id = "material_" + std::to_string(std::time(nullptr));
    }
    return id;
}

std::string MaterialExporter::sanitizeFilename(const std::string& filename) {
    std::string sanitized = filename;

    // Replace invalid characters
    std::replace_if(sanitized.begin(), sanitized.end(),
                   [](char c) { return !std::isalnum(c) && c != '_' && c != '-'; }, '_');

    // Remove leading/trailing underscores
    sanitized.erase(0, sanitized.find_first_not_of('_'));
    sanitized.erase(sanitized.find_last_not_of('_') + 1);

    return sanitized;
}

bool MaterialExporter::writeMTLFile(const std::vector<MaterialExportData>& materials,
                                   const std::string& filePath,
                                   const ExportOptions& options) {
    try {
        std::ofstream file(filePath);
        if (!file.is_open()) {
            return false;
        }

        // Write MTL header
        file << "# PhotonRender MTL Export\n";
        file << "# Generated on " << std::time(nullptr) << "\n\n";

        for (const auto& material : materials) {
            file << "newmtl " << material.name << "\n";

            // Convert Disney BRDF to MTL parameters
            auto mtlParams = convertToMTLParams(material.brdfParams);

            // Write basic material properties
            file << "Ka " << material.brdfParams.baseColor.r << " "
                 << material.brdfParams.baseColor.g << " "
                 << material.brdfParams.baseColor.b << "\n";

            file << "Kd " << material.brdfParams.baseColor.r << " "
                 << material.brdfParams.baseColor.g << " "
                 << material.brdfParams.baseColor.b << "\n";

            file << "Ks " << material.brdfParams.specular << " "
                 << material.brdfParams.specular << " "
                 << material.brdfParams.specular << "\n";

            file << "Ns " << (1.0f - material.brdfParams.roughness) * 1000.0f << "\n";

            // Write texture maps
            if (!material.diffuseTexture.empty()) {
                std::string texPath = options.useRelativePaths ?
                    generateRelativePath(filePath, material.diffuseTexture) :
                    material.diffuseTexture;
                file << "map_Kd " << texPath << "\n";
            }

            if (!material.normalTexture.empty()) {
                std::string texPath = options.useRelativePaths ?
                    generateRelativePath(filePath, material.normalTexture) :
                    material.normalTexture;
                file << "map_Bump " << texPath << "\n";
            }

            file << "\n";
        }

        file.close();
        return true;

    } catch (const std::exception& e) {
        std::cerr << "Error writing MTL file: " << e.what() << std::endl;
        return false;
    }
}

bool MaterialExporter::writeGLTFFile(const std::vector<MaterialExportData>& materials,
                                    const std::string& filePath,
                                    const ExportOptions& options) {
    try {
        std::ofstream file(filePath);
        if (!file.is_open()) {
            return false;
        }

        // Write glTF JSON structure
        file << "{\n";
        file << "  \"asset\": {\n";
        file << "    \"version\": \"2.0\",\n";
        file << "    \"generator\": \"PhotonRender\"\n";
        file << "  },\n";
        file << "  \"materials\": [\n";

        for (size_t i = 0; i < materials.size(); ++i) {
            const auto& material = materials[i];

            file << "    {\n";
            file << "      \"name\": \"" << material.name << "\",\n";
            file << "      \"pbrMetallicRoughness\": {\n";
            file << "        \"baseColorFactor\": ["
                 << material.brdfParams.baseColor.r << ", "
                 << material.brdfParams.baseColor.g << ", "
                 << material.brdfParams.baseColor.b << ", 1.0],\n";
            file << "        \"metallicFactor\": " << material.brdfParams.metallic << ",\n";
            file << "        \"roughnessFactor\": " << material.brdfParams.roughness << "\n";
            file << "      }\n";
            file << "    }";

            if (i < materials.size() - 1) {
                file << ",";
            }
            file << "\n";
        }

        file << "  ]\n";
        file << "}\n";

        file.close();
        return true;

    } catch (const std::exception& e) {
        std::cerr << "Error writing glTF file: " << e.what() << std::endl;
        return false;
    }
}

bool MaterialExporter::writeJSONFile(const std::vector<MaterialExportData>& materials,
                                    const std::string& filePath,
                                    const ExportOptions& options) {
    try {
        std::ofstream file(filePath);
        if (!file.is_open()) {
            return false;
        }

        // Write PhotonRender JSON format
        file << "{\n";
        file << "  \"format\": \"PhotonRender Materials\",\n";
        file << "  \"version\": \"1.0\",\n";
        file << "  \"exportTime\": " << std::time(nullptr) << ",\n";
        file << "  \"materials\": [\n";

        for (size_t i = 0; i < materials.size(); ++i) {
            const auto& material = materials[i];

            file << "    {\n";
            file << "      \"name\": \"" << material.name << "\",\n";
            file << "      \"id\": \"" << material.id << "\",\n";
            file << "      \"type\": \"DisneyBRDF\",\n";
            file << "      \"parameters\": {\n";
            file << "        \"baseColor\": ["
                 << material.brdfParams.baseColor.r << ", "
                 << material.brdfParams.baseColor.g << ", "
                 << material.brdfParams.baseColor.b << "],\n";
            file << "        \"metallic\": " << material.brdfParams.metallic << ",\n";
            file << "        \"roughness\": " << material.brdfParams.roughness << ",\n";
            file << "        \"specular\": " << material.brdfParams.specular << ",\n";
            file << "        \"specularTint\": " << material.brdfParams.specularTint << ",\n";
            file << "        \"anisotropic\": " << material.brdfParams.anisotropic << ",\n";
            file << "        \"sheen\": " << material.brdfParams.sheen << ",\n";
            file << "        \"sheenTint\": " << material.brdfParams.sheenTint << ",\n";
            file << "        \"clearcoat\": " << material.brdfParams.clearcoat << ",\n";
            file << "        \"clearcoatGloss\": " << material.brdfParams.clearcoatGloss << ",\n";
            file << "        \"subsurface\": " << material.brdfParams.subsurface << "\n";
            file << "      }";

            // Add textures if present
            if (!material.diffuseTexture.empty() || !material.normalTexture.empty()) {
                file << ",\n      \"textures\": {\n";

                if (!material.diffuseTexture.empty()) {
                    file << "        \"diffuse\": \"" << material.diffuseTexture << "\"";
                    if (!material.normalTexture.empty()) file << ",";
                    file << "\n";
                }

                if (!material.normalTexture.empty()) {
                    file << "        \"normal\": \"" << material.normalTexture << "\"\n";
                }

                file << "      }";
            }

            file << "\n    }";

            if (i < materials.size() - 1) {
                file << ",";
            }
            file << "\n";
        }

        file << "  ]\n";
        file << "}\n";

        file.close();
        return true;

    } catch (const std::exception& e) {
        std::cerr << "Error writing JSON file: " << e.what() << std::endl;
        return false;
    }
}

std::unordered_map<std::string, std::string> MaterialExporter::convertToMTLParams(const DisneyBRDFParams& params) {
    std::unordered_map<std::string, std::string> mtlParams;

    // Convert Disney BRDF to MTL approximation
    mtlParams["Ka"] = std::to_string(params.baseColor.r * 0.1f) + " " +
                      std::to_string(params.baseColor.g * 0.1f) + " " +
                      std::to_string(params.baseColor.b * 0.1f);

    mtlParams["Kd"] = std::to_string(params.baseColor.r) + " " +
                      std::to_string(params.baseColor.g) + " " +
                      std::to_string(params.baseColor.b);

    mtlParams["Ks"] = std::to_string(params.specular) + " " +
                      std::to_string(params.specular) + " " +
                      std::to_string(params.specular);

    mtlParams["Ns"] = std::to_string((1.0f - params.roughness) * 1000.0f);

    return mtlParams;
}

std::unordered_map<std::string, std::string> MaterialExporter::convertToGLTFParams(const DisneyBRDFParams& params) {
    std::unordered_map<std::string, std::string> gltfParams;

    // Convert Disney BRDF to glTF PBR
    gltfParams["baseColorFactor"] = "[" + std::to_string(params.baseColor.r) + ", " +
                                          std::to_string(params.baseColor.g) + ", " +
                                          std::to_string(params.baseColor.b) + ", 1.0]";

    gltfParams["metallicFactor"] = std::to_string(params.metallic);
    gltfParams["roughnessFactor"] = std::to_string(params.roughness);

    return gltfParams;
}

std::string MaterialExporter::generateRelativePath(const std::string& fromPath,
                                                  const std::string& toPath) {
    try {
        std::filesystem::path from(fromPath);
        std::filesystem::path to(toPath);

        return std::filesystem::relative(to, from.parent_path()).string();
    } catch (const std::exception& e) {
        // Fallback to absolute path
        return toPath;
    }
}

ExportResult MaterialExporter::exportToOBJMTL(const std::vector<MaterialExportData>& materials,
                                             const ExportOptions& options) {
    ExportResult result;

    // Export MTL file first
    std::filesystem::path mtlPath = std::filesystem::path(options.outputPath).replace_extension(".mtl");
    auto mtlResult = exportToMTL(materials, ExportOptions{
        .format = ExportFormat::MTL,
        .outputPath = mtlPath.string(),
        .includeTextures = options.includeTextures,
        .useRelativePaths = options.useRelativePaths,
        .textureSubdirectory = options.textureSubdirectory
    });

    if (!mtlResult.success) {
        result.errors = mtlResult.errors;
        return result;
    }

    // Create simple OBJ file that references the MTL
    try {
        std::ofstream objFile(options.outputPath);
        if (!objFile.is_open()) {
            result.errors.push_back("Failed to create OBJ file");
            return result;
        }

        objFile << "# PhotonRender OBJ Export\n";
        objFile << "mtllib " << mtlPath.filename().string() << "\n\n";

        // Add a simple quad for each material (for preview purposes)
        for (size_t i = 0; i < materials.size(); ++i) {
            float offset = static_cast<float>(i) * 2.0f;

            objFile << "# Material: " << materials[i].name << "\n";
            objFile << "v " << (0.0f + offset) << " 0.0 0.0\n";
            objFile << "v " << (1.0f + offset) << " 0.0 0.0\n";
            objFile << "v " << (1.0f + offset) << " 1.0 0.0\n";
            objFile << "v " << (0.0f + offset) << " 1.0 0.0\n";

            objFile << "vt 0.0 0.0\n";
            objFile << "vt 1.0 0.0\n";
            objFile << "vt 1.0 1.0\n";
            objFile << "vt 0.0 1.0\n";

            objFile << "usemtl " << materials[i].name << "\n";

            int baseVertex = static_cast<int>(i * 4 + 1);
            objFile << "f " << baseVertex << "/" << baseVertex << " "
                    << (baseVertex + 1) << "/" << (baseVertex + 1) << " "
                    << (baseVertex + 2) << "/" << (baseVertex + 2) << " "
                    << (baseVertex + 3) << "/" << (baseVertex + 3) << "\n\n";
        }

        objFile.close();

        result.success = true;
        result.outputPath = options.outputPath;
        result.exportedFiles.push_back(options.outputPath);
        result.exportedFiles.insert(result.exportedFiles.end(),
                                   mtlResult.exportedFiles.begin(),
                                   mtlResult.exportedFiles.end());

    } catch (const std::exception& e) {
        result.errors.push_back("OBJ export error: " + std::string(e.what()));
    }

    return result;
}

} // namespace photon
