// src/core/material/material_library.cpp
// PhotonRender - Material Library Management System Implementation
// Implementazione sistema di gestione libreria materiali

#include "material_library.hpp"
#include "../image/image.hpp"
#include "../preview/preview_renderer.hpp"
#include <filesystem>
#include <fstream>
#include <sstream>
#include <algorithm>
#include <chrono>
#include <random>
#include <iostream>

namespace photon {

MaterialLibrary::MaterialLibrary() {
    // Initialize with empty state
}

MaterialLibrary::~MaterialLibrary() {
    shutdown();
}

bool MaterialLibrary::initialize(const std::string& libraryPath) {
    if (m_initialized) {
        shutdown();
    }
    
    m_libraryPath = libraryPath;
    
    try {
        // Create library directory if it doesn't exist
        std::filesystem::create_directories(libraryPath);
        
        // Create subdirectories
        std::filesystem::create_directories(libraryPath + "/materials");
        std::filesystem::create_directories(libraryPath + "/thumbnails");
        std::filesystem::create_directories(libraryPath + "/previews");
        std::filesystem::create_directories(libraryPath + "/textures");
        
        // Load existing library
        if (!loadLibrary()) {
            std::cout << "Warning: Could not load existing library, starting fresh" << std::endl;
        }
        
        m_initialized = true;
        
        std::cout << "MaterialLibrary initialized successfully" << std::endl;
        std::cout << "Library path: " << libraryPath << std::endl;
        std::cout << "Materials loaded: " << m_materials.size() << std::endl;
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "MaterialLibrary initialization failed: " << e.what() << std::endl;
        return false;
    }
}

void MaterialLibrary::shutdown() {
    if (!m_initialized) return;
    
    // Save library if dirty
    if (m_dirty) {
        saveLibrary();
    }
    
    // Clear all materials
    m_materials.clear();
    m_recentMaterials.clear();
    
    m_initialized = false;
    m_dirty = false;
    
    std::cout << "MaterialLibrary shutdown complete" << std::endl;
}

bool MaterialLibrary::loadLibrary() {
    updateProgress(0.0f, "Loading material library...");
    
    std::string indexPath = m_libraryPath + "/library_index.json";
    
    if (!std::filesystem::exists(indexPath)) {
        std::cout << "Library index not found, creating new library" << std::endl;
        return true; // Not an error, just empty library
    }
    
    try {
        std::ifstream file(indexPath);
        if (!file.is_open()) {
            std::cerr << "Could not open library index file" << std::endl;
            return false;
        }
        
        // TODO: Parse JSON library index
        // For now, scan materials directory
        std::string materialsDir = m_libraryPath + "/materials";
        
        if (!std::filesystem::exists(materialsDir)) {
            return true; // Empty library
        }
        
        int loadedCount = 0;
        int totalFiles = 0;
        
        // Count total files first
        for (const auto& entry : std::filesystem::directory_iterator(materialsDir)) {
            if (entry.path().extension() == ".json") {
                totalFiles++;
            }
        }
        
        // Load materials
        for (const auto& entry : std::filesystem::directory_iterator(materialsDir)) {
            if (entry.path().extension() == ".json") {
                auto materialEntry = loadMaterialFromFile(entry.path().string());
                if (materialEntry) {
                    m_materials[materialEntry->metadata.id] = materialEntry;
                    loadedCount++;
                }
                
                updateProgress(static_cast<float>(loadedCount) / totalFiles, 
                             "Loading material: " + entry.path().filename().string());
            }
        }
        
        updateProgress(1.0f, "Library loaded successfully");
        
        std::cout << "Loaded " << loadedCount << " materials from library" << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "Error loading library: " << e.what() << std::endl;
        return false;
    }
}

bool MaterialLibrary::saveLibrary() {
    if (!m_initialized) return false;
    
    updateProgress(0.0f, "Saving material library...");
    
    try {
        std::string indexPath = m_libraryPath + "/library_index.json";
        std::ofstream file(indexPath);
        
        if (!file.is_open()) {
            std::cerr << "Could not create library index file" << std::endl;
            return false;
        }
        
        // Write library index (simplified JSON format)
        file << "{\n";
        file << "  \"version\": \"1.0\",\n";
        file << "  \"materials\": [\n";
        
        bool first = true;
        for (const auto& pair : m_materials) {
            if (!first) file << ",\n";
            first = false;
            
            const auto& metadata = pair.second->metadata;
            file << "    {\n";
            file << "      \"id\": \"" << metadata.id << "\",\n";
            file << "      \"name\": \"" << metadata.name << "\",\n";
            file << "      \"category\": \"" << getCategoryString(metadata.category) << "\",\n";
            file << "      \"rating\": " << metadata.rating << ",\n";
            file << "      \"downloadCount\": " << metadata.downloadCount << "\n";
            file << "    }";
        }
        
        file << "\n  ]\n";
        file << "}\n";
        
        file.close();
        
        m_dirty = false;
        updateProgress(1.0f, "Library saved successfully");
        
        std::cout << "Library saved with " << m_materials.size() << " materials" << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "Error saving library: " << e.what() << std::endl;
        return false;
    }
}

std::string MaterialLibrary::addMaterial(std::shared_ptr<Material> material, const MaterialMetadata& metadata) {
    if (!m_initialized || !material) return "";
    
    // Generate unique ID if not provided
    std::string materialId = metadata.id.empty() ? generateMaterialId() : metadata.id;
    
    // Check if ID already exists
    if (m_materials.find(materialId) != m_materials.end()) {
        std::cerr << "Material ID already exists: " << materialId << std::endl;
        return "";
    }
    
    // Create material entry
    auto entry = std::make_shared<MaterialEntry>();
    entry->metadata = metadata;
    entry->metadata.id = materialId;
    entry->material = material;
    entry->isLoaded = true;
    entry->isDirty = true;
    
    // Set creation date if not provided
    if (entry->metadata.createdDate.empty()) {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        std::stringstream ss;
        ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
        entry->metadata.createdDate = ss.str();
        entry->metadata.modifiedDate = ss.str();
    }
    
    // Add to library
    m_materials[materialId] = entry;
    m_dirty = true;
    
    // Generate thumbnail and preview
    generateThumbnail(materialId);
    generatePreview(materialId);
    
    std::cout << "Added material to library: " << materialId << " (" << metadata.name << ")" << std::endl;
    return materialId;
}

bool MaterialLibrary::removeMaterial(const std::string& materialId) {
    auto it = m_materials.find(materialId);
    if (it == m_materials.end()) {
        return false;
    }
    
    // Remove files
    std::string materialFile = m_libraryPath + "/materials/" + materialId + ".json";
    std::string thumbnailFile = m_libraryPath + "/thumbnails/" + materialId + ".png";
    std::string previewFile = m_libraryPath + "/previews/" + materialId + ".png";
    
    std::filesystem::remove(materialFile);
    std::filesystem::remove(thumbnailFile);
    std::filesystem::remove(previewFile);
    
    // Remove from recent materials
    m_recentMaterials.erase(
        std::remove(m_recentMaterials.begin(), m_recentMaterials.end(), materialId),
        m_recentMaterials.end()
    );
    
    // Remove from library
    m_materials.erase(it);
    m_dirty = true;
    
    std::cout << "Removed material from library: " << materialId << std::endl;
    return true;
}

std::shared_ptr<MaterialEntry> MaterialLibrary::getMaterial(const std::string& materialId) {
    auto it = m_materials.find(materialId);
    if (it == m_materials.end()) {
        return nullptr;
    }
    
    // Update recent materials list
    auto recentIt = std::find(m_recentMaterials.begin(), m_recentMaterials.end(), materialId);
    if (recentIt != m_recentMaterials.end()) {
        m_recentMaterials.erase(recentIt);
    }
    m_recentMaterials.insert(m_recentMaterials.begin(), materialId);
    
    // Keep only last 50 recent materials
    if (m_recentMaterials.size() > 50) {
        m_recentMaterials.resize(50);
    }
    
    return it->second;
}

std::vector<std::string> MaterialLibrary::searchMaterials(const MaterialSearchCriteria& criteria) {
    std::vector<std::string> results;
    
    for (const auto& pair : m_materials) {
        const auto& entry = pair.second;
        const auto& metadata = entry->metadata;
        
        // Category filter
        if (criteria.category != MaterialCategory::ALL && metadata.category != criteria.category) {
            continue;
        }
        
        // Name filter
        if (!criteria.nameFilter.empty()) {
            std::string lowerName = metadata.name;
            std::string lowerDesc = metadata.description;
            std::string lowerFilter = criteria.nameFilter;
            
            std::transform(lowerName.begin(), lowerName.end(), lowerName.begin(), ::tolower);
            std::transform(lowerDesc.begin(), lowerDesc.end(), lowerDesc.begin(), ::tolower);
            std::transform(lowerFilter.begin(), lowerFilter.end(), lowerFilter.begin(), ::tolower);
            
            if (lowerName.find(lowerFilter) == std::string::npos && 
                lowerDesc.find(lowerFilter) == std::string::npos) {
                continue;
            }
        }
        
        // Rating filter
        if (metadata.rating < criteria.minRating) {
            continue;
        }
        
        // Required tags filter
        bool hasAllRequiredTags = true;
        for (const auto& requiredTag : criteria.requiredTags) {
            if (std::find(metadata.tags.begin(), metadata.tags.end(), requiredTag) == metadata.tags.end()) {
                hasAllRequiredTags = false;
                break;
            }
        }
        if (!hasAllRequiredTags) {
            continue;
        }
        
        // Excluded tags filter
        bool hasExcludedTag = false;
        for (const auto& excludedTag : criteria.excludedTags) {
            if (std::find(metadata.tags.begin(), metadata.tags.end(), excludedTag) != metadata.tags.end()) {
                hasExcludedTag = true;
                break;
            }
        }
        if (hasExcludedTag) {
            continue;
        }
        
        // Custom only filter
        if (criteria.customOnly && metadata.category != MaterialCategory::CUSTOM) {
            continue;
        }
        
        // Recent only filter
        if (criteria.recentOnly) {
            if (std::find(m_recentMaterials.begin(), m_recentMaterials.end(), pair.first) == m_recentMaterials.end()) {
                continue;
            }
        }
        
        // Material passed all filters
        results.push_back(pair.first);
    }
    
    return results;
}

std::vector<std::string> MaterialLibrary::getMaterialsByCategory(MaterialCategory category) {
    std::vector<std::string> results;
    
    for (const auto& pair : m_materials) {
        if (pair.second->metadata.category == category) {
            results.push_back(pair.first);
        }
    }
    
    return results;
}

std::vector<std::string> MaterialLibrary::getAllMaterialIds() {
    std::vector<std::string> results;
    results.reserve(m_materials.size());
    
    for (const auto& pair : m_materials) {
        results.push_back(pair.first);
    }
    
    return results;
}

size_t MaterialLibrary::getMaterialCount() const {
    return m_materials.size();
}

size_t MaterialLibrary::getMaterialCountByCategory(MaterialCategory category) const {
    size_t count = 0;
    
    for (const auto& pair : m_materials) {
        if (pair.second->metadata.category == category) {
            count++;
        }
    }
    
    return count;
}

bool MaterialLibrary::generateThumbnail(const std::string& materialId, int size) {
    auto entry = getMaterial(materialId);
    if (!entry || !entry->material) {
        return false;
    }

    try {
        // Create preview renderer for thumbnail generation
        PreviewRenderer renderer;
        PreviewSettings settings{
            .width = size,
            .height = size,
            .samples = 8,  // Lower samples for faster thumbnail generation
            .maxDepth = 4,
            .geometry = PreviewGeometry::SPHERE,
            .lighting = PreviewLighting::STUDIO,
            .enableDenoising = false,
            .enableToneMapping = true
        };

        if (!renderer.initialize(settings)) {
            std::cerr << "Failed to initialize preview renderer for thumbnail" << std::endl;
            return false;
        }

        // Set material and render
        renderer.setMaterial(entry->material);
        auto image = renderer.render();

        if (!image) {
            std::cerr << "Failed to render thumbnail for material: " << materialId << std::endl;
            return false;
        }

        // Save thumbnail
        std::string thumbnailPath = m_libraryPath + "/thumbnails/" + materialId + ".png";
        if (!image->save(thumbnailPath)) {
            std::cerr << "Failed to save thumbnail: " << thumbnailPath << std::endl;
            return false;
        }

        // Update metadata
        entry->metadata.thumbnailPath = thumbnailPath;
        entry->thumbnail = image;
        entry->isDirty = true;
        m_dirty = true;

        renderer.shutdown();

        std::cout << "Generated thumbnail for material: " << materialId << std::endl;
        return true;

    } catch (const std::exception& e) {
        std::cerr << "Error generating thumbnail: " << e.what() << std::endl;
        return false;
    }
}

bool MaterialLibrary::generatePreview(const std::string& materialId, int size) {
    auto entry = getMaterial(materialId);
    if (!entry || !entry->material) {
        return false;
    }

    try {
        // Create preview renderer for high-quality preview
        PreviewRenderer renderer;
        PreviewSettings settings{
            .width = size,
            .height = size,
            .samples = 32,  // Higher samples for quality preview
            .maxDepth = 8,
            .geometry = PreviewGeometry::SPHERE,
            .lighting = PreviewLighting::STUDIO,
            .enableDenoising = true,
            .enableToneMapping = true
        };

        if (!renderer.initialize(settings)) {
            std::cerr << "Failed to initialize preview renderer for preview" << std::endl;
            return false;
        }

        // Set material and render
        renderer.setMaterial(entry->material);
        auto image = renderer.render();

        if (!image) {
            std::cerr << "Failed to render preview for material: " << materialId << std::endl;
            return false;
        }

        // Save preview
        std::string previewPath = m_libraryPath + "/previews/" + materialId + ".png";
        if (!image->save(previewPath)) {
            std::cerr << "Failed to save preview: " << previewPath << std::endl;
            return false;
        }

        // Update metadata
        entry->metadata.previewPath = previewPath;
        entry->preview = image;
        entry->isDirty = true;
        m_dirty = true;

        renderer.shutdown();

        std::cout << "Generated preview for material: " << materialId << std::endl;
        return true;

    } catch (const std::exception& e) {
        std::cerr << "Error generating preview: " << e.what() << std::endl;
        return false;
    }
}

std::string MaterialLibrary::generateMaterialId() {
    // Generate UUID-like ID
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, 15);

    std::string id = "mat_";
    const char* chars = "0123456789abcdef";

    for (int i = 0; i < 16; ++i) {
        id += chars[dis(gen)];
        if (i == 3 || i == 7 || i == 11) {
            id += "_";
        }
    }

    return id;
}

std::shared_ptr<MaterialEntry> MaterialLibrary::loadMaterialFromFile(const std::string& filePath) {
    try {
        std::ifstream file(filePath);
        if (!file.is_open()) {
            return nullptr;
        }

        // TODO: Parse JSON material file
        // For now, create a basic entry
        auto entry = std::make_shared<MaterialEntry>();

        // Extract ID from filename
        std::filesystem::path path(filePath);
        entry->metadata.id = path.stem().string();
        entry->metadata.name = "Material " + entry->metadata.id;
        entry->metadata.category = MaterialCategory::CUSTOM;
        entry->isLoaded = false;
        entry->isDirty = false;

        return entry;

    } catch (const std::exception& e) {
        std::cerr << "Error loading material from file: " << e.what() << std::endl;
        return nullptr;
    }
}

void MaterialLibrary::updateProgress(float progress, const std::string& message) {
    if (m_progressCallback) {
        m_progressCallback(progress, message);
    }
}

std::string MaterialLibrary::getCategoryString(MaterialCategory category) const {
    switch (category) {
        case MaterialCategory::METALS: return "metals";
        case MaterialCategory::PLASTICS: return "plastics";
        case MaterialCategory::GLASS: return "glass";
        case MaterialCategory::WOOD: return "wood";
        case MaterialCategory::FABRIC: return "fabric";
        case MaterialCategory::STONE: return "stone";
        case MaterialCategory::ORGANIC: return "organic";
        case MaterialCategory::AUTOMOTIVE: return "automotive";
        case MaterialCategory::ARCHITECTURAL: return "architectural";
        case MaterialCategory::CUSTOM: return "custom";
        default: return "unknown";
    }
}

std::string MaterialLibrary::getTagString(MaterialTag tag) const {
    switch (tag) {
        case MaterialTag::ROUGH: return "rough";
        case MaterialTag::SMOOTH: return "smooth";
        case MaterialTag::REFLECTIVE: return "reflective";
        case MaterialTag::TRANSLUCENT: return "translucent";
        case MaterialTag::EMISSIVE: return "emissive";
        case MaterialTag::WEATHERED: return "weathered";
        case MaterialTag::POLISHED: return "polished";
        case MaterialTag::MATTE: return "matte";
        case MaterialTag::GLOSSY: return "glossy";
        case MaterialTag::TEXTURED: return "textured";
        default: return "unknown";
    }
}

std::vector<std::string> MaterialLibrary::getRecentMaterials(int count) {
    std::vector<std::string> result;
    int actualCount = std::min(count, static_cast<int>(m_recentMaterials.size()));

    for (int i = 0; i < actualCount; ++i) {
        result.push_back(m_recentMaterials[i]);
    }

    return result;
}

std::vector<std::string> MaterialLibrary::getPopularMaterials(int count) {
    std::vector<std::pair<std::string, int>> materialUsage;

    // Collect materials with their usage counts
    for (const auto& pair : m_materials) {
        materialUsage.emplace_back(pair.first, pair.second->metadata.downloadCount);
    }

    // Sort by usage count (descending)
    std::sort(materialUsage.begin(), materialUsage.end(),
              [](const auto& a, const auto& b) {
                  return a.second > b.second;
              });

    // Extract top materials
    std::vector<std::string> result;
    int actualCount = std::min(count, static_cast<int>(materialUsage.size()));

    for (int i = 0; i < actualCount; ++i) {
        result.push_back(materialUsage[i].first);
    }

    return result;
}

std::string MaterialLibrary::getLibraryStats() const {
    std::ostringstream stats;

    stats << "Material Library Statistics:\n";
    stats << "Total materials: " << m_materials.size() << "\n";

    // Count by category
    for (int i = 0; i < static_cast<int>(MaterialCategory::ALL); ++i) {
        MaterialCategory cat = static_cast<MaterialCategory>(i);
        size_t count = getMaterialCountByCategory(cat);
        if (count > 0) {
            stats << getCategoryString(cat) << ": " << count << "\n";
        }
    }

    // Recent materials
    stats << "Recent materials: " << m_recentMaterials.size() << "\n";

    return stats.str();
}

void MaterialLibrary::setMaterialRating(const std::string& materialId, float rating) {
    auto entry = getMaterial(materialId);
    if (entry) {
        entry->metadata.rating = std::clamp(rating, 0.0f, 5.0f);
        entry->isDirty = true;
        m_dirty = true;
    }
}

void MaterialLibrary::incrementUsageCount(const std::string& materialId) {
    auto entry = getMaterial(materialId);
    if (entry) {
        entry->metadata.downloadCount++;
        entry->isDirty = true;
        m_dirty = true;
    }
}

} // namespace photon
