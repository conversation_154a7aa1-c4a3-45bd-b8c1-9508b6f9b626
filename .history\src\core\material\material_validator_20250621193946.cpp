// src/core/material/material_validator.cpp
// PhotonRender - Material Validation System Implementation
// Implementazione sistema di validazione materiali

#include "material_validator.hpp"
#include "material.hpp"
#include <algorithm>
#include <sstream>
#include <iomanip>
#include <cmath>
#include <iostream>

namespace photon {

// ValidationIssue implementation
std::string ValidationIssue::getSeverityString() const {
    switch (severity) {
        case ValidationSeverity::INFO: return "Info";
        case ValidationSeverity::WARNING: return "Warning";
        case ValidationSeverity::ERROR: return "Error";
        case ValidationSeverity::CRITICAL: return "Critical";
        default: return "Unknown";
    }
}

std::string ValidationIssue::getCategoryString() const {
    switch (category) {
        case ValidationCategory::ENERGY_CONSERVATION: return "Energy Conservation";
        case ValidationCategory::PARAMETER_RANGE: return "Parameter Range";
        case ValidationCategory::PARAMETER_COMBINATION: return "Parameter Combination";
        case ValidationCategory::TEXTURE_COMPATIBILITY: return "Texture Compatibility";
        case ValidationCategory::PERFORMANCE: return "Performance";
        case ValidationCategory::PHYSICAL_PLAUSIBILITY: return "Physical Plausibility";
        case ValidationCategory::WORKFLOW: return "Workflow";
        default: return "Unknown";
    }
}

std::string ValidationIssue::getFormattedMessage() const {
    std::ostringstream oss;
    oss << "[" << getSeverityString() << "] " << getCategoryString() << ": " << message;
    
    if (!parameter.empty()) {
        oss << " (Parameter: " << parameter;
        if (currentValue != 0.0f) {
            oss << " = " << std::fixed << std::setprecision(3) << currentValue;
        }
        oss << ")";
    }
    
    if (!suggestion.empty()) {
        oss << " Suggestion: " << suggestion;
        if (suggestedValue != 0.0f) {
            oss << " (Try: " << std::fixed << std::setprecision(3) << suggestedValue << ")";
        }
    }
    
    return oss.str();
}

// ValidationResult implementation
std::vector<ValidationIssue> ValidationResult::getIssuesBySeverity(ValidationSeverity severity) const {
    std::vector<ValidationIssue> result;
    std::copy_if(issues.begin(), issues.end(), std::back_inserter(result),
                 [severity](const ValidationIssue& issue) {
                     return issue.severity == severity;
                 });
    return result;
}

std::vector<ValidationIssue> ValidationResult::getIssuesByCategory(ValidationCategory category) const {
    std::vector<ValidationIssue> result;
    std::copy_if(issues.begin(), issues.end(), std::back_inserter(result),
                 [category](const ValidationIssue& issue) {
                     return issue.category == category;
                 });
    return result;
}

std::vector<ValidationIssue> ValidationResult::getAutoFixableIssues() const {
    std::vector<ValidationIssue> result;
    std::copy_if(issues.begin(), issues.end(), std::back_inserter(result),
                 [](const ValidationIssue& issue) {
                     return issue.autoFixable;
                 });
    return result;
}

std::string ValidationResult::getSummary() const {
    std::ostringstream oss;
    
    oss << "Validation Summary:\n";
    oss << "Overall Status: " << (isValid ? "VALID" : "INVALID") << "\n";
    oss << "Total Issues: " << issues.size() << "\n";
    
    auto errors = getIssuesBySeverity(ValidationSeverity::ERROR);
    auto criticals = getIssuesBySeverity(ValidationSeverity::CRITICAL);
    auto warnings = getIssuesBySeverity(ValidationSeverity::WARNING);
    auto infos = getIssuesBySeverity(ValidationSeverity::INFO);
    
    if (!criticals.empty()) oss << "Critical: " << criticals.size() << "\n";
    if (!errors.empty()) oss << "Errors: " << errors.size() << "\n";
    if (!warnings.empty()) oss << "Warnings: " << warnings.size() << "\n";
    if (!infos.empty()) oss << "Info: " << infos.size() << "\n";
    
    oss << "Energy Conservation Score: " << std::fixed << std::setprecision(2) << (energyConservationScore * 100) << "%\n";
    oss << "Physical Plausibility Score: " << std::fixed << std::setprecision(2) << (physicalPlausibilityScore * 100) << "%\n";
    oss << "Performance Score: " << std::fixed << std::setprecision(2) << (performanceScore * 100) << "%";
    
    return oss.str();
}

bool ValidationResult::hasErrors() const {
    return std::any_of(issues.begin(), issues.end(),
                       [](const ValidationIssue& issue) {
                           return issue.severity == ValidationSeverity::ERROR ||
                                  issue.severity == ValidationSeverity::CRITICAL;
                       });
}

bool ValidationResult::hasWarnings() const {
    return std::any_of(issues.begin(), issues.end(),
                       [](const ValidationIssue& issue) {
                           return issue.severity == ValidationSeverity::WARNING;
                       });
}

// MaterialValidator implementation
MaterialValidator::MaterialValidator() {
    // Initialize with default configuration
}

MaterialValidator::~MaterialValidator() {
    // Cleanup
}

void MaterialValidator::setConfig(const ValidationConfig& config) {
    m_config = config;
}

ValidationConfig MaterialValidator::getConfig() const {
    return m_config;
}

ValidationResult MaterialValidator::validateMaterial(const std::shared_ptr<Material>& material) {
    ValidationResult result;
    
    if (!material) {
        result.isValid = false;
        result.issues.push_back(createIssue(
            ValidationSeverity::CRITICAL,
            ValidationCategory::PARAMETER_RANGE,
            "material",
            "Material is null",
            "Provide a valid material instance"
        ));
        return result;
    }
    
    // Try to cast to PBR material for detailed validation
    auto pbrMaterial = std::dynamic_pointer_cast<PBRMaterial>(material);
    if (pbrMaterial) {
        return validatePBRMaterial(pbrMaterial);
    }
    
    // Basic material validation
    result.isValid = true;
    result.energyConservationScore = 1.0f;
    result.physicalPlausibilityScore = 1.0f;
    result.performanceScore = 1.0f;
    
    m_validationCount++;
    triggerCallback(result);
    
    return result;
}

ValidationResult MaterialValidator::validatePBRMaterial(const std::shared_ptr<PBRMaterial>& material) {
    if (!material) {
        ValidationResult result;
        result.isValid = false;
        result.issues.push_back(createIssue(
            ValidationSeverity::CRITICAL,
            ValidationCategory::PARAMETER_RANGE,
            "material",
            "PBR Material is null",
            "Provide a valid PBR material instance"
        ));
        return result;
    }
    
    // Get Disney BRDF parameters
    DisneyBRDFParams params = material->getDisneyParams();
    return validateDisneyBRDF(params);
}

ValidationResult MaterialValidator::validateDisneyBRDF(const DisneyBRDFParams& params) {
    ValidationResult result;
    result.isValid = true;
    
    // Parameter range validation
    if (m_config.enableParameterRange) {
        auto rangeIssues = validateParameter("metallic", params.metallic, 0.0f, 1.0f);
        result.issues.insert(result.issues.end(), rangeIssues.begin(), rangeIssues.end());
        
        rangeIssues = validateParameter("roughness", params.roughness, 0.0f, 1.0f);
        result.issues.insert(result.issues.end(), rangeIssues.begin(), rangeIssues.end());
        
        rangeIssues = validateParameter("specular", params.specular, 0.0f, 1.0f);
        result.issues.insert(result.issues.end(), rangeIssues.begin(), rangeIssues.end());
        
        rangeIssues = validateParameter("specularTint", params.specularTint, 0.0f, 1.0f);
        result.issues.insert(result.issues.end(), rangeIssues.begin(), rangeIssues.end());
        
        rangeIssues = validateParameter("anisotropic", params.anisotropic, 0.0f, 1.0f);
        result.issues.insert(result.issues.end(), rangeIssues.begin(), rangeIssues.end());
        
        rangeIssues = validateParameter("sheen", params.sheen, 0.0f, 1.0f);
        result.issues.insert(result.issues.end(), rangeIssues.begin(), rangeIssues.end());
        
        rangeIssues = validateParameter("sheenTint", params.sheenTint, 0.0f, 1.0f);
        result.issues.insert(result.issues.end(), rangeIssues.begin(), rangeIssues.end());
        
        rangeIssues = validateParameter("clearcoat", params.clearcoat, 0.0f, 1.0f);
        result.issues.insert(result.issues.end(), rangeIssues.begin(), rangeIssues.end());
        
        rangeIssues = validateParameter("clearcoatGloss", params.clearcoatGloss, 0.0f, 1.0f);
        result.issues.insert(result.issues.end(), rangeIssues.begin(), rangeIssues.end());
        
        rangeIssues = validateParameter("subsurface", params.subsurface, 0.0f, 1.0f);
        result.issues.insert(result.issues.end(), rangeIssues.begin(), rangeIssues.end());
    }
    
    // Energy conservation validation
    if (m_config.enableEnergyConservation) {
        auto energyIssues = checkEnergyConservation(params);
        result.issues.insert(result.issues.end(), energyIssues.begin(), energyIssues.end());
    }
    
    // Parameter combination validation
    if (m_config.enableParameterCombination) {
        auto combinationIssues = checkParameterCombinations(params);
        result.issues.insert(result.issues.end(), combinationIssues.begin(), combinationIssues.end());
    }
    
    // Physical plausibility validation
    if (m_config.enablePhysicalPlausibility) {
        auto plausibilityIssues = checkPhysicalPlausibility(params);
        result.issues.insert(result.issues.end(), plausibilityIssues.begin(), plausibilityIssues.end());
    }
    
    // Performance validation
    if (m_config.enablePerformanceWarnings) {
        auto performanceIssues = checkPerformance(params);
        result.issues.insert(result.issues.end(), performanceIssues.begin(), performanceIssues.end());
    }
    
    // Workflow suggestions
    if (m_config.enableWorkflowSuggestions) {
        auto workflowIssues = getWorkflowSuggestions(params);
        result.issues.insert(result.issues.end(), workflowIssues.begin(), workflowIssues.end());
    }
    
    // Calculate scores
    result.energyConservationScore = calculateEnergyConservationScore(params);
    result.physicalPlausibilityScore = calculatePhysicalPlausibilityScore(params);
    result.performanceScore = calculatePerformanceScore(params);
    
    // Determine overall validity
    result.isValid = !result.hasErrors();
    
    // Update statistics
    m_validationCount++;
    m_issueCount += result.issues.size();
    
    // Auto-fix if enabled
    if (m_config.autoFix && !result.isValid) {
        DisneyBRDFParams fixedParams = params;
        auto autoFixableIssues = result.getAutoFixableIssues();
        int fixedCount = autoFixIssues(fixedParams, autoFixableIssues);
        m_autoFixCount += fixedCount;
        
        if (fixedCount > 0) {
            // Re-validate after auto-fix
            result = validateDisneyBRDF(fixedParams);
            result.issues.insert(result.issues.begin(), createIssue(
                ValidationSeverity::INFO,
                ValidationCategory::WORKFLOW,
                "auto-fix",
                "Auto-fixed " + std::to_string(fixedCount) + " issues",
                "Review the automatically applied changes"
            ));
        }
    }
    
    triggerCallback(result);
    return result;
}

std::vector<ValidationIssue> MaterialValidator::validateParameter(const std::string& paramName, 
                                                                 float value, 
                                                                 float minValue, 
                                                                 float maxValue) {
    std::vector<ValidationIssue> issues;
    
    if (value < minValue) {
        issues.push_back(createIssue(
            ValidationSeverity::ERROR,
            ValidationCategory::PARAMETER_RANGE,
            paramName,
            "Parameter value below minimum range",
            "Increase value to valid range",
            value,
            minValue,
            true
        ));
    } else if (value > maxValue) {
        issues.push_back(createIssue(
            ValidationSeverity::ERROR,
            ValidationCategory::PARAMETER_RANGE,
            paramName,
            "Parameter value above maximum range",
            "Decrease value to valid range",
            value,
            maxValue,
            true
        ));
    }
    
    // Additional range warnings
    if (paramName == "roughness" && value < 0.01f) {
        issues.push_back(createIssue(
            ValidationSeverity::WARNING,
            ValidationCategory::PHYSICAL_PLAUSIBILITY,
            paramName,
            "Very low roughness may cause rendering artifacts",
            "Consider using roughness >= 0.01 for stability",
            value,
            0.01f,
            true
        ));
    }
    
    return issues;
}

std::vector<ValidationIssue> MaterialValidator::checkEnergyConservation(const DisneyBRDFParams& params) {
    std::vector<ValidationIssue> issues;

    // Check diffuse + specular energy conservation
    float diffuseEnergy = (1.0f - params.metallic) * (1.0f - params.subsurface);
    float specularEnergy = params.specular * (1.0f - params.metallic) + params.metallic;
    float totalEnergy = diffuseEnergy + specularEnergy;

    if (totalEnergy > 1.0f + m_config.energyConservationTolerance) {
        issues.push_back(createIssue(
            ValidationSeverity::WARNING,
            ValidationCategory::ENERGY_CONSERVATION,
            "energy_total",
            "Material may violate energy conservation",
            "Reduce metallic or specular values",
            totalEnergy,
            1.0f,
            true
        ));
    }

    // Check clearcoat energy conservation
    if (params.clearcoat > 0.0f) {
        float clearcoatEnergy = params.clearcoat * 0.25f; // Approximate clearcoat energy
        if (totalEnergy + clearcoatEnergy > 1.0f + m_config.energyConservationTolerance) {
            issues.push_back(createIssue(
                ValidationSeverity::WARNING,
                ValidationCategory::ENERGY_CONSERVATION,
                "clearcoat",
                "Clearcoat may cause energy conservation violation",
                "Reduce clearcoat or other reflective parameters",
                params.clearcoat,
                params.clearcoat * 0.8f,
                true
            ));
        }
    }

    return issues;
}

std::vector<ValidationIssue> MaterialValidator::checkParameterCombinations(const DisneyBRDFParams& params) {
    std::vector<ValidationIssue> issues;

    // Metallic + Subsurface incompatibility
    if (!isSubsurfaceMetallicCompatible(params.subsurface, params.metallic)) {
        issues.push_back(createIssue(
            ValidationSeverity::WARNING,
            ValidationCategory::PARAMETER_COMBINATION,
            "subsurface+metallic",
            "Subsurface scattering with metallic materials is physically implausible",
            "Set either subsurface or metallic to 0",
            params.subsurface,
            0.0f,
            true
        ));
    }

    // Metallic + Specular combination
    if (!isMetallicSpecularCompatible(params.metallic, params.specular)) {
        issues.push_back(createIssue(
            ValidationSeverity::INFO,
            ValidationCategory::PARAMETER_COMBINATION,
            "metallic+specular",
            "High metallic with low specular may look unrealistic",
            "Consider increasing specular for metallic materials",
            params.specular,
            0.9f,
            false
        ));
    }

    // Clearcoat validation
    if (!isClearcoatValid(params.clearcoat, params.clearcoatGloss)) {
        issues.push_back(createIssue(
            ValidationSeverity::WARNING,
            ValidationCategory::PARAMETER_COMBINATION,
            "clearcoat+clearcoatGloss",
            "Clearcoat without proper gloss setting",
            "Adjust clearcoat gloss for realistic appearance",
            params.clearcoatGloss,
            0.9f,
            true
        ));
    }

    return issues;
}

std::vector<ValidationIssue> MaterialValidator::checkPhysicalPlausibility(const DisneyBRDFParams& params) {
    std::vector<ValidationIssue> issues;

    // Very high roughness with high specular
    if (params.roughness > 0.9f && params.specular > 0.8f) {
        issues.push_back(createIssue(
            ValidationSeverity::INFO,
            ValidationCategory::PHYSICAL_PLAUSIBILITY,
            "roughness+specular",
            "Very rough surfaces typically have lower specular reflection",
            "Consider reducing specular for very rough materials",
            params.specular,
            0.3f,
            false
        ));
    }

    // Very low roughness warning
    if (params.roughness < 0.01f) {
        issues.push_back(createIssue(
            ValidationSeverity::WARNING,
            ValidationCategory::PHYSICAL_PLAUSIBILITY,
            "roughness",
            "Perfect mirror surfaces are rare in nature",
            "Consider minimum roughness of 0.01 for realism",
            params.roughness,
            0.01f,
            true
        ));
    }

    // Anisotropic without proper setup
    if (params.anisotropic > 0.5f && params.roughness < 0.1f) {
        issues.push_back(createIssue(
            ValidationSeverity::INFO,
            ValidationCategory::PHYSICAL_PLAUSIBILITY,
            "anisotropic+roughness",
            "Anisotropic effects are more visible with moderate roughness",
            "Increase roughness for better anisotropic visibility",
            params.roughness,
            0.3f,
            false
        ));
    }

    return issues;
}

std::vector<ValidationIssue> MaterialValidator::checkPerformance(const DisneyBRDFParams& params) {
    std::vector<ValidationIssue> issues;

    float complexity = estimateRenderingComplexity(params);

    if (complexity > m_config.performanceThreshold) {
        issues.push_back(createIssue(
            ValidationSeverity::INFO,
            ValidationCategory::PERFORMANCE,
            "complexity",
            "Material has high rendering complexity",
            "Consider simplifying for better performance",
            complexity,
            m_config.performanceThreshold,
            false
        ));
    }

    // Subsurface scattering performance warning
    if (params.subsurface > 0.1f) {
        issues.push_back(createIssue(
            ValidationSeverity::INFO,
            ValidationCategory::PERFORMANCE,
            "subsurface",
            "Subsurface scattering increases render time",
            "Use sparingly for performance-critical scenes",
            params.subsurface,
            0.0f,
            false
        ));
    }

    // Clearcoat performance warning
    if (params.clearcoat > 0.1f) {
        issues.push_back(createIssue(
            ValidationSeverity::INFO,
            ValidationCategory::PERFORMANCE,
            "clearcoat",
            "Clearcoat adds additional reflection layer",
            "Consider if clearcoat is necessary for the material",
            params.clearcoat,
            0.0f,
            false
        ));
    }

    return issues;
}

std::vector<ValidationIssue> MaterialValidator::getWorkflowSuggestions(const DisneyBRDFParams& params) {
    std::vector<ValidationIssue> issues;

    // Suggest using presets for common materials
    if (params.metallic > 0.9f && params.roughness < 0.1f) {
        issues.push_back(createIssue(
            ValidationSeverity::INFO,
            ValidationCategory::WORKFLOW,
            "preset_suggestion",
            "This looks like a chrome/mirror material",
            "Consider using the Chrome preset as starting point",
            0.0f,
            0.0f,
            false
        ));
    }

    if (params.subsurface > 0.3f && params.metallic < 0.1f) {
        issues.push_back(createIssue(
            ValidationSeverity::INFO,
            ValidationCategory::WORKFLOW,
            "preset_suggestion",
            "This looks like a skin/wax material",
            "Consider using the Skin preset as starting point",
            0.0f,
            0.0f,
            false
        ));
    }

    // Suggest texture usage
    if (params.roughness == 0.5f && params.metallic == 0.0f) {
        issues.push_back(createIssue(
            ValidationSeverity::INFO,
            ValidationCategory::WORKFLOW,
            "texture_suggestion",
            "Consider adding texture maps for more realism",
            "Add roughness and normal maps for surface detail",
            0.0f,
            0.0f,
            false
        ));
    }

    return issues;
}

int MaterialValidator::autoFixIssues(DisneyBRDFParams& params, const std::vector<ValidationIssue>& issues) {
    int fixedCount = 0;

    for (const auto& issue : issues) {
        if (!issue.autoFixable) continue;

        if (issue.parameter == "metallic") {
            params.metallic = std::clamp(issue.suggestedValue, 0.0f, 1.0f);
            fixedCount++;
        } else if (issue.parameter == "roughness") {
            params.roughness = std::clamp(issue.suggestedValue, 0.0f, 1.0f);
            fixedCount++;
        } else if (issue.parameter == "specular") {
            params.specular = std::clamp(issue.suggestedValue, 0.0f, 1.0f);
            fixedCount++;
        } else if (issue.parameter == "subsurface") {
            params.subsurface = std::clamp(issue.suggestedValue, 0.0f, 1.0f);
            fixedCount++;
        } else if (issue.parameter == "clearcoat") {
            params.clearcoat = std::clamp(issue.suggestedValue, 0.0f, 1.0f);
            fixedCount++;
        } else if (issue.parameter == "clearcoatGloss") {
            params.clearcoatGloss = std::clamp(issue.suggestedValue, 0.0f, 1.0f);
            fixedCount++;
        }
    }

    return fixedCount;
}

float MaterialValidator::calculateEnergyConservationScore(const DisneyBRDFParams& params) {
    float diffuseEnergy = (1.0f - params.metallic) * (1.0f - params.subsurface);
    float specularEnergy = params.specular * (1.0f - params.metallic) + params.metallic;
    float clearcoatEnergy = params.clearcoat * 0.25f;
    float totalEnergy = diffuseEnergy + specularEnergy + clearcoatEnergy;

    if (totalEnergy <= 1.0f) {
        return 1.0f; // Perfect conservation
    } else {
        // Penalize energy violations
        float violation = totalEnergy - 1.0f;
        return std::max(0.0f, 1.0f - violation * 2.0f);
    }
}

float MaterialValidator::calculatePhysicalPlausibilityScore(const DisneyBRDFParams& params) {
    float score = 1.0f;

    // Penalize implausible combinations
    if (params.subsurface > 0.1f && params.metallic > 0.1f) {
        score -= 0.3f; // Subsurface + metallic is implausible
    }

    if (params.roughness < 0.01f) {
        score -= 0.2f; // Perfect mirrors are rare
    }

    if (params.roughness > 0.9f && params.specular > 0.8f) {
        score -= 0.1f; // Very rough + high specular is unusual
    }

    return std::max(0.0f, score);
}

float MaterialValidator::calculatePerformanceScore(const DisneyBRDFParams& params) {
    float complexity = estimateRenderingComplexity(params);
    return std::max(0.0f, 1.0f - complexity);
}

void MaterialValidator::setValidationCallback(std::function<void(const ValidationResult&)> callback) {
    m_validationCallback = callback;
}

void MaterialValidator::setRealTimeValidation(bool enable) {
    m_realTimeValidation = enable;
}

std::string MaterialValidator::getValidationStats() const {
    std::ostringstream oss;
    oss << "Validation Statistics:\n";
    oss << "Total Validations: " << m_validationCount << "\n";
    oss << "Total Issues Found: " << m_issueCount << "\n";
    oss << "Auto-fixes Applied: " << m_autoFixCount << "\n";

    if (m_validationCount > 0) {
        oss << "Average Issues per Validation: " << std::fixed << std::setprecision(2)
            << (static_cast<float>(m_issueCount) / m_validationCount) << "\n";
    }

    return oss.str();
}

ValidationIssue MaterialValidator::createIssue(ValidationSeverity severity,
                                              ValidationCategory category,
                                              const std::string& parameter,
                                              const std::string& message,
                                              const std::string& suggestion,
                                              float currentValue,
                                              float suggestedValue,
                                              bool autoFixable) const {
    ValidationIssue issue;
    issue.severity = severity;
    issue.category = category;
    issue.parameter = parameter;
    issue.message = message;
    issue.suggestion = suggestion;
    issue.currentValue = currentValue;
    issue.suggestedValue = suggestedValue;
    issue.autoFixable = autoFixable;

    return issue;
}

bool MaterialValidator::isMetallicSpecularCompatible(float metallic, float specular) const {
    // For metallic materials, specular should typically be high
    if (metallic > 0.8f && specular < 0.5f) {
        return false;
    }
    return true;
}

bool MaterialValidator::isSubsurfaceMetallicCompatible(float subsurface, float metallic) const {
    // Subsurface scattering and metallic are mutually exclusive
    return !(subsurface > 0.1f && metallic > 0.1f);
}

bool MaterialValidator::isClearcoatValid(float clearcoat, float clearcoatGloss) const {
    // If clearcoat is used, gloss should be reasonable
    if (clearcoat > 0.1f && clearcoatGloss < 0.1f) {
        return false; // Very rough clearcoat is unusual
    }
    return true;
}

float MaterialValidator::estimateRenderingComplexity(const DisneyBRDFParams& params) const {
    float complexity = 0.0f;

    // Base complexity
    complexity += 0.1f;

    // Add complexity for each active feature
    if (params.metallic > 0.1f) complexity += 0.1f;
    if (params.subsurface > 0.1f) complexity += 0.3f; // Subsurface is expensive
    if (params.clearcoat > 0.1f) complexity += 0.2f;
    if (params.anisotropic > 0.1f) complexity += 0.1f;
    if (params.sheen > 0.1f) complexity += 0.1f;

    // Very low roughness increases complexity (more bounces needed)
    if (params.roughness < 0.1f) complexity += 0.1f;

    return std::min(1.0f, complexity);
}

void MaterialValidator::triggerCallback(const ValidationResult& result) const {
    if (m_validationCallback && m_realTimeValidation) {
        m_validationCallback(result);
    }
}

} // namespace photon
