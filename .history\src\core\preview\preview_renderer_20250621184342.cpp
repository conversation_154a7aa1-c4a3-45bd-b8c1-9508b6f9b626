// src/core/preview/preview_renderer.cpp
// PhotonRender - Real-time Material Preview System Implementation
// Implementazione sistema di rendering real-time per preview materiali

#include "preview_renderer.hpp"
#include "preview_scene.hpp"
#include "preview_camera.hpp"
#include "../image/image.hpp"
#include "../integrator/path_tracing_integrator.hpp"
#include "../sampler/random_sampler.hpp"
#include "../denoising/ai_denoiser.hpp"
#include <chrono>
#include <thread>
#include <iostream>

namespace photon {

PreviewRenderer::PreviewRenderer() {
    // Initialize default settings
    m_settings = PreviewSettings{};
}

PreviewRenderer::~PreviewRenderer() {
    shutdown();
}

bool PreviewRenderer::initialize(const PreviewSettings& settings) {
    std::lock_guard<std::mutex> lock(m_renderMutex);
    
    if (m_initialized) {
        shutdown();
    }
    
    m_settings = settings;
    
    try {
        // Create preview scene
        m_scene = std::make_shared<PreviewScene>();
        if (!m_scene->initialize(m_settings.geometry)) {
            std::cerr << "Failed to initialize preview scene" << std::endl;
            return false;
        }
        
        // Create preview camera
        m_camera = std::make_shared<PreviewCamera>();
        m_camera->initialize(m_settings);
        
        // Create integrator
        m_integrator = std::make_shared<PathTracingIntegrator>();
        m_integrator->setMaxDepth(m_settings.maxDepth);
        
        // Create sampler
        m_sampler = std::make_shared<RandomSampler>();
        
        // Setup scene components
        createScene();
        setupLighting();
        setupCamera();
        
        m_initialized = true;
        
        std::cout << "PreviewRenderer initialized successfully" << std::endl;
        std::cout << "Resolution: " << m_settings.width << "x" << m_settings.height << std::endl;
        std::cout << "Samples: " << m_settings.samples << std::endl;
        std::cout << "Max depth: " << m_settings.maxDepth << std::endl;
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "PreviewRenderer initialization failed: " << e.what() << std::endl;
        return false;
    }
}

void PreviewRenderer::shutdown() {
    std::lock_guard<std::mutex> lock(m_renderMutex);
    
    if (m_rendering) {
        cancelRender();
    }
    
    if (m_renderThread.joinable()) {
        m_renderThread.join();
    }
    
    m_scene.reset();
    m_camera.reset();
    m_integrator.reset();
    m_sampler.reset();
    m_material.reset();
    
    m_initialized = false;
    
    std::cout << "PreviewRenderer shutdown complete" << std::endl;
}

void PreviewRenderer::setMaterial(std::shared_ptr<Material> material) {
    std::lock_guard<std::mutex> lock(m_renderMutex);
    
    m_material = material;
    
    if (m_scene && material) {
        m_scene->setMaterial(material);
    }
}

void PreviewRenderer::setGeometry(PreviewGeometry geometry) {
    std::lock_guard<std::mutex> lock(m_renderMutex);
    
    m_settings.geometry = geometry;
    
    if (m_scene) {
        m_scene->setGeometry(geometry);
    }
}

void PreviewRenderer::setLighting(PreviewLighting lighting) {
    std::lock_guard<std::mutex> lock(m_renderMutex);
    
    m_settings.lighting = lighting;
    setupLighting();
}

void PreviewRenderer::updateSettings(const PreviewSettings& settings) {
    std::lock_guard<std::mutex> lock(m_renderMutex);
    
    bool needsReinit = (settings.width != m_settings.width || 
                       settings.height != m_settings.height);
    
    m_settings = settings;
    
    if (needsReinit && m_initialized) {
        // Reinitialize with new resolution
        initialize(m_settings);
    } else {
        // Update components
        if (m_camera) {
            m_camera->updateSettings(m_settings);
        }
        if (m_integrator) {
            m_integrator->setMaxDepth(m_settings.maxDepth);
        }
        if (m_scene) {
            m_scene->setGeometry(m_settings.geometry);
        }
        setupLighting();
    }
}

std::shared_ptr<Image> PreviewRenderer::render() {
    if (!m_initialized) {
        std::cerr << "PreviewRenderer not initialized" << std::endl;
        return nullptr;
    }
    
    std::lock_guard<std::mutex> lock(m_renderMutex);
    
    if (m_rendering) {
        std::cerr << "Render already in progress" << std::endl;
        return nullptr;
    }
    
    return renderInternal();
}

void PreviewRenderer::renderAsync(std::function<void(std::shared_ptr<Image>)> callback) {
    if (!m_initialized) {
        std::cerr << "PreviewRenderer not initialized" << std::endl;
        callback(nullptr);
        return;
    }
    
    std::lock_guard<std::mutex> lock(m_renderMutex);
    
    if (m_rendering) {
        std::cerr << "Render already in progress" << std::endl;
        callback(nullptr);
        return;
    }
    
    m_rendering = true;
    m_cancelRender = false;
    m_renderProgress = 0.0f;
    
    // Start render thread
    m_renderThread = std::thread([this, callback]() {
        auto result = renderInternal();
        
        {
            std::lock_guard<std::mutex> lock(m_renderMutex);
            m_rendering = false;
            m_renderProgress = 1.0f;
        }
        
        callback(result);
    });
}

bool PreviewRenderer::isRendering() const {
    std::lock_guard<std::mutex> lock(m_renderMutex);
    return m_rendering;
}

void PreviewRenderer::cancelRender() {
    m_cancelRender = true;
    
    if (m_renderThread.joinable()) {
        m_renderThread.join();
    }
    
    std::lock_guard<std::mutex> lock(m_renderMutex);
    m_rendering = false;
    m_renderProgress = 0.0f;
}

float PreviewRenderer::getRenderProgress() const {
    std::lock_guard<std::mutex> lock(m_renderMutex);
    return m_renderProgress;
}

void PreviewRenderer::updateCamera(float distance, float angleX, float angleY) {
    std::lock_guard<std::mutex> lock(m_renderMutex);
    
    m_settings.cameraDistance = distance;
    m_settings.cameraAngleX = angleX;
    m_settings.cameraAngleY = angleY;
    
    if (m_camera) {
        m_camera->updatePosition(distance, angleX, angleY);
    }
}

void PreviewRenderer::updateLightingIntensity(float intensity) {
    std::lock_guard<std::mutex> lock(m_renderMutex);
    
    if (m_scene) {
        m_scene->setLightingIntensity(intensity);
    }
}

void PreviewRenderer::updateExposure(float exposure) {
    std::lock_guard<std::mutex> lock(m_renderMutex);
    
    m_settings.exposure = exposure;
}

void PreviewRenderer::setAutoRotation(bool enabled, float speed) {
    std::lock_guard<std::mutex> lock(m_renderMutex);
    
    m_settings.enableRotation = enabled;
    m_settings.rotationSpeed = speed;
}

void PreviewRenderer::updateAutoRotation(float deltaTime) {
    if (!m_settings.enableRotation) return;
    
    std::lock_guard<std::mutex> lock(m_renderMutex);
    
    m_currentRotation += m_settings.rotationSpeed * deltaTime;
    if (m_currentRotation >= 360.0f) {
        m_currentRotation -= 360.0f;
    }
    
    if (m_scene) {
        m_scene->setObjectRotation(m_currentRotation);
    }
}

std::string PreviewRenderer::getRenderStats() const {
    std::lock_guard<std::mutex> lock(m_renderMutex);
    
    std::ostringstream stats;
    stats << "Resolution: " << m_settings.width << "x" << m_settings.height << "\n";
    stats << "Samples: " << m_settings.samples << "\n";
    stats << "Last render time: " << std::fixed << std::setprecision(2) << m_lastRenderDuration << "ms\n";
    stats << "Total rays: " << m_totalRays << "\n";
    
    if (m_lastRenderDuration > 0) {
        float mraysPerSec = (m_totalRays / 1000000.0f) / (m_lastRenderDuration / 1000.0f);
        stats << "Performance: " << std::fixed << std::setprecision(2) << mraysPerSec << " Mrays/sec";
    }
    
    return stats.str();
}

void PreviewRenderer::resetCamera() {
    updateCamera(3.0f, 0.0f, 0.0f);
}

bool PreviewRenderer::savePreview(const std::string& filename) {
    auto image = render();
    if (!image) {
        return false;
    }
    
    return image->save(filename);
}

void PreviewRenderer::createScene() {
    if (!m_scene) return;
    
    // Scene creation is handled by PreviewScene
    m_scene->createGeometry(m_settings.geometry);
    
    if (m_material) {
        m_scene->setMaterial(m_material);
    }
}

void PreviewRenderer::setupLighting() {
    if (!m_scene) return;
    
    m_scene->setupLighting(m_settings.lighting);
}

void PreviewRenderer::setupCamera() {
    if (!m_camera) return;
    
    m_camera->updateSettings(m_settings);
    m_camera->updatePosition(m_settings.cameraDistance, 
                           m_settings.cameraAngleX, 
                           m_settings.cameraAngleY);
}

} // namespace photon
