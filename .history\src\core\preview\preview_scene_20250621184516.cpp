// src/core/preview/preview_scene.cpp
// PhotonRender - Preview Scene Management Implementation
// Implementazione sistema di gestione scene per preview materiali

#include "preview_scene.hpp"
#include "../mesh/mesh.hpp"
#include "../light/point_light.hpp"
#include "../light/directional_light.hpp"
#include "../light/environment_light.hpp"
#include "../material/material.hpp"
#include <cmath>
#include <iostream>

namespace photon {

PreviewScene::PreviewScene() {
    // Initialize default ground material
    m_groundMaterial = std::make_shared<DiffuseMaterial>(Color3(0.8f, 0.8f, 0.8f));
}

PreviewScene::~PreviewScene() {
    clearLights();
}

bool PreviewScene::initialize(PreviewGeometry geometry) {
    try {
        // Create initial geometry
        createGeometry(geometry);
        
        // Create ground plane
        if (m_groundPlaneEnabled) {
            m_groundPlane = createGroundPlane();
            if (m_groundPlane) {
                m_groundPlane->setMaterial(m_groundMaterial);
                addMesh(m_groundPlane);
            }
        }
        
        // Setup default lighting
        setupLighting(PreviewLighting::STUDIO);
        
        std::cout << "PreviewScene initialized successfully" << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "PreviewScene initialization failed: " << e.what() << std::endl;
        return false;
    }
}

void PreviewScene::createGeometry(PreviewGeometry geometry) {
    // Remove existing preview object
    if (m_previewObject) {
        removeMesh(m_previewObject);
    }
    
    // Create new geometry
    switch (geometry) {
        case PreviewGeometry::SPHERE:
            m_previewObject = createSphere();
            break;
        case PreviewGeometry::CUBE:
            m_previewObject = createCube();
            break;
        case PreviewGeometry::PLANE:
            m_previewObject = createPlane();
            break;
        case PreviewGeometry::CYLINDER:
            m_previewObject = createCylinder();
            break;
        case PreviewGeometry::TORUS:
            m_previewObject = createTorus();
            break;
        default:
            m_previewObject = createSphere();
            break;
    }
    
    m_currentGeometry = geometry;
    
    // Apply current material
    if (m_previewObject && m_previewMaterial) {
        m_previewObject->setMaterial(m_previewMaterial);
    }
    
    // Add to scene
    if (m_previewObject) {
        addMesh(m_previewObject);
        updateObjectTransform();
    }
    
    std::cout << "Created preview geometry: " << static_cast<int>(geometry) << std::endl;
}

void PreviewScene::setMaterial(std::shared_ptr<Material> material) {
    m_previewMaterial = material;
    
    if (m_previewObject && material) {
        m_previewObject->setMaterial(material);
    }
}

void PreviewScene::setupLighting(PreviewLighting lighting) {
    clearLights();
    m_currentLighting = lighting;
    
    switch (lighting) {
        case PreviewLighting::STUDIO:
            setupStudioLighting();
            break;
        case PreviewLighting::OUTDOOR:
            setupOutdoorLighting();
            break;
        case PreviewLighting::INDOOR:
            setupIndoorLighting();
            break;
        case PreviewLighting::DRAMATIC:
            setupDramaticLighting();
            break;
        case PreviewLighting::SOFT:
            setupSoftLighting();
            break;
        default:
            setupStudioLighting();
            break;
    }
    
    std::cout << "Setup lighting: " << static_cast<int>(lighting) << std::endl;
}

void PreviewScene::setLightingIntensity(float intensity) {
    m_lightingIntensity = std::max(0.0f, intensity);
    
    // Update all lights
    for (auto& light : m_lights) {
        if (auto pointLight = std::dynamic_pointer_cast<PointLight>(light)) {
            pointLight->setIntensity(pointLight->getBaseIntensity() * m_lightingIntensity);
        } else if (auto dirLight = std::dynamic_pointer_cast<DirectionalLight>(light)) {
            dirLight->setIntensity(dirLight->getBaseIntensity() * m_lightingIntensity);
        }
    }
    
    if (m_environmentLight) {
        m_environmentLight->setIntensity(m_environmentLight->getBaseIntensity() * m_lightingIntensity);
    }
}

void PreviewScene::setObjectRotation(float angleY) {
    m_currentRotation = angleY;
    updateObjectTransform();
}

BoundingBox PreviewScene::getObjectBounds() const {
    if (m_previewObject) {
        return m_previewObject->getBounds();
    }
    return BoundingBox();
}

void PreviewScene::setGroundPlaneEnabled(bool enabled) {
    if (m_groundPlaneEnabled == enabled) return;
    
    m_groundPlaneEnabled = enabled;
    
    if (enabled) {
        if (!m_groundPlane) {
            m_groundPlane = createGroundPlane();
            if (m_groundPlane) {
                m_groundPlane->setMaterial(m_groundMaterial);
            }
        }
        if (m_groundPlane) {
            addMesh(m_groundPlane);
        }
    } else {
        if (m_groundPlane) {
            removeMesh(m_groundPlane);
        }
    }
}

void PreviewScene::setGroundPlaneMaterial(std::shared_ptr<Material> material) {
    m_groundMaterial = material;
    
    if (m_groundPlane && material) {
        m_groundPlane->setMaterial(material);
    }
}

void PreviewScene::setEnvironmentEnabled(bool enabled) {
    m_environmentEnabled = enabled;
    
    if (enabled && !m_environmentLight) {
        // Create default environment light
        m_environmentLight = std::make_shared<EnvironmentLight>();
        addLight(m_environmentLight);
    } else if (!enabled && m_environmentLight) {
        removeLight(m_environmentLight);
        m_environmentLight.reset();
    }
}

void PreviewScene::setEnvironmentMap(std::shared_ptr<Texture> envMap) {
    if (m_environmentLight) {
        m_environmentLight->setEnvironmentMap(envMap);
    }
}

std::shared_ptr<Mesh> PreviewScene::createSphere(float radius, int segments) {
    auto mesh = std::make_shared<Mesh>();
    
    // Generate sphere vertices
    for (int lat = 0; lat <= segments; ++lat) {
        float theta = lat * M_PI / segments;
        float sinTheta = std::sin(theta);
        float cosTheta = std::cos(theta);
        
        for (int lon = 0; lon <= segments; ++lon) {
            float phi = lon * 2 * M_PI / segments;
            float sinPhi = std::sin(phi);
            float cosPhi = std::cos(phi);
            
            Vec3 position(radius * sinTheta * cosPhi,
                         radius * cosTheta,
                         radius * sinTheta * sinPhi);
            
            Vec3 normal = position.normalized();
            Vec2 uv(static_cast<float>(lon) / segments,
                    static_cast<float>(lat) / segments);
            
            mesh->addVertex(position, normal, uv);
        }
    }
    
    // Generate sphere indices
    for (int lat = 0; lat < segments; ++lat) {
        for (int lon = 0; lon < segments; ++lon) {
            int current = lat * (segments + 1) + lon;
            int next = current + segments + 1;
            
            // First triangle
            mesh->addTriangle(current, next, current + 1);
            // Second triangle
            mesh->addTriangle(current + 1, next, next + 1);
        }
    }
    
    mesh->buildBVH();
    return mesh;
}

std::shared_ptr<Mesh> PreviewScene::createCube(float size) {
    auto mesh = std::make_shared<Mesh>();
    float half = size * 0.5f;
    
    // Cube vertices (6 faces, 4 vertices each)
    Vec3 vertices[24] = {
        // Front face
        Vec3(-half, -half,  half), Vec3( half, -half,  half),
        Vec3( half,  half,  half), Vec3(-half,  half,  half),
        // Back face
        Vec3(-half, -half, -half), Vec3(-half,  half, -half),
        Vec3( half,  half, -half), Vec3( half, -half, -half),
        // Top face
        Vec3(-half,  half, -half), Vec3(-half,  half,  half),
        Vec3( half,  half,  half), Vec3( half,  half, -half),
        // Bottom face
        Vec3(-half, -half, -half), Vec3( half, -half, -half),
        Vec3( half, -half,  half), Vec3(-half, -half,  half),
        // Right face
        Vec3( half, -half, -half), Vec3( half,  half, -half),
        Vec3( half,  half,  half), Vec3( half, -half,  half),
        // Left face
        Vec3(-half, -half, -half), Vec3(-half, -half,  half),
        Vec3(-half,  half,  half), Vec3(-half,  half, -half)
    };
    
    Vec3 normals[6] = {
        Vec3( 0,  0,  1), Vec3( 0,  0, -1), Vec3( 0,  1,  0),
        Vec3( 0, -1,  0), Vec3( 1,  0,  0), Vec3(-1,  0,  0)
    };
    
    Vec2 uvs[4] = {
        Vec2(0, 0), Vec2(1, 0), Vec2(1, 1), Vec2(0, 1)
    };
    
    // Add vertices
    for (int face = 0; face < 6; ++face) {
        for (int vert = 0; vert < 4; ++vert) {
            mesh->addVertex(vertices[face * 4 + vert], normals[face], uvs[vert]);
        }
    }
    
    // Add triangles (2 per face)
    for (int face = 0; face < 6; ++face) {
        int base = face * 4;
        mesh->addTriangle(base, base + 1, base + 2);
        mesh->addTriangle(base, base + 2, base + 3);
    }
    
    mesh->buildBVH();
    return mesh;
}

} // namespace photon
