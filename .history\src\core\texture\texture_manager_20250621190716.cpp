// src/core/texture/texture_manager.cpp
// PhotonRender - Texture Management System Implementation
// Implementazione sistema di gestione texture

#include "texture_manager.hpp"
#include "../texture/texture.hpp"
#include "../image/image.hpp"
#include <filesystem>
#include <fstream>
#include <algorithm>
#include <chrono>
#include <iostream>
#include <sstream>
#include <iomanip>

namespace photon {

// UVTransform implementation
Vec2 UVTransform::transform(const Vec2& uv) const {
    // Translate to pivot
    Vec2 centered = uv - pivot;
    
    // Apply rotation
    if (rotation != 0.0f) {
        float cosR = std::cos(rotation);
        float sinR = std::sin(rotation);
        float x = centered.x * cosR - centered.y * sinR;
        float y = centered.x * sinR + centered.y * cosR;
        centered = Vec2(x, y);
    }
    
    // Apply scale
    centered.x *= scale.x;
    centered.y *= scale.y;
    
    // Translate back from pivot and apply offset
    return centered + pivot + offset;
}

Matrix4 UVTransform::getMatrix() const {
    Matrix4 result = Matrix4::identity();
    
    // Apply transformations in order: translate to pivot, rotate, scale, translate back, offset
    result = Matrix4::translation(Vec3(pivot.x + offset.x, pivot.y + offset.y, 0.0f)) * result;
    result = Matrix4::translation(Vec3(-pivot.x, -pivot.y, 0.0f)) * result;
    result = Matrix4::scale(Vec3(scale.x, scale.y, 1.0f)) * result;
    result = Matrix4::rotationZ(rotation) * result;
    result = Matrix4::translation(Vec3(pivot.x, pivot.y, 0.0f)) * result;
    
    return result;
}

void UVTransform::reset() {
    offset = Vec2(0.0f, 0.0f);
    scale = Vec2(1.0f, 1.0f);
    rotation = 0.0f;
    pivot = Vec2(0.5f, 0.5f);
}

// TextureManager implementation
TextureManager::TextureManager() {
    // Initialize with default state
}

TextureManager::~TextureManager() {
    shutdown();
}

bool TextureManager::initialize(const std::string& textureLibraryPath, size_t cacheSize) {
    if (m_initialized) {
        shutdown();
    }
    
    m_libraryPath = textureLibraryPath;
    m_maxCacheSize = cacheSize * 1024 * 1024; // Convert MB to bytes
    
    try {
        // Create texture library directory if it doesn't exist
        std::filesystem::create_directories(textureLibraryPath);
        
        // Create subdirectories
        std::filesystem::create_directories(textureLibraryPath + "/thumbnails");
        std::filesystem::create_directories(textureLibraryPath + "/cache");
        
        m_initialized = true;
        
        std::cout << "TextureManager initialized successfully" << std::endl;
        std::cout << "Library path: " << textureLibraryPath << std::endl;
        std::cout << "Cache size: " << cacheSize << " MB" << std::endl;
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "TextureManager initialization failed: " << e.what() << std::endl;
        return false;
    }
}

void TextureManager::shutdown() {
    if (!m_initialized) return;
    
    // Clear cache
    m_cache.clear();
    m_recentTextures.clear();
    
    m_currentCacheSize = 0;
    m_initialized = false;
    
    std::cout << "TextureManager shutdown complete" << std::endl;
}

std::string TextureManager::loadTexture(const std::string& filePath, bool generateThumbnail) {
    if (!m_initialized) {
        std::cerr << "TextureManager not initialized" << std::endl;
        return "";
    }
    
    if (!std::filesystem::exists(filePath)) {
        std::cerr << "Texture file not found: " << filePath << std::endl;
        return "";
    }
    
    // Generate texture ID
    std::string textureId = generateTextureId(filePath);
    
    // Check if already loaded
    if (m_cache.find(textureId) != m_cache.end()) {
        updateRecentTextures(textureId);
        return textureId;
    }
    
    updateProgress(0.0f, "Loading texture: " + std::filesystem::path(filePath).filename().string());
    
    try {
        // Load texture from file
        auto cacheEntry = loadTextureFromFile(filePath);
        if (!cacheEntry) {
            std::cerr << "Failed to load texture: " << filePath << std::endl;
            return "";
        }
        
        updateProgress(0.7f, "Processing texture...");
        
        // Generate thumbnail if requested
        if (generateThumbnail) {
            generateThumbnail(textureId, 128);
        }
        
        // Add to cache
        m_cache[textureId] = cacheEntry;
        updateCacheSize();
        
        // Update recent textures
        updateRecentTextures(textureId);
        
        updateProgress(1.0f, "Texture loaded successfully");
        
        std::cout << "Loaded texture: " << textureId << " (" << filePath << ")" << std::endl;
        return textureId;
        
    } catch (const std::exception& e) {
        std::cerr << "Error loading texture: " << e.what() << std::endl;
        return "";
    }
}

std::shared_ptr<Texture> TextureManager::getTexture(const std::string& textureId) {
    auto it = m_cache.find(textureId);
    if (it == m_cache.end()) {
        return nullptr;
    }
    
    auto& entry = it->second;
    
    // Update access tracking
    entry->lastAccessed = std::chrono::duration_cast<std::chrono::seconds>(
        std::chrono::system_clock::now().time_since_epoch()
    ).count();
    entry->accessCount++;
    
    // Update recent textures
    updateRecentTextures(textureId);
    
    return entry->texture;
}

TextureMetadata TextureManager::getTextureMetadata(const std::string& textureId) {
    auto it = m_cache.find(textureId);
    if (it != m_cache.end()) {
        return it->second->metadata;
    }
    
    // Return empty metadata if not found
    return TextureMetadata{};
}

std::shared_ptr<Image> TextureManager::getTextureThumbnail(const std::string& textureId) {
    auto it = m_cache.find(textureId);
    if (it != m_cache.end()) {
        return it->second->thumbnail;
    }
    
    return nullptr;
}

bool TextureManager::removeTexture(const std::string& textureId) {
    auto it = m_cache.find(textureId);
    if (it == m_cache.end()) {
        return false;
    }
    
    // Remove from cache
    m_cache.erase(it);
    
    // Remove from recent textures
    m_recentTextures.erase(
        std::remove(m_recentTextures.begin(), m_recentTextures.end(), textureId),
        m_recentTextures.end()
    );
    
    // Update cache size
    updateCacheSize();
    
    std::cout << "Removed texture from cache: " << textureId << std::endl;
    return true;
}

std::vector<std::string> TextureManager::searchTextures(const TextureSearchCriteria& criteria) {
    std::vector<std::string> results;
    
    for (const auto& pair : m_cache) {
        const auto& textureId = pair.first;
        const auto& entry = pair.second;
        const auto& metadata = entry->metadata;
        
        // Name filter
        if (!criteria.nameFilter.empty()) {
            std::string lowerName = metadata.name;
            std::string lowerFilter = criteria.nameFilter;
            std::transform(lowerName.begin(), lowerName.end(), lowerName.begin(), ::tolower);
            std::transform(lowerFilter.begin(), lowerFilter.end(), lowerFilter.begin(), ::tolower);
            
            if (lowerName.find(lowerFilter) == std::string::npos) {
                continue;
            }
        }
        
        // Format filter
        if (!criteria.formats.empty()) {
            if (std::find(criteria.formats.begin(), criteria.formats.end(), metadata.format) == criteria.formats.end()) {
                continue;
            }
        }
        
        // Size filters
        if (metadata.width < criteria.minWidth || metadata.width > criteria.maxWidth ||
            metadata.height < criteria.minHeight || metadata.height > criteria.maxHeight) {
            continue;
        }
        
        // HDR filter
        if (criteria.hdrOnly && !metadata.isHDR) {
            continue;
        }
        
        // Recent filter
        if (criteria.recentOnly) {
            if (std::find(m_recentTextures.begin(), m_recentTextures.end(), textureId) == m_recentTextures.end()) {
                continue;
            }
        }
        
        // Tag filter
        if (!criteria.tags.empty()) {
            bool hasAllTags = true;
            for (const auto& tag : criteria.tags) {
                if (std::find(metadata.tags.begin(), metadata.tags.end(), tag) == metadata.tags.end()) {
                    hasAllTags = false;
                    break;
                }
            }
            if (!hasAllTags) {
                continue;
            }
        }
        
        // Texture passed all filters
        results.push_back(textureId);
    }
    
    return results;
}

std::vector<std::string> TextureManager::getAllTextureIds() {
    std::vector<std::string> results;
    results.reserve(m_cache.size());
    
    for (const auto& pair : m_cache) {
        results.push_back(pair.first);
    }
    
    return results;
}

std::vector<std::string> TextureManager::getRecentTextures(int count) {
    std::vector<std::string> result;
    int actualCount = std::min(count, static_cast<int>(m_recentTextures.size()));
    
    for (int i = 0; i < actualCount; ++i) {
        result.push_back(m_recentTextures[i]);
    }
    
    return result;
}

TextureAssignment TextureManager::createAssignment(const std::string& textureId, TextureType type) {
    TextureAssignment assignment;
    assignment.textureId = textureId;
    assignment.type = type;
    assignment.uvTransform.reset();
    assignment.filter = TextureFilter::LINEAR;
    assignment.wrapU = TextureWrap::REPEAT;
    assignment.wrapV = TextureWrap::REPEAT;
    assignment.intensity = 1.0f;
    assignment.enabled = true;
    
    return assignment;
}

bool TextureManager::validateAssignment(const TextureAssignment& assignment) {
    // Check if texture exists
    if (m_cache.find(assignment.textureId) == m_cache.end()) {
        return false;
    }
    
    // Validate intensity range
    if (assignment.intensity < 0.0f || assignment.intensity > 10.0f) {
        return false;
    }
    
    // Validate UV transform
    if (assignment.uvTransform.scale.x <= 0.0f || assignment.uvTransform.scale.y <= 0.0f) {
        return false;
    }
    
    return true;
}

std::vector<std::string> TextureManager::getSupportedFormats() {
    return {
        "png", "jpg", "jpeg", "bmp", "tga", "tiff", "tif",
        "hdr", "exr", "pfm", "ppm", "pgm", "pbm"
    };
}

bool TextureManager::isFormatSupported(const std::string& format) {
    auto supportedFormats = getSupportedFormats();
    std::string lowerFormat = format;
    std::transform(lowerFormat.begin(), lowerFormat.end(), lowerFormat.begin(), ::tolower);
    
    return std::find(supportedFormats.begin(), supportedFormats.end(), lowerFormat) != supportedFormats.end();
}

std::string TextureManager::getCacheStats() {
    std::ostringstream stats;
    
    stats << "Texture Cache Statistics:\n";
    stats << "Cached textures: " << m_cache.size() << "\n";
    stats << "Cache size: " << std::fixed << std::setprecision(2) 
          << (m_currentCacheSize / (1024.0f * 1024.0f)) << " MB\n";
    stats << "Cache limit: " << std::fixed << std::setprecision(2) 
          << (m_maxCacheSize / (1024.0f * 1024.0f)) << " MB\n";
    stats << "Recent textures: " << m_recentTextures.size() << "\n";
    
    return stats.str();
}

void TextureManager::clearCache(bool keepRecent) {
    if (keepRecent && !m_recentTextures.empty()) {
        // Keep only recent textures
        std::unordered_map<std::string, std::shared_ptr<TextureCacheEntry>> newCache;
        
        for (const auto& textureId : m_recentTextures) {
            auto it = m_cache.find(textureId);
            if (it != m_cache.end()) {
                newCache[textureId] = it->second;
            }
        }
        
        m_cache = std::move(newCache);
    } else {
        m_cache.clear();
        m_recentTextures.clear();
    }
    
    updateCacheSize();
    
    std::cout << "Texture cache cleared" << std::endl;
}

void TextureManager::setCacheSize(size_t sizeInMB) {
    m_maxCacheSize = sizeInMB * 1024 * 1024;
    
    // Evict textures if over new limit
    if (m_currentCacheSize > m_maxCacheSize) {
        evictLRU(m_maxCacheSize);
    }
    
    std::cout << "Cache size set to " << sizeInMB << " MB" << std::endl;
}

} // namespace photon
