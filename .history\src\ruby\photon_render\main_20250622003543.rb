# src/ruby/photon_render/main.rb
# Main plugin loader for PhotonRender

module PhotonRender

  # Plugin initialization
  def self.initialize_plugin
    puts "Initializing PhotonRender plugin..."

    # Check if all required modules are loaded
    required_modules = [<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Dialog, ViewportTool, MaterialLibraryManager,
                       TextureAssignmentManager, MaterialValidationManager, MaterialExportImportManager]

    missing_modules = []
    required_modules.each_with_index do |mod, i|
      module_names = ['Menu', 'Toolbar', 'Dialog', 'ViewportTool', 'MaterialLibraryManager',
                     'TextureAssignmentManager', 'MaterialValidationManager', 'MaterialExportImportManager']
      begin
        mod # Try to access the module
      rescue NameError
        missing_modules << module_names[i]
      end
    end

    if missing_modules.any?
      puts "ERROR: Missing required modules: #{missing_modules.join(', ')}"
      UI.messagebox("PhotonRender initialization failed!\nMissing modules: #{missing_modules.join(', ')}")
      return false
    end

    # Create menu items
    Menu.create
    puts "Menu created"

    # Create toolbar
    Toolbar.create
    puts "Toolbar created"

    # Initialize material library manager
    MaterialLibraryManager.initialize
    puts "Material Library Manager initialized"

    # Initialize texture assignment manager
    TextureAssignmentManager.initialize
    puts "Texture Assignment Manager initialized"

    # Initialize material validation manager
    MaterialValidationManager.initialize
    puts "Material Validation Manager initialized"

    # Initialize material export/import manager
    MaterialExportImportManager.initialize
    puts "Material Export/Import Manager initialized"

    # Register observers
    Sketchup.active_model.add_observer(ModelObserver.new)
    puts "Model observer registered"

    # Load preferences
    load_preferences
    puts "Preferences loaded"

    puts "PhotonRender #{PLUGIN_VERSION} loaded successfully"
    puts "Core loaded: #{core_loaded?}"
    
    # Show welcome message
    show_welcome_message
  end
  
  # Show welcome message
  def self.show_welcome_message
    if core_loaded?
      UI.messagebox("PhotonRender #{PLUGIN_VERSION} loaded successfully!\nFull rendering capabilities available.")
    else
      result = UI.messagebox(
        "PhotonRender #{PLUGIN_VERSION} loaded in TESTING MODE\n\n" +
        "The C++ rendering core is not available, but you can:\n" +
        "• Test the user interface\n" +
        "• Export scene geometry\n" +
        "• Configure materials\n" +
        "• Test all plugin features\n\n" +
        "This is normal for development and testing.\n\n" +
        "Continue with testing?", 
        MB_YESNO
      )
      
      if result == IDNO
        puts "User cancelled PhotonRender testing"
      end
    end
  end
  
  # Load user preferences
  def self.load_preferences
    @preferences = {
      last_render_settings: Sketchup.read_default(PLUGIN_ID, 'render_settings', {}),
      viewport_preview: Sketchup.read_default(PLUGIN_ID, 'viewport_preview', true),
      auto_save_render: Sketchup.read_default(PLUGIN_ID, 'auto_save', false)
    }
  end
  
  # Save preferences
  def self.save_preferences
    Sketchup.write_default(PLUGIN_ID, 'render_settings', @preferences[:last_render_settings])
    Sketchup.write_default(PLUGIN_ID, 'viewport_preview', @preferences[:viewport_preview])
    Sketchup.write_default(PLUGIN_ID, 'auto_save', @preferences[:auto_save_render])
  end
  
  # Get preferences
  def self.preferences
    @preferences ||= {}
  end
  
  # Check if viewport preview is enabled
  def self.viewport_preview_enabled?
    preferences[:viewport_preview] || false
  end
  
  # Check if auto save is enabled
  def self.auto_save_enabled?
    preferences[:auto_save_render] || false
  end
  
  # Model observer
  class ModelObserver < Sketchup::ModelObserver
    def onTransactionCommit(model)
      # Update viewport preview if enabled
      if PhotonRender.viewport_preview_enabled?
        puts "Model changed - viewport preview update requested"
        # ViewportTool.update_preview if defined?(ViewportTool)
      end
    end
    
    def onTransactionStart(model)
      puts "Transaction started"
    end
    
    def onTransactionUndo(model)
      puts "Transaction undone"
    end
    
    def onTransactionRedo(model)
      puts "Transaction redone"
    end
  end

end # module PhotonRender

# Initialize plugin after all modules are loaded
PhotonRender.initialize_plugin
