# src/ruby/photon_render/menu.rb
# PhotonRender - SketchUp Menu Integration
# Sistema menu per PhotonRender in SketchUp

module PhotonRender
  module Menu
    
    # Create PhotonRender menu in SketchUp
    def self.create
      puts "Creating PhotonRender menu..."
      
      # Create main menu
      @menu = UI.menu("Plugins").add_submenu("PhotonRender")
      
      # Add menu items
      add_render_items
      add_settings_items
      add_tools_items
      add_help_items
      
      puts "PhotonRender menu created successfully"
    end
    
    # Remove menu (for cleanup)
    def self.remove
      # Note: SketchUp doesn't provide direct menu removal
      # Menu will be removed when plugin is unloaded
      @menu = nil
    end
    
    private
    
    # Add rendering menu items
    def self.add_render_items
      @menu.add_item("Start Render") do
        start_render_command
      end
      
      @menu.add_item("Quick Render") do
        quick_render_command
      end
      
      @menu.add_item("Stop Render") do
        stop_render_command
      end
      
      @menu.add_separator
      
      @menu.add_item("Render Settings...") do
        show_render_settings
      end
      
      @menu.add_item("Render Queue...") do
        show_render_queue
      end
    end
    
    # Add settings menu items
    def self.add_settings_items
      @menu.add_separator
      
      @menu.add_item("Material Editor...") do
        show_material_editor
      end
      
      @menu.add_item("Light Setup...") do
        show_light_setup
      end
      
      @menu.add_item("Environment...") do
        show_environment_settings
      end
      
      @menu.add_separator
      
      @menu.add_item("Preferences...") do
        show_preferences
      end
    end
    
    # Add tools menu items
    def self.add_tools_items
      @menu.add_separator
      
      @menu.add_item("Export Scene...") do
        export_scene_command
      end
      
      @menu.add_item("Import Scene...") do
        import_scene_command
      end
      
      @menu.add_separator
      
      @menu.add_item("Viewport Preview") do
        toggle_viewport_preview
      end
      
      @menu.add_item("Material Preview") do
        toggle_material_preview
      end
    end
    
    # Add help menu items
    def self.add_help_items
      @menu.add_separator
      
      @menu.add_item("About PhotonRender...") do
        show_about_dialog
      end
      
      @menu.add_item("Help Documentation") do
        show_help_documentation
      end
      
      @menu.add_item("System Information") do
        show_system_info
      end
      
      @menu.add_item("Check for Updates") do
        check_for_updates
      end
    end
    
    # Menu command implementations
    
    def self.start_render_command
      puts "Menu: Start Render"
      
      if PhotonRender.render_manager.is_rendering
        UI.messagebox("Render already in progress!")
        return
      end
      
      # Show render settings dialog first
      Dialog.show_render_settings do |settings|
        if settings
          PhotonRender.render_manager.start_render(settings)
        end
      end
    end
    
    def self.quick_render_command
      puts "Menu: Quick Render"
      
      if PhotonRender.render_manager.is_rendering
        UI.messagebox("Render already in progress!")
        return
      end
      
      # Start render with default settings
      PhotonRender.render_manager.start_render
    end
    
    def self.stop_render_command
      puts "Menu: Stop Render"
      
      if PhotonRender.render_manager.is_rendering
        result = UI.messagebox("Stop current render?", MB_YESNO)
        if result == IDYES
          PhotonRender.render_manager.stop_render
        end
      else
        UI.messagebox("No render in progress")
      end
    end
    
    def self.show_render_settings
      puts "Menu: Render Settings"
      Dialog.show_render_settings
    end
    
    def self.show_render_queue
      puts "Menu: Render Queue"
      Dialog.show_render_queue
    end
    
    def self.show_material_editor
      puts "Menu: Material Editor"
      Dialog.show_material_editor
    end
    
    def self.show_light_setup
      puts "Menu: Light Setup"
      Dialog.show_light_setup
    end
    
    def self.show_environment_settings
      puts "Menu: Environment Settings"
      Dialog.show_environment_settings
    end
    
    def self.show_preferences
      puts "Menu: Preferences"
      Dialog.show_preferences
    end
    
    def self.export_scene_command
      puts "Menu: Export Scene"
      
      filename = UI.savepanel("Export Scene", "", "PhotonRender Scene|*.prs")
      if filename
        begin
          model = Sketchup.active_model
          scene_data = SceneExport.export_scene(model)
          
          # Save scene data to file (JSON format)
          File.open(filename, 'w') do |file|
            file.write(JSON.pretty_generate(scene_data))
          end
          
          UI.messagebox("Scene exported to: #{filename}")
        rescue => e
          UI.messagebox("Export failed: #{e.message}")
        end
      end
    end
    
    def self.import_scene_command
      puts "Menu: Import Scene"
      
      filename = UI.openpanel("Import Scene", "", "PhotonRender Scene|*.prs")
      if filename && File.exist?(filename)
        begin
          # Load scene data from file
          scene_data = JSON.parse(File.read(filename))
          
          # TODO: Implement scene import
          UI.messagebox("Scene import not yet implemented")
        rescue => e
          UI.messagebox("Import failed: #{e.message}")
        end
      end
    end
    
    def self.toggle_viewport_preview
      puts "Menu: Toggle Viewport Preview"
      ViewportTool.toggle_preview
    end
    
    def self.toggle_material_preview
      puts "Menu: Toggle Material Preview"
      # TODO: Implement material preview
      UI.messagebox("Material preview not yet implemented")
    end
    
    def self.show_about_dialog
      puts "Menu: About PhotonRender"
      
      version = PhotonCore.version rescue "Unknown"
      capabilities = PhotonCore.capabilities rescue {}
      
      about_text = "PhotonRender v#{version}\n\n"
      about_text += "GPU-Accelerated Ray Tracing for SketchUp\n\n"
      about_text += "Capabilities:\n"
      about_text += "• CUDA: #{capabilities['cuda'] ? 'Available' : 'Not Available'}\n"
      about_text += "• OptiX: #{capabilities['optix'] ? 'Available' : 'Not Available'}\n"
      about_text += "• RT Cores: #{capabilities['rt_cores'] || 'Unknown'}\n"
      about_text += "• Max Threads: #{capabilities['max_threads'] || 'Unknown'}\n\n"
      about_text += "© 2025 PhotonRender Team"
      
      UI.messagebox(about_text, MB_OK, "About PhotonRender")
    end
    
    def self.show_help_documentation
      puts "Menu: Help Documentation"
      
      # Open help documentation in browser
      help_url = "https://photonrender.com/docs"
      UI.openURL(help_url)
    end
    
    def self.show_system_info
      puts "Menu: System Information"
      
      begin
        capabilities = PhotonCore.capabilities
        
        info_text = "PhotonRender System Information\n\n"
        info_text += "Ruby Version: #{RUBY_VERSION}\n"
        info_text += "SketchUp Version: #{Sketchup.version}\n"
        info_text += "Platform: #{RUBY_PLATFORM}\n\n"
        info_text += "PhotonRender Core:\n"
        info_text += "• Version: #{PhotonCore.version}\n"
        info_text += "• Embree: #{capabilities['embree'] ? 'Available' : 'Not Available'}\n"
        info_text += "• CUDA: #{capabilities['cuda'] ? 'Available' : 'Not Available'}\n"
        info_text += "• OptiX: #{capabilities['optix'] ? 'Available' : 'Not Available'}\n"
        info_text += "• OpenMP: #{capabilities['openmp'] ? 'Available' : 'Not Available'}\n"
        info_text += "• Max Threads: #{capabilities['max_threads']}\n"
        info_text += "• GPU Count: #{capabilities['gpu_count']}\n"
        info_text += "• RT Cores: #{capabilities['rt_cores']}\n"
        
        UI.messagebox(info_text, MB_OK, "System Information")
      rescue => e
        UI.messagebox("Failed to get system information: #{e.message}")
      end
    end
    
    def self.check_for_updates
      puts "Menu: Check for Updates"
      
      # TODO: Implement update checking
      UI.messagebox("Update checking not yet implemented")
    end
    
  end
end
