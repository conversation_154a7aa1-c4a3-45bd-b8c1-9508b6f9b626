# test_geometry_export.rb
# Test specifico per il sistema di export geometria PhotonRender
# Verifica conversione Face→Triangle, material mapping, transform handling

puts "=== PhotonRender Geometry Export System Test ==="
puts "Testing geometry conversion and scene export functionality"
puts ""

# Mock delle classi SketchUp per il test
class MockGeomTransformation
  def initialize
    @matrix = [1,0,0,0, 0,1,0,0, 0,0,1,0, 0,0,0,1]
  end
  
  def *(other)
    if other.respond_to?(:to_a)
      # Transform point
      MockPoint3d.new(other.to_a[0], other.to_a[1], other.to_a[2])
    else
      # Transform transformation
      self
    end
  end
  
  def rotation
    self
  end
end

class MockMesh
  def initialize
    @points = [
      MockPoint3d.new(0, 0, 0),
      MockPoint3d.new(1, 0, 0),
      MockPoint3d.new(1, 1, 0),
      MockPoint3d.new(0, 1, 0)
    ]
    @normals = [
      MockVector3d.new(0, 0, 1),
      MockVector3d.new(0, 0, 1),
      MockVector3d.new(0, 0, 1),
      MockVector3d.new(0, 0, 1)
    ]
    @polygons = [[1, 2, 3], [1, 3, 4]]  # Two triangles
  end
  
  def count_points
    @points.length
  end
  
  def point_at(index)
    @points[index - 1]  # SketchUp uses 1-based indexing
  end
  
  def normal_at(index)
    @normals[index - 1]
  end
  
  def count_polygons
    @polygons.length
  end
  
  def polygon_at(index)
    @polygons[index - 1]
  end
  
  def uvs(front_face = true)
    # Mock UV coordinates
    [
      MockUV.new(0.0, 0.0),
      MockUV.new(1.0, 0.0),
      MockUV.new(1.0, 1.0),
      MockUV.new(0.0, 1.0)
    ]
  end
end

class MockUV
  attr_reader :x, :y
  
  def initialize(x, y)
    @x, @y = x, y
  end
end

class MockPoint3d
  attr_reader :x, :y, :z
  
  def initialize(x, y, z)
    @x, @y, @z = x, y, z
  end
  
  def to_a
    [@x, @y, @z]
  end
end

class MockVector3d
  attr_reader :x, :y, :z
  
  def initialize(x, y, z)
    @x, @y, @z = x, y, z
  end
  
  def to_a
    [@x, @y, @z]
  end
end

class MockMaterial
  attr_reader :name, :color, :alpha, :texture
  
  def initialize(name, color = [255, 0, 0], alpha = 1.0, texture = nil)
    @name = name
    @color = MockColor.new(*color)
    @alpha = alpha
    @texture = texture
  end
end

class MockColor
  attr_reader :red, :green, :blue
  
  def initialize(r, g, b)
    @red, @green, @blue = r, g, b
  end
  
  def to_a
    [@red, @green, @blue, 255]
  end
end

class MockTexture
  attr_reader :filename, :width, :height
  
  def initialize(filename, width = 512, height = 512)
    @filename = filename
    @width = width
    @height = height
  end
end

class MockFace
  attr_reader :material
  
  def initialize(material = nil)
    @material = material
  end
  
  def mesh(flags)
    MockMesh.new
  end
end

class MockGroup
  attr_reader :transformation, :entities
  
  def initialize
    @transformation = MockGeomTransformation.new
    @entities = MockEntities.new
  end
end

class MockComponentInstance
  attr_reader :transformation, :definition
  
  def initialize
    @transformation = MockGeomTransformation.new
    @definition = MockComponentDefinition.new
  end
end

class MockComponentDefinition
  attr_reader :entities
  
  def initialize
    @entities = MockEntities.new
  end
end

class MockEntities
  def initialize
    @entities = [
      MockFace.new(MockMaterial.new("Red_Material", [255, 0, 0])),
      MockFace.new(MockMaterial.new("Blue_Material", [0, 0, 255])),
      MockGroup.new
    ]
  end
  
  def each
    @entities.each { |entity| yield entity }
  end
end

class MockModel
  attr_reader :entities, :materials, :active_view, :shadow_info
  
  def initialize
    @entities = MockEntities.new
    @materials = [
      MockMaterial.new("Red_Material", [255, 0, 0]),
      MockMaterial.new("Blue_Material", [0, 0, 255]),
      MockMaterial.new("Textured_Material", [255, 255, 255], 1.0, 
                      MockTexture.new("texture.jpg"))
    ]
    @active_view = MockView.new
    @shadow_info = {
      'DisplayShadows' => true,
      'SunDirection' => MockVector3d.new(-0.5, -0.5, -0.7)
    }
  end
end

class MockView
  attr_reader :camera, :vpwidth, :vpheight
  
  def initialize
    @camera = MockCamera.new
    @vpwidth = 1920
    @vpheight = 1080
  end
end

class MockCamera
  def eye
    MockPoint3d.new(10, 10, 10)
  end
  
  def target
    MockPoint3d.new(0, 0, 0)
  end
  
  def up
    MockVector3d.new(0, 0, 1)
  end
  
  def fov
    45.0
  end
end

# Mock delle costanti SketchUp
module Sketchup
  class Face; end
  class Group; end
  class ComponentInstance; end
end

module Geom
  class Transformation
    def self.new
      MockGeomTransformation.new
    end
  end
end

# Crea file mock per sketchup.rb e extensions.rb
File.write('sketchup.rb', '# Mock SketchUp library')
File.write('extensions.rb', '# Mock Extensions library')

# Mock delle funzioni globali
def file_loaded?(file)
  false
end

def file_loaded(file)
  puts "✓ File marked as loaded: #{File.basename(file)}"
end

# Mock delle costanti SketchUp
Sketchup = Class.new
MB_OK = 0
MB_YESNO = 4
IDYES = 6

# Mock UI
module UI
  def self.messagebox(message, type = nil, title = nil)
    puts "UI Message: #{message}"
    1
  end
end

# Mock SketchupExtension
class SketchupExtension
  attr_accessor :name, :description, :version, :copyright, :creator

  def initialize(name, main_file)
    @name = name
    @main_file = main_file
  end
end

# Carica il modulo SceneExport
require_relative 'src/ruby/photon_render.rb'

puts "1. Testing Camera Export..."
puts ""

begin
  model = MockModel.new
  camera_data = PhotonRender::SceneExport.send(:export_camera, model.active_view)
  
  puts "✓ Camera export successful"
  puts "  - Position: #{camera_data[:position]}"
  puts "  - Target: #{camera_data[:target]}"
  puts "  - Up: #{camera_data[:up]}"
  puts "  - FOV: #{camera_data[:fov]}°"
  puts "  - Aspect: #{camera_data[:aspect]}"
rescue => e
  puts "✗ Camera export failed: #{e.message}"
end

puts ""
puts "2. Testing Geometry Export..."
puts ""

begin
  model = MockModel.new
  geometry_data = PhotonRender::SceneExport.send(:export_geometry, model)
  
  puts "✓ Geometry export successful"
  puts "  - Meshes exported: #{geometry_data.length}"
  
  geometry_data.each_with_index do |mesh, i|
    puts "  - Mesh #{i + 1}:"
    puts "    * Vertices: #{mesh[:vertices].length}"
    puts "    * Triangles: #{mesh[:triangles].length}"
    puts "    * Material: #{mesh[:material_id]}"
  end
rescue => e
  puts "✗ Geometry export failed: #{e.message}"
  puts e.backtrace.first(3).join("\n")
end

puts ""
puts "3. Testing Material Export..."
puts ""

begin
  model = MockModel.new
  materials_data = PhotonRender::SceneExport.send(:export_materials, model)
  
  puts "✓ Material export successful"
  puts "  - Materials exported: #{materials_data.length}"
  
  materials_data.each do |material|
    puts "  - #{material[:name]}: RGB#{material[:color]}, Alpha: #{material[:alpha]}"
    puts "    * Texture: #{material[:texture] || 'None'}"
  end
rescue => e
  puts "✗ Material export failed: #{e.message}"
end

puts ""
puts "4. Testing Light Export..."
puts ""

begin
  model = MockModel.new
  lights_data = PhotonRender::SceneExport.send(:export_lights, model)
  
  puts "✓ Light export successful"
  puts "  - Lights exported: #{lights_data.length}"
  
  lights_data.each_with_index do |light, i|
    puts "  - Light #{i + 1}: #{light[:type]}"
    puts "    * Direction: #{light[:direction]}" if light[:direction]
    puts "    * Color: #{light[:color]}"
    puts "    * Intensity: #{light[:intensity]}"
  end
rescue => e
  puts "✗ Light export failed: #{e.message}"
end

puts ""
puts "5. Testing Complete Scene Export..."
puts ""

begin
  model = MockModel.new
  scene_data = PhotonRender::SceneExport.export_scene(model)
  
  puts "✓ Complete scene export successful"
  puts "  - Camera: #{scene_data[:camera] ? 'OK' : 'Missing'}"
  puts "  - Geometry: #{scene_data[:geometry] ? "#{scene_data[:geometry].length} meshes" : 'Missing'}"
  puts "  - Materials: #{scene_data[:materials] ? "#{scene_data[:materials].length} materials" : 'Missing'}"
  puts "  - Lights: #{scene_data[:lights] ? "#{scene_data[:lights].length} lights" : 'Missing'}"
  puts "  - Environment: #{scene_data[:environment] ? 'OK' : 'Missing'}"
rescue => e
  puts "✗ Complete scene export failed: #{e.message}"
  puts e.backtrace.first(3).join("\n")
end

puts ""
puts "=== Geometry Export Test Summary ==="
puts "PhotonRender Geometry Export System Test completed"
puts "Check the output above for any failures (✗) that need attention"
puts ""

# Cleanup file mock
File.delete('sketchup.rb') if File.exist?('sketchup.rb')
File.delete('extensions.rb') if File.exist?('extensions.rb')
