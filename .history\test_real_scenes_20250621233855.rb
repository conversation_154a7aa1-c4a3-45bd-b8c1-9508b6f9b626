# test_real_scenes.rb
# Test PhotonRender con scene SketchUp realistiche e complesse
# Verifica: architettura, product design, interior design, performance

puts "=== PhotonRender Real Scene Testing ==="
puts "Testing with realistic SketchUp scenes and complex geometries"
puts ""

# Mock delle classi per scene realistiche
class MockComplexMesh
  def initialize(vertex_count, triangle_count)
    @vertex_count = vertex_count
    @triangle_count = triangle_count
    @vertices = generate_vertices(vertex_count)
    @triangles = generate_triangles(triangle_count)
  end
  
  def count_points
    @vertex_count
  end
  
  def count_polygons
    @triangle_count
  end
  
  def point_at(index)
    @vertices[index - 1] || MockPoint3d.new(0, 0, 0)
  end
  
  def normal_at(index)
    MockVector3d.new(0, 0, 1)
  end
  
  def polygon_at(index)
    @triangles[index - 1] || [1, 2, 3]
  end
  
  def uvs(front_face = true)
    Array.new(@vertex_count) { MockUV.new(rand, rand) }
  end
  
  private
  
  def generate_vertices(count)
    Array.new(count) do |i|
      MockPoint3d.new(
        (i % 10) * 2.0,
        ((i / 10) % 10) * 2.0,
        ((i / 100) % 10) * 2.0
      )
    end
  end
  
  def generate_triangles(count)
    Array.new(count) do |i|
      base = i * 3
      [base + 1, base + 2, base + 3]
    end
  end
end

class MockPoint3d
  attr_reader :x, :y, :z
  
  def initialize(x, y, z)
    @x, @y, @z = x, y, z
  end
  
  def to_a
    [@x, @y, @z]
  end
end

class MockVector3d
  attr_reader :x, :y, :z
  
  def initialize(x, y, z)
    @x, @y, @z = x, y, z
  end
  
  def to_a
    [@x, @y, @z]
  end
end

class MockUV
  attr_reader :x, :y
  
  def initialize(x, y)
    @x, @y = x, y
  end
end

class MockComplexMaterial
  attr_reader :name, :color, :alpha, :texture
  
  def initialize(name, type = :pbr)
    @name = name
    @type = type
    @color = MockColor.new(*generate_color_for_type(type))
    @alpha = type == :glass ? 0.3 : 1.0
    @texture = generate_texture_for_type(type)
  end
  
  private
  
  def generate_color_for_type(type)
    case type
    when :wood then [139, 69, 19]
    when :metal then [192, 192, 192]
    when :glass then [230, 230, 250]
    when :concrete then [128, 128, 128]
    when :brick then [178, 34, 34]
    when :fabric then [255, 228, 196]
    else [200, 200, 200]
    end
  end
  
  def generate_texture_for_type(type)
    case type
    when :wood then MockTexture.new("wood_grain.jpg", 1024, 1024)
    when :metal then MockTexture.new("metal_brushed.jpg", 512, 512)
    when :concrete then MockTexture.new("concrete_rough.jpg", 2048, 2048)
    when :brick then MockTexture.new("brick_pattern.jpg", 1024, 512)
    when :fabric then MockTexture.new("fabric_weave.jpg", 512, 512)
    else nil
    end
  end
end

class MockColor
  attr_reader :red, :green, :blue
  
  def initialize(r, g, b)
    @red, @green, @blue = r, g, b
  end
  
  def to_a
    [@red, @green, @blue, 255]
  end
end

class MockTexture
  attr_reader :filename, :width, :height
  
  def initialize(filename, width, height)
    @filename = filename
    @width = width
    @height = height
  end
end

class MockComplexFace
  attr_reader :material
  
  def initialize(material, complexity = :simple)
    @material = material
    @complexity = complexity
  end
  
  def mesh(flags)
    case @complexity
    when :simple then MockComplexMesh.new(4, 2)
    when :medium then MockComplexMesh.new(50, 96)
    when :complex then MockComplexMesh.new(500, 996)
    when :very_complex then MockComplexMesh.new(2000, 3996)
    end
  end
end

class MockComplexGroup
  attr_reader :transformation, :entities
  
  def initialize(entity_count = 10)
    @transformation = MockTransformation.new
    @entities = MockComplexEntities.new(entity_count)
  end
end

class MockTransformation
  def initialize
    @matrix = [1,0,0,0, 0,1,0,0, 0,0,1,0, 0,0,0,1]
  end
  
  def *(other)
    if other.respond_to?(:to_a)
      MockPoint3d.new(other.to_a[0], other.to_a[1], other.to_a[2])
    else
      self
    end
  end
end

class MockComplexEntities
  def initialize(count = 10)
    @entities = generate_entities(count)
  end
  
  def each
    @entities.each { |entity| yield entity }
  end
  
  def length
    @entities.length
  end
  
  private
  
  def generate_entities(count)
    entities = []
    
    # Mix di diversi tipi di entità
    (count / 3).times do |i|
      material_type = [:wood, :metal, :glass, :concrete, :brick, :fabric].sample
      complexity = [:simple, :medium, :complex].sample
      material = MockComplexMaterial.new("Material_#{i}", material_type)
      entities << MockComplexFace.new(material, complexity)
    end
    
    # Aggiungi gruppi nested
    (count / 6).times do |i|
      entities << MockComplexGroup.new(5 + rand(10))
    end
    
    entities
  end
end

# Scene Templates per diversi casi d'uso
class SceneTemplates
  
  # Scena Architetturale: Edificio con molte geometrie
  def self.create_architectural_scene
    {
      name: "Architectural Building",
      description: "Complex building with multiple floors, windows, doors",
      entities: MockComplexEntities.new(150),  # 150 entità
      materials: create_architectural_materials,
      lights: create_architectural_lights,
      complexity_stats: {
        estimated_vertices: 50_000,
        estimated_triangles: 95_000,
        material_count: 15,
        light_count: 8
      }
    }
  end
  
  # Scena Product Design: Oggetto con materiali complessi
  def self.create_product_design_scene
    {
      name: "Product Design Object",
      description: "Detailed product with complex materials and textures",
      entities: MockComplexEntities.new(75),   # 75 entità
      materials: create_product_materials,
      lights: create_product_lights,
      complexity_stats: {
        estimated_vertices: 25_000,
        estimated_triangles: 48_000,
        material_count: 12,
        light_count: 5
      }
    }
  end
  
  # Scena Interior Design: Ambiente interno con illuminazione
  def self.create_interior_design_scene
    {
      name: "Interior Design Room",
      description: "Detailed interior with furniture, lighting, textures",
      entities: MockComplexEntities.new(200),  # 200 entità
      materials: create_interior_materials,
      lights: create_interior_lights,
      complexity_stats: {
        estimated_vertices: 80_000,
        estimated_triangles: 155_000,
        material_count: 20,
        light_count: 12
      }
    }
  end
  
  # Scena Performance Test: Stress test con geometrie molto complesse
  def self.create_performance_test_scene
    {
      name: "Performance Stress Test",
      description: "Large scene for performance testing",
      entities: MockComplexEntities.new(500),  # 500 entità
      materials: create_performance_materials,
      lights: create_performance_lights,
      complexity_stats: {
        estimated_vertices: 200_000,
        estimated_triangles: 395_000,
        material_count: 30,
        light_count: 20
      }
    }
  end
  
  private
  
  def self.create_architectural_materials
    [
      MockComplexMaterial.new("Concrete_Wall", :concrete),
      MockComplexMaterial.new("Glass_Window", :glass),
      MockComplexMaterial.new("Steel_Frame", :metal),
      MockComplexMaterial.new("Wood_Door", :wood),
      MockComplexMaterial.new("Brick_Facade", :brick)
    ]
  end
  
  def self.create_product_materials
    [
      MockComplexMaterial.new("Brushed_Aluminum", :metal),
      MockComplexMaterial.new("Soft_Plastic", :pbr),
      MockComplexMaterial.new("Leather_Texture", :fabric),
      MockComplexMaterial.new("Chrome_Finish", :metal)
    ]
  end
  
  def self.create_interior_materials
    [
      MockComplexMaterial.new("Hardwood_Floor", :wood),
      MockComplexMaterial.new("Fabric_Sofa", :fabric),
      MockComplexMaterial.new("Glass_Table", :glass),
      MockComplexMaterial.new("Metal_Lamp", :metal),
      MockComplexMaterial.new("Painted_Wall", :pbr)
    ]
  end
  
  def self.create_performance_materials
    materials = []
    [:wood, :metal, :glass, :concrete, :brick, :fabric, :pbr].each_with_index do |type, i|
      (1..5).each do |j|
        materials << MockComplexMaterial.new("Material_#{type}_#{j}", type)
      end
    end
    materials
  end
  
  def self.create_architectural_lights
    [
      { type: "directional", name: "Sun", intensity: 5.0, color: [1.0, 1.0, 0.9] },
      { type: "area", name: "Window_Light_1", intensity: 2.0, color: [0.9, 0.9, 1.0] },
      { type: "area", name: "Window_Light_2", intensity: 2.0, color: [0.9, 0.9, 1.0] },
      { type: "point", name: "Interior_Light_1", intensity: 1.5, color: [1.0, 0.9, 0.8] }
    ]
  end
  
  def self.create_product_lights
    [
      { type: "area", name: "Studio_Key", intensity: 3.0, color: [1.0, 1.0, 1.0] },
      { type: "area", name: "Studio_Fill", intensity: 1.0, color: [0.9, 0.9, 1.0] },
      { type: "area", name: "Studio_Rim", intensity: 2.0, color: [1.0, 0.9, 0.8] }
    ]
  end
  
  def self.create_interior_lights
    [
      { type: "directional", name: "Window_Sun", intensity: 3.0, color: [1.0, 1.0, 0.9] },
      { type: "area", name: "Ceiling_Light_1", intensity: 1.5, color: [1.0, 0.9, 0.8] },
      { type: "area", name: "Ceiling_Light_2", intensity: 1.5, color: [1.0, 0.9, 0.8] },
      { type: "point", name: "Table_Lamp", intensity: 1.0, color: [1.0, 0.8, 0.6] },
      { type: "point", name: "Floor_Lamp", intensity: 1.2, color: [1.0, 0.9, 0.8] }
    ]
  end
  
  def self.create_performance_lights
    lights = []
    (1..20).each do |i|
      lights << {
        type: ["directional", "area", "point"].sample,
        name: "Light_#{i}",
        intensity: 0.5 + rand(2.0),
        color: [0.8 + rand(0.2), 0.8 + rand(0.2), 0.8 + rand(0.2)]
      }
    end
    lights
  end
  
end
