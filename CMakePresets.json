{"version": 3, "configurePresets": [{"name": "default", "displayName": "Visual Studio 2022 Release", "description": "Default build using Visual Studio 2022", "generator": "Visual Studio 17 2022", "architecture": "x64", "binaryDir": "${sourceDir}/build", "cacheVariables": {"CMAKE_BUILD_TYPE": "Release", "CMAKE_EXPORT_COMPILE_COMMANDS": "TRUE", "BUILD_BENCHMARKS": "OFF", "USE_CUDA": "OFF", "BUILD_TESTS": "ON"}, "condition": {"type": "equals", "lhs": "${hostSystemName}", "rhs": "Windows"}}, {"name": "vs2022-debug", "displayName": "Visual Studio 2022 Debug", "description": "Debug build using Visual Studio 2022", "generator": "Visual Studio 17 2022", "architecture": "x64", "binaryDir": "${sourceDir}/build-debug", "cacheVariables": {"CMAKE_BUILD_TYPE": "Debug", "CMAKE_EXPORT_COMPILE_COMMANDS": "TRUE", "BUILD_BENCHMARKS": "OFF", "USE_CUDA": "OFF", "BUILD_TESTS": "ON"}, "condition": {"type": "equals", "lhs": "${hostSystemName}", "rhs": "Windows"}}], "buildPresets": [{"name": "default", "configurePreset": "default", "configuration": "Release"}, {"name": "debug", "configurePreset": "vs2022-debug", "configuration": "Debug"}]}