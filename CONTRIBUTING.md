# 🤝 Contributing to PhotonRender

Grazie per il tuo interesse nel contribuire a PhotonRender! Questo documento fornisce le linee guida per contribuire al progetto.

## 📋 Table of Contents

- [🚀 Getting Started](#-getting-started)
- [🔧 Development Setup](#-development-setup)
- [📝 Coding Standards](#-coding-standards)
- [🧪 Testing](#-testing)
- [📤 Submitting Changes](#-submitting-changes)
- [🐛 Bug Reports](#-bug-reports)
- [💡 Feature Requests](#-feature-requests)

## 🚀 Getting Started

### Prerequisites

- **C++17** compatible compiler (GCC 9+, Clang 10+, MSVC 2019+)
- **CMake 3.20+**
- **Git**
- **Ruby 2.7+** (for SketchUp plugin development)

### First Time Setup

```bash
# 1. Fork the repository on GitHub
# 2. Clone your fork
git clone https://github.com/YOUR_USERNAME/photon-render.git
cd photon-render

# 3. Add upstream remote
git remote add upstream https://github.com/ORIGINAL_OWNER/photon-render.git

# 4. Setup development environment
./scripts/setup_dev.sh

# 5. Build the project
cd build
cmake --build . --config Debug -j$(nproc)

# 6. Run tests
ctest
```

## 🔧 Development Setup

### IDE Configuration

Il progetto include configurazioni per:
- **VS Code** (raccomandato) - `.vscode/settings.json`
- **CLion** - CMake integration
- **Visual Studio** - CMake support

### Recommended VS Code Extensions

```json
{
  "recommendations": [
    "ms-vscode.cpptools",
    "ms-vscode.cmake-tools", 
    "rebornix.ruby",
    "gruntfuggly.todo-tree",
    "aaron-bond.better-comments"
  ]
}
```

## 📝 Coding Standards

### C++ Style Guide

Seguiamo una versione modificata del **Google C++ Style Guide**:

#### Naming Conventions

```cpp
// Classes: PascalCase
class RenderManager {};

// Functions/Methods: camelCase  
void renderScene();

// Variables: camelCase
int sampleCount = 100;

// Constants: UPPER_SNAKE_CASE
const float PI = 3.14159f;

// Private members: m_ prefix
class Renderer {
private:
    int m_width;
    bool m_isRendering;
};

// Namespaces: lowercase
namespace photon {
namespace math {
```

#### File Organization

```cpp
// Header file structure
#pragma once

// System includes
#include <memory>
#include <vector>

// Third-party includes  
#include <embree4/rtcore.h>

// Project includes
#include "math/vec3.hpp"

namespace photon {
// Content
} // namespace photon
```

#### Code Formatting

Usa **clang-format** con la configurazione del progetto:

```bash
# Format single file
clang-format -i src/core/renderer.cpp

# Format all files
find src include -name "*.cpp" -o -name "*.hpp" | xargs clang-format -i
```

### Ruby Style Guide

Per il plugin SketchUp seguiamo il **Ruby Style Guide**:

```ruby
# Classes: PascalCase
class RenderManager
end

# Methods/Variables: snake_case
def start_render
  sample_count = 100
end

# Constants: UPPER_SNAKE_CASE
PLUGIN_VERSION = '0.1.0'

# Modules: PascalCase
module PhotonRender
end
```

### Documentation

#### C++ Documentation (Doxygen)

```cpp
/**
 * @brief Brief description of the function
 * 
 * Detailed description of what the function does,
 * its behavior, and any important notes.
 * 
 * @param param1 Description of parameter 1
 * @param param2 Description of parameter 2
 * @return Description of return value
 * 
 * @throws std::runtime_error When something goes wrong
 * 
 * @example
 * ```cpp
 * auto renderer = std::make_unique<Renderer>();
 * renderer->render();
 * ```
 */
void render(int width, int height);
```

#### Ruby Documentation (YARD)

```ruby
# Brief description of the method
#
# Detailed description of what the method does.
#
# @param [Integer] width The render width
# @param [Integer] height The render height
# @return [Boolean] true if successful
#
# @example
#   manager = RenderManager.new
#   manager.start_render(1920, 1080)
def start_render(width, height)
end
```

## 🧪 Testing

### Running Tests

```bash
# All tests
cd build && ctest

# Specific test category
ctest -R "unit_tests"
ctest -R "integration_tests"

# Verbose output
ctest -V
```

### Writing Tests

#### C++ Unit Tests (GoogleTest)

```cpp
// tests/unit/test_vec3.cpp
#include <gtest/gtest.h>
#include "math/vec3.hpp"

using namespace photon;

TEST(Vec3Test, DefaultConstructor) {
    Vec3 v;
    EXPECT_EQ(v.x, 0.0f);
    EXPECT_EQ(v.y, 0.0f);
    EXPECT_EQ(v.z, 0.0f);
}

TEST(Vec3Test, DotProduct) {
    Vec3 a(1, 0, 0);
    Vec3 b(0, 1, 0);
    EXPECT_FLOAT_EQ(a.dot(b), 0.0f);
}
```

#### Ruby Tests (RSpec)

```ruby
# tests/ruby/spec/render_manager_spec.rb
require 'spec_helper'

describe PhotonRender::RenderManager do
  let(:manager) { PhotonRender::RenderManager.new }
  
  describe '#initialize' do
    it 'creates a new render manager' do
      expect(manager).to be_instance_of(PhotonRender::RenderManager)
    end
  end
end
```

## 📤 Submitting Changes

### Workflow

1. **Create a branch** per feature/bugfix:
   ```bash
   git checkout -b feature/new-material-system
   git checkout -b bugfix/memory-leak-fix
   ```

2. **Make your changes** seguendo le coding standards

3. **Add tests** per nuove funzionalità

4. **Run tests** e assicurati che passino tutti:
   ```bash
   cd build && ctest
   ```

5. **Commit your changes** con messaggi descrittivi:
   ```bash
   git add .
   git commit -m "feat: implement Disney BRDF material model
   
   - Add DisneyMaterial class with full parameter set
   - Implement BRDF evaluation and sampling
   - Add unit tests for material properties
   - Update documentation with usage examples"
   ```

6. **Push to your fork**:
   ```bash
   git push origin feature/new-material-system
   ```

7. **Create a Pull Request** su GitHub

### Commit Message Format

Seguiamo **Conventional Commits**:

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

**Types:**
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks

**Examples:**
```
feat(renderer): add GPU acceleration support
fix(ruby): resolve memory leak in scene export
docs(api): update renderer class documentation
test(math): add comprehensive Vec3 unit tests
```

### Pull Request Guidelines

- **Title**: Clear and descriptive
- **Description**: Explain what and why
- **Link issues**: Reference related issues
- **Screenshots**: For UI changes
- **Tests**: Ensure all tests pass
- **Documentation**: Update if needed

## 🐛 Bug Reports

Usa il [GitHub Issues](https://github.com/OWNER/photon-render/issues) template:

```markdown
**Bug Description**
Clear description of the bug.

**Steps to Reproduce**
1. Step one
2. Step two
3. Step three

**Expected Behavior**
What should happen.

**Actual Behavior**  
What actually happens.

**Environment**
- OS: Windows 10 / macOS 12 / Ubuntu 20.04
- Compiler: GCC 11.2 / Clang 13 / MSVC 2022
- GPU: NVIDIA RTX 3080 / AMD RX 6800
- SketchUp: 2024

**Additional Context**
Any other relevant information.
```

## 💡 Feature Requests

Usa [GitHub Discussions](https://github.com/OWNER/photon-render/discussions) per:

- Nuove funzionalità
- Miglioramenti architetturali  
- Discussioni tecniche
- Domande generali

---

## 📞 Getting Help

- **Documentation**: [docs/](docs/)
- **Discussions**: GitHub Discussions
- **Issues**: GitHub Issues
- **Email**: <EMAIL>

Grazie per contribuire a PhotonRender! 🚀
