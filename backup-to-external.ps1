# PhotonRender External Backup Script
# Data: 2025-01-20
# Scopo: Backup completo del repository PhotonRender su unità esterna

param(
    [Parameter(Mandatory=$true)]
    [string]$ExternalDrive,
    
    [Parameter(Mandatory=$false)]
    [string]$BackupType = "complete"
)

# Configurazione
$sourceDir = "C:\xampp\htdocs\progetti\photon-render"
$timestamp = Get-Date -Format "yyyyMMdd-HHmm"
$logFile = "backup-log-$timestamp.txt"

Write-Host "=== PhotonRender External Backup ===" -ForegroundColor Green
Write-Host "Source: $sourceDir" -ForegroundColor Yellow
Write-Host "Target Drive: $ExternalDrive" -ForegroundColor Yellow
Write-Host "Backup Type: $BackupType" -ForegroundColor Yellow
Write-Host "Timestamp: $timestamp" -ForegroundColor Yellow

# Verifica che la directory sorgente esista
if (-not (Test-Path $sourceDir)) {
    Write-Error "Directory sorgente non trovata: $sourceDir"
    exit 1
}

# Verifica che l'unità esterna esista
if (-not (Test-Path "$ExternalDrive\")) {
    Write-Error "Unità esterna non trovata: $ExternalDrive"
    exit 1
}

# Calcola spazio necessario
$sourceSize = (Get-ChildItem -Path $sourceDir -Recurse | Measure-Object -Property Length -Sum).Sum
$sourceSizeGB = [math]::Round($sourceSize / 1GB, 2)

# Verifica spazio disponibile
$freeSpace = (Get-WmiObject -Class Win32_LogicalDisk | Where-Object {$_.DeviceID -eq $ExternalDrive}).FreeSpace
$freeSpaceGB = [math]::Round($freeSpace / 1GB, 2)

Write-Host "Spazio necessario: $sourceSizeGB GB" -ForegroundColor Cyan
Write-Host "Spazio disponibile: $freeSpaceGB GB" -ForegroundColor Cyan

if ($sourceSize -gt $freeSpace) {
    Write-Error "Spazio insufficiente sull'unità esterna!"
    exit 1
}

# Esegui backup in base al tipo
switch ($BackupType) {
    "complete" {
        $targetDir = "$ExternalDrive\PhotonRender-Complete-$timestamp"
        Write-Host "Creando backup completo in: $targetDir" -ForegroundColor Green
        
        # Usa robocopy per copia completa
        $robocopyArgs = @(
            $sourceDir,
            $targetDir,
            "/E",           # Copia subdirectories incluse quelle vuote
            "/COPYALL",     # Copia tutti gli attributi
            "/R:3",         # Retry 3 volte
            "/W:10",        # Wait 10 secondi tra retry
            "/LOG:$logFile" # Log file
        )
        
        Start-Process -FilePath "robocopy" -ArgumentList $robocopyArgs -Wait -NoNewWindow
    }
    
    "git-bare" {
        $targetDir = "$ExternalDrive\photon-render-bare-$timestamp.git"
        Write-Host "Creando repository Git bare in: $targetDir" -ForegroundColor Green
        
        # Crea repository bare
        git clone --bare $sourceDir $targetDir
    }
    
    "compressed" {
        $targetFile = "$ExternalDrive\PhotonRender-$timestamp.zip"
        Write-Host "Creando archivio compresso: $targetFile" -ForegroundColor Green
        
        # Crea archivio ZIP
        Compress-Archive -Path $sourceDir -DestinationPath $targetFile -CompressionLevel Optimal
    }
    
    default {
        Write-Error "Tipo di backup non riconosciuto: $BackupType"
        exit 1
    }
}

# Verifica risultato
if ($LASTEXITCODE -eq 0 -or $LASTEXITCODE -eq 1) {  # robocopy exit codes 0-1 sono successo
    Write-Host "Backup completato con successo!" -ForegroundColor Green
    
    # Mostra informazioni finali
    if ($BackupType -eq "complete") {
        $backupSize = (Get-ChildItem -Path $targetDir -Recurse | Measure-Object -Property Length -Sum).Sum
        $backupSizeGB = [math]::Round($backupSize / 1GB, 2)
        Write-Host "Dimensione backup: $backupSizeGB GB" -ForegroundColor Cyan
    }
    
    Write-Host "Log salvato in: $logFile" -ForegroundColor Yellow
} else {
    Write-Error "Backup fallito! Codice errore: $LASTEXITCODE"
    exit 1
}

# Crea file di informazioni
$infoContent = @"
PhotonRender Backup Information
===============================
Data Backup: $(Get-Date)
Tipo Backup: $BackupType
Directory Sorgente: $sourceDir
Directory/File Target: $targetDir
Dimensione Originale: $sourceSizeGB GB
Timestamp: $timestamp

Status Progetto:
- Phase 3.2.2: 100% Complete (6/6 task)
- Codebase: 10,000+ righe C++17
- Test Suites: 7 test executables
- Documentation: 12 files essenziali
- Repository: Solo locale (no GitHub)

Prossimi Passi:
- Phase 3.2.3: Texture System Enhancement

Note:
- Repository Git completo con cronologia preservata
- Configurazione locale: PhotonRender Developer <<EMAIL>>
- Backup automatico creato con script PowerShell
"@

$infoFile = "$ExternalDrive\PhotonRender-Backup-Info-$timestamp.txt"
$infoContent | Out-File -FilePath $infoFile -Encoding UTF8

Write-Host "File informazioni creato: $infoFile" -ForegroundColor Yellow
Write-Host "=== Backup Completato ===" -ForegroundColor Green
