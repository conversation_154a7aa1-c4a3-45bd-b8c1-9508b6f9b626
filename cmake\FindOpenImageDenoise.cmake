# cmake/FindOpenImageDenoise.cmake
# Find Intel Open Image Denoise library

# Set OIDN root path
set(OIDN_ROOT_PATH "${CMAKE_SOURCE_DIR}/external/oidn")

# Find include directory
find_path(OIDN_INCLUDE_DIR
    NAMES OpenImageDenoise/oidn.h
    PATHS ${OIDN_ROOT_PATH}/include
    NO_DEFAULT_PATH
)

# Find library
find_library(OIDN_LIBRARY
    NAMES OpenImageDenoise
    PATHS ${OIDN_ROOT_PATH}/lib
    NO_DEFAULT_PATH
)

# Find core library
find_library(OIDN_CORE_LIBRARY
    NAMES OpenImageDenoise_core
    PATHS ${OIDN_ROOT_PATH}/lib
    NO_DEFAULT_PATH
)

# Find DLL for Windows
if(WIN32)
    find_file(OIDN_DLL
        NAMES OpenImageDenoise.dll
        PATHS ${OIDN_ROOT_PATH}/bin
        NO_DEFAULT_PATH
    )
    
    find_file(OIDN_CORE_DLL
        NAMES OpenImageDenoise_core.dll
        PATHS ${OIDN_ROOT_PATH}/bin
        NO_DEFAULT_PATH
    )
endif()

# Handle standard arguments
include(FindPackageHandleStandardArgs)
find_package_handle_standard_args(OpenImageDenoise
    REQUIRED_VARS OIDN_INCLUDE_DIR OIDN_LIBRARY
    VERSION_VAR OIDN_VERSION
)

if(OpenImageDenoise_FOUND)
    # Create imported target
    if(NOT TARGET OpenImageDenoise)
        add_library(OpenImageDenoise SHARED IMPORTED)
        
        set_target_properties(OpenImageDenoise PROPERTIES
            IMPORTED_LOCATION "${OIDN_LIBRARY}"
            INTERFACE_INCLUDE_DIRECTORIES "${OIDN_INCLUDE_DIR}"
        )
        
        # Set DLL location for Windows
        if(WIN32 AND OIDN_DLL)
            set_target_properties(OpenImageDenoise PROPERTIES
                IMPORTED_IMPLIB "${OIDN_LIBRARY}"
                IMPORTED_LOCATION "${OIDN_DLL}"
            )
        endif()
    endif()
    
    # Set variables for compatibility
    set(OIDN_LIBRARIES ${OIDN_LIBRARY})
    set(OIDN_INCLUDE_DIRS ${OIDN_INCLUDE_DIR})
    
    message(STATUS "Found Intel OIDN:")
    message(STATUS "  Include dir: ${OIDN_INCLUDE_DIR}")
    message(STATUS "  Library: ${OIDN_LIBRARY}")
    if(WIN32)
        message(STATUS "  DLL: ${OIDN_DLL}")
    endif()
endif()

mark_as_advanced(
    OIDN_INCLUDE_DIR
    OIDN_LIBRARY
    OIDN_CORE_LIBRARY
    OIDN_DLL
    OIDN_CORE_DLL
)
