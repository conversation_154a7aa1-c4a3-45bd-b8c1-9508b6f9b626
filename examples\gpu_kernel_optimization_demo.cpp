// examples/gpu_kernel_optimization_demo.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// GPU Kernel Optimization System Demo

#ifdef CUDA_ENABLED
#include "../src/gpu/cuda/kernel_optimizer.hpp"
#include "../src/gpu/cuda/optimized_kernels.cuh"
#include "../src/core/profiling/performance_profiler.hpp"
#include <iostream>
#include <vector>
#include <chrono>
#include <iomanip>

using namespace photon::gpu;

/**
 * @brief Demo showing basic GPU kernel optimization
 */
void demonstrateBasicOptimization() {
    std::cout << "=== Basic GPU Kernel Optimization Demo ===" << std::endl;
    
    // Initialize CUDA
    int device_count;
    cudaGetDeviceCount(&device_count);
    
    if (device_count == 0) {
        std::cout << "No CUDA devices found. Skipping GPU optimization demo." << std::endl;
        return;
    }
    
    cudaSetDevice(0);
    
    cudaDeviceProp props;
    cudaGetDeviceProperties(&props, 0);
    
    std::cout << "GPU Device: " << props.name << std::endl;
    std::cout << "Compute Capability: " << props.major << "." << props.minor << std::endl;
    std::cout << "Multiprocessors: " << props.multiProcessorCount << std::endl;
    std::cout << "RT Cores: " << (props.major >= 7 ? props.multiProcessorCount : 0) << std::endl;
    
    // Initialize kernel optimizer
    auto optimizer = std::make_unique<KernelOptimizer>();
    optimizer->initialize();
    
    // Setup test parameters
    OptimizedRenderParams params;
    params.width = 1024;
    params.height = 1024;
    params.samples_per_pixel = 64;
    params.strategy = RTCoreStrategy::ADAPTIVE;
    
    // Camera setup
    params.camera_pos = make_float3(0.0f, 0.0f, 5.0f);
    params.camera_dir = make_float3(0.0f, 0.0f, -1.0f);
    params.camera_up = make_float3(0.0f, 1.0f, 0.0f);
    params.fov = 45.0f;
    
    // Wavefront configuration
    params.wavefront_config.max_path_length = 8;
    params.wavefront_config.wavefront_size = 1024 * 64;
    params.wavefront_config.use_rt_cores = true;
    
    // RT core configuration
    params.rt_config.rt_core_count = props.multiProcessorCount;
    params.rt_config.sm_count = props.multiProcessorCount;
    params.rt_config.max_threads_per_sm = props.maxThreadsPerMultiProcessor;
    
    // Analyze workload
    WorkloadCharacteristics workload;
    workload.analyze(params);
    
    std::cout << "\nWorkload Analysis:" << std::endl;
    std::cout << "  Image size: " << workload.image_width << "x" << workload.image_height << std::endl;
    std::cout << "  Samples per pixel: " << workload.samples_per_pixel << std::endl;
    std::cout << "  Max path length: " << workload.max_path_length << std::endl;
    std::cout << "  Complexity score: " << std::fixed << std::setprecision(2) 
              << workload.getComplexityScore() << std::endl;
    
    // Optimize for workload
    OptimizationParams opt_params = optimizer->optimizeForWorkload(workload, OptimizationStrategy::ADAPTIVE);
    
    std::cout << "\nOptimization Results:" << std::endl;
    std::cout << "  Optimal block size: " << opt_params.optimal_block_size.x 
              << "x" << opt_params.optimal_block_size.y << std::endl;
    std::cout << "  Grid size: " << opt_params.optimal_grid_size.x 
              << "x" << opt_params.optimal_grid_size.y << std::endl;
    std::cout << "  Shared memory per block: " << opt_params.shared_memory_per_block << " bytes" << std::endl;
    std::cout << "  Wavefront size: " << opt_params.wavefront_config.wavefront_size << std::endl;
    std::cout << "  RT cores enabled: " << (opt_params.wavefront_config.use_rt_cores ? "Yes" : "No") << std::endl;
}

/**
 * @brief Demo showing kernel profiling
 */
void demonstrateKernelProfiling() {
    std::cout << "\n=== Kernel Profiling Demo ===" << std::endl;
    
    auto optimizer = std::make_unique<KernelOptimizer>();
    optimizer->initialize();
    
    // Setup test parameters
    OptimizedRenderParams params;
    params.width = 512;
    params.height = 512;
    params.samples_per_pixel = 16;
    params.enable_profiling = true;
    
    // Profile different kernels
    std::vector<std::string> kernels = {
        "generatePrimaryRaysOptimized",
        "intersectRaysRTCore",
        "evaluateMaterialsOptimized",
        "compactRaysOptimized",
        "accumulateResultsOptimized"
    };
    
    std::cout << "\nKernel Profiling Results:" << std::endl;
    std::cout << std::setw(30) << "Kernel Name" 
              << std::setw(15) << "Time (ms)" 
              << std::setw(15) << "Occupancy %" 
              << std::setw(15) << "Efficiency" << std::endl;
    std::cout << std::string(75, '-') << std::endl;
    
    for (const auto& kernel_name : kernels) {
        KernelProfile profile = optimizer->profileKernel(kernel_name, params, 5);
        
        std::cout << std::setw(30) << kernel_name
                  << std::setw(15) << std::fixed << std::setprecision(3) << profile.execution_time_ms
                  << std::setw(15) << std::fixed << std::setprecision(1) << profile.occupancy_percentage
                  << std::setw(15) << std::fixed << std::setprecision(3) << profile.efficiency_score
                  << std::endl;
    }
}

/**
 * @brief Demo showing RT core benchmarking
 */
void demonstrateRTCoreBenchmarking() {
    std::cout << "\n=== RT Core Benchmarking Demo ===" << std::endl;
    
    auto optimizer = std::make_unique<KernelOptimizer>();
    optimizer->initialize();
    
    // Setup test parameters
    OptimizedRenderParams params;
    params.width = 1024;
    params.height = 1024;
    params.samples_per_pixel = 32;
    
    // Benchmark RT cores
    RTCoreCounters counters = optimizer->benchmarkRTCores(params, 1000);
    
    std::cout << "\nRT Core Performance:" << std::endl;
    std::cout << "  Rays processed: " << counters.rays_processed << std::endl;
    std::cout << "  Intersections found: " << counters.intersections_found << std::endl;
    std::cout << "  Cache hits: " << counters.cache_hits << std::endl;
    std::cout << "  Cache misses: " << counters.cache_misses << std::endl;
    std::cout << "  Utilization: " << std::fixed << std::setprecision(1) 
              << counters.utilization_percentage << "%" << std::endl;
    std::cout << "  Throughput: " << std::fixed << std::setprecision(1) 
              << counters.throughput_mrays_per_sec << " Mrays/sec" << std::endl;
    
    if (counters.cache_hits + counters.cache_misses > 0) {
        float hit_ratio = (float)counters.cache_hits / (counters.cache_hits + counters.cache_misses);
        std::cout << "  Cache hit ratio: " << std::fixed << std::setprecision(1) 
                  << (hit_ratio * 100.0f) << "%" << std::endl;
    }
}

/**
 * @brief Demo showing memory access optimization
 */
void demonstrateMemoryOptimization() {
    std::cout << "\n=== Memory Access Optimization Demo ===" << std::endl;
    
    auto optimizer = std::make_unique<KernelOptimizer>();
    optimizer->initialize();
    
    // Setup test parameters
    OptimizedRenderParams params;
    params.width = 1024;
    params.height = 1024;
    params.samples_per_pixel = 16;
    
    // Analyze memory access patterns
    auto analysis = optimizer->analyzeMemoryAccess(params);
    
    std::cout << "\nMemory Access Analysis:" << std::endl;
    std::cout << "  Coalescing efficiency: " << std::fixed << std::setprecision(1) 
              << (analysis.coalescing_efficiency * 100.0f) << "%" << std::endl;
    std::cout << "  Cache hit ratio: " << std::fixed << std::setprecision(1) 
              << (analysis.cache_hit_ratio * 100.0f) << "%" << std::endl;
    std::cout << "  Bandwidth utilization: " << std::fixed << std::setprecision(1) 
              << (analysis.bandwidth_utilization * 100.0f) << "%" << std::endl;
    std::cout << "  Memory transactions: " << analysis.memory_transactions << std::endl;
    
    if (!analysis.optimization_suggestions.empty()) {
        std::cout << "\nOptimization Suggestions:" << std::endl;
        for (size_t i = 0; i < analysis.optimization_suggestions.size(); i++) {
            std::cout << "  " << (i + 1) << ". " << analysis.optimization_suggestions[i] << std::endl;
        }
    }
}

/**
 * @brief Demo showing auto-tuning
 */
void demonstrateAutoTuning() {
    std::cout << "\n=== Auto-Tuning Demo ===" << std::endl;
    
    auto optimizer = std::make_unique<KernelOptimizer>();
    optimizer->initialize();
    
    // Setup test parameters
    OptimizedRenderParams params;
    params.width = 512;
    params.height = 512;
    params.samples_per_pixel = 32;
    
    std::cout << "\nAuto-tuning kernels for optimal performance..." << std::endl;
    
    // Auto-tune different kernels
    std::vector<std::pair<std::string, float>> kernels = {
        {"generatePrimaryRaysOptimized", 1000.0f},
        {"intersectRaysRTCore", 2000.0f},
        {"evaluateMaterialsOptimized", 1500.0f}
    };
    
    for (const auto& kernel_info : kernels) {
        std::cout << "\nTuning " << kernel_info.first << "..." << std::endl;
        
        auto start = std::chrono::high_resolution_clock::now();
        
        OptimizationParams tuned_params = optimizer->autoTuneKernel(
            kernel_info.first, params, kernel_info.second);
        
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
        
        std::cout << "  Tuning time: " << duration.count() << " ms" << std::endl;
        std::cout << "  Optimal block size: " << tuned_params.optimal_block_size.x 
                  << "x" << tuned_params.optimal_block_size.y << std::endl;
        std::cout << "  Shared memory: " << tuned_params.shared_memory_per_block << " bytes" << std::endl;
        std::cout << "  Wavefront size: " << tuned_params.wavefront_config.wavefront_size << std::endl;
    }
}

/**
 * @brief Demo showing adaptive quality scaling
 */
void demonstrateAdaptiveQualityScaling() {
    std::cout << "\n=== Adaptive Quality Scaling Demo ===" << std::endl;
    
    auto optimizer = std::make_unique<KernelOptimizer>();
    optimizer->initialize();
    
    // Setup test parameters
    OptimizedRenderParams params;
    params.width = 1024;
    params.height = 1024;
    params.samples_per_pixel = 64;
    
    std::cout << "\nOriginal settings:" << std::endl;
    std::cout << "  Samples per pixel: " << params.samples_per_pixel << std::endl;
    std::cout << "  Max path length: " << params.wavefront_config.max_path_length << std::endl;
    
    // Simulate different framerate scenarios
    std::vector<std::pair<float, float>> scenarios = {
        {60.0f, 30.0f}, // Target 60 FPS, getting 30 FPS
        {30.0f, 60.0f}, // Target 30 FPS, getting 60 FPS
        {60.0f, 45.0f}, // Target 60 FPS, getting 45 FPS
        {30.0f, 15.0f}  // Target 30 FPS, getting 15 FPS
    };
    
    for (const auto& scenario : scenarios) {
        OptimizedRenderParams test_params = params;
        
        std::cout << "\nScenario: Target " << scenario.first << " FPS, Current " << scenario.second << " FPS" << std::endl;
        
        optimizer->adaptiveQualityScaling(test_params, scenario.first, scenario.second);
        
        std::cout << "  Adjusted SPP: " << test_params.samples_per_pixel << std::endl;
        std::cout << "  Adjusted path length: " << test_params.wavefront_config.max_path_length << std::endl;
        
        float quality_change = (float)test_params.samples_per_pixel / params.samples_per_pixel;
        std::cout << "  Quality scaling: " << std::fixed << std::setprecision(2) << quality_change << "x" << std::endl;
    }
}

/**
 * @brief Demo showing optimization report generation
 */
void demonstrateOptimizationReporting() {
    std::cout << "\n=== Optimization Reporting Demo ===" << std::endl;
    
    auto optimizer = std::make_unique<KernelOptimizer>();
    optimizer->initialize();
    
    // Setup test parameters and run some optimizations
    OptimizedRenderParams params;
    params.width = 1024;
    params.height = 1024;
    params.samples_per_pixel = 32;
    
    WorkloadCharacteristics workload;
    workload.analyze(params);
    
    // Run optimizations to generate data
    optimizer->optimizeForWorkload(workload, OptimizationStrategy::BALANCED);
    optimizer->profileKernel("intersectRaysRTCore", params, 3);
    optimizer->benchmarkRTCores(params, 500);
    
    // Generate and display report
    std::string report = optimizer->generateOptimizationReport();
    std::cout << "\n" << report << std::endl;
}

int main() {
    try {
        std::cout << "=== PhotonRender GPU Kernel Optimization Demo ===" << std::endl;
        
        // Check CUDA availability
        int device_count;
        cudaError_t error = cudaGetDeviceCount(&device_count);
        
        if (error != cudaSuccess || device_count == 0) {
            std::cout << "CUDA not available. GPU optimization demo cannot run." << std::endl;
            return 0;
        }
        
        // Run demos
        demonstrateBasicOptimization();
        demonstrateKernelProfiling();
        demonstrateRTCoreBenchmarking();
        demonstrateMemoryOptimization();
        demonstrateAutoTuning();
        demonstrateAdaptiveQualityScaling();
        demonstrateOptimizationReporting();
        
        std::cout << "\n=== GPU Kernel Optimization Demo Complete ===" << std::endl;
        std::cout << "Advanced GPU optimization system successfully demonstrated!" << std::endl;
        
        // Cleanup
        cudaDeviceReset();
        
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}

#else

int main() {
    std::cout << "CUDA not enabled. GPU kernel optimization demo cannot run." << std::endl;
    std::cout << "Please compile with CUDA support to use GPU optimization features." << std::endl;
    return 0;
}

#endif // CUDA_ENABLED
