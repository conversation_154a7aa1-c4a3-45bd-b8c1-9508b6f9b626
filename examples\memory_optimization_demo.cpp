// examples/memory_optimization_demo.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Advanced Memory Optimization System Demo

#include "../src/core/memory/advanced_memory_manager.hpp"
#include "../src/core/profiling/performance_profiler.hpp"
#include <iostream>
#include <vector>
#include <chrono>
#include <thread>
#include <random>

using namespace photon;

/**
 * @brief Demo showing basic memory management
 */
void demonstrateBasicMemoryManagement() {
    std::cout << "=== Basic Memory Management Demo ===" << std::endl;
    
    auto& manager = AdvancedMemoryManager::getInstance();
    manager.initialize();
    
    // Example 1: Basic allocation and deallocation
    std::cout << "\n--- Basic Allocation ---" << std::endl;
    
    void* ptr1 = manager.allocate(1024, 16, "demo_buffer");
    void* ptr2 = manager.allocate(64 * 1024, 16, "large_buffer");
    void* ptr3 = manager.allocate(2 * 1024 * 1024, 16, "huge_buffer");
    
    std::cout << "Allocated 3 buffers of different sizes" << std::endl;
    
    auto stats = manager.getStatistics();
    std::cout << "Current memory usage: " << (stats.current_usage / 1024 / 1024) << " MB" << std::endl;
    std::cout << "Total allocations: " << stats.allocation_count << std::endl;
    
    // Deallocate
    manager.deallocate(ptr1);
    manager.deallocate(ptr2);
    manager.deallocate(ptr3);
    
    stats = manager.getStatistics();
    std::cout << "After deallocation - Total deallocations: " << stats.deallocation_count << std::endl;
    
    // Example 2: Typed allocation
    std::cout << "\n--- Typed Allocation ---" << std::endl;
    
    int* int_array = manager.allocateTyped<int>(1000, "int_array");
    float* float_array = manager.allocateTyped<float>(500, "float_array");
    
    // Initialize arrays
    for (int i = 0; i < 1000; i++) {
        int_array[i] = i;
    }
    
    for (int i = 0; i < 500; i++) {
        float_array[i] = i * 0.5f;
    }
    
    std::cout << "Allocated and initialized typed arrays" << std::endl;
    std::cout << "int_array[999] = " << int_array[999] << std::endl;
    std::cout << "float_array[499] = " << float_array[499] << std::endl;
    
    manager.deallocateTyped(int_array);
    manager.deallocateTyped(float_array);
}

/**
 * @brief Demo showing RAII memory guards
 */
void demonstrateMemoryGuards() {
    std::cout << "\n=== Memory Guards (RAII) Demo ===" << std::endl;
    
    {
        // Create memory guards that automatically manage memory
        auto int_guard = PHOTON_MEMORY_GUARD(int, 1000);
        auto float_guard = PHOTON_MEMORY_GUARD(float, 500);
        
        std::cout << "Created memory guards for " << int_guard.size() 
                  << " ints and " << float_guard.size() << " floats" << std::endl;
        
        // Use the memory
        for (size_t i = 0; i < int_guard.size(); i++) {
            int_guard[i] = static_cast<int>(i * 2);
        }
        
        for (size_t i = 0; i < float_guard.size(); i++) {
            float_guard[i] = static_cast<float>(i) * 3.14f;
        }
        
        std::cout << "Initialized arrays through memory guards" << std::endl;
        std::cout << "int_guard[999] = " << int_guard[999] << std::endl;
        std::cout << "float_guard[499] = " << float_guard[499] << std::endl;
        
        // Memory will be automatically freed when guards go out of scope
    }
    
    std::cout << "Memory guards automatically freed memory on scope exit" << std::endl;
}

/**
 * @brief Demo showing pool-specific allocation
 */
void demonstratePoolAllocation() {
    std::cout << "\n=== Pool-Specific Allocation Demo ===" << std::endl;
    
    auto& manager = AdvancedMemoryManager::getInstance();
    
    // Allocate from different pools
    void* texture_ptr = manager.allocateFromPool(PoolType::TEXTURE_DATA, 4 * 1024 * 1024, 256);
    void* geometry_ptr = manager.allocateFromPool(PoolType::GEOMETRY_DATA, 1 * 1024 * 1024, 128);
    void* light_ptr = manager.allocateFromPool(PoolType::LIGHT_DATA, 64 * 1024, 64);
    void* temp_ptr = manager.allocateFromPool(PoolType::TEMPORARY, 256 * 1024, 32);
    
    std::cout << "Allocated from specialized pools:" << std::endl;
    std::cout << "  Texture data: " << (texture_ptr ? "4MB" : "Failed") << std::endl;
    std::cout << "  Geometry data: " << (geometry_ptr ? "1MB" : "Failed") << std::endl;
    std::cout << "  Light data: " << (light_ptr ? "64KB" : "Failed") << std::endl;
    std::cout << "  Temporary data: " << (temp_ptr ? "256KB" : "Failed") << std::endl;
    
    // Show pool statistics
    auto pool_stats = manager.getPoolStatistics();
    
    std::cout << "\nPool Statistics:" << std::endl;
    const char* pool_names[] = {
        "Small Objects", "Medium Objects", "Large Objects", "Huge Objects",
        "Texture Data", "Geometry Data", "Light Data", "Temporary"
    };
    
    int i = 0;
    for (const auto& pair : pool_stats) {
        const auto& stats = pair.second;
        if (stats.allocation_count > 0) {
            std::cout << "  " << pool_names[static_cast<int>(pair.first)] << ":" << std::endl;
            std::cout << "    Allocations: " << stats.allocation_count << std::endl;
            std::cout << "    Current Usage: " << (stats.current_usage / 1024) << " KB" << std::endl;
            std::cout << "    Hit Ratio: " << (stats.getHitRatio() * 100.0) << "%" << std::endl;
        }
    }
    
    // Cleanup
    manager.deallocate(texture_ptr);
    manager.deallocate(geometry_ptr);
    manager.deallocate(light_ptr);
    manager.deallocate(temp_ptr);
}

/**
 * @brief Demo showing garbage collection
 */
void demonstrateGarbageCollection() {
    std::cout << "\n=== Garbage Collection Demo ===" << std::endl;
    
    auto& manager = AdvancedMemoryManager::getInstance();
    
    // Create many temporary allocations
    std::vector<void*> temp_ptrs;
    
    std::cout << "Creating many temporary allocations..." << std::endl;
    for (int i = 0; i < 1000; i++) {
        void* ptr = manager.allocate(1024 + (i % 1024), 16, "temp_" + std::to_string(i));
        if (ptr) {
            temp_ptrs.push_back(ptr);
        }
        
        // Deallocate every 3rd allocation to create fragmentation
        if (i % 3 == 0 && !temp_ptrs.empty()) {
            manager.deallocate(temp_ptrs.back());
            temp_ptrs.pop_back();
        }
    }
    
    auto stats_before = manager.getStatistics();
    std::cout << "Before GC - Current usage: " << (stats_before.current_usage / 1024 / 1024) << " MB" << std::endl;
    std::cout << "Before GC - Fragmentation: " << (stats_before.getFragmentation() * 100.0) << "%" << std::endl;
    
    // Run garbage collection
    std::cout << "Running garbage collection..." << std::endl;
    size_t reclaimed = manager.runGarbageCollection();
    
    auto stats_after = manager.getStatistics();
    std::cout << "After GC - Bytes reclaimed: " << (reclaimed / 1024) << " KB" << std::endl;
    std::cout << "After GC - Current usage: " << (stats_after.current_usage / 1024 / 1024) << " MB" << std::endl;
    std::cout << "After GC - Fragmentation: " << (stats_after.getFragmentation() * 100.0) << "%" << std::endl;
    
    // Cleanup remaining allocations
    for (void* ptr : temp_ptrs) {
        manager.deallocate(ptr);
    }
}

/**
 * @brief Demo showing performance comparison
 */
void demonstratePerformanceComparison() {
    std::cout << "\n=== Performance Comparison Demo ===" << std::endl;
    
    auto& manager = AdvancedMemoryManager::getInstance();
    auto& profiler = PerformanceProfiler::getInstance();
    
    const int num_allocations = 10000;
    std::vector<void*> ptrs;
    ptrs.reserve(num_allocations);
    
    // Test 1: Pool-based allocation
    std::cout << "Testing pool-based allocation strategy..." << std::endl;
    manager.setAllocationStrategy(AllocationStrategy::POOL_BASED);
    
    {
        PHOTON_PROFILE_SCOPE("PoolBased_Allocation");
        
        for (int i = 0; i < num_allocations; i++) {
            size_t size = 64 + (i % 1024);
            void* ptr = manager.allocate(size, 16, "pool_test");
            if (ptr) ptrs.push_back(ptr);
        }
        
        for (void* ptr : ptrs) {
            manager.deallocate(ptr);
        }
        ptrs.clear();
    }
    
    // Test 2: Hybrid allocation
    std::cout << "Testing hybrid allocation strategy..." << std::endl;
    manager.setAllocationStrategy(AllocationStrategy::HYBRID);
    
    {
        PHOTON_PROFILE_SCOPE("Hybrid_Allocation");
        
        for (int i = 0; i < num_allocations; i++) {
            size_t size = 64 + (i % 1024);
            void* ptr = manager.allocate(size, 16, "hybrid_test");
            if (ptr) ptrs.push_back(ptr);
        }
        
        for (void* ptr : ptrs) {
            manager.deallocate(ptr);
        }
        ptrs.clear();
    }
    
    // Show performance comparison
    auto pool_metric = profiler.getMetric("PoolBased_Allocation");
    auto hybrid_metric = profiler.getMetric("Hybrid_Allocation");
    
    std::cout << "Performance Results:" << std::endl;
    std::cout << "  Pool-based: " << pool_metric.value << "ms" << std::endl;
    std::cout << "  Hybrid: " << hybrid_metric.value << "ms" << std::endl;
    
    if (pool_metric.value > 0 && hybrid_metric.value > 0) {
        double speedup = pool_metric.value / hybrid_metric.value;
        std::cout << "  Speedup: " << speedup << "x" << std::endl;
    }
}

/**
 * @brief Demo showing thread safety
 */
void demonstrateThreadSafety() {
    std::cout << "\n=== Thread Safety Demo ===" << std::endl;
    
    auto& manager = AdvancedMemoryManager::getInstance();
    
    const int num_threads = 4;
    const int allocations_per_thread = 1000;
    std::vector<std::thread> threads;
    
    std::cout << "Starting " << num_threads << " threads, each doing " 
              << allocations_per_thread << " allocations..." << std::endl;
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    // Launch threads
    for (int t = 0; t < num_threads; t++) {
        threads.emplace_back([&manager, t, allocations_per_thread]() {
            std::vector<void*> thread_ptrs;
            std::mt19937 rng(t); // Thread-specific RNG
            std::uniform_int_distribution<size_t> size_dist(64, 4096);
            
            for (int i = 0; i < allocations_per_thread; i++) {
                size_t size = size_dist(rng);
                void* ptr = manager.allocate(size, 16, "thread_" + std::to_string(t));
                if (ptr) {
                    thread_ptrs.push_back(ptr);
                    
                    // Write some data to test memory integrity
                    memset(ptr, (t + i) % 256, size);
                }
                
                // Occasionally deallocate some memory
                if (i % 10 == 0 && !thread_ptrs.empty()) {
                    manager.deallocate(thread_ptrs.back());
                    thread_ptrs.pop_back();
                }
            }
            
            // Cleanup remaining allocations
            for (void* ptr : thread_ptrs) {
                manager.deallocate(ptr);
            }
        });
    }
    
    // Wait for all threads
    for (auto& thread : threads) {
        thread.join();
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    std::cout << "Thread safety test completed in " << duration.count() << "ms" << std::endl;
    
    auto stats = manager.getStatistics();
    std::cout << "Final statistics:" << std::endl;
    std::cout << "  Total allocations: " << stats.allocation_count << std::endl;
    std::cout << "  Total deallocations: " << stats.deallocation_count << std::endl;
    std::cout << "  Hit ratio: " << (stats.getHitRatio() * 100.0) << "%" << std::endl;
}

/**
 * @brief Demo showing memory reporting
 */
void demonstrateMemoryReporting() {
    std::cout << "\n=== Memory Reporting Demo ===" << std::endl;
    
    auto& manager = AdvancedMemoryManager::getInstance();
    
    // Create some allocations for reporting
    std::vector<void*> ptrs;
    ptrs.push_back(manager.allocate(1024, 16, "small_buffer"));
    ptrs.push_back(manager.allocate(64 * 1024, 16, "medium_buffer"));
    ptrs.push_back(manager.allocate(1024 * 1024, 16, "large_buffer"));
    ptrs.push_back(manager.allocateFromPool(PoolType::TEXTURE_DATA, 2 * 1024 * 1024, 256));
    
    // Generate and display memory report
    std::string report = manager.generateMemoryReport();
    std::cout << report << std::endl;
    
    // Cleanup
    for (void* ptr : ptrs) {
        manager.deallocate(ptr);
    }
}

int main() {
    try {
        // Initialize PhotonRender
        photon::initialize();
        
        // Run memory optimization demos
        demonstrateBasicMemoryManagement();
        demonstrateMemoryGuards();
        demonstratePoolAllocation();
        demonstrateGarbageCollection();
        demonstratePerformanceComparison();
        demonstrateThreadSafety();
        demonstrateMemoryReporting();
        
        std::cout << "\n=== Memory Optimization Demo Complete ===" << std::endl;
        std::cout << "Advanced memory management system successfully demonstrated!" << std::endl;
        
        // Cleanup
        AdvancedMemoryManager::getInstance().shutdown();
        photon::cleanup();
        
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
