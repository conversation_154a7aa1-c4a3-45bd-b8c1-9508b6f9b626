// examples/quality_assurance_demo.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Quality Assurance System Demo

#include "../src/qa/quality_assurance_system.hpp"
#include <iostream>
#include <iomanip>

using namespace photon::qa;

/**
 * @brief Demo showing basic QA system usage
 */
void demonstrateBasicQA() {
    std::cout << "=== Basic Quality Assurance Demo ===" << std::endl;
    
    // Initialize QA system
    QualityAssuranceSystem qa_system;
    
    TestSuiteConfig config;
    config.run_stress_tests = false; // Skip stress tests for demo
    config.generate_detailed_reports = true;
    config.output_directory = "qa_demo_results";
    
    qa_system.initialize(config);
    
    // Register custom tests
    qa_system.registerTest(
        "demo_math_test",
        "Demonstrate math operations testing",
        TestCategory::UNIT,
        TestSeverity::HIGH,
        [](TestContext& context) -> bool {
            context.output_stream << "Testing basic math operations...\n";
            
            // Test addition
            int result = 2 + 3;
            if (result != 5) {
                context.output_stream << "Addition test failed: 2 + 3 = " << result << "\n";
                return false;
            }
            
            // Test multiplication
            result = 4 * 6;
            if (result != 24) {
                context.output_stream << "Multiplication test failed: 4 * 6 = " << result << "\n";
                return false;
            }
            
            context.output_stream << "Math operations test passed\n";
            return true;
        }
    );
    
    qa_system.registerTest(
        "demo_performance_test",
        "Demonstrate performance testing",
        TestCategory::PERFORMANCE,
        TestSeverity::MEDIUM,
        [](TestContext& context) -> bool {
            context.output_stream << "Running performance test...\n";
            context.startProfiling();
            
            // Simulate some computational work
            volatile double sum = 0.0;
            for (int i = 0; i < 1000000; i++) {
                sum += std::sin(i * 0.001);
            }
            
            context.stopProfiling();
            context.output_stream << "Performance test completed, sum = " << sum << "\n";
            return true;
        }
    );
    
    qa_system.registerTest(
        "demo_memory_test",
        "Demonstrate memory usage testing",
        TestCategory::MEMORY,
        TestSeverity::HIGH,
        [](TestContext& context) -> bool {
            context.output_stream << "Testing memory allocation...\n";
            
            // Allocate and use memory
            std::vector<int> large_vector(100000, 42);
            
            // Verify memory content
            bool all_correct = true;
            for (size_t i = 0; i < large_vector.size(); i++) {
                if (large_vector[i] != 42) {
                    all_correct = false;
                    break;
                }
            }
            
            context.output_stream << "Memory test " << (all_correct ? "passed" : "failed") << "\n";
            return all_correct;
        }
    );
    
    // Run all tests
    std::cout << "\nRunning QA tests...\n";
    bool success = qa_system.runAllTests();
    
    // Display summary
    auto summary = qa_system.getTestSummary();
    std::cout << "\n=== Test Summary ===" << std::endl;
    std::cout << "Total tests: " << summary.total_tests << std::endl;
    std::cout << "Passed: " << summary.passed_tests << std::endl;
    std::cout << "Failed: " << summary.failed_tests << std::endl;
    std::cout << "Overall score: " << std::fixed << std::setprecision(1) 
              << (summary.overall_score * 100.0) << "%" << std::endl;
    std::cout << "All critical passed: " << (summary.all_critical_passed ? "YES" : "NO") << std::endl;
    
    // Generate reports
    qa_system.generateReports();
    std::cout << "\nReports generated in: " << config.output_directory << std::endl;
    
    qa_system.shutdown();
}

/**
 * @brief Demo showing regression testing
 */
void demonstrateRegressionTesting() {
    std::cout << "\n=== Regression Testing Demo ===" << std::endl;
    
    QualityAssuranceSystem qa_system;
    
    TestSuiteConfig config;
    config.run_unit_tests = false;
    config.run_integration_tests = false;
    config.run_performance_tests = false;
    config.run_regression_tests = true;
    config.run_memory_tests = false;
    config.run_gpu_tests = false;
    config.run_stress_tests = false;
    config.run_validation_tests = false;
    config.run_compatibility_tests = false;
    config.output_directory = "regression_demo_results";
    
    qa_system.initialize(config);
    
    // Set up regression baselines
    std::vector<RegressionBaseline> baselines;
    
    RegressionBaseline perf_baseline;
    perf_baseline.test_name = "performance_regression_test";
    perf_baseline.baseline_performance = 1000.0; // 1000 operations/sec
    perf_baseline.performance_tolerance = 0.1;   // 10% tolerance
    baselines.push_back(perf_baseline);
    
    RegressionBaseline memory_baseline;
    memory_baseline.test_name = "memory_regression_test";
    memory_baseline.baseline_memory_usage = 1024 * 1024; // 1MB
    memory_baseline.memory_tolerance = 0.2;              // 20% tolerance
    baselines.push_back(memory_baseline);
    
    qa_system.setRegressionBaselines(baselines);
    
    // Register regression tests
    qa_system.registerTest(
        "performance_regression_test",
        "Test for performance regression",
        TestCategory::REGRESSION,
        TestSeverity::CRITICAL,
        [](TestContext& context) -> bool {
            context.output_stream << "Running performance regression test...\n";
            
            auto start = std::chrono::high_resolution_clock::now();
            
            // Simulate work
            volatile int sum = 0;
            for (int i = 0; i < 1000; i++) {
                sum += i * i;
            }
            
            auto end = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
            
            double ops_per_sec = 1000.0 / (duration.count() / 1000000.0);
            context.output_stream << "Performance: " << ops_per_sec << " ops/sec\n";
            
            return true;
        }
    );
    
    // Run regression tests
    bool success = qa_system.runRegressionTests();
    
    std::cout << "Regression testing " << (success ? "passed" : "failed") << std::endl;
    
    qa_system.shutdown();
}

/**
 * @brief Demo showing custom validation
 */
void demonstrateCustomValidation() {
    std::cout << "\n=== Custom Validation Demo ===" << std::endl;
    
    QualityAssuranceSystem qa_system;
    qa_system.initialize();
    
    // Add custom validator for output format
    qa_system.addCustomValidator("output_format_test", [](const std::string& output) -> bool {
        // Validate that output contains expected format markers
        return output.find("FORMAT_START") != std::string::npos &&
               output.find("FORMAT_END") != std::string::npos &&
               output.find("DATA:") != std::string::npos;
    });
    
    // Register test with custom validation
    qa_system.registerTest(
        "output_format_test",
        "Test output format validation",
        TestCategory::VALIDATION,
        TestSeverity::HIGH,
        [](TestContext& context) -> bool {
            context.output_stream << "FORMAT_START\n";
            context.output_stream << "DATA: Test data content\n";
            context.output_stream << "Additional information\n";
            context.output_stream << "FORMAT_END\n";
            return true;
        }
    );
    
    // Register test that fails validation
    qa_system.registerTest(
        "invalid_format_test",
        "Test that fails format validation",
        TestCategory::VALIDATION,
        TestSeverity::MEDIUM,
        [](TestContext& context) -> bool {
            context.output_stream << "This output doesn't follow the expected format\n";
            return true; // Test logic passes, but validation should fail
        }
    );
    
    // Add validator for the failing test
    qa_system.addCustomValidator("invalid_format_test", [](const std::string& output) -> bool {
        return output.find("FORMAT_START") != std::string::npos;
    });
    
    // Run validation tests
    bool success = qa_system.runTestsByCategory(TestCategory::VALIDATION);
    
    auto results = qa_system.getTestResults();
    for (const auto& result : results) {
        if (result.category == TestCategory::VALIDATION) {
            std::cout << "Test: " << result.test_name 
                      << " - Status: " << result.getStatusString()
                      << " - Valid: " << (result.output_valid ? "YES" : "NO") << std::endl;
        }
    }
    
    qa_system.shutdown();
}

/**
 * @brief Demo showing automated test runner
 */
void demonstrateAutomatedRunner() {
    std::cout << "\n=== Automated Test Runner Demo ===" << std::endl;
    
    // Simulate command line arguments
    const char* argv[] = {
        "qa_demo",
        "--no-stress",
        "--parallel", "2",
        "--output", "automated_results",
        "--timeout", "60"
    };
    int argc = sizeof(argv) / sizeof(argv[0]);
    
    std::cout << "Simulating automated test runner with arguments:" << std::endl;
    for (int i = 0; i < argc; i++) {
        std::cout << "  " << argv[i] << std::endl;
    }
    
    // Parse configuration
    TestSuiteConfig config = AutomatedTestRunner::parseCommandLineArgs(argc, const_cast<char**>(argv));
    
    std::cout << "\nParsed configuration:" << std::endl;
    std::cout << "  Stress tests: " << (config.run_stress_tests ? "enabled" : "disabled") << std::endl;
    std::cout << "  Parallel tests: " << config.max_parallel_tests << std::endl;
    std::cout << "  Output directory: " << config.output_directory << std::endl;
    std::cout << "  Timeout: " << config.test_timeout.count() << " seconds" << std::endl;
    
    // Run with parsed configuration
    QualityAssuranceSystem qa_system;
    qa_system.initialize(config);
    
    bool success = qa_system.runAllTests();
    auto summary = qa_system.getTestSummary();
    
    int exit_code = AutomatedTestRunner::generateExitCode(summary);
    std::cout << "\nAutomated runner would exit with code: " << exit_code << std::endl;
    
    qa_system.shutdown();
}

/**
 * @brief Demo showing performance validation
 */
void demonstratePerformanceValidation() {
    std::cout << "\n=== Performance Validation Demo ===" << std::endl;
    
    QualityAssuranceSystem qa_system;
    
    TestSuiteConfig config;
    config.min_performance_score = 0.8; // Require 80% performance score
    config.max_memory_usage_mb = 100;   // Limit to 100MB
    config.output_directory = "performance_demo_results";
    
    qa_system.initialize(config);
    
    // Register performance-sensitive test
    qa_system.registerTest(
        "fast_algorithm_test",
        "Test fast algorithm performance",
        TestCategory::PERFORMANCE,
        TestSeverity::HIGH,
        [](TestContext& context) -> bool {
            context.output_stream << "Running fast algorithm...\n";
            context.startProfiling();
            
            // Efficient algorithm
            std::vector<int> data(10000);
            std::iota(data.begin(), data.end(), 1);
            
            auto start = std::chrono::high_resolution_clock::now();
            
            // Fast operation
            volatile long long sum = 0;
            for (int val : data) {
                sum += val;
            }
            
            auto end = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
            
            context.stopProfiling();
            context.output_stream << "Algorithm completed in " << duration.count() << " μs\n";
            context.output_stream << "Sum: " << sum << "\n";
            
            return true;
        }
    );
    
    // Register memory-intensive test
    qa_system.registerTest(
        "memory_efficient_test",
        "Test memory-efficient algorithm",
        TestCategory::MEMORY,
        TestSeverity::HIGH,
        [](TestContext& context) -> bool {
            context.output_stream << "Running memory-efficient algorithm...\n";
            
            // Use memory efficiently
            const int chunk_size = 1000;
            for (int chunk = 0; chunk < 10; chunk++) {
                std::vector<int> small_chunk(chunk_size, chunk);
                
                // Process chunk
                volatile int sum = 0;
                for (int val : small_chunk) {
                    sum += val;
                }
                
                // Chunk goes out of scope, memory is freed
            }
            
            context.output_stream << "Memory-efficient algorithm completed\n";
            return true;
        }
    );
    
    // Run performance validation
    bool success = qa_system.runPerformanceValidation();
    
    std::cout << "Performance validation " << (success ? "passed" : "failed") << std::endl;
    
    // Show detailed results
    auto results = qa_system.getTestResults();
    for (const auto& result : results) {
        std::cout << "Test: " << result.test_name << std::endl;
        std::cout << "  Performance score: " << std::fixed << std::setprecision(3) 
                  << result.performance_score << std::endl;
        std::cout << "  Memory usage: " << (result.memory_usage_bytes / 1024) << " KB" << std::endl;
        std::cout << "  Execution time: " << result.execution_time.count() << " ms" << std::endl;
    }
    
    qa_system.shutdown();
}

int main() {
    try {
        std::cout << "=== PhotonRender Quality Assurance System Demo ===" << std::endl;
        
        // Run different demo scenarios
        demonstrateBasicQA();
        demonstrateRegressionTesting();
        demonstrateCustomValidation();
        demonstrateAutomatedRunner();
        demonstratePerformanceValidation();
        
        std::cout << "\n=== Quality Assurance Demo Complete ===" << std::endl;
        std::cout << "Advanced QA system successfully demonstrated!" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
