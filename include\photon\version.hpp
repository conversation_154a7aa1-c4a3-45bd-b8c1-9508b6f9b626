// include/photon/version.hpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Version information and build details

#pragma once

#include <string>

namespace photon {

/**
 * @brief Version information structure
 */
struct Version {
    int major;        ///< Major version number
    int minor;        ///< Minor version number  
    int patch;        ///< Patch version number
    std::string pre;  ///< Pre-release identifier (e.g., "alpha", "beta")
    std::string build;///< Build metadata
    
    Version(int maj = 0, int min = 1, int pat = 0, 
            const std::string& prerelease = "", 
            const std::string& buildMeta = "")
        : major(maj), minor(min), patch(pat), pre(prerelease), build(buildMeta) {}
    
    /**
     * @brief Get version as string
     * @return Version string in format "major.minor.patch[-pre][+build]"
     */
    std::string toString() const {
        std::string result = std::to_string(major) + "." + 
                           std::to_string(minor) + "." + 
                           std::to_string(patch);
        
        if (!pre.empty()) {
            result += "-" + pre;
        }
        
        if (!build.empty()) {
            result += "+" + build;
        }
        
        return result;
    }
    
    /**
     * @brief Compare versions
     */
    bool operator==(const Version& other) const {
        return major == other.major && minor == other.minor && 
               patch == other.patch && pre == other.pre;
    }
    
    bool operator!=(const Version& other) const {
        return !(*this == other);
    }
    
    bool operator<(const Version& other) const {
        if (major != other.major) return major < other.major;
        if (minor != other.minor) return minor < other.minor;
        if (patch != other.patch) return patch < other.patch;
        
        // Pre-release versions are less than normal versions
        if (pre.empty() && !other.pre.empty()) return false;
        if (!pre.empty() && other.pre.empty()) return true;
        
        return pre < other.pre;
    }
    
    bool operator<=(const Version& other) const {
        return *this < other || *this == other;
    }
    
    bool operator>(const Version& other) const {
        return !(*this <= other);
    }
    
    bool operator>=(const Version& other) const {
        return !(*this < other);
    }
};

/**
 * @brief Build information structure
 */
struct BuildInfo {
    std::string compiler;      ///< Compiler name and version
    std::string buildType;     ///< Debug/Release/RelWithDebInfo
    std::string buildDate;     ///< Build date
    std::string buildTime;     ///< Build time
    std::string gitHash;       ///< Git commit hash
    std::string gitBranch;     ///< Git branch name
    bool gitDirty;            ///< Git working directory dirty
    std::string platform;      ///< Target platform
    std::string architecture;  ///< Target architecture
    
    /**
     * @brief Get build info as formatted string
     */
    std::string toString() const {
        std::string result;
        result += "Compiler: " + compiler + "\n";
        result += "Build Type: " + buildType + "\n";
        result += "Build Date: " + buildDate + " " + buildTime + "\n";
        result += "Platform: " + platform + " (" + architecture + ")\n";
        result += "Git: " + gitHash;
        if (!gitBranch.empty()) {
            result += " (" + gitBranch + ")";
        }
        if (gitDirty) {
            result += " [dirty]";
        }
        result += "\n";
        return result;
    }
};

// Current version constants
constexpr int VERSION_MAJOR = 0;
constexpr int VERSION_MINOR = 1;
constexpr int VERSION_PATCH = 0;

// Version functions
Version getCurrentVersion();
BuildInfo getBuildInfo();

} // namespace photon
