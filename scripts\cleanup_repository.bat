@echo off
REM scripts/cleanup_repository.bat
REM PhotonRender Repository Cleanup Script
REM Removes temporary files, history files, and optimizes repository

echo ============================================================================
echo PhotonRender Repository Cleanup Script
echo ============================================================================
echo.

REM Change to project root
cd /d "%~dp0\.."

echo [1/6] Removing VS Code Local History files...
if exist ".history" (
    rmdir /s /q ".history"
    echo   - Removed .history directory
) else (
    echo   - No .history directory found
)

echo.
echo [2/6] Removing backup and temporary files...
del /q /s *.backup 2>nul
del /q /s backup*.txt 2>nul
del /q /s backup*.md 2>nul
del /q /s *_backup.* 2>nul
del /q /s *_temp.* 2>nul
del /q /s *_tmp.* 2>nul
del /q /s *_draft.* 2>nul
del /q /s *_copy.* 2>nul
del /q /s *_old.* 2>nul
echo   - Removed backup and temporary files

echo.
echo [3/6] Removing test output directories...
if exist "qa_results" rmdir /s /q "qa_results"
if exist "qa_demo_results" rmdir /s /q "qa_demo_results"
if exist "regression_demo_results" rmdir /s /q "regression_demo_results"
if exist "performance_demo_results" rmdir /s /q "performance_demo_results"
if exist "automated_results" rmdir /s /q "automated_results"
if exist "test_qa_output" rmdir /s /q "test_qa_output"
if exist "custom_output" rmdir /s /q "custom_output"
echo   - Removed test output directories

echo.
echo [4/6] Removing build artifacts...
del /q /s *.pdb 2>nul
del /q /s *.ilk 2>nul
del /q /s *.exp 2>nul
del /q /s *.idb 2>nul
del /q /s *.ipdb 2>nul
del /q /s *.tmp 2>nul
del /q /s *.temp 2>nul
echo   - Removed build artifacts

echo.
echo [5/6] Removing temporary development files...
del /q /s PhotonRender_*_Fix__*.md 2>nul
del /q /s *_temp_*.md 2>nul
del /q /s temp_*.txt 2>nul
if exist "backup.txt" del /q "backup.txt"
echo   - Removed temporary development files

echo.
echo [6/6] Git status cleanup...
git add .gitignore
echo   - Updated .gitignore

echo.
echo ============================================================================
echo Repository Cleanup Complete!
echo ============================================================================
echo.
echo Summary of actions:
echo   - Removed VS Code Local History files (.history/)
echo   - Removed backup and temporary files (*_backup.*, *_temp.*, etc.)
echo   - Removed test output directories
echo   - Removed build artifacts (*.pdb, *.ilk, etc.)
echo   - Removed temporary development files
echo   - Updated .gitignore with optimization rules
echo.
echo The repository is now optimized and clean!
echo.
echo Next steps:
echo   1. Review git status: git status
echo   2. Add important files: git add [files]
echo   3. Commit changes: git commit -m "Optimize repository structure"
echo.
pause
