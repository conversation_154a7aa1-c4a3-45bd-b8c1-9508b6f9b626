#!/bin/bash
# setup_dev.sh - Development environment setup script

set -e  # Exit on error

echo "🚀 PhotonRender Development Setup"
echo "================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Detect OS
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    OS="linux"
elif [[ "$OSTYPE" == "darwin"* ]]; then
    OS="macos"
elif [[ "$OSTYPE" == "msys" || "$OSTYPE" == "cygwin" ]]; then
    OS="windows"
else
    echo -e "${RED}Unsupported OS: $OSTYPE${NC}"
    exit 1
fi

echo "Detected OS: $OS"

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
echo -e "\n${YELLOW}Checking prerequisites...${NC}"

# Check for Git
if ! command_exists git; then
    echo -e "${RED}Git not found! Please install Git first.${NC}"
    exit 1
fi
echo -e "${GREEN}✓ Git found${NC}"

# Check for CMake
if ! command_exists cmake; then
    echo -e "${RED}CMake not found! Please install CMake 3.20+${NC}"
    exit 1
fi
echo -e "${GREEN}✓ CMake found${NC}"

# Check for C++ compiler
if command_exists g++; then
    echo -e "${GREEN}✓ g++ found${NC}"
elif command_exists clang++; then
    echo -e "${GREEN}✓ clang++ found${NC}"
elif command_exists cl; then
    echo -e "${GREEN}✓ MSVC found${NC}"
else
    echo -e "${RED}No C++ compiler found!${NC}"
    exit 1
fi

# Check for Ruby (for SketchUp)
if ! command_exists ruby; then
    echo -e "${YELLOW}⚠ Ruby not found (needed for SketchUp plugin)${NC}"
fi

# Check for CUDA (optional)
if command_exists nvcc; then
    echo -e "${GREEN}✓ CUDA found${NC}"
    USE_CUDA=ON
else
    echo -e "${YELLOW}⚠ CUDA not found (GPU acceleration disabled)${NC}"
    USE_CUDA=OFF
fi

# Create directory structure
echo -e "\n${YELLOW}Creating project structure...${NC}"

directories=(
    "build"
    "third_party"
    "assets/hdri"
    "assets/textures"
    "assets/models"
    "docs/api"
    "docs/guides"
    "tests/scenes"
)

for dir in "${directories[@]}"; do
    mkdir -p "$dir"
    echo "Created: $dir"
done

# Download sample assets
echo -e "\n${YELLOW}Downloading sample assets...${NC}"

# Cornell Box scene
cat > tests/scenes/cornell_box.json << 'EOF'
{
    "camera": {
        "position": [0, 1, 3],
        "target": [0, 1, 0],
        "fov": 45,
        "resolution": [512, 512]
    },
    "materials": {
        "white": {"type": "diffuse", "albedo": [0.8, 0.8, 0.8]},
        "red": {"type": "diffuse", "albedo": [0.8, 0.1, 0.1]},
        "green": {"type": "diffuse", "albedo": [0.1, 0.8, 0.1]},
        "light": {"type": "emissive", "emission": [10, 10, 10]}
    },
    "objects": [
        {
            "type": "quad",
            "material": "white",
            "transform": {
                "position": [0, 0, -1],
                "scale": [2, 2, 1]
            }
        },
        {
            "type": "quad",
            "material": "white",
            "transform": {
                "position": [0, 2, 0],
                "rotation": [90, 0, 0],
                "scale": [2, 2, 1]
            }
        },
        {
            "type": "quad",
            "material": "white",
            "transform": {
                "position": [0, 0, 0],
                "rotation": [-90, 0, 0],
                "scale": [2, 2, 1]
            }
        },
        {
            "type": "quad",
            "material": "red",
            "transform": {
                "position": [-1, 1, 0],
                "rotation": [0, 90, 0],
                "scale": [2, 2, 1]
            }
        },
        {
            "type": "quad",
            "material": "green",
            "transform": {
                "position": [1, 1, 0],
                "rotation": [0, -90, 0],
                "scale": [2, 2, 1]
            }
        },
        {
            "type": "quad",
            "material": "light",
            "transform": {
                "position": [0, 1.99, 0],
                "rotation": [90, 0, 0],
                "scale": [0.5, 0.5, 1]
            }
        }
    ]
}
EOF

echo "Created Cornell Box scene"

# Setup Git hooks
echo -e "\n${YELLOW}Setting up Git hooks...${NC}"

cat > .git/hooks/pre-commit << 'EOF'
#!/bin/bash
# Run clang-format on changed files
for file in $(git diff --cached --name-only | grep -E '\.(cpp|hpp|cu|h)$'); do
    clang-format -i "$file"
    git add "$file"
done
EOF
chmod +x .git/hooks/pre-commit

# Configure build
echo -e "\n${YELLOW}Configuring build...${NC}"

cd build
cmake .. \
    -DCMAKE_BUILD_TYPE=Debug \
    -DBUILD_TESTS=ON \
    -DBUILD_BENCHMARKS=ON \
    -DUSE_CUDA=$USE_CUDA

# VS Code setup
echo -e "\n${YELLOW}Setting up VS Code...${NC}"

# Install VS Code extensions
if command_exists code; then
    extensions=(
        "ms-vscode.cpptools"
        "ms-vscode.cmake-tools"
        "twxs.cmake"
        "rebornix.ruby"
        "gruntfuggly.todo-tree"
        "aaron-bond.better-comments"
    )
    
    for ext in "${extensions[@]}"; do
        code --install-extension "$ext" || true
    done
    echo -e "${GREEN}✓ VS Code extensions installed${NC}"
else
    echo -e "${YELLOW}⚠ VS Code not found${NC}"
fi

# Create initial README
cat > ../README.md << 'EOF'
# PhotonRender - Professional Rendering Engine for SketchUp

A high-performance, physically-based rendering engine for SketchUp with CPU/GPU hybrid rendering capabilities.

## Features

- 🎯 Path tracing with multiple importance sampling
- 🎨 Disney BRDF material model
- 🚀 GPU acceleration with CUDA/OptiX
- 🖼️ AI-powered denoising
- 🔧 Easy SketchUp integration

## Quick Start

```bash
# Build the project
cd build
cmake --build . --config Release

# Run tests
ctest

# Install SketchUp plugin
./scripts/install_plugin.sh
```

## Development

See [docs/development/getting_started.md](docs/development/getting_started.md) for detailed instructions.

## License

Apache 2.0 - See LICENSE file
EOF

echo -e "\n${GREEN}✅ Setup complete!${NC}"
echo -e "\nNext steps:"
echo "1. cd build && make -j$(nproc)"
echo "2. ./bin/photon_render --scene=../tests/scenes/cornell_box.json"
echo "3. code .. (to open in VS Code)"
echo -e "\nHappy coding! 🚀"