@echo off
REM scripts/smart_commit.bat
REM PhotonRender Smart Commit Script
REM Automatically organizes and commits only essential files

echo ============================================================================
echo PhotonRender Smart Commit Script
echo ============================================================================
echo.

REM Change to project root
cd /d "%~dp0\.."

echo [1/5] Running repository cleanup...
call scripts\cleanup_repository.bat

echo.
echo [2/5] Analyzing repository status...
git status --porcelain > temp_status.txt

echo.
echo [3/5] Categorizing files for commit...

REM Core source files
echo   - Adding core source files...
git add src/ 2>nul
git add include/ 2>nul
git add tests/ 2>nul
git add examples/ 2>nul

REM Build and configuration files
echo   - Adding build configuration...
git add CMakeLists.txt 2>nul
git add cmake/ 2>nul
git add .gitignore 2>nul

REM Essential documentation (exclude drafts and temps)
echo   - Adding essential documentation...
git add README.md 2>nul
git add docs/app_map.md 2>nul
git add docs/technical-guide.md 2>nul
git add docs/phase3-3-completion-report.md 2>nul
git add docs/oidn-installation-guide.md 2>nul
git add docs/documentation-index.md 2>nul
git add docs/task3-3-*-completion-report.md 2>nul

REM Scripts and utilities
echo   - Adding scripts...
git add scripts/ 2>nul

REM Presentation
echo   - Adding presentation...
git add presentation.html 2>nul

REM External dependencies (only OIDN - others are auto-downloaded)
echo   - Adding external dependencies...
git add external/oidn/ 2>nul

echo.
echo [4/5] Excluding unwanted files...
REM Remove any accidentally added files
git reset HEAD .history/ 2>nul
git reset HEAD **/.history/ 2>nul
git reset HEAD *_backup.* 2>nul
git reset HEAD *_temp.* 2>nul
git reset HEAD *_draft.* 2>nul
git reset HEAD qa_results/ 2>nul
git reset HEAD test_output/ 2>nul
git reset HEAD build/ 2>nul

echo.
echo [5/5] Preparing commit...

REM Clean up temp file
if exist temp_status.txt del temp_status.txt

echo.
echo ============================================================================
echo Smart Commit Preparation Complete!
echo ============================================================================
echo.
echo Files staged for commit:
git status --short
echo.
echo Commit message suggestions:
echo   1. "Phase 3.3 AI & Optimization - Complete implementation"
echo   2. "Add Intel OIDN 2.1.0 integration and GPU optimization"
echo   3. "Implement adaptive sampling and quality assurance system"
echo   4. "Update documentation for Phase 3.3 completion"
echo   5. "Repository optimization and cleanup"
echo.
set /p commit_msg="Enter commit message (or press Enter for default): "

if "%commit_msg%"=="" (
    set commit_msg=Phase 3.3 AI & Optimization - Complete implementation with Intel OIDN 2.1.0, adaptive sampling, GPU optimization, and QA automation
)

echo.
echo Committing with message: "%commit_msg%"
git commit -m "%commit_msg%"

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ============================================================================
    echo Commit successful!
    echo ============================================================================
    echo.
    echo Repository is now clean and optimized.
    echo All essential files have been committed.
    echo.
    echo Next steps:
    echo   1. Push to remote: git push origin main
    echo   2. Create release tag: git tag v3.3.6-alpha
    echo   3. Push tags: git push --tags
) else (
    echo.
    echo ============================================================================
    echo Commit failed or no changes to commit.
    echo ============================================================================
    echo.
    echo Please review the status and try again.
)

echo.
pause
