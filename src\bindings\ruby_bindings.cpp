// src/bindings/ruby_bindings.cpp
// PhotonRender - Ruby-C++ Bindings for SketchUp Integration
// Bridge per comunicazione SketchUp ↔ PhotonRender

#include <ruby.h>
#include <iostream>
#include <memory>
#include <vector>

// PhotonRender headers
#include "../../include/photon/photon.hpp"
#include "../core/renderer.hpp"
#include "../core/scene/scene.hpp"
#include "../core/scene/camera.hpp"
#include "../core/integrator/integrator.hpp"

// Namespace per evitare conflitti
namespace photon_ruby {

// Forward declarations
static VALUE rb_mPhotonCore;
static VALUE rb_cRender;
static VALUE rb_cSceneData;

// Wrapper per Renderer C++
struct RendererWrapper {
    std::shared_ptr<photon::Renderer> renderer;
    std::shared_ptr<photon::Scene> scene;
    std::shared_ptr<photon::Camera> camera;
    
    RendererWrapper() {
        renderer = std::make_shared<photon::Renderer>();
        scene = std::make_shared<photon::Scene>();
        camera = std::make_shared<photon::Camera>();
    }
};

// Cleanup function per Ruby GC
static void renderer_free(void* ptr) {
    if (ptr) {
        delete static_cast<RendererWrapper*>(ptr);
    }
}

// Allocator per Ruby objects
static VALUE renderer_alloc(VALUE klass) {
    RendererWrapper* wrapper = new RendererWrapper();
    return Data_Wrap_Struct(klass, nullptr, renderer_free, wrapper);
}

// PhotonCore.initialize
static VALUE photon_core_initialize(VALUE self) {
    bool success = photon::initialize();
    return success ? Qtrue : Qfalse;
}

// PhotonCore.shutdown
static VALUE photon_core_shutdown(VALUE self) {
    photon::shutdown();
    return Qnil;
}

// PhotonCore.version
static VALUE photon_core_version(VALUE self) {
    photon::Version version = photon::getVersion();
    return rb_sprintf("%d.%d.%d", version.major, version.minor, version.patch);
}

// PhotonCore.has_cuda_device?
static VALUE photon_core_has_cuda(VALUE self) {
    // TODO: Implementare detection CUDA
    return Qfalse;
}

// PhotonCore.has_optix_device?
static VALUE photon_core_has_optix(VALUE self) {
    // TODO: Implementare detection OptiX
    return Qtrue; // Sappiamo che OptiX è disponibile
}

// PhotonCore.get_capabilities
static VALUE photon_core_capabilities(VALUE self) {
    photon::Capabilities caps = photon::getCapabilities();
    
    VALUE hash = rb_hash_new();
    rb_hash_aset(hash, rb_str_new_cstr("embree"), caps.hasEmbree ? Qtrue : Qfalse);
    rb_hash_aset(hash, rb_str_new_cstr("cuda"), caps.hasCUDA ? Qtrue : Qfalse);
    rb_hash_aset(hash, rb_str_new_cstr("optix"), caps.hasOptiX ? Qtrue : Qfalse);
    rb_hash_aset(hash, rb_str_new_cstr("openmp"), caps.hasOpenMP ? Qtrue : Qfalse);
    rb_hash_aset(hash, rb_str_new_cstr("max_threads"), INT2NUM(caps.maxThreads));
    rb_hash_aset(hash, rb_str_new_cstr("gpu_count"), INT2NUM(caps.gpuCount));
    
    return hash;
}

// Render.new
static VALUE render_initialize(VALUE self) {
    // Wrapper già allocato in renderer_alloc
    return self;
}

// Render#set_scene(scene_data)
static VALUE render_set_scene(VALUE self, VALUE scene_data) {
    RendererWrapper* wrapper;
    Data_Get_Struct(self, RendererWrapper, wrapper);
    
    // TODO: Convertire scene_data Ruby in Scene C++
    // Per ora creiamo una scena di test
    wrapper->renderer->setScene(wrapper->scene);
    
    return Qnil;
}

// Render#set_settings(settings_hash)
static VALUE render_set_settings(VALUE self, VALUE settings) {
    RendererWrapper* wrapper;
    Data_Get_Struct(self, RendererWrapper, wrapper);
    
    photon::RenderSettings renderSettings;
    
    // Estrai settings da hash Ruby
    if (TYPE(settings) == T_HASH) {
        VALUE width = rb_hash_aref(settings, rb_str_new_cstr("width"));
        VALUE height = rb_hash_aref(settings, rb_str_new_cstr("height"));
        VALUE samples = rb_hash_aref(settings, rb_str_new_cstr("samples_per_pixel"));
        VALUE bounces = rb_hash_aref(settings, rb_str_new_cstr("max_bounces"));
        VALUE use_gpu = rb_hash_aref(settings, rb_str_new_cstr("use_gpu"));
        
        if (!NIL_P(width)) renderSettings.width = NUM2INT(width);
        if (!NIL_P(height)) renderSettings.height = NUM2INT(height);
        if (!NIL_P(samples)) renderSettings.samplesPerPixel = NUM2INT(samples);
        if (!NIL_P(bounces)) renderSettings.maxBounces = NUM2INT(bounces);
        if (!NIL_P(use_gpu)) renderSettings.useGPU = RTEST(use_gpu);
    }
    
    wrapper->renderer->setSettings(renderSettings);
    return Qnil;
}

// Render#render
static VALUE render_start(VALUE self) {
    RendererWrapper* wrapper;
    Data_Get_Struct(self, RendererWrapper, wrapper);
    
    try {
        wrapper->renderer->render();
        return Qtrue;
    } catch (const std::exception& e) {
        rb_raise(rb_eRuntimeError, "Render failed: %s", e.what());
        return Qfalse;
    }
}

// Render#stop
static VALUE render_stop(VALUE self) {
    RendererWrapper* wrapper;
    Data_Get_Struct(self, RendererWrapper, wrapper);
    
    wrapper->renderer->stop();
    return Qnil;
}

// Render#progress
static VALUE render_progress(VALUE self) {
    RendererWrapper* wrapper;
    Data_Get_Struct(self, RendererWrapper, wrapper);
    
    const photon::RenderStats& stats = wrapper->renderer->getStats();
    return rb_float_new(stats.getProgress());
}

// Render#is_complete?
static VALUE render_is_complete(VALUE self) {
    RendererWrapper* wrapper;
    Data_Get_Struct(self, RendererWrapper, wrapper);
    
    return wrapper->renderer->isRendering() ? Qfalse : Qtrue;
}

// Render#get_pixels
static VALUE render_get_pixels(VALUE self) {
    RendererWrapper* wrapper;
    Data_Get_Struct(self, RendererWrapper, wrapper);
    
    const float* pixels = wrapper->renderer->getPixels();
    const photon::Film& film = wrapper->renderer->getFilm();
    
    int width = film.getWidth();
    int height = film.getHeight();
    int size = width * height * 3;
    
    VALUE array = rb_ary_new2(size);
    for (int i = 0; i < size; i++) {
        rb_ary_push(array, rb_float_new(pixels[i]));
    }
    
    return array;
}

// Render#width
static VALUE render_width(VALUE self) {
    RendererWrapper* wrapper;
    Data_Get_Struct(self, RendererWrapper, wrapper);
    
    return INT2NUM(wrapper->renderer->getFilm().getWidth());
}

// Render#height
static VALUE render_height(VALUE self) {
    RendererWrapper* wrapper;
    Data_Get_Struct(self, RendererWrapper, wrapper);
    
    return INT2NUM(wrapper->renderer->getFilm().getHeight());
}

// PhotonCore.save_png(filename, pixels, width, height)
static VALUE photon_core_save_png(VALUE self, VALUE filename, VALUE pixels, VALUE width, VALUE height) {
    // TODO: Implementare salvataggio PNG
    return Qtrue;
}

// PhotonCore.save_jpeg(filename, pixels, width, height, quality)
static VALUE photon_core_save_jpeg(VALUE self, VALUE filename, VALUE pixels, VALUE width, VALUE height, VALUE quality) {
    // TODO: Implementare salvataggio JPEG
    return Qtrue;
}

} // namespace photon_ruby

// Entry point per Ruby extension
extern "C" {

void Init_photon_core() {
    using namespace photon_ruby;
    
    // Modulo principale PhotonCore
    rb_mPhotonCore = rb_define_module("PhotonCore");
    
    // Metodi del modulo
    rb_define_singleton_method(rb_mPhotonCore, "initialize", RUBY_METHOD_FUNC(photon_core_initialize), 0);
    rb_define_singleton_method(rb_mPhotonCore, "shutdown", RUBY_METHOD_FUNC(photon_core_shutdown), 0);
    rb_define_singleton_method(rb_mPhotonCore, "version", RUBY_METHOD_FUNC(photon_core_version), 0);
    rb_define_singleton_method(rb_mPhotonCore, "has_cuda_device?", RUBY_METHOD_FUNC(photon_core_has_cuda), 0);
    rb_define_singleton_method(rb_mPhotonCore, "has_optix_device?", RUBY_METHOD_FUNC(photon_core_has_optix), 0);
    rb_define_singleton_method(rb_mPhotonCore, "capabilities", RUBY_METHOD_FUNC(photon_core_capabilities), 0);
    rb_define_singleton_method(rb_mPhotonCore, "save_png", RUBY_METHOD_FUNC(photon_core_save_png), 4);
    rb_define_singleton_method(rb_mPhotonCore, "save_jpeg", RUBY_METHOD_FUNC(photon_core_save_jpeg), 5);
    
    // Classe Render
    rb_cRender = rb_define_class_under(rb_mPhotonCore, "Render", rb_cObject);
    rb_define_alloc_func(rb_cRender, renderer_alloc);
    rb_define_method(rb_cRender, "initialize", RUBY_METHOD_FUNC(render_initialize), 0);
    rb_define_method(rb_cRender, "set_scene", RUBY_METHOD_FUNC(render_set_scene), 1);
    rb_define_method(rb_cRender, "set_settings", RUBY_METHOD_FUNC(render_set_settings), 1);
    rb_define_method(rb_cRender, "render", RUBY_METHOD_FUNC(render_start), 0);
    rb_define_method(rb_cRender, "stop", RUBY_METHOD_FUNC(render_stop), 0);
    rb_define_method(rb_cRender, "progress", RUBY_METHOD_FUNC(render_progress), 0);
    rb_define_method(rb_cRender, "is_complete?", RUBY_METHOD_FUNC(render_is_complete), 0);
    rb_define_method(rb_cRender, "get_pixels", RUBY_METHOD_FUNC(render_get_pixels), 0);
    rb_define_method(rb_cRender, "width", RUBY_METHOD_FUNC(render_width), 0);
    rb_define_method(rb_cRender, "height", RUBY_METHOD_FUNC(render_height), 0);
    
    // Inizializza PhotonRender automaticamente
    photon::initialize();
}

} // extern "C"
