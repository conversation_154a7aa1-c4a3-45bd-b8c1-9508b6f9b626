// src/bindings/ruby_bindings.hpp
// PhotonRender - Ruby-C++ Bindings Header
// Definizioni per bridge SketchUp ↔ PhotonRender

#pragma once

#include <ruby.h>

// Dichiarazioni per Ruby extension
extern "C" {
    void Init_photon_core();
}

// Utility functions per conversioni Ruby ↔ C++
namespace photon_ruby {

// Conversioni Vec3
VALUE vec3_to_ruby(const photon::Vec3& vec);
photon::Vec3 ruby_to_vec3(VALUE array);

// Conversioni Color3
VALUE color3_to_ruby(const photon::Color3& color);
photon::Color3 ruby_to_color3(VALUE array);

// Conversioni Matrix4
VALUE matrix4_to_ruby(const photon::Matrix4& matrix);
photon::Matrix4 ruby_to_matrix4(VALUE array);

// Conversioni Scene Data
photon::Scene ruby_to_scene(VALUE scene_data);
VALUE scene_to_ruby(const photon::Scene& scene);

// Error handling
void ruby_raise_photon_error(const std::string& message);

} // namespace photon_ruby
