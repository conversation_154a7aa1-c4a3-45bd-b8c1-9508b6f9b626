// src/core/accelerator/light_bvh.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Light BVH implementation for spatial acceleration of light queries

#include "light_bvh.hpp"
#include "../scene/scene.hpp"
#include "../light/spot_light.hpp"
#include "../light/area_light_base.hpp"
#include "../light/rectangle_light.hpp"
#include "../light/disk_light.hpp"
#include "../light/sphere_light.hpp"
#include "../light/photometric_light.hpp"
#include <iostream>
#include <algorithm>
#include <cassert>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

namespace photon {

// LightBVH implementation
LightBVH::LightBVH() : m_maxLeafSize(4) {
}

LightBVH::~LightBVH() {
    clear();
}

void LightBVH::build(const std::vector<std::shared_ptr<Light>>& lights, int maxLeafSize) {
    clear();
    m_maxLeafSize = maxLeafSize;
    
    if (lights.empty()) {
        return;
    }
    
    // Create light references with spatial information
    m_allLights.reserve(lights.size());
    for (const auto& light : lights) {
        if (!light) continue;
        
        LightBounds bounds = computeLightBounds(light);
        Point3 position = getLightPosition(light);
        float radius = getLightRadius(light);
        float importance = getLightImportance(light);
        
        m_allLights.emplace_back(light, bounds, position, radius, importance);
    }
    
    if (m_allLights.empty()) {
        return;
    }
    
    // Build BVH recursively
    std::vector<LightReference> lightsCopy = m_allLights;
    m_root = buildRecursive(lightsCopy, 0);
    
    std::cout << "Light BVH built with " << m_allLights.size() << " lights" << std::endl;
}

std::unique_ptr<LightBVHNode> LightBVH::buildRecursive(std::vector<LightReference>& lights, int depth) {
    auto node = std::make_unique<LightBVHNode>();
    
    // Compute bounds for all lights in this node
    node->bounds = computeBounds(lights);
    
    // Check if we should create a leaf
    if (lights.size() <= static_cast<size_t>(m_maxLeafSize) || depth > 20) {
        node->isLeaf = true;
        node->lights = std::move(lights);
        return node;
    }
    
    // Choose split axis (longest axis)
    int splitAxis = chooseSplitAxis(lights, node->bounds);
    
    // Sort lights along split axis
    std::sort(lights.begin(), lights.end(), [splitAxis](const LightReference& a, const LightReference& b) {
        switch (splitAxis) {
            case 0: return a.position.x < b.position.x;
            case 1: return a.position.y < b.position.y;
            case 2: return a.position.z < b.position.z;
            default: return false;
        }
    });
    
    // Split lights in half
    size_t mid = lights.size() / 2;
    std::vector<LightReference> leftLights(lights.begin(), lights.begin() + mid);
    std::vector<LightReference> rightLights(lights.begin() + mid, lights.end());
    
    // Recursively build children
    if (!leftLights.empty()) {
        node->left = buildRecursive(leftLights, depth + 1);
    }
    if (!rightLights.empty()) {
        node->right = buildRecursive(rightLights, depth + 1);
    }
    
    return node;
}

LightBounds LightBVH::computeBounds(const std::vector<LightReference>& lights) const {
    LightBounds bounds;
    
    for (const auto& lightRef : lights) {
        bounds.expand(lightRef.bounds);
    }
    
    return bounds;
}

int LightBVH::chooseSplitAxis(const std::vector<LightReference>& lights, const LightBounds& bounds) const {
    Vec3 diagonal = bounds.diagonal();
    
    // Choose the longest axis
    if (diagonal.x >= diagonal.y && diagonal.x >= diagonal.z) {
        return 0; // X axis
    } else if (diagonal.y >= diagonal.z) {
        return 1; // Y axis
    } else {
        return 2; // Z axis
    }
}

std::vector<LightReference> LightBVH::queryLights(const LightQuery& query) const {
    std::vector<LightReference> results;
    
    if (!m_root) {
        return results;
    }
    
    queryRecursive(m_root.get(), query, results);
    return results;
}

void LightBVH::queryRecursive(const LightBVHNode* node, const LightQuery& query, 
                             std::vector<LightReference>& results) const {
    if (!node) return;
    
    // Check if query position is too far from node bounds
    float distToBounds = node->bounds.distanceTo(query.position);
    if (distToBounds > query.maxDistance) {
        return;
    }
    
    if (node->isLeaf) {
        // Leaf node: check each light
        for (const auto& lightRef : node->lights) {
            // Distance check
            float dist = (lightRef.position - query.position).length();
            if (dist > query.maxDistance) continue;
            
            // Importance check
            if (lightRef.importance < query.minImportance) continue;
            
            results.push_back(lightRef);
        }
    } else {
        // Internal node: recurse to children
        if (node->left) {
            queryRecursive(node->left.get(), query, results);
        }
        if (node->right) {
            queryRecursive(node->right.get(), query, results);
        }
    }
}

std::vector<LightReference> LightBVH::queryLightsFrustum(const std::array<Vec4, 6>& frustumPlanes, 
                                                        const LightQuery& query) const {
    std::vector<LightReference> results;
    
    if (!m_root) {
        return results;
    }
    
    queryFrustumRecursive(m_root.get(), frustumPlanes, query, results);
    return results;
}

void LightBVH::queryFrustumRecursive(const LightBVHNode* node, const std::array<Vec4, 6>& frustumPlanes,
                                    const LightQuery& query, std::vector<LightReference>& results) const {
    if (!node) return;
    
    // Check if node bounds intersect frustum
    if (!frustumContainsBounds(frustumPlanes, node->bounds)) {
        return;
    }
    
    // Check distance to query position
    float distToBounds = node->bounds.distanceTo(query.position);
    if (distToBounds > query.maxDistance) {
        return;
    }
    
    if (node->isLeaf) {
        // Leaf node: check each light
        for (const auto& lightRef : node->lights) {
            // Distance check
            float dist = (lightRef.position - query.position).length();
            if (dist > query.maxDistance) continue;
            
            // Importance check
            if (lightRef.importance < query.minImportance) continue;
            
            // Frustum check for individual light
            if (!frustumContainsBounds(frustumPlanes, lightRef.bounds)) continue;
            
            results.push_back(lightRef);
        }
    } else {
        // Internal node: recurse to children
        if (node->left) {
            queryFrustumRecursive(node->left.get(), frustumPlanes, query, results);
        }
        if (node->right) {
            queryFrustumRecursive(node->right.get(), frustumPlanes, query, results);
        }
    }
}

LightBVH::Statistics LightBVH::getStatistics() const {
    Statistics stats = {};
    
    if (m_root) {
        computeStatisticsRecursive(m_root.get(), stats, 0);
        
        if (stats.leafCount > 0) {
            stats.avgLightsPerLeaf = static_cast<float>(stats.lightCount) / stats.leafCount;
        }
    }
    
    return stats;
}

void LightBVH::computeStatisticsRecursive(const LightBVHNode* node, Statistics& stats, int depth) const {
    if (!node) return;
    
    stats.nodeCount++;
    stats.maxDepth = std::max(stats.maxDepth, depth);
    
    if (node->isLeaf) {
        stats.leafCount++;
        stats.lightCount += static_cast<int>(node->lights.size());
    } else {
        if (node->left) {
            computeStatisticsRecursive(node->left.get(), stats, depth + 1);
        }
        if (node->right) {
            computeStatisticsRecursive(node->right.get(), stats, depth + 1);
        }
    }
}

void LightBVH::clear() {
    m_root.reset();
    m_allLights.clear();
}

// Utility function implementations
LightBounds LightBVH::computeLightBounds(std::shared_ptr<Light> light) const {
    if (!light) {
        return LightBounds();
    }
    
    // Get light position
    Point3 position = getLightPosition(light);
    float radius = getLightRadius(light);
    
    // Create bounds based on light type
    if (light->isDelta()) {
        // Point/spot lights: small bounds around position
        float epsilon = 0.01f;
        return LightBounds(
            Point3(position.x - epsilon, position.y - epsilon, position.z - epsilon),
            Point3(position.x + epsilon, position.y + epsilon, position.z + epsilon)
        );
    } else {
        // Area lights: bounds based on radius
        return LightBounds(
            Point3(position.x - radius, position.y - radius, position.z - radius),
            Point3(position.x + radius, position.y + radius, position.z + radius)
        );
    }
}

Point3 LightBVH::getLightPosition(std::shared_ptr<Light> light) const {
    if (!light) {
        return Point3(0, 0, 0);
    }

    // Get position based on light type using dynamic_cast
    std::string lightName = light->getName();

    // Point Light
    if (lightName == "Point") {
        // PointLight has m_position as private member, but we can use a workaround
        // For now, use a default position - this should be improved with proper virtual methods
        return Point3(0, 0, 0);
    }

    // Spot Light
    if (lightName == "SpotLight") {
        auto spotLight = std::dynamic_pointer_cast<SpotLight>(light);
        if (spotLight) {
            return Point3(spotLight->getPosition().x, spotLight->getPosition().y, spotLight->getPosition().z);
        }
    }

    // Area Lights (Rectangle, Disk, Sphere)
    if (lightName == "Rectangle") {
        auto rectLight = std::dynamic_pointer_cast<RectangleLight>(light);
        if (rectLight) {
            return Point3(rectLight->getCenter().x, rectLight->getCenter().y, rectLight->getCenter().z);
        }
    }

    if (lightName == "Disk") {
        auto diskLight = std::dynamic_pointer_cast<DiskLight>(light);
        if (diskLight) {
            return Point3(diskLight->getCenter().x, diskLight->getCenter().y, diskLight->getCenter().z);
        }
    }

    if (lightName == "Sphere") {
        auto sphereLight = std::dynamic_pointer_cast<SphereLight>(light);
        if (sphereLight) {
            return Point3(sphereLight->getCenter().x, sphereLight->getCenter().y, sphereLight->getCenter().z);
        }
    }

    // Photometric Light
    if (lightName == "PhotometricLight") {
        auto photometricLight = std::dynamic_pointer_cast<PhotometricLight>(light);
        if (photometricLight) {
            return Point3(photometricLight->getPosition().x, photometricLight->getPosition().y, photometricLight->getPosition().z);
        }
    }

    // Environment Light (no specific position)
    if (lightName == "Environment") {
        return Point3(0, 0, 0); // Environment lights don't have a specific position
    }

    // Directional Light (no specific position)
    if (lightName == "Directional") {
        return Point3(0, 0, 0); // Directional lights are at infinity
    }

    // Default fallback
    return Point3(0, 0, 0);
}

float LightBVH::getLightRadius(std::shared_ptr<Light> light) const {
    if (!light) {
        return 0.0f;
    }

    std::string lightName = light->getName();

    // Point Light - small radius
    if (lightName == "Point") {
        return 0.01f;
    }

    // Spot Light - small radius (delta light)
    if (lightName == "SpotLight") {
        return 0.01f;
    }

    // Rectangle Light - use diagonal as radius
    if (lightName == "Rectangle") {
        auto rectLight = std::dynamic_pointer_cast<RectangleLight>(light);
        if (rectLight) {
            // Estimate radius from area (assuming square for simplicity)
            float area = rectLight->getArea();
            return std::sqrt(area / M_PI); // Equivalent circle radius
        }
        return 1.0f;
    }

    // Disk Light - use actual radius
    if (lightName == "Disk") {
        auto diskLight = std::dynamic_pointer_cast<DiskLight>(light);
        if (diskLight) {
            return diskLight->getRadius();
        }
        return 1.0f;
    }

    // Sphere Light - use actual radius
    if (lightName == "Sphere") {
        auto sphereLight = std::dynamic_pointer_cast<SphereLight>(light);
        if (sphereLight) {
            return sphereLight->getRadius();
        }
        return 1.0f;
    }

    // Photometric Light - small radius for delta types, larger for area types
    if (lightName == "PhotometricLight") {
        auto photometricLight = std::dynamic_pointer_cast<PhotometricLight>(light);
        if (photometricLight) {
            return photometricLight->isDelta() ? 0.01f : 1.0f;
        }
        return 0.01f;
    }

    // Environment Light - infinite radius
    if (lightName == "Environment") {
        return 1e30f; // Very large radius
    }

    // Directional Light - infinite radius
    if (lightName == "Directional") {
        return 1e30f; // Very large radius
    }

    // Default fallback
    return light->isDelta() ? 0.01f : 1.0f;
}

float LightBVH::getLightImportance(std::shared_ptr<Light> light) const {
    if (!light) {
        return 0.0f;
    }
    
    // Calculate importance based on light power
    Color3 power = light->power();
    return (power.r + power.g + power.b) / 3.0f;
}

bool LightBVH::frustumContainsBounds(const std::array<Vec4, 6>& frustumPlanes, const LightBounds& bounds) const {
    if (!bounds.isValid()) {
        return false;
    }
    
    // Test all 8 corners of the bounding box against all 6 frustum planes
    Point3 corners[8] = {
        Point3(bounds.min.x, bounds.min.y, bounds.min.z),
        Point3(bounds.max.x, bounds.min.y, bounds.min.z),
        Point3(bounds.min.x, bounds.max.y, bounds.min.z),
        Point3(bounds.max.x, bounds.max.y, bounds.min.z),
        Point3(bounds.min.x, bounds.min.y, bounds.max.z),
        Point3(bounds.max.x, bounds.min.y, bounds.max.z),
        Point3(bounds.min.x, bounds.max.y, bounds.max.z),
        Point3(bounds.max.x, bounds.max.y, bounds.max.z)
    };
    
    // For each frustum plane
    for (const auto& plane : frustumPlanes) {
        bool allOutside = true;
        
        // Check if all corners are outside this plane
        for (int i = 0; i < 8; i++) {
            float distance = plane.x * corners[i].x + plane.y * corners[i].y + plane.z * corners[i].z + plane.w;
            if (distance >= 0) {
                allOutside = false;
                break;
            }
        }
        
        // If all corners are outside any plane, the bounds is outside the frustum
        if (allOutside) {
            return false;
        }
    }
    
    return true;
}

} // namespace photon
