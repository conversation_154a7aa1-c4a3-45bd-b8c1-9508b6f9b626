// src/core/accelerator/light_bvh.hpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Light BVH (Bounding Volume Hierarchy) for spatial acceleration of light queries

#pragma once

#include "../math/vec3.hpp"
#include "../math/ray.hpp"
#include "../scene/light.hpp"

// Vec4 for frustum planes (x, y, z, w) where plane equation is: x*px + y*py + z*pz + w = 0
struct Vec4 {
    float x, y, z, w;
    Vec4() : x(0), y(0), z(0), w(0) {}
    Vec4(float x, float y, float z, float w) : x(x), y(y), z(z), w(w) {}
};
#include <memory>
#include <vector>
#include <array>
#include <algorithm>
#include <cmath>

namespace photon {

// Forward declarations
class Scene;
class Intersection;

/**
 * @brief Axis-aligned bounding box for lights
 */
struct LightBounds {
    Point3 min;     ///< Minimum corner
    Point3 max;     ///< Maximum corner
    
    /**
     * @brief Default constructor (invalid bounds)
     */
    LightBounds() : min(Point3(1e30f)), max(Point3(-1e30f)) {}
    
    /**
     * @brief Constructor
     */
    LightBounds(const Point3& min, const Point3& max) : min(min), max(max) {}
    
    /**
     * @brief Check if bounds are valid
     */
    bool isValid() const {
        return min.x <= max.x && min.y <= max.y && min.z <= max.z;
    }
    
    /**
     * @brief Get center point
     */
    Point3 center() const {
        return Point3((min.x + max.x) * 0.5f, (min.y + max.y) * 0.5f, (min.z + max.z) * 0.5f);
    }
    
    /**
     * @brief Get diagonal vector
     */
    Vec3 diagonal() const {
        return Vec3(max.x - min.x, max.y - min.y, max.z - min.z);
    }
    
    /**
     * @brief Get surface area
     */
    float surfaceArea() const {
        if (!isValid()) return 0.0f;
        Vec3 d = diagonal();
        return 2.0f * (d.x * d.y + d.x * d.z + d.y * d.z);
    }
    
    /**
     * @brief Expand bounds to include point
     */
    void expand(const Point3& point) {
        min.x = std::min(min.x, point.x);
        min.y = std::min(min.y, point.y);
        min.z = std::min(min.z, point.z);
        max.x = std::max(max.x, point.x);
        max.y = std::max(max.y, point.y);
        max.z = std::max(max.z, point.z);
    }
    
    /**
     * @brief Expand bounds to include another bounds
     */
    void expand(const LightBounds& other) {
        if (other.isValid()) {
            expand(other.min);
            expand(other.max);
        }
    }
    
    /**
     * @brief Check if point is inside bounds
     */
    bool contains(const Point3& point) const {
        return point.x >= min.x && point.x <= max.x &&
               point.y >= min.y && point.y <= max.y &&
               point.z >= min.z && point.z <= max.z;
    }
    
    /**
     * @brief Check if bounds intersect
     */
    bool intersects(const LightBounds& other) const {
        return min.x <= other.max.x && max.x >= other.min.x &&
               min.y <= other.max.y && max.y >= other.min.y &&
               min.z <= other.max.z && max.z >= other.min.z;
    }
    
    /**
     * @brief Get distance to point (0 if inside)
     */
    float distanceTo(const Point3& point) const {
        float dx = std::max(0.0f, std::max(min.x - point.x, point.x - max.x));
        float dy = std::max(0.0f, std::max(min.y - point.y, point.y - max.y));
        float dz = std::max(0.0f, std::max(min.z - point.z, point.z - max.z));
        return std::sqrt(dx * dx + dy * dy + dz * dz);
    }
};

/**
 * @brief Light reference with spatial information
 */
struct LightReference {
    std::shared_ptr<Light> light;   ///< Light pointer
    LightBounds bounds;             ///< Light bounding box
    Point3 position;                ///< Light position (for point/spot lights)
    float radius;                   ///< Light influence radius
    float importance;               ///< Light importance factor
    
    /**
     * @brief Constructor
     */
    LightReference(std::shared_ptr<Light> light, const LightBounds& bounds, 
                  const Point3& position, float radius, float importance = 1.0f)
        : light(light), bounds(bounds), position(position), radius(radius), importance(importance) {}
};

/**
 * @brief BVH node for light acceleration
 */
struct LightBVHNode {
    LightBounds bounds;                     ///< Node bounding box
    std::vector<LightReference> lights;     ///< Lights in this node (leaf only)
    std::unique_ptr<LightBVHNode> left;     ///< Left child
    std::unique_ptr<LightBVHNode> right;    ///< Right child
    bool isLeaf;                           ///< Is this a leaf node?
    
    /**
     * @brief Constructor
     */
    LightBVHNode() : isLeaf(false) {}
    
    /**
     * @brief Check if this is a leaf node
     */
    bool isLeafNode() const {
        return isLeaf && !lights.empty();
    }
};

/**
 * @brief Light query parameters
 */
struct LightQuery {
    Point3 position;        ///< Query position
    float maxDistance;      ///< Maximum distance to consider
    float minImportance;    ///< Minimum importance threshold
    bool frustumCulling;    ///< Enable frustum culling
    
    /**
     * @brief Constructor
     */
    LightQuery(const Point3& pos, float maxDist = 1e30f, float minImp = 0.0f, bool frustum = false)
        : position(pos), maxDistance(maxDist), minImportance(minImp), frustumCulling(frustum) {}
};

/**
 * @brief Light BVH for spatial acceleration of light queries
 */
class LightBVH {
public:
    /**
     * @brief Constructor
     */
    LightBVH();
    
    /**
     * @brief Destructor
     */
    ~LightBVH();
    
    /**
     * @brief Build BVH from lights
     * 
     * @param lights Vector of lights to build BVH from
     * @param maxLeafSize Maximum number of lights per leaf node
     */
    void build(const std::vector<std::shared_ptr<Light>>& lights, int maxLeafSize = 4);
    
    /**
     * @brief Query lights near a position
     * 
     * @param query Query parameters
     * @return Vector of light references within query parameters
     */
    std::vector<LightReference> queryLights(const LightQuery& query) const;
    
    /**
     * @brief Query lights within frustum
     * 
     * @param frustumPlanes Array of 6 frustum planes
     * @param query Additional query parameters
     * @return Vector of light references within frustum
     */
    std::vector<LightReference> queryLightsFrustum(const std::array<Vec4, 6>& frustumPlanes, 
                                                   const LightQuery& query) const;
    
    /**
     * @brief Get all lights (for fallback)
     */
    const std::vector<LightReference>& getAllLights() const { return m_allLights; }
    
    /**
     * @brief Check if BVH is built
     */
    bool isBuilt() const { return m_root != nullptr; }
    
    /**
     * @brief Get statistics
     */
    struct Statistics {
        int nodeCount;          ///< Total number of nodes
        int leafCount;          ///< Number of leaf nodes
        int lightCount;         ///< Total number of lights
        int maxDepth;           ///< Maximum tree depth
        float avgLightsPerLeaf; ///< Average lights per leaf
    };
    
    Statistics getStatistics() const;
    
    /**
     * @brief Clear BVH
     */
    void clear();

private:
    std::unique_ptr<LightBVHNode> m_root;       ///< Root node
    std::vector<LightReference> m_allLights;    ///< All light references
    int m_maxLeafSize;                          ///< Maximum lights per leaf
    
    // Build helpers
    std::unique_ptr<LightBVHNode> buildRecursive(std::vector<LightReference>& lights, int depth = 0);
    LightBounds computeBounds(const std::vector<LightReference>& lights) const;
    int chooseSplitAxis(const std::vector<LightReference>& lights, const LightBounds& bounds) const;
    void partitionLights(std::vector<LightReference>& lights, int axis, float splitPos);
    
    // Query helpers
    void queryRecursive(const LightBVHNode* node, const LightQuery& query, 
                       std::vector<LightReference>& results) const;
    void queryFrustumRecursive(const LightBVHNode* node, const std::array<Vec4, 6>& frustumPlanes,
                              const LightQuery& query, std::vector<LightReference>& results) const;
    
    // Utility functions
    LightBounds computeLightBounds(std::shared_ptr<Light> light) const;
    Point3 getLightPosition(std::shared_ptr<Light> light) const;
    float getLightRadius(std::shared_ptr<Light> light) const;
    float getLightImportance(std::shared_ptr<Light> light) const;
    bool frustumContainsBounds(const std::array<Vec4, 6>& frustumPlanes, const LightBounds& bounds) const;
    
    // Statistics helpers
    void computeStatisticsRecursive(const LightBVHNode* node, Statistics& stats, int depth) const;
};

} // namespace photon
