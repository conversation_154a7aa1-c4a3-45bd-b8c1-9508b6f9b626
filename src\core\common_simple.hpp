// src/core/common_simple.hpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Common definitions for simplified build (without <PERSON><PERSON><PERSON>)

#pragma once

// Windows math constants
#define _USE_MATH_DEFINES
#include <cmath>

#include <memory>
#include <vector>
#include <string>
#include <limits>
#include <iostream>
#include <array>
#include <algorithm>

// Math constants (in case M_PI is not defined)
#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

namespace photon {

// Forward declarations
class Scene;
class Camera;
class Integrator;
class Sampler;
class Material;
class Light;
class Mesh;
class Transform;

/**
 * @brief 3D Vector class for mathematical operations
 */
class Vec3 {
public:
    float x, y, z;

    // Constructors
    Vec3() : x(0), y(0), z(0) {}
    Vec3(float v) : x(v), y(v), z(v) {}
    Vec3(float x, float y, float z) : x(x), y(y), z(z) {}

    // Copy constructor and assignment
    Vec3(const Vec3& v) = default;
    Vec3& operator=(const Vec3& v) = default;

    // Array access
    float& operator[](int i) { return (&x)[i]; }
    const float& operator[](int i) const { return (&x)[i]; }

    // Arithmetic operators
    Vec3 operator+(const Vec3& v) const { return Vec3(x + v.x, y + v.y, z + v.z); }
    Vec3 operator-(const Vec3& v) const { return Vec3(x - v.x, y - v.y, z - v.z); }
    Vec3 operator*(const Vec3& v) const { return Vec3(x * v.x, y * v.y, z * v.z); }
    Vec3 operator/(const Vec3& v) const { return Vec3(x / v.x, y / v.y, z / v.z); }

    Vec3 operator*(float t) const { return Vec3(x * t, y * t, z * t); }
    Vec3 operator/(float t) const { float inv = 1.0f / t; return Vec3(x * inv, y * inv, z * inv); }

    // Unary operators
    Vec3 operator-() const { return Vec3(-x, -y, -z); }

    // Compound assignment operators
    Vec3& operator+=(const Vec3& v) { x += v.x; y += v.y; z += v.z; return *this; }
    Vec3& operator-=(const Vec3& v) { x -= v.x; y -= v.y; z -= v.z; return *this; }
    Vec3& operator*=(const Vec3& v) { x *= v.x; y *= v.y; z *= v.z; return *this; }
    Vec3& operator/=(const Vec3& v) { x /= v.x; y /= v.y; z /= v.z; return *this; }

    Vec3& operator*=(float t) { x *= t; y *= t; z *= t; return *this; }
    Vec3& operator/=(float t) { float inv = 1.0f / t; x *= inv; y *= inv; z *= inv; return *this; }

    // Comparison operators
    bool operator==(const Vec3& v) const { return x == v.x && y == v.y && z == v.z; }
    bool operator!=(const Vec3& v) const { return !(*this == v); }

    // Vector operations
    float length() const { return std::sqrt(x * x + y * y + z * z); }
    float lengthSquared() const { return x * x + y * y + z * z; }

    Vec3 normalized() const {
        float len = length();
        return len > 0 ? *this / len : Vec3(0);
    }

    void normalize() {
        float len = length();
        if (len > 0) *this /= len;
    }

    float dot(const Vec3& v) const { return x * v.x + y * v.y + z * v.z; }

    Vec3 cross(const Vec3& v) const {
        return Vec3(
            y * v.z - z * v.y,
            z * v.x - x * v.z,
            x * v.y - y * v.x
        );
    }

    // Component-wise operations
    Vec3 abs() const { return Vec3(std::abs(x), std::abs(y), std::abs(z)); }
    Vec3 min(const Vec3& v) const { return Vec3(std::min(x, v.x), std::min(y, v.y), std::min(z, v.z)); }
    Vec3 max(const Vec3& v) const { return Vec3(std::max(x, v.x), std::max(y, v.y), std::max(z, v.z)); }

    float minComponent() const { return std::min({x, y, z}); }
    float maxComponent() const { return std::max({x, y, z}); }

    // Utility functions
    bool isZero() const { return x == 0 && y == 0 && z == 0; }
    bool hasNaN() const { return std::isnan(x) || std::isnan(y) || std::isnan(z); }
    bool hasInf() const { return std::isinf(x) || std::isinf(y) || std::isinf(z); }

    // Static utility functions
    static Vec3 lerp(const Vec3& a, const Vec3& b, float t) {
        return a + (b - a) * t;
    }

    static Vec3 reflect(const Vec3& incident, const Vec3& normal) {
        return incident - normal * (2.0f * incident.dot(normal));
    }

    static Vec3 refract(const Vec3& incident, const Vec3& normal, float eta) {
        float cosI = -incident.dot(normal);
        float sinT2 = eta * eta * (1.0f - cosI * cosI);
        if (sinT2 >= 1.0f) return Vec3(0); // Total internal reflection
        float cosT = std::sqrt(1.0f - sinT2);
        return incident * eta + normal * (eta * cosI - cosT);
    }
};

// Non-member operators
inline Vec3 operator*(float t, const Vec3& v) { return v * t; }

// Stream operators
inline std::ostream& operator<<(std::ostream& os, const Vec3& v) {
    return os << "Vec3(" << v.x << ", " << v.y << ", " << v.z << ")";
}

// Type aliases
using Point3 = Vec3;  // 3D point
using Color3 = Vec3;  // RGB color
using Normal3 = Vec3; // Surface normal

/**
 * @brief 4x4 Matrix class for transformations
 */
class Matrix4 {
public:
    // Column-major storage: m[column][row]
    std::array<std::array<float, 4>, 4> m;

    // Constructors
    Matrix4() { setIdentity(); }

    Matrix4(float m00, float m01, float m02, float m03,
           float m10, float m11, float m12, float m13,
           float m20, float m21, float m22, float m23,
           float m30, float m31, float m32, float m33) {
        // Row-major input, column-major storage
        m[0][0] = m00; m[1][0] = m01; m[2][0] = m02; m[3][0] = m03;
        m[0][1] = m10; m[1][1] = m11; m[2][1] = m12; m[3][1] = m13;
        m[0][2] = m20; m[1][2] = m21; m[2][2] = m22; m[3][2] = m23;
        m[0][3] = m30; m[1][3] = m31; m[2][3] = m32; m[3][3] = m33;
    }

    // Copy constructor and assignment
    Matrix4(const Matrix4& other) = default;
    Matrix4& operator=(const Matrix4& other) = default;

    // Element access
    float& operator()(int row, int col) { return m[col][row]; }
    const float& operator()(int row, int col) const { return m[col][row]; }

    std::array<float, 4>& operator[](int col) { return m[col]; }
    const std::array<float, 4>& operator[](int col) const { return m[col]; }

    // Matrix initialization
    void setIdentity() {
        for (int i = 0; i < 4; ++i) {
            for (int j = 0; j < 4; ++j) {
                m[i][j] = (i == j) ? 1.0f : 0.0f;
            }
        }
    }

    // Static factory methods
    static Matrix4 identity() {
        Matrix4 result;
        result.setIdentity();
        return result;
    }

    static Matrix4 translation(const Vec3& t) {
        Matrix4 result;
        result.m[3][0] = t.x;
        result.m[3][1] = t.y;
        result.m[3][2] = t.z;
        return result;
    }

    static Matrix4 translation(float x, float y, float z) {
        return translation(Vec3(x, y, z));
    }
};

// Simplified intersection structure (without Embree)
struct Intersection {
    bool hit = false;
    float t = std::numeric_limits<float>::infinity();
    Point3 point;
    Normal3 normal;
    Vec3 dpdu, dpdv;
    float u = 0, v = 0;
    std::shared_ptr<Material> material;

    Intersection() = default;

    bool isValid() const { return hit && t > 0; }
};

// Simplified ray structure
struct Ray {
    Point3 origin;
    Vec3 direction;
    float tMin = 1e-4f;
    float tMax = std::numeric_limits<float>::infinity();

    Ray() = default;
    Ray(const Point3& o, const Vec3& d) : origin(o), direction(d) {}
    Ray(const Point3& o, const Vec3& d, float tmin, float tmax)
        : origin(o), direction(d), tMin(tmin), tMax(tmax) {}

    Point3 at(float t) const { return origin + t * direction; }
};

// Dummy RTCDevice type for simplified build
using RTCDevice = void*;

// Dummy functions for simplified build
inline RTCDevice getEmbreeDevice() { return nullptr; }

// Version information
#define PHOTON_VERSION_MAJOR 1
#define PHOTON_VERSION_MINOR 0
#define PHOTON_VERSION_PATCH 0
#define PHOTON_VERSION_STRING "1.0.0"

} // namespace photon
