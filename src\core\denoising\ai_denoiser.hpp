// src/core/denoising/ai_denoiser.hpp
// PhotonRender - AI Denoising System
// Intel OIDN integration for noise reduction and quality enhancement

#pragma once

#include "../common.hpp"
#include "../math/color3.hpp"
#include <memory>
#include <string>
#include <vector>
#include <functional>

// Forward declarations
namespace oidn {
    class DeviceRef;
    class FilterRef;
    class BufferRef;
}

namespace photon {

// Forward declarations
class Film;

/**
 * @brief Denoising quality levels
 */
enum class DenoisingQuality {
    FAST,       ///< Fast denoising for preview
    BALANCED,   ///< Balanced quality/speed
    HIGH,       ///< High quality for final renders
    ULTRA       ///< Ultra quality for production
};

/**
 * @brief Denoising algorithm types
 */
enum class DenoisingAlgorithm {
    RT,         ///< Ray tracing denoiser (default)
    RTLightmap  ///< Lightmap denoiser for baked lighting
};

/**
 * @brief Denoising settings
 */
struct DenoisingSettings {
    bool enabled = true;                           ///< Enable denoising
    DenoisingQuality quality = DenoisingQuality::BALANCED;
    DenoisingAlgorithm algorithm = DenoisingAlgorithm::RT;
    bool useAlbedo = true;                        ///< Use albedo buffer for better results
    bool useNormals = true;                       ///< Use normal buffer for better results
    bool useMotionVectors = false;                ///< Use motion vectors (for animation)
    float strength = 1.0f;                        ///< Denoising strength (0.0-1.0)
    bool prefilterAuxiliary = true;               ///< Prefilter auxiliary buffers
    bool cleanAuxiliary = true;                   ///< Clean auxiliary buffers
    int tileSize = 256;                           ///< Tile size for memory efficiency
    bool verbose = false;                         ///< Verbose logging
    
    /**
     * @brief Default settings for different use cases
     */
    static DenoisingSettings preview() {
        DenoisingSettings settings;
        settings.quality = DenoisingQuality::FAST;
        settings.useAlbedo = false;
        settings.useNormals = false;
        settings.tileSize = 128;
        return settings;
    }
    
    static DenoisingSettings production() {
        DenoisingSettings settings;
        settings.quality = DenoisingQuality::HIGH;
        settings.useAlbedo = true;
        settings.useNormals = true;
        settings.tileSize = 512;
        return settings;
    }
    
    static DenoisingSettings ultra() {
        DenoisingSettings settings;
        settings.quality = DenoisingQuality::ULTRA;
        settings.useAlbedo = true;
        settings.useNormals = true;
        settings.useMotionVectors = false;
        settings.tileSize = 1024;
        return settings;
    }
};

/**
 * @brief Denoising statistics
 */
struct DenoisingStats {
    double denoisingTime = 0.0;                   ///< Time spent denoising (seconds)
    size_t memoryUsed = 0;                        ///< Memory used for denoising (bytes)
    int tilesProcessed = 0;                       ///< Number of tiles processed
    double noiseReduction = 0.0;                  ///< Estimated noise reduction (0.0-1.0)
    bool success = false;                         ///< Denoising success flag
    std::string errorMessage;                     ///< Error message if failed
    
    /**
     * @brief Get memory usage in MB
     */
    double getMemoryUsageMB() const {
        return static_cast<double>(memoryUsed) / (1024.0 * 1024.0);
    }
    
    /**
     * @brief Get performance in MPix/sec
     */
    double getPerformanceMPixPerSec(int width, int height) const {
        if (denoisingTime <= 0.0) return 0.0;
        double megapixels = (width * height) / 1000000.0;
        return megapixels / denoisingTime;
    }
};

/**
 * @brief Image buffer for denoising
 */
struct DenoisingBuffer {
    int width = 0;
    int height = 0;
    int channels = 0;
    std::vector<float> data;
    
    DenoisingBuffer() = default;
    DenoisingBuffer(int w, int h, int c) 
        : width(w), height(h), channels(c), data(w * h * c, 0.0f) {}
    
    /**
     * @brief Get pixel value
     */
    Color3 getPixel(int x, int y) const {
        if (x < 0 || x >= width || y < 0 || y >= height || channels < 3) {
            return Color3(0.0f);
        }
        int idx = (y * width + x) * channels;
        return Color3(data[idx], data[idx + 1], data[idx + 2]);
    }
    
    /**
     * @brief Set pixel value
     */
    void setPixel(int x, int y, const Color3& color) {
        if (x < 0 || x >= width || y < 0 || y >= height || channels < 3) {
            return;
        }
        int idx = (y * width + x) * channels;
        data[idx] = color.x;
        data[idx + 1] = color.y;
        data[idx + 2] = color.z;
    }
    
    /**
     * @brief Clear buffer
     */
    void clear() {
        std::fill(data.begin(), data.end(), 0.0f);
    }
    
    /**
     * @brief Get memory usage in bytes
     */
    size_t getMemoryUsage() const {
        return data.size() * sizeof(float);
    }
};

/**
 * @brief AI Denoiser using Intel OIDN
 */
class AIDenoiser {
public:
    /**
     * @brief Constructor
     */
    AIDenoiser();
    
    /**
     * @brief Destructor
     */
    ~AIDenoiser();
    
    /**
     * @brief Initialize denoiser
     */
    bool initialize(const DenoisingSettings& settings = DenoisingSettings{});
    
    /**
     * @brief Shutdown denoiser
     */
    void shutdown();
    
    /**
     * @brief Check if denoiser is available
     */
    static bool isAvailable();
    
    /**
     * @brief Get OIDN version
     */
    static std::string getVersion();
    
    /**
     * @brief Denoise image from Film
     */
    bool denoise(Film& film, const DenoisingSettings& settings = DenoisingSettings{});
    
    /**
     * @brief Denoise image buffer
     */
    bool denoise(
        DenoisingBuffer& colorBuffer,
        const DenoisingBuffer* albedoBuffer = nullptr,
        const DenoisingBuffer* normalBuffer = nullptr,
        const DenoisingSettings& settings = DenoisingSettings{}
    );
    
    /**
     * @brief Denoise image with tiling for large images
     */
    bool denoiseTiled(
        DenoisingBuffer& colorBuffer,
        const DenoisingBuffer* albedoBuffer = nullptr,
        const DenoisingBuffer* normalBuffer = nullptr,
        const DenoisingSettings& settings = DenoisingSettings{}
    );
    
    /**
     * @brief Get last denoising statistics
     */
    const DenoisingStats& getStats() const { return m_stats; }
    
    /**
     * @brief Set progress callback
     */
    void setProgressCallback(std::function<void(float)> callback) {
        m_progressCallback = callback;
    }
    
    /**
     * @brief Get supported device types
     */
    static std::vector<std::string> getSupportedDevices();
    
    /**
     * @brief Estimate memory usage for denoising
     */
    static size_t estimateMemoryUsage(int width, int height, const DenoisingSettings& settings);

private:
    bool m_initialized = false;
    DenoisingSettings m_settings;
    DenoisingStats m_stats;
    std::function<void(float)> m_progressCallback;
    
    // OIDN objects (using raw pointers to avoid header dependencies)
    void* m_device;  // oidn::DeviceRef*
    void* m_filter;  // oidn::FilterRef*
    
    /**
     * @brief Initialize OIDN device
     */
    bool initializeDevice();
    
    /**
     * @brief Create OIDN filter
     */
    bool createFilter(const DenoisingSettings& settings);
    
    /**
     * @brief Process single tile
     */
    bool processTile(
        DenoisingBuffer& colorBuffer,
        const DenoisingBuffer* albedoBuffer,
        const DenoisingBuffer* normalBuffer,
        int tileX, int tileY, int tileWidth, int tileHeight,
        const DenoisingSettings& settings
    );
    
    /**
     * @brief Convert Film to DenoisingBuffer
     */
    void filmToBuffer(const Film& film, DenoisingBuffer& buffer);
    
    /**
     * @brief Convert DenoisingBuffer to Film
     */
    void bufferToFilm(const DenoisingBuffer& buffer, Film& film);
    
    /**
     * @brief Calculate optimal tile size
     */
    int calculateOptimalTileSize(int width, int height, const DenoisingSettings& settings);
    
    /**
     * @brief Validate buffer compatibility
     */
    bool validateBuffers(
        const DenoisingBuffer& colorBuffer,
        const DenoisingBuffer* albedoBuffer,
        const DenoisingBuffer* normalBuffer
    );
};

/**
 * @brief Global denoiser instance
 */
class DenoisingManager {
public:
    /**
     * @brief Get singleton instance
     */
    static DenoisingManager& getInstance();
    
    /**
     * @brief Initialize global denoiser
     */
    bool initialize(const DenoisingSettings& settings = DenoisingSettings{});
    
    /**
     * @brief Shutdown global denoiser
     */
    void shutdown();
    
    /**
     * @brief Get global denoiser
     */
    AIDenoiser& getDenoiser() { return m_denoiser; }
    
    /**
     * @brief Quick denoise function
     */
    bool denoise(Film& film, const DenoisingSettings& settings = DenoisingSettings{});

private:
    DenoisingManager() = default;
    ~DenoisingManager() = default;
    DenoisingManager(const DenoisingManager&) = delete;
    DenoisingManager& operator=(const DenoisingManager&) = delete;
    
    AIDenoiser m_denoiser;
    bool m_initialized = false;
};

} // namespace photon
