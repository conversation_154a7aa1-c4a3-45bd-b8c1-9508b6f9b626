// src/core/geometry/mesh_loader.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Mesh loading implementation

#include "mesh_loader.hpp"
#include "../math/vec2.hpp"
#include <fstream>
#include <sstream>
#include <chrono>
#include <iostream>
#include <algorithm>
#define _USE_MATH_DEFINES
#include <cmath>

namespace photon {

std::shared_ptr<Mesh> MeshLoader::loadFromFile(const std::string& filename,
                                              const MeshLoadConfig& config,
                                              MeshLoadStats* stats) {
    auto startTime = std::chrono::high_resolution_clock::now();
    
    MeshLoadStats localStats;
    if (!stats) stats = &localStats;
    
    // Check if file exists
    if (!fileExists(filename)) {
        stats->addError("File not found: " + filename);
        return nullptr;
    }
    
    // Detect format
    MeshFormat format = detectFormat(filename);
    if (format == MeshFormat::AUTO_DETECT) {
        stats->addError("Unknown file format: " + filename);
        return nullptr;
    }
    
    std::shared_ptr<Mesh> mesh;
    
    switch (format) {
        case MeshFormat::OBJ:
            mesh = loadOBJ(filename, config, stats);
            break;
        case MeshFormat::PLY:
            mesh = loadPLY(filename, config, stats);
            break;
        case MeshFormat::STL:
            mesh = loadSTL(filename, config, stats);
            break;
        default:
            stats->addError("Unsupported format");
            return nullptr;
    }
    
    // Calculate load time
    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(endTime - startTime);
    stats->loadTimeMs = duration.count() / 1000.0f;
    
    return mesh;
}

std::vector<std::shared_ptr<Mesh>> MeshLoader::loadMultipleFromFile(const std::string& filename,
                                                                   const MeshLoadConfig& config,
                                                                   MeshLoadStats* stats) {
    // For now, just return single mesh in vector
    auto mesh = loadFromFile(filename, config, stats);
    if (mesh) {
        return {mesh};
    }
    return {};
}

bool MeshLoader::saveToFile(const Mesh& mesh, const std::string& filename, MeshFormat format) {
    if (format == MeshFormat::AUTO_DETECT) {
        format = detectFormat(filename);
    }
    
    switch (format) {
        case MeshFormat::OBJ:
            return saveOBJ(mesh, filename);
        case MeshFormat::PLY:
            return savePLY(mesh, filename);
        case MeshFormat::STL:
            return saveSTL(mesh, filename);
        default:
            std::cerr << "Unsupported export format" << std::endl;
            return false;
    }
}

MeshFormat MeshLoader::detectFormat(const std::string& filename) {
    std::string ext = getFileExtension(filename);
    std::transform(ext.begin(), ext.end(), ext.begin(), ::tolower);
    
    if (ext == ".obj") return MeshFormat::OBJ;
    if (ext == ".ply") return MeshFormat::PLY;
    if (ext == ".stl") return MeshFormat::STL;
    
    return MeshFormat::AUTO_DETECT;
}

bool MeshLoader::validateFile(const std::string& filename) {
    if (!fileExists(filename)) return false;
    
    MeshFormat format = detectFormat(filename);
    if (format == MeshFormat::AUTO_DETECT) return false;
    
    // Basic validation - try to read first few lines
    std::ifstream file(filename);
    if (!file.is_open()) return false;
    
    std::string line;
    bool hasContent = false;
    for (int i = 0; i < 10 && std::getline(file, line); ++i) {
        if (!line.empty() && line[0] != '#') {
            hasContent = true;
            break;
        }
    }
    
    return hasContent;
}

std::shared_ptr<Mesh> MeshLoader::createTestCube() {
    auto mesh = std::make_shared<Mesh>();
    
    // Define cube vertices
    std::vector<Vec3> vertices = {
        // Front face
        Vec3(-1, -1,  1), Vec3( 1, -1,  1), Vec3( 1,  1,  1), Vec3(-1,  1,  1),
        // Back face
        Vec3(-1, -1, -1), Vec3(-1,  1, -1), Vec3( 1,  1, -1), Vec3( 1, -1, -1),
        // Top face
        Vec3(-1,  1, -1), Vec3(-1,  1,  1), Vec3( 1,  1,  1), Vec3( 1,  1, -1),
        // Bottom face
        Vec3(-1, -1, -1), Vec3( 1, -1, -1), Vec3( 1, -1,  1), Vec3(-1, -1,  1),
        // Right face
        Vec3( 1, -1, -1), Vec3( 1,  1, -1), Vec3( 1,  1,  1), Vec3( 1, -1,  1),
        // Left face
        Vec3(-1, -1, -1), Vec3(-1, -1,  1), Vec3(-1,  1,  1), Vec3(-1,  1, -1)
    };
    
    // Add faces (each face is 2 triangles)
    for (int i = 0; i < 6; ++i) {
        int base = i * 4;
        // First triangle
        mesh->addTriangleFromVertices(Point3(vertices[base]), Point3(vertices[base + 1]), Point3(vertices[base + 2]));
        // Second triangle
        mesh->addTriangleFromVertices(Point3(vertices[base]), Point3(vertices[base + 2]), Point3(vertices[base + 3]));
    }
    
    return mesh;
}

std::shared_ptr<Mesh> MeshLoader::createTestSphere(float radius, int subdivisions) {
    auto mesh = std::make_shared<Mesh>();
    
    // Simple UV sphere generation
    for (int i = 0; i <= subdivisions; ++i) {
        float phi = M_PI * i / subdivisions;
        for (int j = 0; j <= subdivisions; ++j) {
            float theta = 2.0f * M_PI * j / subdivisions;
            
            float x = radius * sin(phi) * cos(theta);
            float y = radius * cos(phi);
            float z = radius * sin(phi) * sin(theta);
            
            // Add triangles (except for poles)
            if (i > 0 && j > 0) {
                Vec3 v1(x, y, z);
                
                // Previous phi
                float phi_prev = M_PI * (i - 1) / subdivisions;
                float x_prev = radius * sin(phi_prev) * cos(theta);
                float y_prev = radius * cos(phi_prev);
                float z_prev = radius * sin(phi_prev) * sin(theta);
                Vec3 v2(x_prev, y_prev, z_prev);
                
                // Previous theta
                float theta_prev = 2.0f * M_PI * (j - 1) / subdivisions;
                float x_prev_theta = radius * sin(phi) * cos(theta_prev);
                float y_prev_theta = radius * cos(phi);
                float z_prev_theta = radius * sin(phi) * sin(theta_prev);
                Vec3 v3(x_prev_theta, y_prev_theta, z_prev_theta);
                
                mesh->addTriangleFromVertices(Point3(v1), Point3(v2), Point3(v3));
            }
        }
    }
    
    return mesh;
}

std::shared_ptr<Mesh> MeshLoader::createTestPlane(float width, float height, int subdivisions) {
    auto mesh = std::make_shared<Mesh>();
    
    float stepX = width / subdivisions;
    float stepY = height / subdivisions;
    float halfWidth = width * 0.5f;
    float halfHeight = height * 0.5f;
    
    for (int i = 0; i < subdivisions; ++i) {
        for (int j = 0; j < subdivisions; ++j) {
            float x1 = -halfWidth + i * stepX;
            float x2 = -halfWidth + (i + 1) * stepX;
            float y1 = -halfHeight + j * stepY;
            float y2 = -halfHeight + (j + 1) * stepY;
            
            // Create quad as two triangles
            Vec3 v1(x1, 0, y1);
            Vec3 v2(x2, 0, y1);
            Vec3 v3(x2, 0, y2);
            Vec3 v4(x1, 0, y2);
            
            mesh->addTriangleFromVertices(Point3(v1), Point3(v2), Point3(v3));
            mesh->addTriangleFromVertices(Point3(v1), Point3(v3), Point3(v4));
        }
    }
    
    return mesh;
}

// Private implementation functions
std::shared_ptr<Mesh> MeshLoader::loadOBJ(const std::string& filename,
                                          const MeshLoadConfig& config,
                                          MeshLoadStats* stats) {
    auto objData = SimpleOBJParser::parse(filename, stats);
    if (!objData.isValid()) {
        stats->addError("Failed to parse OBJ file");
        return nullptr;
    }
    
    auto mesh = SimpleOBJParser::convertToMesh(objData, config);
    if (!mesh) {
        stats->addError("Failed to convert OBJ data to mesh");
        return nullptr;
    }
    
    stats->verticesLoaded = objData.vertices.size();
    stats->facesLoaded = objData.faces.size();
    stats->normalsLoaded = objData.normals.size();
    stats->texCoordsLoaded = objData.texCoords.size();
    
    return mesh;
}

std::shared_ptr<Mesh> MeshLoader::loadPLY(const std::string& filename,
                                          const MeshLoadConfig& config,
                                          MeshLoadStats* stats) {
    // TODO: Implement PLY loading
    stats->addError("PLY format not yet implemented");
    return nullptr;
}

std::shared_ptr<Mesh> MeshLoader::loadSTL(const std::string& filename,
                                          const MeshLoadConfig& config,
                                          MeshLoadStats* stats) {
    // TODO: Implement STL loading
    stats->addError("STL format not yet implemented");
    return nullptr;
}

bool MeshLoader::saveOBJ(const Mesh& mesh, const std::string& filename) {
    std::ofstream file(filename);
    if (!file.is_open()) {
        std::cerr << "Failed to open file for writing: " << filename << std::endl;
        return false;
    }
    
    file << "# PhotonRender OBJ Export\n";
    file << "# Vertices: " << mesh.getVertexCount() << "\n";
    file << "# Faces: " << mesh.getTriangleCount() << "\n\n";
    
    // Write vertices
    const auto& vertices = mesh.getVertices();
    for (size_t i = 0; i < vertices.size(); ++i) {
        const Point3& v = vertices[i].position;
        file << "v " << v.x << " " << v.y << " " << v.z << "\n";
    }
    
    file << "\n";
    
    // Write faces
    const auto& triangles = mesh.getTriangles();
    for (size_t i = 0; i < triangles.size(); ++i) {
        const auto& tri = triangles[i];
        // OBJ indices are 1-based
        file << "f " << (tri.v0 + 1) << " " << (tri.v1 + 1) << " " << (tri.v2 + 1) << "\n";
    }
    
    file.close();
    std::cout << "Mesh saved to: " << filename << std::endl;
    return true;
}

bool MeshLoader::savePLY(const Mesh& mesh, const std::string& filename) {
    // TODO: Implement PLY saving
    std::cerr << "PLY export not yet implemented" << std::endl;
    return false;
}

bool MeshLoader::saveSTL(const Mesh& mesh, const std::string& filename) {
    // TODO: Implement STL saving
    std::cerr << "STL export not yet implemented" << std::endl;
    return false;
}

// Utility functions
std::string MeshLoader::readFileToString(const std::string& filename) {
    std::ifstream file(filename);
    if (!file.is_open()) return "";
    
    std::ostringstream content;
    content << file.rdbuf();
    return content.str();
}

std::string MeshLoader::getFileExtension(const std::string& filename) {
    size_t pos = filename.find_last_of('.');
    if (pos == std::string::npos) return "";
    return filename.substr(pos);
}

std::string MeshLoader::getBasePath(const std::string& filename) {
    size_t pos = filename.find_last_of("/\\");
    if (pos == std::string::npos) return "";
    return filename.substr(0, pos + 1);
}

bool MeshLoader::fileExists(const std::string& filename) {
    std::ifstream file(filename);
    return file.good();
}

std::vector<std::string> MeshLoader::splitString(const std::string& str, char delimiter) {
    std::vector<std::string> tokens;
    std::stringstream ss(str);
    std::string token;
    
    while (std::getline(ss, token, delimiter)) {
        tokens.push_back(trim(token));
    }
    
    return tokens;
}

std::string MeshLoader::trim(const std::string& str) {
    size_t start = str.find_first_not_of(" \t\r\n");
    if (start == std::string::npos) return "";
    
    size_t end = str.find_last_not_of(" \t\r\n");
    return str.substr(start, end - start + 1);
}

// Simple OBJ Parser Implementation
SimpleOBJParser::OBJData SimpleOBJParser::parse(const std::string& filename, MeshLoadStats* stats) {
    OBJData data;

    std::ifstream file(filename);
    if (!file.is_open()) {
        if (stats) stats->addError("Cannot open OBJ file: " + filename);
        return data;
    }

    std::string line;
    int lineNumber = 0;

    while (std::getline(file, line)) {
        lineNumber++;

        // Skip empty lines and comments
        if (line.empty() || line[0] == '#') continue;

        try {
            parseLine(line, data, stats);
        } catch (const std::exception& e) {
            if (stats) {
                stats->addWarning("Error parsing line " + std::to_string(lineNumber) + ": " + e.what());
            }
        }
    }

    file.close();
    return data;
}

std::shared_ptr<Mesh> SimpleOBJParser::convertToMesh(const OBJData& data, const MeshLoadConfig& config) {
    if (!data.isValid()) return nullptr;

    auto mesh = std::make_shared<Mesh>();

    // Convert faces to triangles
    for (size_t i = 0; i < data.faces.size(); ++i) {
        const auto& face = data.faces[i];

        if (face.size() < 3) continue; // Skip invalid faces

        if (face.size() == 3) {
            // Triangle
            Vec3 v1 = data.vertices[face[0] - 1]; // OBJ indices are 1-based
            Vec3 v2 = data.vertices[face[1] - 1];
            Vec3 v3 = data.vertices[face[2] - 1];

            // Apply transform
            v1 = v1 * config.scaleFactor + config.offset;
            v2 = v2 * config.scaleFactor + config.offset;
            v3 = v3 * config.scaleFactor + config.offset;

            mesh->addTriangleFromVertices(Point3(v1), Point3(v2), Point3(v3));
        } else if (face.size() == 4 && config.triangulate) {
            // Quad -> 2 triangles
            Vec3 v1 = data.vertices[face[0] - 1];
            Vec3 v2 = data.vertices[face[1] - 1];
            Vec3 v3 = data.vertices[face[2] - 1];
            Vec3 v4 = data.vertices[face[3] - 1];

            // Apply transform
            v1 = v1 * config.scaleFactor + config.offset;
            v2 = v2 * config.scaleFactor + config.offset;
            v3 = v3 * config.scaleFactor + config.offset;
            v4 = v4 * config.scaleFactor + config.offset;

            mesh->addTriangleFromVertices(Point3(v1), Point3(v2), Point3(v3));
            mesh->addTriangleFromVertices(Point3(v1), Point3(v3), Point3(v4));
        }
    }

    return mesh;
}

void SimpleOBJParser::parseLine(const std::string& line, OBJData& data, MeshLoadStats* stats) {
    std::istringstream iss(line);
    std::string prefix;
    iss >> prefix;

    if (prefix == "v") {
        // Vertex
        data.vertices.push_back(parseVertex(line));
    } else if (prefix == "vn") {
        // Normal
        data.normals.push_back(parseNormal(line));
    } else if (prefix == "vt") {
        // Texture coordinate
        data.texCoords.push_back(parseTexCoord(line));
    } else if (prefix == "f") {
        // Face
        parseFace(line, data);
    } else if (prefix == "usemtl") {
        // Material
        iss >> data.currentMaterial;
    } else if (prefix == "mtllib") {
        // Material library (ignore for now)
        if (stats) stats->addWarning("Material libraries not yet supported");
    }
    // Ignore other commands for now
}

Vec3 SimpleOBJParser::parseVertex(const std::string& line) {
    std::istringstream iss(line);
    std::string prefix;
    float x, y, z;

    iss >> prefix >> x >> y >> z;
    return Vec3(x, y, z);
}

Vec3 SimpleOBJParser::parseNormal(const std::string& line) {
    std::istringstream iss(line);
    std::string prefix;
    float x, y, z;

    iss >> prefix >> x >> y >> z;
    return Vec3(x, y, z);
}

Vec2 SimpleOBJParser::parseTexCoord(const std::string& line) {
    std::istringstream iss(line);
    std::string prefix;
    float u, v = 0;

    iss >> prefix >> u >> v;
    return Vec2(u, v);
}

void SimpleOBJParser::parseFace(const std::string& line, OBJData& data) {
    std::istringstream iss(line);
    std::string prefix;
    iss >> prefix;

    std::vector<int> vertexIndices;
    std::vector<int> normalIndices;
    std::vector<int> texIndices;

    std::string faceVertex;
    while (iss >> faceVertex) {
        auto indices = parseFaceIndices(faceVertex);
        if (!indices.empty()) {
            vertexIndices.push_back(indices[0]);
            if (indices.size() > 1) texIndices.push_back(indices[1]);
            if (indices.size() > 2) normalIndices.push_back(indices[2]);
        }
    }

    if (!vertexIndices.empty()) {
        data.faces.push_back(vertexIndices);
        if (!normalIndices.empty()) data.normalFaces.push_back(normalIndices);
        if (!texIndices.empty()) data.texFaces.push_back(texIndices);
    }
}

std::vector<int> SimpleOBJParser::parseFaceIndices(const std::string& faceStr) {
    std::vector<int> indices;
    std::stringstream ss(faceStr);
    std::string index;

    while (std::getline(ss, index, '/')) {
        if (!index.empty()) {
            indices.push_back(std::stoi(index));
        } else {
            indices.push_back(0); // Missing index
        }
    }

    return indices;
}

} // namespace photon
