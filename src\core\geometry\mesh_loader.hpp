// src/core/geometry/mesh_loader.hpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Mesh loading from OBJ/PLY files

#pragma once

#include "../common.hpp"
#include "mesh.hpp"
#include <string>
#include <memory>
#include <vector>

namespace photon {

/**
 * @brief Mesh file format enumeration
 */
enum class MeshFormat {
    OBJ,
    PLY,
    STL,
    AUTO_DETECT  // Detect from file extension
};

/**
 * @brief Mesh loading configuration
 */
struct MeshLoadConfig {
    bool loadNormals = true;        ///< Load vertex normals
    bool loadTexCoords = true;      ///< Load texture coordinates
    bool loadMaterials = true;      ///< Load material information
    bool generateNormals = true;    ///< Generate normals if missing
    bool flipNormals = false;       ///< Flip normal direction
    bool triangulate = true;        ///< Convert quads to triangles
    float scaleFactor = 1.0f;       ///< Scale factor for vertices
    Vec3 offset = Vec3(0);          ///< Offset for vertices
    std::string basePath = "";      ///< Base path for material files
};

/**
 * @brief Mesh loading statistics
 */
struct MeshLoadStats {
    int verticesLoaded = 0;
    int facesLoaded = 0;
    int normalsLoaded = 0;
    int texCoordsLoaded = 0;
    int materialsLoaded = 0;
    float loadTimeMs = 0.0f;
    std::vector<std::string> warnings;
    std::vector<std::string> errors;
    
    void addWarning(const std::string& warning) {
        warnings.push_back(warning);
    }
    
    void addError(const std::string& error) {
        errors.push_back(error);
    }
    
    bool hasErrors() const {
        return !errors.empty();
    }
    
    void print() const {
        std::cout << "Mesh Load Statistics:" << std::endl;
        std::cout << "  Vertices: " << verticesLoaded << std::endl;
        std::cout << "  Faces: " << facesLoaded << std::endl;
        std::cout << "  Normals: " << normalsLoaded << std::endl;
        std::cout << "  TexCoords: " << texCoordsLoaded << std::endl;
        std::cout << "  Materials: " << materialsLoaded << std::endl;
        std::cout << "  Load Time: " << loadTimeMs << "ms" << std::endl;
        
        if (!warnings.empty()) {
            std::cout << "  Warnings:" << std::endl;
            for (const auto& warning : warnings) {
                std::cout << "    - " << warning << std::endl;
            }
        }
        
        if (!errors.empty()) {
            std::cout << "  Errors:" << std::endl;
            for (const auto& error : errors) {
                std::cout << "    - " << error << std::endl;
            }
        }
    }
};

/**
 * @brief Mesh loader class
 */
class MeshLoader {
public:
    /**
     * @brief Load mesh from file
     * 
     * @param filename Mesh file path
     * @param config Loading configuration
     * @param stats Output loading statistics
     * @return Loaded mesh or nullptr on failure
     */
    static std::shared_ptr<Mesh> loadFromFile(const std::string& filename,
                                             const MeshLoadConfig& config = MeshLoadConfig(),
                                             MeshLoadStats* stats = nullptr);
    
    /**
     * @brief Load multiple meshes from file
     * 
     * @param filename Mesh file path
     * @param config Loading configuration
     * @param stats Output loading statistics
     * @return Vector of loaded meshes
     */
    static std::vector<std::shared_ptr<Mesh>> loadMultipleFromFile(const std::string& filename,
                                                                  const MeshLoadConfig& config = MeshLoadConfig(),
                                                                  MeshLoadStats* stats = nullptr);
    
    /**
     * @brief Save mesh to file
     * 
     * @param mesh Mesh to save
     * @param filename Output file path
     * @param format Output format (auto-detect from extension if AUTO_DETECT)
     * @return true if successful, false otherwise
     */
    static bool saveToFile(const Mesh& mesh, const std::string& filename,
                          MeshFormat format = MeshFormat::AUTO_DETECT);
    
    /**
     * @brief Detect mesh format from file extension
     * 
     * @param filename File path
     * @return Detected format
     */
    static MeshFormat detectFormat(const std::string& filename);
    
    /**
     * @brief Validate mesh file without loading
     * 
     * @param filename Mesh file path
     * @return true if valid, false otherwise
     */
    static bool validateFile(const std::string& filename);
    
    /**
     * @brief Create a simple test mesh (cube)
     * 
     * @return Test mesh
     */
    static std::shared_ptr<Mesh> createTestCube();
    
    /**
     * @brief Create a simple test mesh (sphere)
     * 
     * @param radius Sphere radius
     * @param subdivisions Number of subdivisions
     * @return Test mesh
     */
    static std::shared_ptr<Mesh> createTestSphere(float radius = 1.0f, int subdivisions = 16);
    
    /**
     * @brief Create a simple test mesh (plane)
     * 
     * @param width Plane width
     * @param height Plane height
     * @param subdivisions Number of subdivisions
     * @return Test mesh
     */
    static std::shared_ptr<Mesh> createTestPlane(float width = 2.0f, float height = 2.0f, int subdivisions = 1);

private:
    // Format-specific loading functions
    static std::shared_ptr<Mesh> loadOBJ(const std::string& filename,
                                        const MeshLoadConfig& config,
                                        MeshLoadStats* stats);
    
    static std::shared_ptr<Mesh> loadPLY(const std::string& filename,
                                        const MeshLoadConfig& config,
                                        MeshLoadStats* stats);
    
    static std::shared_ptr<Mesh> loadSTL(const std::string& filename,
                                        const MeshLoadConfig& config,
                                        MeshLoadStats* stats);
    
    // Format-specific saving functions
    static bool saveOBJ(const Mesh& mesh, const std::string& filename);
    static bool savePLY(const Mesh& mesh, const std::string& filename);
    static bool saveSTL(const Mesh& mesh, const std::string& filename);
    
    // Utility functions
    static std::string readFileToString(const std::string& filename);
    static std::string getFileExtension(const std::string& filename);
    static std::string getBasePath(const std::string& filename);
    static bool fileExists(const std::string& filename);
    static std::vector<std::string> splitString(const std::string& str, char delimiter);
    static std::string trim(const std::string& str);
    
    // Mesh processing functions
    static void generateNormals(Mesh& mesh);
    static void flipNormals(Mesh& mesh);
    static void triangulate(Mesh& mesh);
    static void applyTransform(Mesh& mesh, const MeshLoadConfig& config);
};

/**
 * @brief Simple OBJ parser for mesh loading
 */
class SimpleOBJParser {
public:
    struct OBJData {
        std::vector<Vec3> vertices;
        std::vector<Vec3> normals;
        std::vector<Vec2> texCoords;
        std::vector<std::vector<int>> faces;        // Vertex indices
        std::vector<std::vector<int>> normalFaces;  // Normal indices
        std::vector<std::vector<int>> texFaces;     // Texture indices
        std::vector<std::string> materialNames;
        std::string currentMaterial;
        
        void clear() {
            vertices.clear();
            normals.clear();
            texCoords.clear();
            faces.clear();
            normalFaces.clear();
            texFaces.clear();
            materialNames.clear();
            currentMaterial.clear();
        }
        
        bool isValid() const {
            return !vertices.empty() && !faces.empty();
        }
    };
    
    static OBJData parse(const std::string& filename, MeshLoadStats* stats = nullptr);
    static std::shared_ptr<Mesh> convertToMesh(const OBJData& data, const MeshLoadConfig& config);

private:
    static void parseLine(const std::string& line, OBJData& data, MeshLoadStats* stats);
    static Vec3 parseVertex(const std::string& line);
    static Vec3 parseNormal(const std::string& line);
    static Vec2 parseTexCoord(const std::string& line);
    static void parseFace(const std::string& line, OBJData& data);
    static std::vector<int> parseFaceIndices(const std::string& faceStr);
};

} // namespace photon
