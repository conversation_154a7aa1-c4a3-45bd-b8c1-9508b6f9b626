// src/core/image/image_io.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Image input/output implementation

#include "image_io.hpp"
#ifdef PHOTON_SIMPLE_BUILD
#include "../common_simple.hpp"
#else
#include "../renderer.hpp"
#endif
#include <iostream>
#include <algorithm>
#include <cmath>

// STB Image includes
#define STB_IMAGE_IMPLEMENTATION
#define STB_IMAGE_WRITE_IMPLEMENTATION
#include <stb_image.h>
#include <stb_image_write.h>

namespace photon {

bool ImageIO::saveImage(const std::string& filename, const ImageData& image, int quality) {
    if (!image.isValid()) {
        std::cerr << "Error: Invalid image data" << std::endl;
        return false;
    }
    
    ImageFormat format = getFormatFromExtension(filename);
    
    switch (format) {
        case ImageFormat::PNG:
            return savePNG(filename, image);
        case ImageFormat::JPEG:
            return saveJPEG(filename, image, quality);
        case ImageFormat::EXR:
            return saveEXR(filename, image);
        case ImageFormat::HDR:
            return saveHDR(filename, image);
        case ImageFormat::BMP:
            return saveBMP(filename, image);
        case ImageFormat::TGA:
            return saveTGA(filename, image);
        default:
            std::cerr << "Error: Unsupported image format for file: " << filename << std::endl;
            return false;
    }
}

bool ImageIO::saveFromFilm(const std::string& filename, const Film& film, int quality) {
#ifdef PHOTON_SIMPLE_BUILD
    // Simplified version - not implemented for simple build
    std::cerr << "Error: saveFromFilm not available in simplified build" << std::endl;
    return false;
#else
    // Create ImageData from Film
    ImageData image(film.getWidth(), film.getHeight(), 3);

    for (int y = 0; y < film.getHeight(); ++y) {
        for (int x = 0; x < film.getWidth(); ++x) {
            Color3 pixel = film.getPixel(x, y);
            image.setPixel(x, y, pixel);
        }
    }

    return saveImage(filename, image, quality);
#endif
}

bool ImageIO::saveFromPixels(const std::string& filename, const float* pixels, 
                            int width, int height, int quality) {
    if (!pixels || width <= 0 || height <= 0) {
        std::cerr << "Error: Invalid pixel data" << std::endl;
        return false;
    }
    
    // Create ImageData from raw pixels
    ImageData image(width, height, 3);
    std::copy(pixels, pixels + (width * height * 3), image.pixels.begin());
    
    return saveImage(filename, image, quality);
}

ImageData ImageIO::loadImage(const std::string& filename) {
    ImageFormat format = getFormatFromExtension(filename);
    
    switch (format) {
        case ImageFormat::EXR:
            return loadEXR(filename);
        default:
            return loadSTB(filename);
    }
}

ImageFormat ImageIO::getFormatFromExtension(const std::string& filename) {
    std::string ext = toLower(getFileExtension(filename));
    
    if (ext == ".png") return ImageFormat::PNG;
    if (ext == ".jpg" || ext == ".jpeg") return ImageFormat::JPEG;
    if (ext == ".exr") return ImageFormat::EXR;
    if (ext == ".hdr") return ImageFormat::HDR;
    if (ext == ".bmp") return ImageFormat::BMP;
    if (ext == ".tga") return ImageFormat::TGA;
    
    return ImageFormat::PNG; // Default fallback
}

bool ImageIO::isFormatSupported(ImageFormat format) {
    switch (format) {
        case ImageFormat::PNG:
        case ImageFormat::JPEG:
        case ImageFormat::BMP:
        case ImageFormat::TGA:
            return true;
        case ImageFormat::EXR:
        case ImageFormat::HDR:
            return false; // TODO: Implement EXR/HDR support
        default:
            return false;
    }
}

void ImageIO::toneMap(ImageData& image, float exposure, float gamma) {
    float exposureFactor = std::pow(2.0f, exposure);
    float invGamma = 1.0f / gamma;
    
    for (size_t i = 0; i < image.pixels.size(); i += image.channels) {
        // Apply exposure
        float r = image.pixels[i] * exposureFactor;
        float g = image.pixels[i + 1] * exposureFactor;
        float b = image.pixels[i + 2] * exposureFactor;
        
        // Simple Reinhard tone mapping
        r = r / (1.0f + r);
        g = g / (1.0f + g);
        b = b / (1.0f + b);
        
        // Gamma correction
        r = std::pow(r, invGamma);
        g = std::pow(g, invGamma);
        b = std::pow(b, invGamma);
        
        // Clamp to [0, 1]
        image.pixels[i] = std::clamp(r, 0.0f, 1.0f);
        image.pixels[i + 1] = std::clamp(g, 0.0f, 1.0f);
        image.pixels[i + 2] = std::clamp(b, 0.0f, 1.0f);
    }
}

void ImageIO::hdrToLdr(const float* hdrPixels, unsigned char* ldrPixels,
                      int width, int height, float exposure, float gamma) {
    float exposureFactor = std::pow(2.0f, exposure);
    float invGamma = 1.0f / gamma;
    
    for (int i = 0; i < width * height * 3; i += 3) {
        // Apply exposure
        float r = hdrPixels[i] * exposureFactor;
        float g = hdrPixels[i + 1] * exposureFactor;
        float b = hdrPixels[i + 2] * exposureFactor;
        
        // Simple Reinhard tone mapping
        r = r / (1.0f + r);
        g = g / (1.0f + g);
        b = b / (1.0f + b);
        
        // Gamma correction
        r = std::pow(r, invGamma);
        g = std::pow(g, invGamma);
        b = std::pow(b, invGamma);
        
        // Convert to 8-bit
        ldrPixels[i] = static_cast<unsigned char>(std::clamp(r * 255.0f, 0.0f, 255.0f));
        ldrPixels[i + 1] = static_cast<unsigned char>(std::clamp(g * 255.0f, 0.0f, 255.0f));
        ldrPixels[i + 2] = static_cast<unsigned char>(std::clamp(b * 255.0f, 0.0f, 255.0f));
    }
}

// Private implementation functions
bool ImageIO::savePNG(const std::string& filename, const ImageData& image) {
    // Convert float to 8-bit
    std::vector<unsigned char> ldrPixels(image.width * image.height * 3);
    hdrToLdr(image.data(), ldrPixels.data(), image.width, image.height);
    
    int result = stbi_write_png(filename.c_str(), image.width, image.height, 3, 
                               ldrPixels.data(), image.width * 3);
    
    if (!result) {
        std::cerr << "Error: Failed to save PNG file: " << filename << std::endl;
        return false;
    }
    
    std::cout << "Saved PNG: " << filename << " (" << image.width << "x" << image.height << ")" << std::endl;
    return true;
}

bool ImageIO::saveJPEG(const std::string& filename, const ImageData& image, int quality) {
    // Convert float to 8-bit
    std::vector<unsigned char> ldrPixels(image.width * image.height * 3);
    hdrToLdr(image.data(), ldrPixels.data(), image.width, image.height);
    
    int result = stbi_write_jpg(filename.c_str(), image.width, image.height, 3, 
                               ldrPixels.data(), quality);
    
    if (!result) {
        std::cerr << "Error: Failed to save JPEG file: " << filename << std::endl;
        return false;
    }
    
    std::cout << "Saved JPEG: " << filename << " (" << image.width << "x" << image.height 
              << ", quality=" << quality << ")" << std::endl;
    return true;
}

bool ImageIO::saveEXR(const std::string& filename, const ImageData& image) {
    // TODO: Implement EXR saving (requires OpenEXR library)
    std::cerr << "Error: EXR format not yet implemented" << std::endl;
    return false;
}

bool ImageIO::saveHDR(const std::string& filename, const ImageData& image) {
    int result = stbi_write_hdr(filename.c_str(), image.width, image.height, 3, image.data());
    
    if (!result) {
        std::cerr << "Error: Failed to save HDR file: " << filename << std::endl;
        return false;
    }
    
    std::cout << "Saved HDR: " << filename << " (" << image.width << "x" << image.height << ")" << std::endl;
    return true;
}

bool ImageIO::saveBMP(const std::string& filename, const ImageData& image) {
    // Convert float to 8-bit
    std::vector<unsigned char> ldrPixels(image.width * image.height * 3);
    hdrToLdr(image.data(), ldrPixels.data(), image.width, image.height);
    
    int result = stbi_write_bmp(filename.c_str(), image.width, image.height, 3, ldrPixels.data());
    
    if (!result) {
        std::cerr << "Error: Failed to save BMP file: " << filename << std::endl;
        return false;
    }
    
    std::cout << "Saved BMP: " << filename << " (" << image.width << "x" << image.height << ")" << std::endl;
    return true;
}

bool ImageIO::saveTGA(const std::string& filename, const ImageData& image) {
    // Convert float to 8-bit
    std::vector<unsigned char> ldrPixels(image.width * image.height * 3);
    hdrToLdr(image.data(), ldrPixels.data(), image.width, image.height);
    
    int result = stbi_write_tga(filename.c_str(), image.width, image.height, 3, ldrPixels.data());
    
    if (!result) {
        std::cerr << "Error: Failed to save TGA file: " << filename << std::endl;
        return false;
    }
    
    std::cout << "Saved TGA: " << filename << " (" << image.width << "x" << image.height << ")" << std::endl;
    return true;
}

ImageData ImageIO::loadSTB(const std::string& filename) {
    int width, height, channels;
    float* data = stbi_loadf(filename.c_str(), &width, &height, &channels, 0);
    
    if (!data) {
        std::cerr << "Error: Failed to load image: " << filename << std::endl;
        return ImageData();
    }
    
    ImageData image(width, height, channels);
    std::copy(data, data + (width * height * channels), image.pixels.begin());
    
    stbi_image_free(data);
    
    std::cout << "Loaded image: " << filename << " (" << width << "x" << height 
              << ", " << channels << " channels)" << std::endl;
    return image;
}

ImageData ImageIO::loadEXR(const std::string& filename) {
    // TODO: Implement EXR loading (requires OpenEXR library)
    std::cerr << "Error: EXR format not yet implemented" << std::endl;
    return ImageData();
}

// Utility functions
std::string ImageIO::getFileExtension(const std::string& filename) {
    size_t pos = filename.find_last_of('.');
    if (pos == std::string::npos) return "";
    return filename.substr(pos);
}

std::string ImageIO::toLower(const std::string& str) {
    std::string result = str;
    std::transform(result.begin(), result.end(), result.begin(), ::tolower);
    return result;
}

} // namespace photon
