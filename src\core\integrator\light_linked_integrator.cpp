// src/core/integrator/light_linked_integrator.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Light Linking aware integrator implementation

#include "light_linked_integrator.hpp"
#include "../scene/scene.hpp"
#include "../scene/light.hpp"
#include "../material/material.hpp"
#include "../common.hpp"
#include <algorithm>
#include <chrono>

namespace photon {

LightLinkedIntegrator::LightLinkedIntegrator(std::unique_ptr<Integrator> baseIntegrator, bool enableLightLinking)
    : m_baseIntegrator(std::move(baseIntegrator)), m_lightLinkingEnabled(enableLightLinking) {
    resetLightLinkingStats();
}

Color3 LightLinkedIntegrator::Li(const Ray& ray, const Scene& scene, Sampler& sampler) const {
    if (!m_lightLinkingEnabled || !scene.getLightLinkingManager().isEnabled()) {
        // Light linking disabled: use base integrator directly
        return m_baseIntegrator->Li(ray, scene, sampler);
    }
    
    // For now, delegate to base integrator
    // The light linking is handled at the scene level through getEffectiveLights()
    // This ensures compatibility with existing integrators
    return m_baseIntegrator->Li(ray, scene, sampler);
}

std::string LightLinkedIntegrator::getName() const {
    return "LightLinked(" + m_baseIntegrator->getName() + ")";
}

void LightLinkedIntegrator::preprocess(const Scene& scene) {
    // Preprocess base integrator
    m_baseIntegrator->preprocess(scene);
    
    // Optimize light linking if enabled
    if (m_lightLinkingEnabled) {
        const_cast<Scene&>(scene).getLightLinkingManager().optimize();
    }
    
    resetLightLinkingStats();
}

Color3 LightLinkedIntegrator::sampleDirectLightingLinked(const Intersection& isect, const Scene& scene,
                                                        Sampler& sampler, const Vec3& wo) const {
    auto startTime = std::chrono::high_resolution_clock::now();
    
    // Get effective lights for this intersection
    std::vector<std::shared_ptr<Light>> effectiveLights = scene.getEffectiveLights(isect);
    
    // Calculate overhead
    auto endTime = std::chrono::high_resolution_clock::now();
    float overhead = std::chrono::duration<float, std::nano>(endTime - startTime).count();
    
    // Update statistics
    updateStats(scene.getLights().size(), effectiveLights.size(), overhead);
    
    // Sample effective lights
    Color3 L(0);
    for (const auto& light : effectiveLights) {
        L += LightLinkedDirectLighting::sampleOneLight(isect, *light, scene, sampler, wo, 
                                                      scene.getLightLinkingManager());
    }
    
    return L;
}

void LightLinkedIntegrator::updateStats(size_t totalLights, size_t effectiveLights, float overhead) const {
    m_stats.totalIntersections++;
    m_stats.totalLightQueries += totalLights;
    
    // Running averages
    float alpha = 1.0f / m_stats.totalIntersections;
    m_stats.averageEffectiveLights = (1.0f - alpha) * m_stats.averageEffectiveLights + 
                                    alpha * effectiveLights;
    m_stats.linkingOverhead = (1.0f - alpha) * m_stats.linkingOverhead + alpha * overhead;
    
    // Calculate culling ratio
    if (totalLights > 0) {
        float currentCullingRatio = 1.0f - (static_cast<float>(effectiveLights) / totalLights);
        m_stats.lightCullingRatio = (1.0f - alpha) * m_stats.lightCullingRatio + 
                                   alpha * currentCullingRatio;
    }
}

void LightLinkedIntegrator::resetLightLinkingStats() {
    m_stats = LightLinkingStats{};
}

// Factory implementations
namespace LightLinkedIntegratorFactory {

std::unique_ptr<LightLinkedIntegrator> createPathTracing(int maxDepth, int rrDepth, int lightSamples) {
    auto pathTracer = std::make_unique<PathTracingIntegrator>(maxDepth, rrDepth, lightSamples);
    return std::make_unique<LightLinkedIntegrator>(std::move(pathTracer), true);
}

std::unique_ptr<LightLinkedIntegrator> createMIS(int maxDepth, int rrDepth, 
                                                int lightSamples, int bsdfSamples,
                                                MISStrategy strategy) {
    auto misIntegrator = std::make_unique<MISIntegrator>(maxDepth, rrDepth, lightSamples, bsdfSamples, strategy);
    return std::make_unique<LightLinkedIntegrator>(std::move(misIntegrator), true);
}

std::unique_ptr<LightLinkedIntegrator> createDirectLighting() {
    auto directLighting = std::make_unique<DirectLightingIntegrator>();
    return std::make_unique<LightLinkedIntegrator>(std::move(directLighting), true);
}

std::unique_ptr<LightLinkedIntegrator> wrapIntegrator(std::unique_ptr<Integrator> integrator) {
    return std::make_unique<LightLinkedIntegrator>(std::move(integrator), true);
}

} // namespace LightLinkedIntegratorFactory

// LightLinkedDirectLighting implementations
Color3 LightLinkedDirectLighting::sample(const Intersection& isect, const Scene& scene, Sampler& sampler,
                                         const Vec3& wo, const LightLinkingManager& lightLinkingManager) {
    // Get effective lights
    std::vector<std::shared_ptr<Light>> effectiveLights = 
        lightLinkingManager.getEffectiveLights(scene.getLights(), isect);
    
    Color3 L(0);
    for (const auto& light : effectiveLights) {
        L += sampleOneLight(isect, *light, scene, sampler, wo, lightLinkingManager);
    }
    
    return L;
}

Color3 LightLinkedDirectLighting::sampleOneLight(const Intersection& isect, const Light& light, const Scene& scene,
                                                Sampler& sampler, const Vec3& wo, 
                                                const LightLinkingManager& lightLinkingManager) {
    // Check if light should illuminate this intersection
    auto lightPtr = std::shared_ptr<Light>(const_cast<Light*>(&light), [](Light*){});
    if (!lightLinkingManager.shouldIlluminate(lightPtr, isect)) {
        return Color3(0);
    }
    
    // Sample light
    LightSample lightSample = light.sample(isect, sampler);
    if (!lightSample.isValid()) return Color3(0);
    
    // Check visibility
    Ray shadowRay(isect.p, lightSample.wi, 0.001f, lightSample.distance - 0.001f);
    if (scene.intersectShadow(shadowRay)) {
        return Color3(0); // Occluded
    }
    
    // Evaluate BSDF
    if (!isect.material) return Color3(0);
    Color3 f = isect.material->f(isect, wo, lightSample.wi);
    
    // Compute contribution
    float cosTheta = std::max(0.0f, lightSample.wi.dot(isect.n));
    return f * lightSample.Li * cosTheta / lightSample.pdf;
}

size_t LightLinkedDirectLighting::getEffectiveLightCount(const Intersection& isect, 
                                                        const std::vector<std::shared_ptr<Light>>& allLights,
                                                        const LightLinkingManager& lightLinkingManager) {
    if (!lightLinkingManager.isEnabled()) {
        return allLights.size();
    }
    
    size_t count = 0;
    for (const auto& light : allLights) {
        if (lightLinkingManager.shouldIlluminate(light, isect)) {
            count++;
        }
    }
    
    return count;
}

} // namespace photon
