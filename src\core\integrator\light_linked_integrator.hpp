// src/core/integrator/light_linked_integrator.hpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Light Linking aware integrator wrapper

#pragma once

#include "integrator.hpp"
#include "mis_integrator.hpp"
#include "../scene/light_linking.hpp"
#include <memory>

namespace photon {

/**
 * @brief Light linking aware integrator wrapper
 * 
 * Wraps existing integrators to add light linking functionality
 * without modifying the original integrator implementations.
 */
class LightLinkedIntegrator : public Integrator {
public:
    /**
     * @brief Constructor
     * 
     * @param baseIntegrator Base integrator to wrap
     * @param enableLightLinking Enable light linking (default: true)
     */
    LightLinkedIntegrator(std::unique_ptr<Integrator> baseIntegrator, bool enableLightLinking = true);
    
    /**
     * @brief Destructor
     */
    ~LightLinkedIntegrator() = default;
    
    // Non-copyable
    LightLinkedIntegrator(const LightLinkedIntegrator&) = delete;
    LightLinkedIntegrator& operator=(const LightLinkedIntegrator&) = delete;
    
    // Integrator interface
    Color3 Li(const Ray& ray, const Scene& scene, Sampler& sampler) const override;
    std::string getName() const override;
    void preprocess(const Scene& scene) override;
    
    /**
     * @brief Enable/disable light linking
     */
    void setLightLinkingEnabled(bool enabled) { m_lightLinkingEnabled = enabled; }
    
    /**
     * @brief Check if light linking is enabled
     */
    bool isLightLinkingEnabled() const { return m_lightLinkingEnabled; }
    
    /**
     * @brief Get base integrator
     */
    Integrator* getBaseIntegrator() const { return m_baseIntegrator.get(); }
    
    /**
     * @brief Get light linking performance statistics
     */
    struct LightLinkingStats {
        float averageEffectiveLights = 0.0f;  ///< Average lights per intersection
        float lightCullingRatio = 0.0f;      ///< Percentage of lights culled
        float linkingOverhead = 0.0f;        ///< Overhead in nanoseconds
        size_t totalIntersections = 0;       ///< Total intersections processed
        size_t totalLightQueries = 0;        ///< Total light queries made
    };
    
    /**
     * @brief Get light linking statistics
     */
    LightLinkingStats getLightLinkingStats() const { return m_stats; }
    
    /**
     * @brief Reset light linking statistics
     */
    void resetLightLinkingStats();

private:
    std::unique_ptr<Integrator> m_baseIntegrator;
    bool m_lightLinkingEnabled;
    mutable LightLinkingStats m_stats;
    
    /**
     * @brief Sample direct lighting with light linking
     * 
     * @param isect Surface intersection
     * @param scene Scene containing lights
     * @param sampler Random sampler
     * @param wo Outgoing direction
     * @return Direct lighting contribution
     */
    Color3 sampleDirectLightingLinked(const Intersection& isect, const Scene& scene,
                                     Sampler& sampler, const Vec3& wo) const;
    
    /**
     * @brief Update light linking statistics
     */
    void updateStats(size_t totalLights, size_t effectiveLights, float overhead) const;
};

/**
 * @brief Factory for creating light linked integrators
 */
namespace LightLinkedIntegratorFactory {
    
    /**
     * @brief Create light linked path tracing integrator
     * 
     * @param maxDepth Maximum path depth
     * @param rrDepth Russian roulette depth
     * @param lightSamples Light samples per intersection
     * @return Light linked path tracing integrator
     */
    std::unique_ptr<LightLinkedIntegrator> createPathTracing(int maxDepth = 8, int rrDepth = 3, int lightSamples = 1);
    
    /**
     * @brief Create light linked MIS integrator
     * 
     * @param maxDepth Maximum path depth
     * @param rrDepth Russian roulette depth
     * @param lightSamples Light samples per intersection
     * @param bsdfSamples BSDF samples per intersection
     * @param strategy MIS strategy
     * @return Light linked MIS integrator
     */
    std::unique_ptr<LightLinkedIntegrator> createMIS(int maxDepth = 8, int rrDepth = 3, 
                                                    int lightSamples = 1, int bsdfSamples = 1,
                                                    MISStrategy strategy = MISStrategy::POWER_HEURISTIC);
    
    /**
     * @brief Create light linked direct lighting integrator
     * 
     * @return Light linked direct lighting integrator
     */
    std::unique_ptr<LightLinkedIntegrator> createDirectLighting();
    
    /**
     * @brief Wrap existing integrator with light linking
     * 
     * @param integrator Existing integrator to wrap
     * @return Light linked wrapper
     */
    std::unique_ptr<LightLinkedIntegrator> wrapIntegrator(std::unique_ptr<Integrator> integrator);
}

/**
 * @brief Light linking aware direct lighting sampler
 * 
 * Utility class for sampling direct lighting with light linking support
 */
class LightLinkedDirectLighting {
public:
    /**
     * @brief Sample direct lighting with light linking
     * 
     * @param isect Surface intersection
     * @param scene Scene containing lights
     * @param sampler Random sampler
     * @param wo Outgoing direction
     * @param lightLinkingManager Light linking manager
     * @return Direct lighting contribution
     */
    static Color3 sample(const Intersection& isect, const Scene& scene, Sampler& sampler,
                        const Vec3& wo, const LightLinkingManager& lightLinkingManager);
    
    /**
     * @brief Sample single light with light linking check
     * 
     * @param isect Surface intersection
     * @param light Light to sample
     * @param scene Scene for visibility testing
     * @param sampler Random sampler
     * @param wo Outgoing direction
     * @param lightLinkingManager Light linking manager
     * @return Light contribution
     */
    static Color3 sampleOneLight(const Intersection& isect, const Light& light, const Scene& scene,
                                Sampler& sampler, const Vec3& wo, const LightLinkingManager& lightLinkingManager);
    
    /**
     * @brief Get effective light count for intersection
     * 
     * @param isect Surface intersection
     * @param allLights All lights in scene
     * @param lightLinkingManager Light linking manager
     * @return Number of effective lights
     */
    static size_t getEffectiveLightCount(const Intersection& isect, 
                                        const std::vector<std::shared_ptr<Light>>& allLights,
                                        const LightLinkingManager& lightLinkingManager);
};

} // namespace photon
