// src/core/integrator/mis_integrator.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Multiple Importance Sampling integrator implementation

#include "mis_integrator.hpp"
#include "../scene/scene.hpp"
#include "../scene/light.hpp"
#include "../material/material.hpp"
#include "../common.hpp"
#include <algorithm>
#include <cmath>
#include <chrono>

namespace photon {

MISIntegrator::MISIntegrator(int maxDepth, int rrDepth, int lightSamples, int bsdfSamples,
                            MISStrategy strategy)
    : m_maxDepth(maxDepth), m_rrDepth(rrDepth), m_directLightingOnly(false), 
      m_varianceReduction(true) {
    
    m_misSampling = std::make_unique<MISSampling>(strategy, lightSamples, bsdfSamples);
    m_metrics = PerformanceMetrics{};
}

void MISIntegrator::preprocess(const Scene& scene) {
    // Reset statistics for new render
    resetMISStatistics();
    m_metrics = PerformanceMetrics{};
}

Color3 MISIntegrator::Li(const Ray& ray, const Scene& scene, Sampler& sampler) const {
    auto startTime = std::chrono::high_resolution_clock::now();
    
    Color3 L(0);
    Color3 beta(1); // Path throughput
    Ray currentRay = ray;
    
    for (int depth = 0; depth < m_maxDepth; ++depth) {
        // Find intersection
        Intersection isect;
        if (!scene.intersect(currentRay, isect)) {
            // Hit environment/background
            const auto& lights = scene.getLights();
            for (const auto& light : lights) {
                if (!light->isDelta()) {
                    L += beta * light->Li(isect, currentRay.d);
                }
            }
            break;
        }
        
        // Add material emission
        if (isect.material) {
            L += beta * isect.material->Le(isect, -currentRay.d);
        }
        
        // Sample direct lighting using MIS
        L += beta * sampleDirectLightingMIS(isect, scene, sampler, -currentRay.d);
        
        // Stop if direct lighting only mode
        if (m_directLightingOnly) break;
        
        // Sample BSDF for next direction
        if (!isect.material) break;
        
        BSDFSample bsdfSample;
        if (m_varianceReduction) {
            bsdfSample = sampleBSDFWithVarianceReduction(isect, -currentRay.d, sampler);
        } else {
            bsdfSample = isect.material->sample(isect, -currentRay.d, sampler);
        }
        
        if (!bsdfSample.isValid()) break;
        
        // Update path throughput
        beta = calculatePathThroughput(beta, bsdfSample, isect.n);
        
        // Russian roulette termination
        if (!russianRoulette(beta, depth, sampler)) break;
        
        // Generate next ray
        currentRay = Ray(isect.p, bsdfSample.wi);
    }
    
    // Update performance metrics
    auto endTime = std::chrono::high_resolution_clock::now();
    float renderTime = std::chrono::duration<float, std::milli>(endTime - startTime).count();
    
    // Estimate noise reduction (simplified)
    Color3 standardResult = L * 0.8f; // Placeholder for comparison
    float noiseReduction = estimateNoiseReduction(L, standardResult);
    
    updatePerformanceMetrics(renderTime, noiseReduction);
    
    return L;
}

Color3 MISIntegrator::sampleDirectLightingMIS(const Intersection& isect, const Scene& scene,
                                             Sampler& sampler, const Vec3& wo) const {
    // Fast path for single light scenes (optimization)
    const auto& lights = scene.getLights();
    if (lights.size() == 1 && !m_varianceReduction) {
        // Direct sampling for single light (faster than MIS)
        const auto& light = lights[0];
        LightSample lightSample = light->sample(isect, sampler);

        if (lightSample.isValid() && isect.material) {
            // Quick visibility check
            Ray shadowRay(isect.p, lightSample.wi, 0.001f, lightSample.distance - 0.001f);
            if (!scene.intersectShadow(shadowRay)) {
                Color3 bsdfValue = isect.material->f(isect, wo, lightSample.wi);
                float cosTheta = std::max(0.0f, lightSample.wi.dot(isect.n));
                return bsdfValue * lightSample.Li * cosTheta / lightSample.pdf;
            }
        }
        return Color3(0);
    }

    // Use MIS framework for complex scenes
    MISSample misSample = m_misSampling->sampleDirectLighting(isect, scene, sampler, wo);

    if (misSample.isValid) {
        return misSample.Li;
    }

    return Color3(0);
}

BSDFSample MISIntegrator::sampleBSDFWithVarianceReduction(const Intersection& isect, 
                                                         const Vec3& wo, Sampler& sampler) const {
    if (!isect.material) return BSDFSample();
    
    // Standard BSDF sampling with potential variance reduction
    BSDFSample sample = isect.material->sample(isect, wo, sampler);
    
    if (m_varianceReduction && sample.isValid()) {
        // Apply variance reduction techniques
        // For now, just ensure the sample is well-conditioned
        if (sample.pdf < 1e-6f) {
            return BSDFSample(); // Invalid sample
        }
        
        // Clamp extreme values to reduce variance
        sample.f = Color3(
            std::min(sample.f.r, 10.0f),
            std::min(sample.f.g, 10.0f),
            std::min(sample.f.b, 10.0f)
        );
    }
    
    return sample;
}

Color3 MISIntegrator::evaluateBSDF(const Intersection& isect, const Vec3& wo, const Vec3& wi) const {
    if (!isect.material) return Color3(0);
    return isect.material->f(isect, wo, wi);
}

float MISIntegrator::getBSDFPdf(const Intersection& isect, const Vec3& wo, const Vec3& wi) const {
    if (!isect.material) return 0.0f;
    return isect.material->pdf(isect, wo, wi);
}

bool MISIntegrator::russianRoulette(const Color3& beta, int depth, Sampler& sampler) const {
    if (depth < m_rrDepth) return true;
    
    // Calculate continuation probability based on path throughput
    float maxComponent = beta.maxComponent();
    float q = std::max(0.05f, 1.0f - maxComponent);
    
    if (sampler.get1D() < q) {
        return false; // Terminate path
    }
    
    return true; // Continue path (throughput will be adjusted by caller)
}

Color3 MISIntegrator::calculatePathThroughput(const Color3& beta, const BSDFSample& bsdfSample,
                                             const Vec3& normal) const {
    float cosTheta = std::max(0.0f, bsdfSample.wi.dot(normal));
    Color3 newBeta = beta * bsdfSample.f * cosTheta / bsdfSample.pdf;
    
    if (m_varianceReduction) {
        // Apply variance reduction to throughput
        float maxComponent = newBeta.maxComponent();
        if (maxComponent > 100.0f) {
            // Clamp extreme throughput values
            newBeta *= (100.0f / maxComponent);
        }
    }
    
    return newBeta;
}

void MISIntegrator::updatePerformanceMetrics(float renderTime, float noiseReduction) const {
    m_metrics.totalSamples++;
    m_metrics.pathsTraced++;
    
    // Running averages
    float alpha = 1.0f / m_metrics.totalSamples;
    m_metrics.avgRenderTime = (1.0f - alpha) * m_metrics.avgRenderTime + alpha * renderTime;
    m_metrics.noiseReduction = (1.0f - alpha) * m_metrics.noiseReduction + alpha * noiseReduction;
    
    // Estimate convergence rate improvement
    m_metrics.convergenceRate = std::min(m_metrics.noiseReduction * 1.5f, 50.0f);
}

float MISIntegrator::estimateNoiseReduction(const Color3& misResult, const Color3& standardResult) const {
    // Simplified noise reduction estimation
    float misVariance = misResult.variance();
    float standardVariance = standardResult.variance();
    
    if (standardVariance > 0.0f) {
        float reduction = (standardVariance - misVariance) / standardVariance;
        return std::max(0.0f, std::min(reduction * 100.0f, 50.0f));
    }
    
    return 0.0f;
}

// MIS Integrator Factory implementations
namespace MISIntegratorFactory {

std::unique_ptr<MISIntegrator> createHighQuality() {
    auto integrator = std::make_unique<MISIntegrator>(
        12,  // maxDepth
        4,   // rrDepth
        2,   // lightSamples
        2,   // bsdfSamples
        MISStrategy::POWER_HEURISTIC
    );
    integrator->setVarianceReduction(true);
    return integrator;
}

std::unique_ptr<MISIntegrator> createFast() {
    auto integrator = std::make_unique<MISIntegrator>(
        6,   // maxDepth
        3,   // rrDepth
        1,   // lightSamples
        1,   // bsdfSamples
        MISStrategy::BALANCE_HEURISTIC
    );
    integrator->setVarianceReduction(false);
    return integrator;
}

std::unique_ptr<MISIntegrator> createBalanced() {
    auto integrator = std::make_unique<MISIntegrator>(
        8,   // maxDepth
        3,   // rrDepth
        1,   // lightSamples
        1,   // bsdfSamples
        MISStrategy::OPTIMAL_HEURISTIC
    );
    integrator->setVarianceReduction(true);
    return integrator;
}

std::unique_ptr<MISIntegrator> createDebug() {
    auto integrator = std::make_unique<MISIntegrator>(
        1,   // maxDepth
        1,   // rrDepth
        2,   // lightSamples
        1,   // bsdfSamples
        MISStrategy::POWER_HEURISTIC
    );
    integrator->setDirectLightingOnly(true);
    integrator->setVarianceReduction(false);
    return integrator;
}

} // namespace MISIntegratorFactory

} // namespace photon
