// src/core/integrator/mis_integrator.hpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Multiple Importance Sampling integrator

#pragma once

#include "integrator.hpp"
#include "../sampling/mis_sampling.hpp"
#include <memory>

namespace photon {

/**
 * @brief Multiple Importance Sampling integrator
 * 
 * Advanced path tracing integrator that uses MIS to combine light sampling
 * and BSDF sampling for optimal variance reduction and faster convergence.
 */
class MISIntegrator : public Integrator {
public:
    /**
     * @brief Constructor
     * 
     * @param maxDepth Maximum path depth
     * @param rrDepth Depth to start Russian roulette
     * @param lightSamples Number of light samples per intersection
     * @param bsdfSamples Number of BSDF samples per intersection
     * @param strategy MIS strategy to use
     */
    MISIntegrator(int maxDepth = 8, int rrDepth = 3, 
                  int lightSamples = 1, int bsdfSamples = 1,
                  MISStrategy strategy = MISStrategy::POWER_HEURISTIC);
    
    /**
     * @brief Destructor
     */
    ~MISIntegrator() = default;
    
    // Integrator interface
    Color3 Li(const Ray& ray, const Scene& scene, Sampler& sampler) const override;
    std::string getName() const override { return "MIS"; }
    void preprocess(const Scene& scene) override;
    
    /**
     * @brief Set maximum path depth
     */
    void setMaxDepth(int depth) { m_maxDepth = depth; }
    
    /**
     * @brief Set Russian roulette depth
     */
    void setRussianRouletteDepth(int depth) { m_rrDepth = depth; }
    
    /**
     * @brief Set number of light samples
     */
    void setLightSamples(int samples) { 
        m_misSampling->setLightSamples(samples); 
    }
    
    /**
     * @brief Set number of BSDF samples
     */
    void setBSDFSamples(int samples) { 
        m_misSampling->setBSDFSamples(samples); 
    }
    
    /**
     * @brief Set MIS strategy
     */
    void setMISStrategy(MISStrategy strategy) { 
        m_misSampling->setStrategy(strategy); 
    }
    
    /**
     * @brief Get MIS performance statistics
     */
    MISSampling::Statistics getMISStatistics() const {
        return m_misSampling->getStatistics();
    }
    
    /**
     * @brief Reset MIS performance statistics
     */
    void resetMISStatistics() {
        m_misSampling->resetStatistics();
    }
    
    /**
     * @brief Enable/disable direct lighting only mode
     * 
     * When enabled, only computes direct lighting (no global illumination)
     * Useful for debugging and performance comparison
     */
    void setDirectLightingOnly(bool enable) { m_directLightingOnly = enable; }
    
    /**
     * @brief Enable/disable variance reduction techniques
     */
    void setVarianceReduction(bool enable) { m_varianceReduction = enable; }
    
    /**
     * @brief Get integrator performance metrics
     */
    struct PerformanceMetrics {
        float avgRenderTime;        ///< Average render time per sample (ms)
        float noiseReduction;       ///< Noise reduction vs standard path tracing (%)
        float convergenceRate;      ///< Convergence rate improvement (%)
        int totalSamples;           ///< Total samples processed
        int pathsTraced;            ///< Total paths traced
    };
    
    /**
     * @brief Get performance metrics
     */
    PerformanceMetrics getPerformanceMetrics() const { return m_metrics; }

private:
    int m_maxDepth;                             ///< Maximum path depth
    int m_rrDepth;                              ///< Russian roulette depth
    bool m_directLightingOnly;                  ///< Direct lighting only mode
    bool m_varianceReduction;                   ///< Variance reduction enabled
    
    std::unique_ptr<MISSampling> m_misSampling; ///< MIS sampling framework
    mutable PerformanceMetrics m_metrics;       ///< Performance metrics
    
    /**
     * @brief Sample direct lighting using MIS
     * 
     * @param isect Surface intersection
     * @param scene Scene containing lights
     * @param sampler Random sampler
     * @param wo Outgoing direction
     * @return Direct lighting contribution
     */
    Color3 sampleDirectLightingMIS(const Intersection& isect, const Scene& scene,
                                  Sampler& sampler, const Vec3& wo) const;
    
    /**
     * @brief Sample BSDF for next path direction with variance reduction
     * 
     * @param isect Surface intersection
     * @param wo Outgoing direction
     * @param sampler Random sampler
     * @return BSDF sample with variance reduction
     */
    BSDFSample sampleBSDFWithVarianceReduction(const Intersection& isect, 
                                              const Vec3& wo, Sampler& sampler) const;
    
    /**
     * @brief Evaluate BSDF with importance sampling
     */
    Color3 evaluateBSDF(const Intersection& isect, const Vec3& wo, const Vec3& wi) const;
    
    /**
     * @brief Get BSDF PDF with importance sampling
     */
    float getBSDFPdf(const Intersection& isect, const Vec3& wo, const Vec3& wi) const;
    
    /**
     * @brief Apply Russian roulette termination
     * 
     * @param beta Current path throughput
     * @param depth Current path depth
     * @param sampler Random sampler
     * @return True if path should continue, false if terminated
     */
    bool russianRoulette(const Color3& beta, int depth, Sampler& sampler) const;
    
    /**
     * @brief Calculate path throughput with variance reduction
     */
    Color3 calculatePathThroughput(const Color3& beta, const BSDFSample& bsdfSample,
                                  const Vec3& normal) const;
    
    /**
     * @brief Update performance metrics
     */
    void updatePerformanceMetrics(float renderTime, float noiseReduction) const;
    
    /**
     * @brief Estimate noise reduction compared to standard path tracing
     */
    float estimateNoiseReduction(const Color3& misResult, const Color3& standardResult) const;
};

/**
 * @brief MIS integrator factory
 * 
 * Convenience functions for creating MIS integrators with common configurations
 */
namespace MISIntegratorFactory {
    /**
     * @brief Create high-quality MIS integrator
     * 
     * Optimized for quality with higher sample counts
     */
    std::unique_ptr<MISIntegrator> createHighQuality();
    
    /**
     * @brief Create fast MIS integrator
     * 
     * Optimized for speed with lower sample counts
     */
    std::unique_ptr<MISIntegrator> createFast();
    
    /**
     * @brief Create balanced MIS integrator
     * 
     * Balanced quality/speed configuration
     */
    std::unique_ptr<MISIntegrator> createBalanced();
    
    /**
     * @brief Create debug MIS integrator
     * 
     * Direct lighting only for debugging
     */
    std::unique_ptr<MISIntegrator> createDebug();
}

} // namespace photon
