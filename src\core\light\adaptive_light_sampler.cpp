// src/core/light/adaptive_light_sampler.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Adaptive Light Sampling implementation

#include "adaptive_light_sampler.hpp"
#include "../scene/intersection.hpp"
#include "../sampler/sampler.hpp"
#include "../scene/scene.hpp"
#include <iostream>
#include <algorithm>
#include <numeric>
#include <cmath>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

namespace photon {

// LightImportanceDistribution implementation
LightImportanceDistribution::LightImportanceDistribution() : m_totalWeight(0.0f) {
}

void LightImportanceDistribution::build(const std::vector<ManagedLight>& lights, const Point3& position,
                                       const AdaptiveSamplingParams& params) {
    m_weights.clear();
    m_cdf.clear();
    m_totalWeight = 0.0f;
    
    if (lights.empty()) return;
    
    m_weights.reserve(lights.size());
    
    // Calculate weights based on strategy
    for (const auto& managedLight : lights) {
        if (!managedLight.light || !managedLight.isVisible) {
            m_weights.push_back(0.0f);
            continue;
        }
        
        float weight = 0.0f;
        
        switch (params.strategy) {
            case LightSamplingStrategy::UNIFORM:
                weight = 1.0f;
                break;
                
            case LightSamplingStrategy::IMPORTANCE_BASED:
                weight = managedLight.importance;
                break;
                
            case LightSamplingStrategy::DISTANCE_BASED: {
                float distance = managedLight.distance;
                weight = 1.0f / (1.0f + distance * distance * params.distanceWeight);
                break;
            }
            
            case LightSamplingStrategy::POWER_BASED: {
                Color3 power = managedLight.light->power();
                weight = (power.r + power.g + power.b) / 3.0f;
                break;
            }
            
            case LightSamplingStrategy::ADAPTIVE: {
                float importanceWeight = managedLight.importance * params.importanceWeight;
                float distanceWeight = 1.0f / (1.0f + managedLight.distance * managedLight.distance * params.distanceWeight);
                Color3 power = managedLight.light->power();
                float powerWeight = (power.r + power.g + power.b) / 3.0f * params.powerWeight;
                
                weight = importanceWeight + distanceWeight + powerWeight;
                break;
            }
        }
        
        // Apply LOD scaling if enabled
        if (params.enableLODSampling) {
            switch (managedLight.lod) {
                case LightLOD::HIGH: weight *= 1.0f; break;
                case LightLOD::MEDIUM: weight *= 0.7f; break;
                case LightLOD::LOW: weight *= 0.4f; break;
                case LightLOD::DISABLED: weight = 0.0f; break;
            }
        }
        
        // Apply importance threshold
        if (managedLight.importance < params.importanceThreshold) {
            weight = 0.0f;
        }
        
        m_weights.push_back(std::max(0.0f, weight));
        m_totalWeight += weight;
    }
    
    buildCDF();
}

std::pair<int, float> LightImportanceDistribution::sample(float u) const {
    if (!isValid() || u < 0.0f || u >= 1.0f) {
        return {-1, 0.0f};
    }
    
    // Binary search in CDF
    float target = u * m_totalWeight;
    auto it = std::lower_bound(m_cdf.begin(), m_cdf.end(), target);
    int index = static_cast<int>(std::distance(m_cdf.begin(), it));
    
    // Clamp to valid range
    index = std::min(index, static_cast<int>(m_weights.size()) - 1);
    
    // Calculate PDF
    float pdf = (m_weights[index] > 0.0f) ? (m_weights[index] / m_totalWeight) : 0.0f;
    
    return {index, pdf};
}

float LightImportanceDistribution::pdf(int index) const {
    if (index < 0 || index >= static_cast<int>(m_weights.size()) || m_totalWeight <= 0.0f) {
        return 0.0f;
    }
    
    return m_weights[index] / m_totalWeight;
}

void LightImportanceDistribution::buildCDF() {
    m_cdf.clear();
    if (m_weights.empty()) return;
    
    m_cdf.reserve(m_weights.size());
    float cumulative = 0.0f;
    
    for (float weight : m_weights) {
        cumulative += weight;
        m_cdf.push_back(cumulative);
    }
}

// AdaptiveLightSampler implementation
AdaptiveLightSampler::AdaptiveLightSampler() {
}

AdaptiveLightSampler::~AdaptiveLightSampler() {
}

void AdaptiveLightSampler::initialize(std::shared_ptr<LightManager> lightManager) {
    m_lightManager = lightManager;
}

std::vector<AdaptiveLightSample> AdaptiveLightSampler::sampleLights(const Intersection& isect, Sampler& sampler,
                                                                   const AdaptiveSamplingParams& params) const {
    if (!m_lightManager) {
        return {};
    }
    
    // Get visible lights from light manager
    std::vector<ManagedLight> lights = m_lightManager->getManagedLights(isect.p, params.importanceThreshold);
    
    if (lights.empty()) {
        return {};
    }
    
    // Calculate optimal number of samples
    int numSamples = getOptimalSampleCount(isect, params);
    numSamples = std::clamp(numSamples, params.minSamples, params.maxSamples);
    
    // Sample based on strategy
    std::vector<AdaptiveLightSample> samples;
    
    switch (params.strategy) {
        case LightSamplingStrategy::UNIFORM:
            samples = sampleUniform(isect, sampler, lights, numSamples);
            break;
        case LightSamplingStrategy::IMPORTANCE_BASED:
            samples = sampleImportanceBased(isect, sampler, lights, numSamples);
            break;
        case LightSamplingStrategy::DISTANCE_BASED:
            samples = sampleDistanceBased(isect, sampler, lights, numSamples);
            break;
        case LightSamplingStrategy::ADAPTIVE:
            samples = sampleAdaptive(isect, sampler, lights, numSamples);
            break;
        case LightSamplingStrategy::POWER_BASED:
            samples = samplePowerBased(isect, sampler, lights, numSamples);
            break;
    }
    
    // Update statistics
    updateStatistics(samples);
    
    return samples;
}

AdaptiveLightSample AdaptiveLightSampler::sampleSingleLight(const Intersection& isect, Sampler& sampler,
                                                           const AdaptiveSamplingParams& params) const {
    auto samples = sampleLights(isect, sampler, params);
    
    if (samples.empty()) {
        return AdaptiveLightSample(nullptr, LightSample(), 0.0f, 0.0f, 0.0f, LightLOD::DISABLED);
    }
    
    // Return first valid sample
    for (const auto& sample : samples) {
        if (sample.isValid()) {
            return sample;
        }
    }
    
    return AdaptiveLightSample(nullptr, LightSample(), 0.0f, 0.0f, 0.0f, LightLOD::DISABLED);
}

int AdaptiveLightSampler::getOptimalSampleCount(const Intersection& isect, const AdaptiveSamplingParams& params) const {
    if (!m_lightManager) {
        return params.minSamples;
    }
    
    // Get visible lights
    std::vector<ManagedLight> lights = m_lightManager->getManagedLights(isect.p);
    
    return calculateAdaptiveSampleCount(isect, lights);
}

float AdaptiveLightSampler::calculateMISWeight(const AdaptiveLightSample& lightSample, float bsdfPdf) const {
    if (!m_params.enableMIS || !lightSample.isValid()) {
        return 1.0f;
    }
    
    float lightPdf = lightSample.selectionPdf * lightSample.lightSample.pdf;
    
    // Use power heuristic for MIS
    return powerHeuristic(lightPdf, bsdfPdf);
}

// Private sampling methods
std::vector<AdaptiveLightSample> AdaptiveLightSampler::sampleUniform(const Intersection& isect, Sampler& sampler,
                                                                    const std::vector<ManagedLight>& lights, int numSamples) const {
    std::vector<AdaptiveLightSample> samples;
    samples.reserve(numSamples);
    
    if (lights.empty()) return samples;
    
    float selectionPdf = 1.0f / lights.size();
    
    for (int i = 0; i < numSamples; i++) {
        // Select random light
        int lightIndex = static_cast<int>(sampler.get1D() * lights.size());
        lightIndex = std::min(lightIndex, static_cast<int>(lights.size()) - 1);
        
        const auto& managedLight = lights[lightIndex];
        if (!managedLight.light || !managedLight.isVisible) continue;
        
        // Sample the light
        LightSample lightSample = managedLight.light->sample(isect, sampler);
        
        if (lightSample.isValid()) {
            samples.emplace_back(managedLight.light, lightSample, selectionPdf,
                               managedLight.importance, managedLight.distance, managedLight.lod);
        }
    }
    
    return samples;
}

std::vector<AdaptiveLightSample> AdaptiveLightSampler::sampleImportanceBased(const Intersection& isect, Sampler& sampler,
                                                                            const std::vector<ManagedLight>& lights, int numSamples) const {
    std::vector<AdaptiveLightSample> samples;
    samples.reserve(numSamples);
    
    // Build importance distribution
    LightImportanceDistribution distribution;
    distribution.build(lights, isect.p, m_params);
    
    if (!distribution.isValid()) {
        return sampleUniform(isect, sampler, lights, numSamples);
    }
    
    for (int i = 0; i < numSamples; i++) {
        // Sample light based on importance
        auto [lightIndex, selectionPdf] = distribution.sample(sampler.get1D());
        
        if (lightIndex < 0 || lightIndex >= static_cast<int>(lights.size())) continue;
        
        const auto& managedLight = lights[lightIndex];
        if (!managedLight.light || !managedLight.isVisible) continue;
        
        // Sample the light
        LightSample lightSample = managedLight.light->sample(isect, sampler);
        
        if (lightSample.isValid()) {
            samples.emplace_back(managedLight.light, lightSample, selectionPdf,
                               managedLight.importance, managedLight.distance, managedLight.lod);
        }
    }
    
    return samples;
}

std::vector<AdaptiveLightSample> AdaptiveLightSampler::sampleDistanceBased(const Intersection& isect, Sampler& sampler,
                                                                          const std::vector<ManagedLight>& lights, int numSamples) const {
    // Use importance-based sampling with distance weights
    AdaptiveSamplingParams distanceParams = m_params;
    distanceParams.strategy = LightSamplingStrategy::DISTANCE_BASED;
    
    LightImportanceDistribution distribution;
    distribution.build(lights, isect.p, distanceParams);
    
    if (!distribution.isValid()) {
        return sampleUniform(isect, sampler, lights, numSamples);
    }
    
    std::vector<AdaptiveLightSample> samples;
    samples.reserve(numSamples);
    
    for (int i = 0; i < numSamples; i++) {
        auto [lightIndex, selectionPdf] = distribution.sample(sampler.get1D());
        
        if (lightIndex < 0 || lightIndex >= static_cast<int>(lights.size())) continue;
        
        const auto& managedLight = lights[lightIndex];
        if (!managedLight.light || !managedLight.isVisible) continue;
        
        LightSample lightSample = managedLight.light->sample(isect, sampler);
        
        if (lightSample.isValid()) {
            samples.emplace_back(managedLight.light, lightSample, selectionPdf,
                               managedLight.importance, managedLight.distance, managedLight.lod);
        }
    }
    
    return samples;
}

std::vector<AdaptiveLightSample> AdaptiveLightSampler::sampleAdaptive(const Intersection& isect, Sampler& sampler,
                                                                     const std::vector<ManagedLight>& lights, int numSamples) const {
    // Use the current parameters which should be ADAPTIVE strategy
    return sampleImportanceBased(isect, sampler, lights, numSamples);
}

std::vector<AdaptiveLightSample> AdaptiveLightSampler::samplePowerBased(const Intersection& isect, Sampler& sampler,
                                                                       const std::vector<ManagedLight>& lights, int numSamples) const {
    AdaptiveSamplingParams powerParams = m_params;
    powerParams.strategy = LightSamplingStrategy::POWER_BASED;
    
    LightImportanceDistribution distribution;
    distribution.build(lights, isect.p, powerParams);
    
    if (!distribution.isValid()) {
        return sampleUniform(isect, sampler, lights, numSamples);
    }
    
    std::vector<AdaptiveLightSample> samples;
    samples.reserve(numSamples);
    
    for (int i = 0; i < numSamples; i++) {
        auto [lightIndex, selectionPdf] = distribution.sample(sampler.get1D());
        
        if (lightIndex < 0 || lightIndex >= static_cast<int>(lights.size())) continue;
        
        const auto& managedLight = lights[lightIndex];
        if (!managedLight.light || !managedLight.isVisible) continue;
        
        LightSample lightSample = managedLight.light->sample(isect, sampler);
        
        if (lightSample.isValid()) {
            samples.emplace_back(managedLight.light, lightSample, selectionPdf,
                               managedLight.importance, managedLight.distance, managedLight.lod);
        }
    }
    
    return samples;
}

// Utility functions
float AdaptiveLightSampler::calculateLightImportance(const ManagedLight& light, const Point3& position) const {
    return light.importance;
}

float AdaptiveLightSampler::calculateDistanceWeight(float distance, float maxDistance) const {
    if (distance >= maxDistance) return 0.0f;
    return 1.0f / (1.0f + distance * distance);
}

float AdaptiveLightSampler::calculatePowerWeight(std::shared_ptr<Light> light) const {
    if (!light) return 0.0f;
    
    Color3 power = light->power();
    return (power.r + power.g + power.b) / 3.0f;
}

int AdaptiveLightSampler::calculateAdaptiveSampleCount(const Intersection& isect, const std::vector<ManagedLight>& lights) const {
    if (lights.empty()) return m_params.minSamples;
    
    // Calculate based on light distribution and importance
    float totalImportance = 0.0f;
    int highImportanceLights = 0;
    
    for (const auto& light : lights) {
        totalImportance += light.importance;
        if (light.importance > m_params.importanceThreshold * 10.0f) {
            highImportanceLights++;
        }
    }
    
    // More samples for scenes with many important lights
    int adaptiveSamples = m_params.minSamples + (highImportanceLights / 2);
    
    return std::clamp(adaptiveSamples, m_params.minSamples, m_params.maxSamples);
}

float AdaptiveLightSampler::powerHeuristic(float pdf1, float pdf2, int beta) const {
    if (pdf1 <= 0.0f && pdf2 <= 0.0f) return 0.0f;
    if (pdf2 <= 0.0f) return 1.0f;
    if (pdf1 <= 0.0f) return 0.0f;
    
    float ratio = pdf1 / pdf2;
    float power = std::pow(ratio, beta);
    return power / (1.0f + power);
}

float AdaptiveLightSampler::balanceHeuristic(float pdf1, float pdf2) const {
    if (pdf1 <= 0.0f && pdf2 <= 0.0f) return 0.0f;
    return pdf1 / (pdf1 + pdf2);
}

void AdaptiveLightSampler::updateStatistics(const std::vector<AdaptiveLightSample>& samples) const {
    m_stats.totalSamples += static_cast<int>(samples.size());
    
    int validCount = 0;
    float totalImportance = 0.0f;
    
    for (const auto& sample : samples) {
        if (sample.isValid()) {
            validCount++;
            totalImportance += sample.importance;
        }
    }
    
    m_stats.validSamples += validCount;
    
    if (validCount > 0) {
        m_stats.avgImportance = totalImportance / validCount;
    }
    
    // Update averages
    if (m_stats.totalSamples > 0) {
        m_stats.avgSamplesPerQuery = static_cast<float>(m_stats.totalSamples);
    }
}

} // namespace photon
