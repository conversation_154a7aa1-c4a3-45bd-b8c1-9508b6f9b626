// src/core/light/adaptive_light_sampler.hpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Adaptive Light Sampling for importance-based light selection

#pragma once

#include "../math/vec3.hpp"
#include "../scene/light.hpp"
#include "light_manager.hpp"
#include <memory>
#include <vector>
#include <random>
#include <algorithm>

namespace photon {

// Forward declarations
class Sampler;
class Intersection;

/**
 * @brief Light sampling strategy
 */
enum class LightSamplingStrategy {
    UNIFORM,            ///< Uniform sampling of all lights
    IMPORTANCE_BASED,   ///< Sample based on light importance
    DISTANCE_BASED,     ///< Sample based on distance to lights
    ADAPTIVE,           ///< Adaptive strategy combining importance and distance
    POWER_BASED         ///< Sample based on light power
};

/**
 * @brief Adaptive sampling parameters
 */
struct AdaptiveSamplingParams {
    LightSamplingStrategy strategy;     ///< Sampling strategy to use
    int maxSamples;                     ///< Maximum number of light samples
    int minSamples;                     ///< Minimum number of light samples
    float importanceThreshold;          ///< Minimum importance to consider
    float distanceWeight;               ///< Weight for distance-based sampling
    float importanceWeight;             ///< Weight for importance-based sampling
    float powerWeight;                  ///< Weight for power-based sampling
    bool enableMIS;                     ///< Enable Multiple Importance Sampling
    bool enableLODSampling;             ///< Enable LOD-based sampling
    
    /**
     * @brief Default constructor
     */
    AdaptiveSamplingParams()
        : strategy(LightSamplingStrategy::ADAPTIVE)
        , maxSamples(8)
        , minSamples(1)
        , importanceThreshold(0.01f)
        , distanceWeight(0.3f)
        , importanceWeight(0.5f)
        , powerWeight(0.2f)
        , enableMIS(true)
        , enableLODSampling(true) {}
};

/**
 * @brief Light sample with importance information
 */
struct AdaptiveLightSample {
    std::shared_ptr<Light> light;       ///< Sampled light
    LightSample lightSample;            ///< Light sampling result
    float selectionPdf;                 ///< PDF for selecting this light
    float importance;                   ///< Light importance factor
    float distance;                     ///< Distance to light
    LightLOD lod;                       ///< Light LOD level
    
    /**
     * @brief Constructor
     */
    AdaptiveLightSample(std::shared_ptr<Light> light, const LightSample& sample, 
                       float selectionPdf, float importance, float distance, LightLOD lod)
        : light(light), lightSample(sample), selectionPdf(selectionPdf)
        , importance(importance), distance(distance), lod(lod) {}
    
    /**
     * @brief Check if sample is valid
     */
    bool isValid() const {
        return light && lightSample.isValid() && selectionPdf > 0.0f;
    }
};

/**
 * @brief Light importance distribution for sampling
 */
class LightImportanceDistribution {
public:
    /**
     * @brief Constructor
     */
    LightImportanceDistribution();
    
    /**
     * @brief Build distribution from lights
     * 
     * @param lights Vector of managed lights
     * @param position Query position for importance calculation
     * @param params Sampling parameters
     */
    void build(const std::vector<ManagedLight>& lights, const Point3& position,
               const AdaptiveSamplingParams& params);
    
    /**
     * @brief Sample light index based on importance
     * 
     * @param u Random sample [0,1)
     * @return Light index and selection PDF
     */
    std::pair<int, float> sample(float u) const;
    
    /**
     * @brief Get PDF for selecting specific light index
     * 
     * @param index Light index
     * @return Selection PDF
     */
    float pdf(int index) const;
    
    /**
     * @brief Get number of lights in distribution
     */
    size_t size() const { return m_weights.size(); }
    
    /**
     * @brief Check if distribution is valid
     */
    bool isValid() const { return !m_weights.empty() && m_totalWeight > 0.0f; }

private:
    std::vector<float> m_weights;       ///< Light weights
    std::vector<float> m_cdf;           ///< Cumulative distribution function
    float m_totalWeight;                ///< Total weight sum
    
    void buildCDF();
};

/**
 * @brief Adaptive Light Sampler
 */
class AdaptiveLightSampler {
public:
    /**
     * @brief Constructor
     */
    AdaptiveLightSampler();
    
    /**
     * @brief Destructor
     */
    ~AdaptiveLightSampler();
    
    /**
     * @brief Initialize with light manager
     * 
     * @param lightManager Light manager to use
     */
    void initialize(std::shared_ptr<LightManager> lightManager);
    
    /**
     * @brief Sample lights adaptively for intersection
     * 
     * @param isect Surface intersection
     * @param sampler Random sampler
     * @param params Sampling parameters
     * @return Vector of adaptive light samples
     */
    std::vector<AdaptiveLightSample> sampleLights(const Intersection& isect, Sampler& sampler,
                                                  const AdaptiveSamplingParams& params = AdaptiveSamplingParams()) const;
    
    /**
     * @brief Sample single light adaptively
     * 
     * @param isect Surface intersection
     * @param sampler Random sampler
     * @param params Sampling parameters
     * @return Single adaptive light sample
     */
    AdaptiveLightSample sampleSingleLight(const Intersection& isect, Sampler& sampler,
                                         const AdaptiveSamplingParams& params = AdaptiveSamplingParams()) const;
    
    /**
     * @brief Get optimal number of samples for intersection
     * 
     * @param isect Surface intersection
     * @param params Sampling parameters
     * @return Optimal number of light samples
     */
    int getOptimalSampleCount(const Intersection& isect, const AdaptiveSamplingParams& params) const;
    
    /**
     * @brief Calculate MIS weight for light sampling
     * 
     * @param lightSample Light sample
     * @param bsdfPdf BSDF PDF for same direction
     * @return MIS weight
     */
    float calculateMISWeight(const AdaptiveLightSample& lightSample, float bsdfPdf) const;
    
    /**
     * @brief Set sampling parameters
     */
    void setSamplingParams(const AdaptiveSamplingParams& params) { m_params = params; }
    
    /**
     * @brief Get sampling parameters
     */
    const AdaptiveSamplingParams& getSamplingParams() const { return m_params; }
    
    /**
     * @brief Get statistics
     */
    struct Statistics {
        int totalSamples;           ///< Total light samples generated
        int validSamples;           ///< Valid light samples
        float avgSamplesPerQuery;   ///< Average samples per query
        float avgImportance;        ///< Average light importance
        float misEfficiency;        ///< MIS efficiency (0-1)
        
        Statistics() { reset(); }
        void reset() {
            totalSamples = validSamples = 0;
            avgSamplesPerQuery = avgImportance = misEfficiency = 0.0f;
        }
    };
    
    const Statistics& getStatistics() const { return m_stats; }
    void resetStatistics() { m_stats.reset(); }

private:
    std::shared_ptr<LightManager> m_lightManager;   ///< Light manager
    AdaptiveSamplingParams m_params;                ///< Sampling parameters
    mutable Statistics m_stats;                     ///< Performance statistics
    
    // Sampling strategies
    std::vector<AdaptiveLightSample> sampleUniform(const Intersection& isect, Sampler& sampler,
                                                   const std::vector<ManagedLight>& lights, int numSamples) const;
    std::vector<AdaptiveLightSample> sampleImportanceBased(const Intersection& isect, Sampler& sampler,
                                                          const std::vector<ManagedLight>& lights, int numSamples) const;
    std::vector<AdaptiveLightSample> sampleDistanceBased(const Intersection& isect, Sampler& sampler,
                                                        const std::vector<ManagedLight>& lights, int numSamples) const;
    std::vector<AdaptiveLightSample> sampleAdaptive(const Intersection& isect, Sampler& sampler,
                                                    const std::vector<ManagedLight>& lights, int numSamples) const;
    std::vector<AdaptiveLightSample> samplePowerBased(const Intersection& isect, Sampler& sampler,
                                                      const std::vector<ManagedLight>& lights, int numSamples) const;
    
    // Utility functions
    float calculateLightImportance(const ManagedLight& light, const Point3& position) const;
    float calculateDistanceWeight(float distance, float maxDistance) const;
    float calculatePowerWeight(std::shared_ptr<Light> light) const;
    int calculateAdaptiveSampleCount(const Intersection& isect, const std::vector<ManagedLight>& lights) const;
    
    // MIS utilities
    float powerHeuristic(float pdf1, float pdf2, int beta = 2) const;
    float balanceHeuristic(float pdf1, float pdf2) const;
    
    // Statistics helpers
    void updateStatistics(const std::vector<AdaptiveLightSample>& samples) const;
};

/**
 * @brief Utility functions for adaptive light sampling
 */
namespace AdaptiveSamplingUtils {
    /**
     * @brief Calculate light visibility factor
     * 
     * @param light Light to test
     * @param position Query position
     * @param scene Scene for visibility testing
     * @return Visibility factor [0, 1]
     */
    float calculateVisibilityFactor(std::shared_ptr<Light> light, const Point3& position, const Scene& scene);
    
    /**
     * @brief Estimate light contribution
     * 
     * @param light Light to evaluate
     * @param isect Surface intersection
     * @return Estimated contribution
     */
    Color3 estimateLightContribution(std::shared_ptr<Light> light, const Intersection& isect);
    
    /**
     * @brief Calculate optimal sample count based on light distribution
     * 
     * @param lights Vector of lights
     * @param position Query position
     * @param maxSamples Maximum allowed samples
     * @return Optimal sample count
     */
    int calculateOptimalSampleCount(const std::vector<ManagedLight>& lights, const Point3& position, int maxSamples);
}

} // namespace photon
