// src/core/light/area_light_base.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Extended Area Light base implementation

#include "area_light_base.hpp"
#include "../scene/scene.hpp"
#include "../scene/intersection.hpp"
#include "../sampler/sampler.hpp"
#include "../math/ray.hpp"
#include <iostream>
#include <cmath>
#include <algorithm>

namespace photon {

AreaLightBase::AreaLightBase(const Color3& emission, float intensity, bool twoSided)
    : m_emission(emission), m_intensity(intensity), m_twoSided(twoSided) {
}

LightSample AreaLightBase::sample(const Intersection& isect, Sampler& sampler) const {
    // Sample point on light surface
    Vec3 lightPoint, lightNormal;
    float areaPDF;
    sampleSurface(sampler, lightPoint, lightNormal, areaPDF);
    
    // Compute direction from surface to light
    Vec3 wi = lightPoint - isect.p;
    float distance = wi.length();
    wi /= distance; // Normalize
    
    // Check if light faces the surface
    float cosTheta = -wi.dot(lightNormal);
    if (!m_twoSided && cosTheta <= 0.0f) {
        return LightSample(); // Back-facing, no contribution
    }
    
    // For two-sided lights, use absolute cosine
    if (m_twoSided) {
        cosTheta = std::abs(cosTheta);
    }
    
    // Convert area PDF to solid angle PDF
    float solidAnglePDF = areaToSolidAnglePDF(areaPDF, distance, cosTheta);
    
    // Compute radiance
    Color3 Li = getEffectiveEmission();
    
    return LightSample(Li, wi, solidAnglePDF, distance, false);
}

Color3 AreaLightBase::Li(const Intersection& isect, const Vec3& wi) const {
    // Check if ray intersects light surface
    Ray ray(isect.p, wi);
    float t;
    Vec3 point, normal;
    
    if (!intersect(ray, t, point, normal)) {
        return Color3(0); // No intersection
    }
    
    // Check if light faces the ray direction
    float cosTheta = -wi.dot(normal);
    if (!m_twoSided && cosTheta <= 0.0f) {
        return Color3(0); // Back-facing
    }
    
    return getEffectiveEmission();
}

float AreaLightBase::pdf(const Intersection& isect, const Vec3& wi) const {
    // Check if ray intersects light surface
    Ray ray(isect.p, wi);
    float t;
    Vec3 point, normal;
    
    if (!intersect(ray, t, point, normal)) {
        return 0.0f; // No intersection
    }
    
    // Check if light faces the ray direction
    float cosTheta = -wi.dot(normal);
    if (!m_twoSided && cosTheta <= 0.0f) {
        return 0.0f; // Back-facing
    }
    
    if (m_twoSided) {
        cosTheta = std::abs(cosTheta);
    }
    
    // Compute area PDF (uniform for most shapes)
    float areaPDF = 1.0f / getArea();
    
    // Convert to solid angle PDF
    float distance = t;
    return areaToSolidAnglePDF(areaPDF, distance, cosTheta);
}

Color3 AreaLightBase::power() const {
    float area = getArea();
    float emissionFactor = m_twoSided ? 2.0f : 1.0f;
    return getEffectiveEmission() * area * M_PI * emissionFactor;
}

void AreaLightBase::preprocess(const Scene& scene) {
    // Base implementation - derived classes can override
    std::cout << "Area light preprocessed: " << getName() 
              << " (area: " << getArea() << ", two-sided: " << m_twoSided << ")" << std::endl;
}

std::pair<Vec3, float> AreaLightBase::sampleCosineHemisphere(const Vec3& normal, Sampler& sampler) const {
    Vec2 u = sampler.next2D();
    
    // Cosine-weighted hemisphere sampling
    float cosTheta = std::sqrt(u.x);
    float sinTheta = std::sqrt(1.0f - u.x);
    float phi = 2.0f * M_PI * u.y;
    
    // Local coordinates
    Vec3 localDir(sinTheta * std::cos(phi), cosTheta, sinTheta * std::sin(phi));
    
    // Transform to world coordinates
    Vec3 tangent, bitangent;
    if (std::abs(normal.x) > 0.1f) {
        tangent = Vec3(0, 1, 0).cross(normal).normalized();
    } else {
        tangent = Vec3(1, 0, 0).cross(normal).normalized();
    }
    bitangent = normal.cross(tangent);
    
    Vec3 worldDir = tangent * localDir.x + normal * localDir.y + bitangent * localDir.z;
    float pdf = cosTheta / M_PI;
    
    return std::make_pair(worldDir, pdf);
}

float AreaLightBase::getCosineHemispherePDF(const Vec3& normal, const Vec3& direction) const {
    float cosTheta = std::max(0.0f, direction.dot(normal));
    return cosTheta > 0.0f ? cosTheta / M_PI : 0.0f;
}

float AreaLightBase::areaToSolidAnglePDF(float areaPDF, float distance, float cosTheta) const {
    if (cosTheta <= 0.0f) return 0.0f;
    return areaPDF * (distance * distance) / cosTheta;
}

bool AreaLightBase::isValidEmissionDirection(const Vec3& normal, const Vec3& direction) const {
    if (m_twoSided) {
        return true; // Two-sided lights emit in all directions
    } else {
        return direction.dot(normal) > 0.0f; // Single-sided: only front-facing
    }
}

// Area light utility functions
namespace AreaLightUtils {

Vec2 sampleUniformDisk(const Vec2& u) {
    float r = std::sqrt(u.x);
    float theta = 2.0f * M_PI * u.y;
    return Vec2(r * std::cos(theta), r * std::sin(theta));
}

Vec3 sampleUniformSphere(const Vec2& u) {
    float z = 1.0f - 2.0f * u.x;
    float r = std::sqrt(std::max(0.0f, 1.0f - z * z));
    float phi = 2.0f * M_PI * u.y;
    return Vec3(r * std::cos(phi), r * std::sin(phi), z);
}

std::pair<Vec3, float> sampleCosineHemisphere(const Vec2& u) {
    float cosTheta = std::sqrt(u.x);
    float sinTheta = std::sqrt(1.0f - u.x);
    float phi = 2.0f * M_PI * u.y;
    
    Vec3 direction(sinTheta * std::cos(phi), cosTheta, sinTheta * std::sin(phi));
    float pdf = cosTheta / M_PI;
    
    return std::make_pair(direction, pdf);
}

float rectangleSolidAngle(const Vec3& p, const Vec3 corners[4]) {
    // Compute solid angle subtended by rectangle using spherical polygon formula
    Vec3 v[4];
    for (int i = 0; i < 4; ++i) {
        v[i] = (corners[i] - p).normalized();
    }
    
    float solidAngle = 0.0f;
    for (int i = 0; i < 4; ++i) {
        int j = (i + 1) % 4;
        float cosTheta = v[i].dot(v[j]);
        cosTheta = std::clamp(cosTheta, -1.0f, 1.0f);
        solidAngle += std::acos(cosTheta);
    }
    
    // Subtract 2π for spherical polygon
    solidAngle -= 2.0f * M_PI;
    return std::abs(solidAngle);
}

float diskSolidAngle(const Vec3& p, const Vec3& center, const Vec3& normal, float radius) {
    Vec3 toCenter = center - p;
    float distance = toCenter.length();
    
    if (distance <= radius) {
        // Point is inside or on the disk
        return 2.0f * M_PI;
    }
    
    // Compute solid angle for disk
    float cosTheta = toCenter.dot(normal) / distance;
    if (cosTheta <= 0.0f) {
        return 0.0f; // Disk is back-facing
    }
    
    float sinAlpha = radius / distance;
    if (sinAlpha >= 1.0f) {
        return 2.0f * M_PI; // Point is very close to disk
    }
    
    float cosAlpha = std::sqrt(1.0f - sinAlpha * sinAlpha);
    return 2.0f * M_PI * (1.0f - cosAlpha * cosTheta);
}

} // namespace AreaLightUtils

} // namespace photon
