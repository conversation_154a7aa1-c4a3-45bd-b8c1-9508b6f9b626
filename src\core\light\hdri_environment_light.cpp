// src/core/light/hdri_environment_light.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// HDRI Environment Light implementation

#include "hdri_environment_light.hpp"
#include "../scene/scene.hpp"
#include "../scene/intersection.hpp"
#include "../sampler/sampler.hpp"
#include "../texture/hdr_texture.hpp"
#include <iostream>
#include <cmath>

namespace photon {

HDRIEnvironmentLight::HDRIEnvironmentLight(std::shared_ptr<HDRTexture> hdrTexture, float intensity)
    : m_hdrTexture(hdrTexture), m_intensity(intensity) {
    if (m_hdrTexture && !m_hdrTexture->isLoaded()) {
        std::cerr << "Warning: HDR texture is not loaded" << std::endl;
    }
}

HDRIEnvironmentLight::HDRIEnvironmentLight(const std::string& filename, float intensity)
    : m_intensity(intensity) {
    loadHDRFile(filename);
}

HDRIEnvironmentLight::HDRIEnvironmentLight(const Color3& color, float intensity)
    : m_intensity(intensity), m_fallbackColor(color) {
    // Create solid color HDR texture
    m_hdrTexture = HDRTextureFactory::createSolid(color);
}

LightSample HDRIEnvironmentLight::sample(const Intersection& isect, Sampler& sampler) const {
    Vec3 wi;
    float pdf;
    
    if (m_useImportanceSampling && hasHDRTexture()) {
        auto result = sampleImportance(isect, sampler);
        wi = result.first;
        pdf = result.second;
    } else {
        auto result = sampleCosineHemisphere(isect, sampler);
        wi = result.first;
        pdf = result.second;
    }
    
    // Evaluate environment lighting
    Color3 Li = evaluate(wi);
    
    // Distance to environment sphere
    float distance = 2.0f * m_worldRadius;
    
    return LightSample(Li, wi, pdf, distance, false);
}

Color3 HDRIEnvironmentLight::Li(const Intersection& isect, const Vec3& wi) const {
    return evaluate(wi);
}

float HDRIEnvironmentLight::pdf(const Intersection& isect, const Vec3& wi) const {
    if (m_useImportanceSampling && hasHDRTexture()) {
        Vec3 rotatedWi = removeRotation(wi);
        return m_hdrTexture->getImportancePDF(rotatedWi);
    } else {
        return getCosineHemispherePDF(isect, wi);
    }
}

Color3 HDRIEnvironmentLight::power() const {
    if (hasHDRTexture()) {
        float avgLuminance = m_hdrTexture->getAverageLuminance();
        return Color3(avgLuminance * m_intensity) * 4.0f * M_PI * m_worldRadius * m_worldRadius;
    } else {
        return m_fallbackColor * m_intensity * 4.0f * M_PI * m_worldRadius * m_worldRadius;
    }
}

void HDRIEnvironmentLight::preprocess(const Scene& scene) {
    // Get world bounding sphere radius
    Scene::Bounds bounds = scene.getBounds();
    Vec3 diagonal = bounds.max - bounds.min;
    m_worldRadius = diagonal.length() * 0.5f;
    
    // Build importance sampling if needed
    if (m_useImportanceSampling && hasHDRTexture()) {
        m_hdrTexture->buildImportanceSampling();
    }
    
    std::cout << "HDRI Environment Light preprocessed (world radius: " << m_worldRadius << ")" << std::endl;
}

Color3 HDRIEnvironmentLight::evaluate(const Vec3& direction) const {
    if (hasHDRTexture()) {
        Vec3 rotatedDirection = removeRotation(direction);
        return m_hdrTexture->sampleDirection(rotatedDirection) * m_intensity;
    } else {
        return m_fallbackColor * m_intensity;
    }
}

void HDRIEnvironmentLight::setHDRTexture(std::shared_ptr<HDRTexture> hdrTexture) {
    m_hdrTexture = hdrTexture;
    
    if (m_useImportanceSampling && hasHDRTexture()) {
        m_hdrTexture->buildImportanceSampling();
    }
}

void HDRIEnvironmentLight::setRotation(float rotation) {
    m_rotation = rotation;
    
    if (hasHDRTexture()) {
        m_hdrTexture->setRotation(rotation);
    }
}

void HDRIEnvironmentLight::setImportanceSampling(bool enable) {
    m_useImportanceSampling = enable;
    
    if (enable && hasHDRTexture()) {
        m_hdrTexture->buildImportanceSampling();
    }
}

bool HDRIEnvironmentLight::loadHDRFile(const std::string& filename) {
    m_hdrTexture = std::make_shared<HDRTexture>();
    
    if (m_hdrTexture->load(filename)) {
        if (m_useImportanceSampling) {
            m_hdrTexture->buildImportanceSampling();
        }
        std::cout << "Loaded HDRI environment: " << filename << std::endl;
        return true;
    } else {
        std::cerr << "Failed to load HDRI environment: " << filename << std::endl;
        m_hdrTexture.reset();
        return false;
    }
}

void HDRIEnvironmentLight::createProceduralSky(const Color3& zenithColor, 
                                              const Color3& horizonColor, 
                                              float sunIntensity) {
    m_hdrTexture = HDRTextureFactory::createSky(zenithColor, horizonColor, sunIntensity);
    
    if (m_useImportanceSampling) {
        m_hdrTexture->buildImportanceSampling();
    }
    
    std::cout << "Created procedural sky environment" << std::endl;
}

Vec3 HDRIEnvironmentLight::applyRotation(const Vec3& direction) const {
    if (m_rotation == 0.0f) return direction;
    
    float cosR = std::cos(m_rotation);
    float sinR = std::sin(m_rotation);
    
    // Rotate around Y axis
    return Vec3(
        direction.x * cosR - direction.z * sinR,
        direction.y,
        direction.x * sinR + direction.z * cosR
    );
}

Vec3 HDRIEnvironmentLight::removeRotation(const Vec3& direction) const {
    if (m_rotation == 0.0f) return direction;
    
    float cosR = std::cos(-m_rotation);
    float sinR = std::sin(-m_rotation);
    
    // Rotate around Y axis (inverse)
    return Vec3(
        direction.x * cosR - direction.z * sinR,
        direction.y,
        direction.x * sinR + direction.z * cosR
    );
}

std::pair<Vec3, float> HDRIEnvironmentLight::sampleCosineHemisphere(const Intersection& isect, Sampler& sampler) const {
    Vec2 u = sampler.next2D();
    
    // Cosine-weighted hemisphere sampling
    float cosTheta = std::sqrt(u.x);
    float sinTheta = std::sqrt(1.0f - u.x);
    float phi = 2.0f * M_PI * u.y;
    
    // Local coordinates
    Vec3 localWi(sinTheta * std::cos(phi), cosTheta, sinTheta * std::sin(phi));
    
    // Transform to world coordinates
    Vec3 tangent, bitangent;
    if (std::abs(isect.n.x) > 0.1f) {
        tangent = Vec3(0, 1, 0).cross(isect.n).normalized();
    } else {
        tangent = Vec3(1, 0, 0).cross(isect.n).normalized();
    }
    bitangent = isect.n.cross(tangent);
    
    Vec3 wi = tangent * localWi.x + isect.n * localWi.y + bitangent * localWi.z;
    float pdf = cosTheta / M_PI;
    
    return std::make_pair(wi, pdf);
}

std::pair<Vec3, float> HDRIEnvironmentLight::sampleImportance(const Intersection& isect, Sampler& sampler) const {
    Vec2 u = sampler.next2D();
    
    float pdf;
    Vec3 wi = m_hdrTexture->sampleImportance(u, pdf);
    
    // Apply rotation
    wi = applyRotation(wi);
    
    // Check if direction is above surface
    if (wi.dot(isect.n) <= 0.0f) {
        // Fallback to cosine hemisphere sampling
        return sampleCosineHemisphere(isect, sampler);
    }
    
    return std::make_pair(wi, pdf);
}

float HDRIEnvironmentLight::getCosineHemispherePDF(const Intersection& isect, const Vec3& wi) const {
    float cosTheta = std::max(0.0f, wi.dot(isect.n));
    return cosTheta > 0.0f ? cosTheta / M_PI : 0.0f;
}

// Factory functions
namespace HDRIEnvironmentLightFactory {

std::shared_ptr<HDRIEnvironmentLight> fromFile(const std::string& filename, float intensity) {
    auto light = std::make_shared<HDRIEnvironmentLight>(filename, intensity);
    return light;
}

std::shared_ptr<HDRIEnvironmentLight> createSky(const Color3& zenithColor,
                                               const Color3& horizonColor,
                                               float sunIntensity,
                                               float intensity) {
    auto light = std::make_shared<HDRIEnvironmentLight>(Color3(0.1f), intensity);
    light->createProceduralSky(zenithColor, horizonColor, sunIntensity);
    return light;
}

std::shared_ptr<HDRIEnvironmentLight> createSolid(const Color3& color, float intensity) {
    return std::make_shared<HDRIEnvironmentLight>(color, intensity);
}

} // namespace HDRIEnvironmentLightFactory

} // namespace photon
