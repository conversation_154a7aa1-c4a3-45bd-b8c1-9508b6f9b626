// src/core/light/hdri_environment_light.hpp
// PhotonRender - Professional Rendering Engine for SketchUp
// HDRI Environment Light implementation

#pragma once

#include "../scene/light.hpp"
#include "../texture/hdr_texture.hpp"
#include "../math/vec3.hpp"
#include "../math/color3.hpp"
#include <memory>

namespace photon {

// Forward declarations
class Scene;
class Sampler;

/**
 * @brief HDRI Environment Light for realistic environment lighting
 */
class HDRIEnvironmentLight : public Light {
public:
    /**
     * @brief Constructor with HDR texture
     * @param hdrTexture HDR environment texture
     * @param intensity Intensity multiplier
     */
    HDRIEnvironmentLight(std::shared_ptr<HDRTexture> hdrTexture, float intensity = 1.0f);
    
    /**
     * @brief Constructor with HDR file
     * @param filename Path to HDR file
     * @param intensity Intensity multiplier
     */
    HDRIEnvironmentLight(const std::string& filename, float intensity = 1.0f);
    
    /**
     * @brief Constructor with solid color (fallback)
     * @param color Environment color
     * @param intensity Intensity multiplier
     */
    HDRIEnvironmentLight(const Color3& color = Color3(0.1f), float intensity = 1.0f);
    
    /**
     * @brief Destructor
     */
    virtual ~HDRIEnvironmentLight() = default;
    
    // Light interface implementation
    LightSample sample(const Intersection& isect, Sampler& sampler) const override;
    Color3 Li(const Intersection& isect, const Vec3& wi) const override;
    float pdf(const Intersection& isect, const Vec3& wi) const override;
    Color3 power() const override;
    bool isDelta() const override { return false; }
    std::string getName() const override { return "HDRIEnvironment"; }
    void preprocess(const Scene& scene) override;
    
    /**
     * @brief Evaluate environment lighting for direction
     * @param direction World direction
     * @return Environment radiance
     */
    Color3 evaluate(const Vec3& direction) const;
    
    /**
     * @brief Set HDR texture
     * @param hdrTexture New HDR texture
     */
    void setHDRTexture(std::shared_ptr<HDRTexture> hdrTexture);
    
    /**
     * @brief Get HDR texture
     */
    std::shared_ptr<HDRTexture> getHDRTexture() const { return m_hdrTexture; }
    
    /**
     * @brief Set intensity multiplier
     * @param intensity New intensity
     */
    void setIntensity(float intensity) { m_intensity = intensity; }
    
    /**
     * @brief Get intensity multiplier
     */
    float getIntensity() const { return m_intensity; }
    
    /**
     * @brief Set environment rotation (radians)
     * @param rotation Rotation around Y axis
     */
    void setRotation(float rotation);
    
    /**
     * @brief Get environment rotation
     */
    float getRotation() const { return m_rotation; }
    
    /**
     * @brief Set world radius (for preprocessing)
     * @param radius World bounding sphere radius
     */
    void setWorldRadius(float radius) { m_worldRadius = radius; }
    
    /**
     * @brief Get world radius
     */
    float getWorldRadius() const { return m_worldRadius; }
    
    /**
     * @brief Enable/disable importance sampling
     * @param enable True to enable importance sampling
     */
    void setImportanceSampling(bool enable);
    
    /**
     * @brief Check if importance sampling is enabled
     */
    bool isImportanceSamplingEnabled() const { return m_useImportanceSampling; }
    
    /**
     * @brief Load HDR texture from file
     * @param filename Path to HDR file
     * @return True if loaded successfully
     */
    bool loadHDRFile(const std::string& filename);
    
    /**
     * @brief Create procedural sky environment
     * @param zenithColor Sky color at zenith
     * @param horizonColor Sky color at horizon
     * @param sunIntensity Sun intensity
     */
    void createProceduralSky(const Color3& zenithColor = Color3(0.5f, 0.7f, 1.0f),
                            const Color3& horizonColor = Color3(0.8f, 0.9f, 1.0f),
                            float sunIntensity = 10.0f);

private:
    std::shared_ptr<HDRTexture> m_hdrTexture;
    float m_intensity = 1.0f;
    float m_rotation = 0.0f;
    float m_worldRadius = 1000.0f;
    bool m_useImportanceSampling = true;
    
    // Fallback for when no HDR texture is available
    Color3 m_fallbackColor = Color3(0.1f);
    
    /**
     * @brief Apply rotation to direction
     * @param direction Original direction
     * @return Rotated direction
     */
    Vec3 applyRotation(const Vec3& direction) const;
    
    /**
     * @brief Remove rotation from direction
     * @param direction Rotated direction
     * @return Original direction
     */
    Vec3 removeRotation(const Vec3& direction) const;
    
    /**
     * @brief Sample direction using cosine-weighted hemisphere sampling
     * @param isect Surface intersection
     * @param sampler Random sampler
     * @return Sampled direction and PDF
     */
    std::pair<Vec3, float> sampleCosineHemisphere(const Intersection& isect, Sampler& sampler) const;
    
    /**
     * @brief Sample direction using importance sampling
     * @param isect Surface intersection
     * @param sampler Random sampler
     * @return Sampled direction and PDF
     */
    std::pair<Vec3, float> sampleImportance(const Intersection& isect, Sampler& sampler) const;
    
    /**
     * @brief Get PDF for cosine-weighted hemisphere sampling
     * @param isect Surface intersection
     * @param wi Direction
     * @return PDF value
     */
    float getCosineHemispherePDF(const Intersection& isect, const Vec3& wi) const;
    
    /**
     * @brief Check if HDR texture is available
     */
    bool hasHDRTexture() const { return m_hdrTexture && m_hdrTexture->isLoaded(); }
};

/**
 * @brief HDRI Environment Light factory functions
 */
namespace HDRIEnvironmentLightFactory {
    /**
     * @brief Create HDRI environment light from file
     * @param filename Path to HDR file
     * @param intensity Intensity multiplier
     * @return HDRI environment light
     */
    std::shared_ptr<HDRIEnvironmentLight> fromFile(const std::string& filename, float intensity = 1.0f);
    
    /**
     * @brief Create procedural sky environment
     * @param zenithColor Sky color at zenith
     * @param horizonColor Sky color at horizon
     * @param sunIntensity Sun intensity
     * @param intensity Overall intensity multiplier
     * @return HDRI environment light
     */
    std::shared_ptr<HDRIEnvironmentLight> createSky(const Color3& zenithColor = Color3(0.5f, 0.7f, 1.0f),
                                                    const Color3& horizonColor = Color3(0.8f, 0.9f, 1.0f),
                                                    float sunIntensity = 10.0f,
                                                    float intensity = 1.0f);
    
    /**
     * @brief Create solid color environment
     * @param color Environment color
     * @param intensity Intensity multiplier
     * @return HDRI environment light
     */
    std::shared_ptr<HDRIEnvironmentLight> createSolid(const Color3& color, float intensity = 1.0f);
}

} // namespace photon
