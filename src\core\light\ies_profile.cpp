// src/core/light/ies_profile.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// IES Profile System implementation

#include "ies_profile.hpp"
#include <fstream>
#include <sstream>
#include <algorithm>
#include <cmath>
#include <iostream>
#include <limits>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

namespace photon {

IESProfile::IESProfile() {
    m_data.isValid = false;
}

bool IESProfile::loadFromFile(const std::string& filePath) {
    std::ifstream file(filePath);
    if (!file.is_open()) {
        std::cerr << "Failed to open IES file: " << filePath << std::endl;
        return false;
    }
    
    std::string content((std::istreambuf_iterator<char>(file)),
                        std::istreambuf_iterator<char>());
    file.close();
    
    return loadFromString(content);
}

bool IESProfile::loadFromString(const std::string& iesData) {
    // Reset data
    m_data = IESData();
    m_cacheValid = false;
    
    // Split into lines
    std::istringstream stream(iesData);
    std::vector<std::string> lines;
    std::string line;
    
    while (std::getline(stream, line)) {
        // Remove carriage return if present
        if (!line.empty() && line.back() == '\r') {
            line.pop_back();
        }
        lines.push_back(line);
    }
    
    if (lines.empty()) {
        return false;
    }
    
    size_t lineIndex = 0;
    
    // Parse header
    if (!parseHeader(lines, lineIndex)) {
        return false;
    }
    
    // Parse photometric data
    if (!parsePhotometricData(lines, lineIndex)) {
        return false;
    }
    
    // Parse angular data
    if (!parseAngularData(lines, lineIndex)) {
        return false;
    }
    
    // Parse candela values
    if (!parseCandelaValues(lines, lineIndex)) {
        return false;
    }
    
    // Validate and compute derived values
    if (!validateData()) {
        return false;
    }
    
    computeDerivedValues();
    m_data.isValid = true;
    
    return true;
}

float IESProfile::evaluate(const Vec3& direction) const {
    if (!m_data.isValid) {
        return 0.0f;
    }
    
    float theta, phi;
    directionToSpherical(direction, theta, phi);
    return evaluate(theta, phi);
}

float IESProfile::evaluate(float theta, float phi) const {
    if (!m_data.isValid || m_data.maxCandela <= 0.0f) {
        return 0.0f;
    }
    
    // Clamp theta to [0, π]
    theta = std::max(0.0f, std::min(theta, (float)M_PI));
    
    // Normalize phi to [0, 2π]
    phi = normalizeAngle(phi);
    
    // Interpolate candela value
    float candela = interpolateCandela(theta, phi);
    
    // Normalize by maximum candela
    return candela / m_data.maxCandela;
}

bool IESProfile::parseHeader(const std::vector<std::string>& lines, size_t& lineIndex) {
    // Skip to TILT line
    while (lineIndex < lines.size()) {
        const std::string& line = lines[lineIndex];
        if (line.find("TILT=") != std::string::npos) {
            lineIndex++;
            break;
        }
        
        // Extract manufacturer info if available
        if (line.find("[") != std::string::npos) {
            m_data.manufacturer = line;
        }
        
        lineIndex++;
    }
    
    return lineIndex < lines.size();
}

bool IESProfile::parsePhotometricData(const std::vector<std::string>& lines, size_t& lineIndex) {
    if (lineIndex >= lines.size()) {
        return false;
    }
    
    // Parse photometric data line
    std::vector<std::string> tokens = splitString(lines[lineIndex]);
    if (tokens.size() < 10) {
        return false;
    }
    
    // Parse required values
    if (!parseInt(tokens[3], m_data.numVerticalAngles) ||
        !parseInt(tokens[4], m_data.numHorizontalAngles) ||
        !parseFloat(tokens[9], m_data.lumensPerLamp)) {
        return false;
    }
    
    // Parse multiplier if available
    if (tokens.size() > 10) {
        parseFloat(tokens[10], m_data.multiplier);
    }
    
    lineIndex++;
    return true;
}

bool IESProfile::parseAngularData(const std::vector<std::string>& lines, size_t& lineIndex) {
    // Parse vertical angles
    m_data.verticalAngles.clear();
    m_data.verticalAngles.reserve(m_data.numVerticalAngles);
    
    while (m_data.verticalAngles.size() < m_data.numVerticalAngles && lineIndex < lines.size()) {
        std::vector<std::string> tokens = splitString(lines[lineIndex]);
        for (const std::string& token : tokens) {
            if (m_data.verticalAngles.size() >= m_data.numVerticalAngles) break;
            
            float angle;
            if (parseFloat(token, angle)) {
                m_data.verticalAngles.push_back(angle * M_PI / 180.0f); // Convert to radians
            }
        }
        lineIndex++;
    }
    
    // Parse horizontal angles
    m_data.horizontalAngles.clear();
    m_data.horizontalAngles.reserve(m_data.numHorizontalAngles);
    
    while (m_data.horizontalAngles.size() < m_data.numHorizontalAngles && lineIndex < lines.size()) {
        std::vector<std::string> tokens = splitString(lines[lineIndex]);
        for (const std::string& token : tokens) {
            if (m_data.horizontalAngles.size() >= m_data.numHorizontalAngles) break;
            
            float angle;
            if (parseFloat(token, angle)) {
                m_data.horizontalAngles.push_back(angle * M_PI / 180.0f); // Convert to radians
            }
        }
        lineIndex++;
    }
    
    return m_data.verticalAngles.size() == m_data.numVerticalAngles &&
           m_data.horizontalAngles.size() == m_data.numHorizontalAngles;
}

bool IESProfile::parseCandelaValues(const std::vector<std::string>& lines, size_t& lineIndex) {
    // Initialize candela array
    m_data.candela.resize(m_data.numVerticalAngles);
    for (auto& row : m_data.candela) {
        row.resize(m_data.numHorizontalAngles);
    }
    
    // Parse candela values
    int totalValues = m_data.numVerticalAngles * m_data.numHorizontalAngles;
    int parsedValues = 0;
    
    for (int v = 0; v < m_data.numVerticalAngles && lineIndex < lines.size(); v++) {
        for (int h = 0; h < m_data.numHorizontalAngles; h++) {
            // Find next value
            while (lineIndex < lines.size() && parsedValues < totalValues) {
                std::vector<std::string> tokens = splitString(lines[lineIndex]);
                
                for (const std::string& token : tokens) {
                    if (parsedValues >= totalValues) break;
                    
                    float candela;
                    if (parseFloat(token, candela)) {
                        int vIndex = parsedValues / m_data.numHorizontalAngles;
                        int hIndex = parsedValues % m_data.numHorizontalAngles;
                        
                        if (vIndex < m_data.numVerticalAngles && hIndex < m_data.numHorizontalAngles) {
                            m_data.candela[vIndex][hIndex] = candela * m_data.multiplier;
                        }
                        
                        parsedValues++;
                    }
                }
                
                if (parsedValues < totalValues) {
                    lineIndex++;
                }
            }
        }
    }
    
    return parsedValues == totalValues;
}

bool IESProfile::validateData() {
    if (m_data.numVerticalAngles <= 0 || m_data.numHorizontalAngles <= 0) {
        return false;
    }
    
    if (m_data.verticalAngles.size() != m_data.numVerticalAngles ||
        m_data.horizontalAngles.size() != m_data.numHorizontalAngles) {
        return false;
    }
    
    if (m_data.candela.size() != m_data.numVerticalAngles) {
        return false;
    }
    
    for (const auto& row : m_data.candela) {
        if (row.size() != m_data.numHorizontalAngles) {
            return false;
        }
    }
    
    return true;
}

void IESProfile::computeDerivedValues() {
    // Find maximum candela
    m_data.maxCandela = 0.0f;
    for (const auto& row : m_data.candela) {
        for (float candela : row) {
            m_data.maxCandela = std::max(m_data.maxCandela, candela);
        }
    }
    
    // Compute total lumens (simplified)
    m_data.totalLumens = m_data.lumensPerLamp;
    if (m_data.totalLumens <= 0.0f) {
        m_data.totalLumens = m_data.maxCandela * 4.0f * M_PI; // Approximate
    }
}

float IESProfile::interpolateCandela(float theta, float phi) const {
    // Find indices for interpolation
    int thetaIndex0, thetaIndex1;
    float thetaT;
    findAngleIndices(m_data.verticalAngles, theta, thetaIndex0, thetaIndex1, thetaT);
    
    int phiIndex0, phiIndex1;
    float phiT;
    findAngleIndices(m_data.horizontalAngles, phi, phiIndex0, phiIndex1, phiT);
    
    // Bilinear interpolation
    float c00 = m_data.candela[thetaIndex0][phiIndex0];
    float c01 = m_data.candela[thetaIndex0][phiIndex1];
    float c10 = m_data.candela[thetaIndex1][phiIndex0];
    float c11 = m_data.candela[thetaIndex1][phiIndex1];
    
    float c0 = c00 * (1.0f - phiT) + c01 * phiT;
    float c1 = c10 * (1.0f - phiT) + c11 * phiT;
    
    return c0 * (1.0f - thetaT) + c1 * thetaT;
}

void IESProfile::findAngleIndices(const std::vector<float>& angles, float angle,
                                 int& index0, int& index1, float& t) const {
    if (angles.empty()) {
        index0 = index1 = 0;
        t = 0.0f;
        return;
    }
    
    // Find the interval containing the angle
    auto it = std::lower_bound(angles.begin(), angles.end(), angle);
    
    if (it == angles.begin()) {
        index0 = index1 = 0;
        t = 0.0f;
    } else if (it == angles.end()) {
        index0 = index1 = angles.size() - 1;
        t = 0.0f;
    } else {
        index1 = std::distance(angles.begin(), it);
        index0 = index1 - 1;
        
        float angle0 = angles[index0];
        float angle1 = angles[index1];
        t = (angle - angle0) / (angle1 - angle0);
    }
}

void IESProfile::directionToSpherical(const Vec3& direction, float& theta, float& phi) const {
    // Convert to spherical coordinates
    // theta: angle from +Z axis (0 to π)
    // phi: angle around Z axis (0 to 2π)
    
    Vec3 dir = direction.normalized();
    
    theta = std::acos(std::max(-1.0f, std::min(1.0f, dir.z)));
    phi = std::atan2(dir.y, dir.x);
    
    if (phi < 0.0f) {
        phi += 2.0f * M_PI;
    }
}

float IESProfile::normalizeAngle(float angle) const {
    while (angle < 0.0f) angle += 2.0f * M_PI;
    while (angle >= 2.0f * M_PI) angle -= 2.0f * M_PI;
    return angle;
}

std::vector<std::string> IESProfile::splitString(const std::string& str) const {
    std::vector<std::string> tokens;
    std::istringstream iss(str);
    std::string token;
    
    while (iss >> token) {
        tokens.push_back(token);
    }
    
    return tokens;
}

bool IESProfile::parseFloat(const std::string& str, float& value) const {
    try {
        value = std::stof(str);
        return true;
    } catch (...) {
        return false;
    }
}

bool IESProfile::parseInt(const std::string& str, int& value) const {
    try {
        value = std::stoi(str);
        return true;
    } catch (...) {
        return false;
    }
}

IESProfile::Statistics IESProfile::getStatistics() const {
    Statistics stats;

    if (!m_data.isValid) {
        return stats;
    }

    // Compute intensity statistics
    float minIntensity = std::numeric_limits<float>::max();
    float maxIntensity = 0.0f;
    float totalIntensity = 0.0f;
    int count = 0;

    for (const auto& row : m_data.candela) {
        for (float candela : row) {
            float intensity = candela / m_data.maxCandela;
            minIntensity = std::min(minIntensity, intensity);
            maxIntensity = std::max(maxIntensity, intensity);
            totalIntensity += intensity;
            count++;
        }
    }

    stats.minIntensity = minIntensity;
    stats.maxIntensity = maxIntensity;
    stats.avgIntensity = count > 0 ? totalIntensity / count : 0.0f;

    // Find beam and field angles (simplified)
    stats.beamAngle = M_PI / 4.0f;  // 45 degrees default
    stats.fieldAngle = M_PI / 2.0f; // 90 degrees default
    stats.primaryDirection = Vec3(0, 0, -1); // Downward
    stats.isSymmetric = (m_data.numHorizontalAngles <= 1);

    return stats;
}

std::shared_ptr<IESProfile> IESProfile::createConeProfile(float beamAngle, float fieldAngle) {
    auto profile = std::make_shared<IESProfile>();

    // Create simple cone distribution
    profile->m_data.numVerticalAngles = 19;   // 0 to 90 degrees in 5-degree steps
    profile->m_data.numHorizontalAngles = 1;  // Symmetric
    profile->m_data.lumensPerLamp = 1000.0f;
    profile->m_data.multiplier = 1.0f;

    // Setup angles
    for (int i = 0; i < profile->m_data.numVerticalAngles; i++) {
        float angle = (i * 5.0f) * M_PI / 180.0f; // 0, 5, 10, ..., 90 degrees
        profile->m_data.verticalAngles.push_back(angle);
    }

    profile->m_data.horizontalAngles.push_back(0.0f);

    // Setup candela values
    profile->m_data.candela.resize(profile->m_data.numVerticalAngles);
    for (int v = 0; v < profile->m_data.numVerticalAngles; v++) {
        profile->m_data.candela[v].resize(1);

        float theta = profile->m_data.verticalAngles[v];
        float intensity = 0.0f;

        if (theta <= beamAngle) {
            intensity = 1000.0f; // Full intensity
        } else if (theta <= fieldAngle) {
            // Linear falloff
            float t = (theta - beamAngle) / (fieldAngle - beamAngle);
            intensity = 1000.0f * (1.0f - t);
        }

        profile->m_data.candela[v][0] = intensity;
    }

    profile->validateData();
    profile->computeDerivedValues();
    profile->m_data.isValid = true;

    return profile;
}

std::shared_ptr<IESProfile> IESProfile::createUniformProfile() {
    auto profile = std::make_shared<IESProfile>();

    // Create uniform distribution
    profile->m_data.numVerticalAngles = 19;   // 0 to 90 degrees
    profile->m_data.numHorizontalAngles = 1;  // Symmetric
    profile->m_data.lumensPerLamp = 1000.0f;
    profile->m_data.multiplier = 1.0f;

    // Setup angles
    for (int i = 0; i < profile->m_data.numVerticalAngles; i++) {
        float angle = (i * 5.0f) * M_PI / 180.0f;
        profile->m_data.verticalAngles.push_back(angle);
    }

    profile->m_data.horizontalAngles.push_back(0.0f);

    // Setup uniform candela values
    profile->m_data.candela.resize(profile->m_data.numVerticalAngles);
    for (int v = 0; v < profile->m_data.numVerticalAngles; v++) {
        profile->m_data.candela[v].resize(1);
        profile->m_data.candela[v][0] = 1000.0f; // Uniform intensity
    }

    profile->validateData();
    profile->computeDerivedValues();
    profile->m_data.isValid = true;

    return profile;
}

} // namespace photon
