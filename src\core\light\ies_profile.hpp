// src/core/light/ies_profile.hpp
// PhotonRender - Professional Rendering Engine for SketchUp
// IES Profile System for photometric light data

#pragma once

#include "../math/vec3.hpp"
#include <vector>
#include <string>
#include <memory>
#include <cmath>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

namespace photon {

/**
 * @brief IES photometric data structure
 */
struct IESData {
    // Header information
    std::string manufacturer;
    std::string luminaire;
    std::string lampCatalog;
    std::string lampDescription;
    
    // Photometric data
    int numVerticalAngles = 0;      ///< Number of vertical angles
    int numHorizontalAngles = 0;    ///< Number of horizontal angles
    float lumensPerLamp = 0.0f;     ///< Lumens per lamp
    float multiplier = 1.0f;        ///< Candela multiplier
    
    // Angular data
    std::vector<float> verticalAngles;      ///< Vertical angles (0-180°)
    std::vector<float> horizontalAngles;    ///< Horizontal angles (0-360°)
    std::vector<std::vector<float>> candela; ///< Candela values [vertical][horizontal]
    
    // Computed values
    float maxCandela = 0.0f;        ///< Maximum candela value
    float totalLumens = 0.0f;       ///< Total lumens
    bool isValid = false;           ///< Data validity flag
};

/**
 * @brief IES Profile loader and evaluator
 * 
 * Loads and processes IES photometric data files for realistic
 * light distribution patterns.
 */
class IESProfile {
public:
    /**
     * @brief Constructor
     */
    IESProfile();
    
    /**
     * @brief Destructor
     */
    ~IESProfile() = default;
    
    // Non-copyable
    IESProfile(const IESProfile&) = delete;
    IESProfile& operator=(const IESProfile&) = delete;
    
    /**
     * @brief Load IES file from path
     * 
     * @param filePath Path to IES file
     * @return True if loaded successfully
     */
    bool loadFromFile(const std::string& filePath);
    
    /**
     * @brief Load IES data from string
     * 
     * @param iesData IES file content as string
     * @return True if loaded successfully
     */
    bool loadFromString(const std::string& iesData);
    
    /**
     * @brief Evaluate intensity at given direction
     * 
     * @param direction Direction vector (normalized)
     * @return Intensity factor [0,1]
     */
    float evaluate(const Vec3& direction) const;
    
    /**
     * @brief Evaluate intensity at spherical coordinates
     * 
     * @param theta Vertical angle (0-π radians)
     * @param phi Horizontal angle (0-2π radians)
     * @return Intensity factor [0,1]
     */
    float evaluate(float theta, float phi) const;
    
    /**
     * @brief Get maximum intensity
     */
    float getMaxIntensity() const { return m_data.maxCandela; }
    
    /**
     * @brief Get total lumens
     */
    float getTotalLumens() const { return m_data.totalLumens; }
    
    /**
     * @brief Check if profile is valid
     */
    bool isValid() const { return m_data.isValid; }
    
    /**
     * @brief Get IES data
     */
    const IESData& getData() const { return m_data; }
    
    /**
     * @brief Get profile statistics
     */
    struct Statistics {
        float maxIntensity = 0.0f;
        float minIntensity = 0.0f;
        float avgIntensity = 0.0f;
        float beamAngle = 0.0f;        ///< Beam angle (50% intensity)
        float fieldAngle = 0.0f;       ///< Field angle (10% intensity)
        Vec3 primaryDirection;         ///< Primary beam direction
        bool isSymmetric = false;      ///< Symmetric distribution
    };
    
    Statistics getStatistics() const;
    
    /**
     * @brief Create simple cone profile for testing
     * 
     * @param beamAngle Beam angle in radians
     * @param fieldAngle Field angle in radians
     * @return IES profile with cone distribution
     */
    static std::shared_ptr<IESProfile> createConeProfile(float beamAngle, float fieldAngle);
    
    /**
     * @brief Create uniform sphere profile
     * 
     * @return IES profile with uniform distribution
     */
    static std::shared_ptr<IESProfile> createUniformProfile();

private:
    IESData m_data;                     ///< Photometric data
    
    // Interpolation cache for performance
    mutable std::vector<float> m_thetaCache;
    mutable std::vector<float> m_phiCache;
    mutable bool m_cacheValid = false;
    
    /**
     * @brief Parse IES file header
     */
    bool parseHeader(const std::vector<std::string>& lines, size_t& lineIndex);
    
    /**
     * @brief Parse photometric data
     */
    bool parsePhotometricData(const std::vector<std::string>& lines, size_t& lineIndex);
    
    /**
     * @brief Parse angular data
     */
    bool parseAngularData(const std::vector<std::string>& lines, size_t& lineIndex);
    
    /**
     * @brief Parse candela values
     */
    bool parseCandelaValues(const std::vector<std::string>& lines, size_t& lineIndex);
    
    /**
     * @brief Validate loaded data
     */
    bool validateData();
    
    /**
     * @brief Compute derived values
     */
    void computeDerivedValues();
    
    /**
     * @brief Bilinear interpolation of candela values
     */
    float interpolateCandela(float theta, float phi) const;
    
    /**
     * @brief Find angle indices for interpolation
     */
    void findAngleIndices(const std::vector<float>& angles, float angle,
                         int& index0, int& index1, float& t) const;
    
    /**
     * @brief Convert direction to spherical coordinates
     */
    void directionToSpherical(const Vec3& direction, float& theta, float& phi) const;
    
    /**
     * @brief Normalize angle to [0, 2π]
     */
    float normalizeAngle(float angle) const;
    
    /**
     * @brief Update interpolation cache
     */
    void updateCache() const;
    
    /**
     * @brief Split string by whitespace
     */
    std::vector<std::string> splitString(const std::string& str) const;
    
    /**
     * @brief Parse float from string with error checking
     */
    bool parseFloat(const std::string& str, float& value) const;
    
    /**
     * @brief Parse int from string with error checking
     */
    bool parseInt(const std::string& str, int& value) const;
};

} // namespace photon
