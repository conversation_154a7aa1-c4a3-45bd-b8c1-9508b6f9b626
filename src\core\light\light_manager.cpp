// src/core/light/light_manager.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Light Manager implementation for efficient light management

#include "light_manager.hpp"
#include "../scene/scene.hpp"
#include "../scene/camera.hpp"
#include <iostream>
#include <algorithm>
#include <chrono>
#include <cmath>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

namespace photon {

// LightManager implementation
LightManager::LightManager() : m_cacheValid(false) {
}

LightManager::~LightManager() {
    clear();
}

void LightManager::initialize(const std::vector<std::shared_ptr<Light>>& lights, bool buildBVH) {
    clear();
    
    // Create managed lights
    m_managedLights.reserve(lights.size());
    for (const auto& light : lights) {
        if (light) {
            float importance = calculateLightImportance(light, Point3(0, 0, 0));
            m_managedLights.emplace_back(light, 0.0f, importance);
        }
    }
    
    // Build BVH if requested
    if (buildBVH && !lights.empty()) {
        m_bvh.build(lights);
    }
    
    m_cacheValid = false;
    std::cout << "LightManager initialized with " << m_managedLights.size() << " lights" << std::endl;
}

void LightManager::update(const Point3& cameraPosition, const std::array<Vec4, 6>& frustumPlanes, 
                         const LightCullingParams& params) {
    auto startTime = std::chrono::high_resolution_clock::now();
    
    m_cullingParams = params;
    resetFrameStatistics();
    
    // Update each managed light
    for (auto& managedLight : m_managedLights) {
        // Calculate distance to camera
        if (managedLight.light) {
            // Get light position (simplified - should use proper position extraction)
            Point3 lightPos = Point3(0, 0, 0); // TODO: Extract from light
            managedLight.distance = (lightPos - cameraPosition).length();
            
            // Update LOD based on distance
            updateLightLOD(managedLight, cameraPosition, params);
            
            // Update visibility (culling)
            updateLightVisibility(managedLight, cameraPosition, frustumPlanes, params);
        }
    }
    
    // Update statistics
    updateStatistics();
    
    // Cache current frame data
    m_lastCameraPosition = cameraPosition;
    m_lastFrustumPlanes = frustumPlanes;
    m_cacheValid = true;
    
    auto endTime = std::chrono::high_resolution_clock::now();
    m_stats.cullingTime = std::chrono::duration<float, std::milli>(endTime - startTime).count();
}

std::vector<std::shared_ptr<Light>> LightManager::getVisibleLights(const Point3& position, 
                                                                   float maxDistance) const {
    std::vector<std::shared_ptr<Light>> visibleLights;
    
    if (m_bvh.isBuilt()) {
        // Use BVH for efficient spatial queries
        auto startTime = std::chrono::high_resolution_clock::now();
        
        LightQuery query(position, maxDistance, m_cullingParams.minImportance);
        auto lightRefs = m_bvh.queryLights(query);
        
        auto endTime = std::chrono::high_resolution_clock::now();
        const_cast<LightManager*>(this)->m_stats.bvhQueryTime = 
            std::chrono::duration<float, std::milli>(endTime - startTime).count();
        
        // Filter by visibility
        for (const auto& lightRef : lightRefs) {
            // Find corresponding managed light
            auto it = std::find_if(m_managedLights.begin(), m_managedLights.end(),
                [&lightRef](const ManagedLight& ml) { return ml.light == lightRef.light; });
            
            if (it != m_managedLights.end() && it->isVisible) {
                visibleLights.push_back(lightRef.light);
            }
        }
    } else {
        // Fallback: linear search through managed lights
        for (const auto& managedLight : m_managedLights) {
            if (managedLight.isVisible && managedLight.light) {
                // Distance check
                Point3 lightPos = Point3(0, 0, 0); // TODO: Extract from light
                float distance = (lightPos - position).length();
                if (distance <= maxDistance) {
                    visibleLights.push_back(managedLight.light);
                }
            }
        }
    }
    
    return visibleLights;
}

std::vector<ManagedLight> LightManager::getManagedLights(const Point3& position, 
                                                        float maxDistance) const {
    std::vector<ManagedLight> result;
    
    for (const auto& managedLight : m_managedLights) {
        if (managedLight.isVisible && managedLight.light) {
            // Distance check
            Point3 lightPos = Point3(0, 0, 0); // TODO: Extract from light
            float distance = (lightPos - position).length();
            if (distance <= maxDistance) {
                result.push_back(managedLight);
            }
        }
    }
    
    return result;
}

std::vector<std::shared_ptr<Light>> LightManager::getLightsByLOD(LightLOD lod) const {
    std::vector<std::shared_ptr<Light>> result;
    
    for (const auto& managedLight : m_managedLights) {
        if (managedLight.lod == lod && managedLight.isVisible && managedLight.light) {
            result.push_back(managedLight.light);
        }
    }
    
    return result;
}

void LightManager::clear() {
    m_managedLights.clear();
    m_bvh.clear();
    m_cacheValid = false;
    m_stats.reset();
}

void LightManager::addLight(std::shared_ptr<Light> light) {
    if (light) {
        float importance = calculateLightImportance(light, Point3(0, 0, 0));
        m_managedLights.emplace_back(light, 0.0f, importance);
        m_cacheValid = false;
    }
}

void LightManager::removeLight(std::shared_ptr<Light> light) {
    auto it = std::remove_if(m_managedLights.begin(), m_managedLights.end(),
        [light](const ManagedLight& ml) { return ml.light == light; });
    
    if (it != m_managedLights.end()) {
        m_managedLights.erase(it, m_managedLights.end());
        m_cacheValid = false;
    }
}

void LightManager::rebuildBVH() {
    std::vector<std::shared_ptr<Light>> lights;
    lights.reserve(m_managedLights.size());
    
    for (const auto& managedLight : m_managedLights) {
        if (managedLight.light) {
            lights.push_back(managedLight.light);
        }
    }
    
    if (!lights.empty()) {
        m_bvh.build(lights);
    }
    
    m_cacheValid = false;
}

// Private methods
void LightManager::updateLightLOD(ManagedLight& managedLight, const Point3& cameraPosition, 
                                 const LightCullingParams& params) {
    if (!params.enableLOD) {
        managedLight.lod = LightLOD::HIGH;
        return;
    }
    
    float distance = managedLight.distance;
    
    if (distance <= params.lodHighDistance) {
        managedLight.lod = LightLOD::HIGH;
    } else if (distance <= params.lodMediumDistance) {
        managedLight.lod = LightLOD::MEDIUM;
    } else if (distance <= params.lodLowDistance) {
        managedLight.lod = LightLOD::LOW;
    } else {
        managedLight.lod = LightLOD::DISABLED;
    }
}

void LightManager::updateLightVisibility(ManagedLight& managedLight, const Point3& cameraPosition,
                                        const std::array<Vec4, 6>& frustumPlanes, 
                                        const LightCullingParams& params) {
    managedLight.isVisible = true;
    managedLight.inFrustum = true;
    
    // Distance culling
    if (params.enableDistanceCulling && managedLight.distance > params.maxDistance) {
        managedLight.isVisible = false;
        return;
    }
    
    // LOD culling (disabled lights are not visible)
    if (managedLight.lod == LightLOD::DISABLED) {
        managedLight.isVisible = false;
        return;
    }
    
    // Importance culling
    if (managedLight.importance < params.minImportance) {
        managedLight.isVisible = false;
        return;
    }
    
    // Frustum culling
    if (params.enableFrustumCulling) {
        managedLight.inFrustum = isLightInFrustum(managedLight, frustumPlanes);
        if (!managedLight.inFrustum) {
            managedLight.isVisible = false;
            return;
        }
    }
}

bool LightManager::isLightInFrustum(const ManagedLight& managedLight, 
                                   const std::array<Vec4, 6>& frustumPlanes) const {
    if (!managedLight.light) return false;
    
    // Get light position (simplified)
    Point3 lightPos = Point3(0, 0, 0); // TODO: Extract from light
    
    // Test point against all frustum planes
    for (const auto& plane : frustumPlanes) {
        float distance = plane.x * lightPos.x + plane.y * lightPos.y + plane.z * lightPos.z + plane.w;
        if (distance < 0.0f) {
            return false; // Outside this plane
        }
    }
    
    return true;
}

float LightManager::calculateLightImportance(std::shared_ptr<Light> light, const Point3& position) const {
    if (!light) return 0.0f;
    
    // Calculate importance based on light power
    Color3 power = light->power();
    float luminance = 0.299f * power.r + 0.587f * power.g + 0.114f * power.b;
    
    return std::max(0.0f, luminance);
}

void LightManager::updateStatistics() {
    m_stats.totalLights = static_cast<int>(m_managedLights.size());
    m_stats.visibleLights = 0;
    m_stats.culledByDistance = 0;
    m_stats.culledByFrustum = 0;
    m_stats.culledByImportance = 0;
    m_stats.highLOD = 0;
    m_stats.mediumLOD = 0;
    m_stats.lowLOD = 0;
    m_stats.disabledLOD = 0;
    
    for (const auto& managedLight : m_managedLights) {
        if (managedLight.isVisible) {
            m_stats.visibleLights++;
        } else {
            // Count culling reasons
            if (managedLight.distance > m_cullingParams.maxDistance) {
                m_stats.culledByDistance++;
            } else if (!managedLight.inFrustum) {
                m_stats.culledByFrustum++;
            } else if (managedLight.importance < m_cullingParams.minImportance) {
                m_stats.culledByImportance++;
            }
        }
        
        // Count LOD levels
        switch (managedLight.lod) {
            case LightLOD::HIGH: m_stats.highLOD++; break;
            case LightLOD::MEDIUM: m_stats.mediumLOD++; break;
            case LightLOD::LOW: m_stats.lowLOD++; break;
            case LightLOD::DISABLED: m_stats.disabledLOD++; break;
        }
    }
}

void LightManager::resetFrameStatistics() {
    m_stats.cullingTime = 0.0f;
    m_stats.bvhQueryTime = 0.0f;
}

// LightManagerUtils implementation
namespace LightManagerUtils {

std::array<Vec4, 6> extractFrustumPlanes(const Matrix4& viewProjMatrix) {
    std::array<Vec4, 6> planes;

    // Extract frustum planes from view-projection matrix
    // Left plane
    planes[0] = Vec4(
        viewProjMatrix.m[3][0] + viewProjMatrix.m[0][0],
        viewProjMatrix.m[3][1] + viewProjMatrix.m[0][1],
        viewProjMatrix.m[3][2] + viewProjMatrix.m[0][2],
        viewProjMatrix.m[3][3] + viewProjMatrix.m[0][3]
    );

    // Right plane
    planes[1] = Vec4(
        viewProjMatrix.m[3][0] - viewProjMatrix.m[0][0],
        viewProjMatrix.m[3][1] - viewProjMatrix.m[0][1],
        viewProjMatrix.m[3][2] - viewProjMatrix.m[0][2],
        viewProjMatrix.m[3][3] - viewProjMatrix.m[0][3]
    );

    // Bottom plane
    planes[2] = Vec4(
        viewProjMatrix.m[3][0] + viewProjMatrix.m[1][0],
        viewProjMatrix.m[3][1] + viewProjMatrix.m[1][1],
        viewProjMatrix.m[3][2] + viewProjMatrix.m[1][2],
        viewProjMatrix.m[3][3] + viewProjMatrix.m[1][3]
    );

    // Top plane
    planes[3] = Vec4(
        viewProjMatrix.m[3][0] - viewProjMatrix.m[1][0],
        viewProjMatrix.m[3][1] - viewProjMatrix.m[1][1],
        viewProjMatrix.m[3][2] - viewProjMatrix.m[1][2],
        viewProjMatrix.m[3][3] - viewProjMatrix.m[1][3]
    );

    // Near plane
    planes[4] = Vec4(
        viewProjMatrix.m[3][0] + viewProjMatrix.m[2][0],
        viewProjMatrix.m[3][1] + viewProjMatrix.m[2][1],
        viewProjMatrix.m[3][2] + viewProjMatrix.m[2][2],
        viewProjMatrix.m[3][3] + viewProjMatrix.m[2][3]
    );

    // Far plane
    planes[5] = Vec4(
        viewProjMatrix.m[3][0] - viewProjMatrix.m[2][0],
        viewProjMatrix.m[3][1] - viewProjMatrix.m[2][1],
        viewProjMatrix.m[3][2] - viewProjMatrix.m[2][2],
        viewProjMatrix.m[3][3] - viewProjMatrix.m[2][3]
    );

    // Normalize planes
    for (auto& plane : planes) {
        float length = std::sqrt(plane.x * plane.x + plane.y * plane.y + plane.z * plane.z);
        if (length > 0.0f) {
            plane.x /= length;
            plane.y /= length;
            plane.z /= length;
            plane.w /= length;
        }
    }

    return planes;
}

bool isPointInFrustum(const Point3& point, const std::array<Vec4, 6>& frustumPlanes) {
    for (const auto& plane : frustumPlanes) {
        float distance = plane.x * point.x + plane.y * point.y + plane.z * point.z + plane.w;
        if (distance < 0.0f) {
            return false; // Outside this plane
        }
    }
    return true;
}

float calculateLightImportance(std::shared_ptr<Light> light, const Point3& position) {
    if (!light) return 0.0f;

    // Calculate importance based on light power and distance
    Color3 power = light->power();
    float luminance = 0.299f * power.r + 0.587f * power.g + 0.114f * power.b;

    // For delta lights, importance doesn't depend on distance
    if (light->isDelta()) {
        return luminance;
    }

    // For area lights, importance decreases with distance
    // TODO: Get actual light position
    Point3 lightPos = Point3(0, 0, 0);
    float distance = (lightPos - position).length();
    float attenuation = 1.0f / (1.0f + distance * distance);

    return luminance * attenuation;
}

} // namespace LightManagerUtils

} // namespace photon
