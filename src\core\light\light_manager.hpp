// src/core/light/light_manager.hpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Light Manager for efficient light management with culling and LOD

#pragma once

#include "../math/vec3.hpp"
#include "../math/ray.hpp"
#include "../math/matrix4.hpp"
#include "../scene/light.hpp"
#include "../accelerator/light_bvh.hpp"
#include <memory>
#include <vector>
#include <unordered_map>
#include <array>
#include <chrono>

namespace photon {

// Forward declarations
class Scene;
class Camera;
class Intersection;

/**
 * @brief Light Level-of-Detail settings
 */
enum class LightLOD {
    HIGH,       ///< Full quality (close lights)
    MEDIUM,     ///< Reduced quality (medium distance)
    LOW,        ///< Minimal quality (far lights)
    DISABLED    ///< Light disabled (very far)
};

/**
 * @brief Light culling parameters
 */
struct LightCullingParams {
    float maxDistance;          ///< Maximum light distance
    float lodHighDistance;      ///< Distance for HIGH LOD
    float lodMediumDistance;    ///< Distance for MEDIUM LOD
    float lodLowDistance;       ///< Distance for LOW LOD
    bool enableFrustumCulling;  ///< Enable frustum culling
    bool enableDistanceCulling; ///< Enable distance culling
    bool enableLOD;             ///< Enable LOD system
    float minImportance;        ///< Minimum light importance
    
    /**
     * @brief Default constructor with reasonable defaults
     */
    LightCullingParams() 
        : maxDistance(100.0f)
        , lodHighDistance(10.0f)
        , lodMediumDistance(25.0f)
        , lodLowDistance(50.0f)
        , enableFrustumCulling(true)
        , enableDistanceCulling(true)
        , enableLOD(true)
        , minImportance(0.01f) {}
};

/**
 * @brief Light with LOD information
 */
struct ManagedLight {
    std::shared_ptr<Light> light;   ///< Light pointer
    LightLOD lod;                   ///< Current LOD level
    float distance;                 ///< Distance to camera/query point
    float importance;               ///< Light importance factor
    bool isVisible;                 ///< Is light visible (not culled)
    bool inFrustum;                 ///< Is light in camera frustum
    
    /**
     * @brief Constructor
     */
    ManagedLight(std::shared_ptr<Light> light, float distance = 0.0f, float importance = 1.0f)
        : light(light), lod(LightLOD::HIGH), distance(distance), importance(importance)
        , isVisible(true), inFrustum(true) {}
};

/**
 * @brief Light Manager statistics
 */
struct LightManagerStats {
    int totalLights;            ///< Total number of lights
    int visibleLights;          ///< Number of visible lights
    int culledByDistance;       ///< Lights culled by distance
    int culledByFrustum;        ///< Lights culled by frustum
    int culledByImportance;     ///< Lights culled by importance
    int highLOD;                ///< Lights at HIGH LOD
    int mediumLOD;              ///< Lights at MEDIUM LOD
    int lowLOD;                 ///< Lights at LOW LOD
    int disabledLOD;            ///< Lights at DISABLED LOD
    float cullingTime;          ///< Time spent culling (ms)
    float bvhQueryTime;         ///< Time spent in BVH queries (ms)
    
    /**
     * @brief Constructor
     */
    LightManagerStats() { reset(); }
    
    /**
     * @brief Reset all statistics
     */
    void reset() {
        totalLights = visibleLights = culledByDistance = culledByFrustum = culledByImportance = 0;
        highLOD = mediumLOD = lowLOD = disabledLOD = 0;
        cullingTime = bvhQueryTime = 0.0f;
    }
    
    /**
     * @brief Get culling efficiency (percentage of lights culled)
     */
    float getCullingEfficiency() const {
        if (totalLights == 0) return 0.0f;
        return 100.0f * (1.0f - static_cast<float>(visibleLights) / totalLights);
    }
};

/**
 * @brief Light Manager for efficient light management
 */
class LightManager {
public:
    /**
     * @brief Constructor
     */
    LightManager();
    
    /**
     * @brief Destructor
     */
    ~LightManager();
    
    /**
     * @brief Initialize with lights from scene
     * 
     * @param lights Vector of lights to manage
     * @param buildBVH Whether to build BVH acceleration structure
     */
    void initialize(const std::vector<std::shared_ptr<Light>>& lights, bool buildBVH = true);
    
    /**
     * @brief Update light management for current frame
     * 
     * @param cameraPosition Camera position for distance calculations
     * @param frustumPlanes Camera frustum planes (6 planes)
     * @param params Culling parameters
     */
    void update(const Point3& cameraPosition, const std::array<Vec4, 6>& frustumPlanes, 
                const LightCullingParams& params = LightCullingParams());
    
    /**
     * @brief Get visible lights for rendering
     * 
     * @param position Query position (e.g., surface point)
     * @param maxDistance Maximum distance to consider
     * @return Vector of visible lights within distance
     */
    std::vector<std::shared_ptr<Light>> getVisibleLights(const Point3& position, 
                                                         float maxDistance = 1e30f) const;
    
    /**
     * @brief Get lights with LOD information
     * 
     * @param position Query position
     * @param maxDistance Maximum distance to consider
     * @return Vector of managed lights with LOD info
     */
    std::vector<ManagedLight> getManagedLights(const Point3& position, 
                                              float maxDistance = 1e30f) const;
    
    /**
     * @brief Get lights for specific LOD level
     * 
     * @param lod LOD level to filter by
     * @return Vector of lights at specified LOD
     */
    std::vector<std::shared_ptr<Light>> getLightsByLOD(LightLOD lod) const;
    
    /**
     * @brief Get all managed lights (for fallback)
     */
    const std::vector<ManagedLight>& getAllManagedLights() const { return m_managedLights; }
    
    /**
     * @brief Check if BVH is enabled
     */
    bool isBVHEnabled() const { return m_bvh.isBuilt(); }
    
    /**
     * @brief Get BVH statistics
     */
    LightBVH::Statistics getBVHStatistics() const { return m_bvh.getStatistics(); }
    
    /**
     * @brief Get light manager statistics
     */
    const LightManagerStats& getStatistics() const { return m_stats; }
    
    /**
     * @brief Set culling parameters
     */
    void setCullingParams(const LightCullingParams& params) { m_cullingParams = params; }
    
    /**
     * @brief Get culling parameters
     */
    const LightCullingParams& getCullingParams() const { return m_cullingParams; }
    
    /**
     * @brief Clear all lights
     */
    void clear();
    
    /**
     * @brief Add single light
     */
    void addLight(std::shared_ptr<Light> light);
    
    /**
     * @brief Remove light
     */
    void removeLight(std::shared_ptr<Light> light);
    
    /**
     * @brief Rebuild BVH (call after adding/removing lights)
     */
    void rebuildBVH();

private:
    LightBVH m_bvh;                         ///< Light BVH for spatial acceleration
    std::vector<ManagedLight> m_managedLights; ///< All managed lights
    LightCullingParams m_cullingParams;     ///< Current culling parameters
    LightManagerStats m_stats;              ///< Performance statistics
    
    // Cache for current frame
    Point3 m_lastCameraPosition;            ///< Last camera position
    std::array<Vec4, 6> m_lastFrustumPlanes; ///< Last frustum planes
    bool m_cacheValid;                      ///< Is cache valid?
    
    // Internal methods
    void updateLightLOD(ManagedLight& managedLight, const Point3& cameraPosition, 
                       const LightCullingParams& params);
    void updateLightVisibility(ManagedLight& managedLight, const Point3& cameraPosition,
                              const std::array<Vec4, 6>& frustumPlanes, 
                              const LightCullingParams& params);
    bool isLightInFrustum(const ManagedLight& managedLight, 
                         const std::array<Vec4, 6>& frustumPlanes) const;
    float calculateLightImportance(std::shared_ptr<Light> light, const Point3& position) const;
    
    // Statistics helpers
    void updateStatistics();
    void resetFrameStatistics();
};

/**
 * @brief Utility functions for frustum calculations
 */
namespace LightManagerUtils {
    /**
     * @brief Extract frustum planes from view-projection matrix
     * 
     * @param viewProjMatrix Combined view-projection matrix
     * @return Array of 6 frustum planes
     */
    std::array<Vec4, 6> extractFrustumPlanes(const Matrix4& viewProjMatrix);
    
    /**
     * @brief Check if point is inside frustum
     * 
     * @param point Point to test
     * @param frustumPlanes Array of 6 frustum planes
     * @return True if point is inside frustum
     */
    bool isPointInFrustum(const Point3& point, const std::array<Vec4, 6>& frustumPlanes);
    
    /**
     * @brief Calculate light importance based on power and distance
     * 
     * @param light Light to evaluate
     * @param position Query position
     * @return Importance factor [0, inf)
     */
    float calculateLightImportance(std::shared_ptr<Light> light, const Point3& position);
}

} // namespace photon
