// src/core/light/light_memory_pool.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Memory Pool implementation for light data optimization

#include "light_memory_pool.hpp"
#include "../scene/light.hpp"
#include "../light/spot_light.hpp"
#include "../light/area_light_base.hpp"
#include "../light/rectangle_light.hpp"
#include "../light/disk_light.hpp"
#include "../light/sphere_light.hpp"
#include "../light/photometric_light.hpp"
#include <iostream>
#include <algorithm>
#include <cmath>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

namespace photon {

// LightMemoryPool implementation
LightMemoryPool::LightMemoryPool(size_t blockSize, size_t initialBlocks)
    : m_blockSize(blockSize), m_currentBlock(0), m_threadSafe(false), m_totalAllocated(0), m_totalUsed(0) {
    
    // Allocate initial blocks
    for (size_t i = 0; i < initialBlocks; i++) {
        addNewBlock();
    }
    
    std::cout << "LightMemoryPool initialized with " << initialBlocks << " blocks of " 
              << blockSize << " bytes each" << std::endl;
}

LightMemoryPool::~LightMemoryPool() {
    reset();
}

void* LightMemoryPool::allocate(size_t size, size_t alignment) {
    if (size == 0) return nullptr;
    
    // Find block with enough space
    MemoryBlock* block = findBlockWithSpace(size, alignment);
    
    if (!block) {
        // Add new block
        addNewBlock();
        block = findBlockWithSpace(size, alignment);
        
        if (!block) {
            std::cerr << "LightMemoryPool: Failed to allocate " << size << " bytes" << std::endl;
            return nullptr;
        }
    }
    
    void* ptr = block->allocate(size, alignment);
    if (ptr) {
        m_totalUsed += size;
    }
    
    return ptr;
}

void LightMemoryPool::deallocate(void* ptr) {
    // Pool allocator doesn't support individual deallocation
    // Memory is freed when pool is reset
}

void LightMemoryPool::reset() {
    for (auto& block : m_blocks) {
        block->reset();
    }
    m_currentBlock = 0;
    m_totalUsed = 0;
}

LightMemoryPool::Statistics LightMemoryPool::getStatistics() const {
    Statistics stats;
    
    stats.totalBlocks = m_blocks.size();
    stats.totalAllocated = m_blocks.size() * m_blockSize;
    stats.totalUsed = m_totalUsed;
    
    // Find largest free block
    stats.largestFreeBlock = 0;
    for (const auto& block : m_blocks) {
        stats.largestFreeBlock = std::max(stats.largestFreeBlock, block->getFreeSpace());
    }
    
    // Calculate fragmentation
    if (stats.totalAllocated > 0) {
        size_t totalFree = stats.totalAllocated - stats.totalUsed;
        if (totalFree > 0) {
            stats.fragmentation = 1.0f - (static_cast<float>(stats.largestFreeBlock) / totalFree);
        }
    }
    
    return stats;
}

void LightMemoryPool::addNewBlock() {
    m_blocks.push_back(std::make_unique<MemoryBlock>(m_blockSize));
    m_totalAllocated += m_blockSize;
}

LightMemoryPool::MemoryBlock* LightMemoryPool::findBlockWithSpace(size_t size, size_t alignment) {
    // Try current block first
    if (m_currentBlock < m_blocks.size()) {
        auto& block = m_blocks[m_currentBlock];
        if (block->getFreeSpace() >= size + alignment) {
            return block.get();
        }
    }
    
    // Search all blocks
    for (size_t i = 0; i < m_blocks.size(); i++) {
        auto& block = m_blocks[i];
        if (block->getFreeSpace() >= size + alignment) {
            m_currentBlock = i;
            return block.get();
        }
    }
    
    return nullptr;
}

// LightDataStorage implementation
LightDataStorage::LightDataStorage() : m_nextLightId(1) {
}

LightDataStorage::~LightDataStorage() {
    clear();
}

void LightDataStorage::initialize(std::shared_ptr<LightMemoryPool> memoryPool) {
    m_memoryPool = memoryPool;
}

uint32_t LightDataStorage::addLight(std::shared_ptr<Light> light) {
    if (!light) return 0;
    
    // Compress light data
    CompressedLightData compressedData = compressLight(light);
    
    // Allocate slot
    size_t slot = allocateSlot();
    
    // Store data
    if (slot < m_lightData.size()) {
        m_lightData[slot] = compressedData;
    } else {
        m_lightData.push_back(compressedData);
    }
    
    // Map light ID to slot
    uint32_t lightId = m_nextLightId++;
    m_lightIdMap[lightId] = slot;
    
    return lightId;
}

void LightDataStorage::removeLight(uint32_t lightId) {
    auto it = m_lightIdMap.find(lightId);
    if (it != m_lightIdMap.end()) {
        size_t slot = it->second;
        deallocateSlot(slot);
        m_lightIdMap.erase(it);
    }
}

const CompressedLightData* LightDataStorage::getLightData(uint32_t lightId) const {
    auto it = m_lightIdMap.find(lightId);
    if (it != m_lightIdMap.end() && it->second < m_lightData.size()) {
        return &m_lightData[it->second];
    }
    return nullptr;
}

void LightDataStorage::updateLight(uint32_t lightId, std::shared_ptr<Light> light) {
    auto it = m_lightIdMap.find(lightId);
    if (it != m_lightIdMap.end() && it->second < m_lightData.size()) {
        m_lightData[it->second] = compressLight(light);
    }
}

void LightDataStorage::clear() {
    m_lightData.clear();
    m_lightIdMap.clear();
    m_freeSlots.clear();
    m_nextLightId = 1;
}

void LightDataStorage::compact() {
    if (m_freeSlots.empty()) return;
    
    // Sort free slots in descending order
    std::sort(m_freeSlots.begin(), m_freeSlots.end(), std::greater<size_t>());
    
    // Remove free slots from the end
    for (size_t freeSlot : m_freeSlots) {
        if (freeSlot == m_lightData.size() - 1) {
            m_lightData.pop_back();
        }
    }
    
    m_freeSlots.clear();
}

LightDataStorage::Statistics LightDataStorage::getStatistics() const {
    Statistics stats;
    
    stats.lightCount = m_lightData.size() - m_freeSlots.size();
    stats.dataSize = m_lightData.size() * sizeof(CompressedLightData);
    stats.memoryUsed = stats.dataSize;
    
    // Estimate compression ratio (compressed vs uncompressed)
    if (stats.lightCount > 0) {
        size_t uncompressedSize = stats.lightCount * 200; // Estimate 200 bytes per light
        stats.compressionRatio = static_cast<float>(stats.dataSize) / uncompressedSize;
    }
    
    // Estimate cache efficiency
    stats.cacheEfficiency = LightMemoryUtils::calculateCacheEfficiency(m_lightData);
    
    return stats;
}

CompressedLightData LightDataStorage::compressLight(std::shared_ptr<Light> light) const {
    CompressedLightData data;
    
    if (!light) return data;
    
    // Set light type
    data.lightType = static_cast<uint32_t>(getLightType(light));
    
    // Set light flags
    data.flags = getLightFlags(light);
    
    // Extract light-specific data
    std::string lightName = light->getName();
    
    if (lightName == "Point") {
        // Point light - position only
        data.setPosition(Point3(0, 0, 0)); // TODO: Extract actual position
        data.radius = 0.01f;
    }
    else if (lightName == "SpotLight") {
        auto spotLight = std::dynamic_pointer_cast<SpotLight>(light);
        if (spotLight) {
            data.setPosition(Point3(spotLight->getPosition().x, spotLight->getPosition().y, spotLight->getPosition().z));
            data.setDirection(spotLight->getDirection());
            data.innerAngle = spotLight->getInnerAngle();
            data.outerAngle = spotLight->getOuterAngle();
            data.radius = 0.01f;
        }
    }
    else if (lightName == "Rectangle") {
        auto rectLight = std::dynamic_pointer_cast<RectangleLight>(light);
        if (rectLight) {
            data.setPosition(Point3(rectLight->getCenter().x, rectLight->getCenter().y, rectLight->getCenter().z));
            data.radius = std::sqrt(rectLight->getArea() / M_PI);
        }
    }
    else if (lightName == "Disk") {
        auto diskLight = std::dynamic_pointer_cast<DiskLight>(light);
        if (diskLight) {
            data.setPosition(Point3(diskLight->getCenter().x, diskLight->getCenter().y, diskLight->getCenter().z));
            data.radius = diskLight->getRadius();
        }
    }
    else if (lightName == "Sphere") {
        auto sphereLight = std::dynamic_pointer_cast<SphereLight>(light);
        if (sphereLight) {
            data.setPosition(Point3(sphereLight->getCenter().x, sphereLight->getCenter().y, sphereLight->getCenter().z));
            data.radius = sphereLight->getRadius();
        }
    }
    
    // Set intensity from light power
    Color3 power = light->power();
    data.setIntensity(power);
    
    return data;
}

CompressedLightType LightDataStorage::getLightType(std::shared_ptr<Light> light) const {
    if (!light) return CompressedLightType::POINT;
    
    std::string lightName = light->getName();
    
    if (lightName == "Point") return CompressedLightType::POINT;
    if (lightName == "Directional") return CompressedLightType::DIRECTIONAL;
    if (lightName == "SpotLight") return CompressedLightType::SPOT;
    if (lightName == "Rectangle") return CompressedLightType::AREA_RECTANGLE;
    if (lightName == "Disk") return CompressedLightType::AREA_DISK;
    if (lightName == "Sphere") return CompressedLightType::AREA_SPHERE;
    if (lightName == "Environment") return CompressedLightType::ENVIRONMENT;
    if (lightName == "PhotometricLight") return CompressedLightType::PHOTOMETRIC;
    
    return CompressedLightType::POINT;
}

uint32_t LightDataStorage::getLightFlags(std::shared_ptr<Light> light) const {
    if (!light) return 0;
    
    uint32_t flags = 0;
    
    // Always enabled for now
    flags |= static_cast<uint32_t>(CompressedLightFlags::ENABLED);
    
    // Check if delta light
    if (light->isDelta()) {
        flags |= static_cast<uint32_t>(CompressedLightFlags::DELTA);
    }
    
    // Default flags
    flags |= static_cast<uint32_t>(CompressedLightFlags::CAST_SHADOWS);
    flags |= static_cast<uint32_t>(CompressedLightFlags::VISIBLE_IN_RENDER);
    
    return flags;
}

size_t LightDataStorage::allocateSlot() {
    if (!m_freeSlots.empty()) {
        size_t slot = m_freeSlots.back();
        m_freeSlots.pop_back();
        return slot;
    }
    
    return m_lightData.size();
}

void LightDataStorage::deallocateSlot(size_t slot) {
    if (slot < m_lightData.size()) {
        m_freeSlots.push_back(slot);
    }
}

// LightMemoryManager implementation
LightMemoryManager::LightMemoryManager() : m_compressionEnabled(true) {
}

LightMemoryManager::~LightMemoryManager() {
    clear();
}

void LightMemoryManager::initialize(size_t poolSize, bool enableCompression) {
    m_compressionEnabled = enableCompression;
    
    // Create memory pool
    size_t blockSize = std::min(poolSize / 4, static_cast<size_t>(64 * 1024));
    size_t numBlocks = (poolSize + blockSize - 1) / blockSize;
    
    m_memoryPool = std::make_shared<LightMemoryPool>(blockSize, numBlocks);
    
    // Create storage
    m_storage = std::make_unique<LightDataStorage>();
    m_storage->initialize(m_memoryPool);
    
    std::cout << "LightMemoryManager initialized with " << poolSize << " bytes pool" << std::endl;
}

uint32_t LightMemoryManager::registerLight(std::shared_ptr<Light> light) {
    if (!m_storage) return 0;
    
    return m_storage->addLight(light);
}

void LightMemoryManager::unregisterLight(uint32_t lightHandle) {
    if (m_storage) {
        m_storage->removeLight(lightHandle);
    }
}

void LightMemoryManager::updateLight(uint32_t lightHandle, std::shared_ptr<Light> light) {
    if (m_storage) {
        m_storage->updateLight(lightHandle, light);
    }
}

const CompressedLightData* LightMemoryManager::getLightData(uint32_t lightHandle) const {
    if (m_storage) {
        return m_storage->getLightData(lightHandle);
    }
    return nullptr;
}

std::vector<CompressedLightData> LightMemoryManager::getAllLights() const {
    if (m_storage) {
        return m_storage->getAllLightData();
    }
    return {};
}

void LightMemoryManager::optimize() {
    if (m_storage) {
        m_storage->compact();
    }
}

LightMemoryManager::Statistics LightMemoryManager::getStatistics() const {
    updateStatistics();
    return m_lastStats;
}

void LightMemoryManager::clear() {
    if (m_storage) {
        m_storage->clear();
    }
    if (m_memoryPool) {
        m_memoryPool->reset();
    }
}

void LightMemoryManager::updateStatistics() const {
    auto now = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - m_lastStatsUpdate);
    
    // Update stats every 100ms
    if (elapsed.count() < 100) return;
    
    if (m_memoryPool) {
        m_lastStats.poolStats = m_memoryPool->getStatistics();
    }
    
    if (m_storage) {
        m_lastStats.storageStats = m_storage->getStatistics();
    }
    
    m_lastStats.totalMemoryUsed = m_lastStats.poolStats.totalUsed + m_lastStats.storageStats.memoryUsed;
    
    // Calculate overall efficiency
    if (m_lastStats.poolStats.totalAllocated > 0) {
        float poolEfficiency = static_cast<float>(m_lastStats.poolStats.totalUsed) / m_lastStats.poolStats.totalAllocated;
        float storageEfficiency = m_lastStats.storageStats.cacheEfficiency;
        m_lastStats.overallEfficiency = (poolEfficiency + storageEfficiency) / 2.0f;
    }
    
    m_lastStatsUpdate = now;
}

// LightMemoryUtils implementation
namespace LightMemoryUtils {

size_t calculateOptimalPoolSize(size_t lightCount) {
    // Estimate memory per light
    size_t bytesPerLight = sizeof(CompressedLightData) + 64; // Extra for overhead

    // Base pool size
    size_t baseSize = lightCount * bytesPerLight;

    // Add 50% overhead for fragmentation and growth
    size_t poolSize = baseSize + (baseSize / 2);

    // Minimum 64KB, maximum 16MB
    poolSize = std::max(poolSize, static_cast<size_t>(64 * 1024));
    poolSize = std::min(poolSize, static_cast<size_t>(16 * 1024 * 1024));

    return poolSize;
}

size_t estimateLightMemoryUsage(std::shared_ptr<Light> light) {
    if (!light) return 0;

    // Base compressed data size
    size_t baseSize = sizeof(CompressedLightData);

    // Additional memory based on light type
    std::string lightName = light->getName();

    if (lightName == "PhotometricLight") {
        baseSize += 1024; // IES profile data
    } else if (lightName == "Environment") {
        baseSize += 4096; // HDR texture data
    } else if (lightName.find("Area") != std::string::npos) {
        baseSize += 256; // Area light geometry
    }

    return baseSize;
}

float calculateCacheEfficiency(const std::vector<CompressedLightData>& data) {
    if (data.empty()) return 1.0f;

    // Check data alignment and layout
    size_t dataSize = data.size() * sizeof(CompressedLightData);
    size_t cacheLineSize = 64; // Typical cache line size

    // Calculate how well data fits in cache lines
    size_t cacheLinesUsed = (dataSize + cacheLineSize - 1) / cacheLineSize;
    size_t optimalCacheLines = dataSize / cacheLineSize;

    if (cacheLinesUsed == 0) return 1.0f;

    float efficiency = static_cast<float>(optimalCacheLines) / cacheLinesUsed;

    // Bonus for sequential access pattern
    if (data.size() > 1) {
        efficiency *= 1.1f; // 10% bonus for sequential layout
    }

    return std::min(1.0f, efficiency);
}

} // namespace LightMemoryUtils

} // namespace photon
