// src/core/light/light_memory_pool.hpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Memory Pool and optimization for light data

#pragma once

#include "../math/vec3.hpp"
#include "../math/color3.hpp"
#include <memory>
#include <vector>
#include <unordered_map>
#include <cstdint>
#include <cstring>
#include <atomic>
#include <chrono>

namespace photon {

// Forward declarations
class Light;

/**
 * @brief Compressed light data for cache-friendly storage
 */
struct CompressedLightData {
    uint32_t lightType;         ///< Light type ID (4 bytes)
    float position[3];          ///< Light position (12 bytes)
    float direction[3];         ///< Light direction (12 bytes)
    float intensity[3];         ///< Light intensity RGB (12 bytes)
    float radius;               ///< Light radius/size (4 bytes)
    float innerAngle;           ///< Inner angle for spot lights (4 bytes)
    float outerAngle;           ///< Outer angle for spot lights (4 bytes)
    uint32_t flags;             ///< Light flags (enabled, delta, etc.) (4 bytes)
    
    // Total: 56 bytes (cache-line friendly)
    
    /**
     * @brief Default constructor
     */
    CompressedLightData() {
        std::memset(this, 0, sizeof(CompressedLightData));
    }
    
    /**
     * @brief Set position
     */
    void setPosition(const Point3& pos) {
        position[0] = pos.x;
        position[1] = pos.y;
        position[2] = pos.z;
    }
    
    /**
     * @brief Get position
     */
    Point3 getPosition() const {
        return Point3(position[0], position[1], position[2]);
    }
    
    /**
     * @brief Set direction
     */
    void setDirection(const Vec3& dir) {
        direction[0] = dir.x;
        direction[1] = dir.y;
        direction[2] = dir.z;
    }
    
    /**
     * @brief Get direction
     */
    Vec3 getDirection() const {
        return Vec3(direction[0], direction[1], direction[2]);
    }
    
    /**
     * @brief Set intensity
     */
    void setIntensity(const Color3& color) {
        intensity[0] = color.r;
        intensity[1] = color.g;
        intensity[2] = color.b;
    }
    
    /**
     * @brief Get intensity
     */
    Color3 getIntensity() const {
        return Color3(intensity[0], intensity[1], intensity[2]);
    }
};

/**
 * @brief Light type enumeration for compressed storage
 */
enum class CompressedLightType : uint32_t {
    POINT = 0,
    DIRECTIONAL = 1,
    SPOT = 2,
    AREA_RECTANGLE = 3,
    AREA_DISK = 4,
    AREA_SPHERE = 5,
    ENVIRONMENT = 6,
    PHOTOMETRIC = 7
};

/**
 * @brief Light flags for compressed storage
 */
enum class CompressedLightFlags : uint32_t {
    ENABLED = 1 << 0,
    DELTA = 1 << 1,
    TWO_SIDED = 1 << 2,
    CAST_SHADOWS = 1 << 3,
    VISIBLE_IN_RENDER = 1 << 4
};

/**
 * @brief Memory pool for light data allocation
 */
class LightMemoryPool {
public:
    /**
     * @brief Constructor
     * 
     * @param blockSize Size of each memory block
     * @param initialBlocks Initial number of blocks to allocate
     */
    LightMemoryPool(size_t blockSize = 64 * 1024, size_t initialBlocks = 4);
    
    /**
     * @brief Destructor
     */
    ~LightMemoryPool();
    
    /**
     * @brief Allocate memory
     * 
     * @param size Size to allocate
     * @param alignment Memory alignment (default: 16 bytes)
     * @return Pointer to allocated memory, nullptr on failure
     */
    void* allocate(size_t size, size_t alignment = 16);
    
    /**
     * @brief Deallocate memory (no-op for pool allocator)
     * 
     * @param ptr Pointer to deallocate
     */
    void deallocate(void* ptr);
    
    /**
     * @brief Reset pool (deallocate all memory)
     */
    void reset();
    
    /**
     * @brief Get memory statistics
     */
    struct Statistics {
        size_t totalAllocated;      ///< Total memory allocated
        size_t totalUsed;           ///< Total memory used
        size_t totalBlocks;         ///< Number of memory blocks
        size_t largestFreeBlock;    ///< Largest free block size
        float fragmentation;        ///< Memory fragmentation (0-1)
        
        Statistics() : totalAllocated(0), totalUsed(0), totalBlocks(0), 
                      largestFreeBlock(0), fragmentation(0.0f) {}
    };
    
    Statistics getStatistics() const;
    
    /**
     * @brief Check if pool is thread-safe
     */
    bool isThreadSafe() const { return m_threadSafe; }
    
    /**
     * @brief Set thread safety
     */
    void setThreadSafe(bool threadSafe) { m_threadSafe = threadSafe; }

private:
    struct MemoryBlock {
        uint8_t* data;              ///< Block data
        size_t size;                ///< Block size
        size_t used;                ///< Used bytes in block
        
        MemoryBlock(size_t blockSize) : size(blockSize), used(0) {
            data = new uint8_t[blockSize];
        }
        
        ~MemoryBlock() {
            delete[] data;
        }
        
        void* allocate(size_t allocSize, size_t alignment) {
            // Align current position
            size_t alignedPos = (used + alignment - 1) & ~(alignment - 1);
            
            if (alignedPos + allocSize > size) {
                return nullptr; // Not enough space
            }
            
            void* ptr = data + alignedPos;
            used = alignedPos + allocSize;
            return ptr;
        }
        
        void reset() {
            used = 0;
        }
        
        size_t getFreeSpace() const {
            return size - used;
        }
    };
    
    std::vector<std::unique_ptr<MemoryBlock>> m_blocks;
    size_t m_blockSize;
    size_t m_currentBlock;
    bool m_threadSafe;
    mutable std::atomic<size_t> m_totalAllocated;
    mutable std::atomic<size_t> m_totalUsed;
    
    void addNewBlock();
    MemoryBlock* findBlockWithSpace(size_t size, size_t alignment);
};

/**
 * @brief Cache-friendly light data storage
 */
class LightDataStorage {
public:
    /**
     * @brief Constructor
     */
    LightDataStorage();
    
    /**
     * @brief Destructor
     */
    ~LightDataStorage();
    
    /**
     * @brief Initialize with memory pool
     * 
     * @param memoryPool Memory pool to use for allocations
     */
    void initialize(std::shared_ptr<LightMemoryPool> memoryPool);
    
    /**
     * @brief Add light to storage
     * 
     * @param light Light to add
     * @return Light ID for future reference
     */
    uint32_t addLight(std::shared_ptr<Light> light);
    
    /**
     * @brief Remove light from storage
     * 
     * @param lightId Light ID to remove
     */
    void removeLight(uint32_t lightId);
    
    /**
     * @brief Get compressed light data
     * 
     * @param lightId Light ID
     * @return Pointer to compressed data, nullptr if not found
     */
    const CompressedLightData* getLightData(uint32_t lightId) const;
    
    /**
     * @brief Update light data
     * 
     * @param lightId Light ID
     * @param light Updated light
     */
    void updateLight(uint32_t lightId, std::shared_ptr<Light> light);
    
    /**
     * @brief Get all light data (for batch processing)
     * 
     * @return Vector of compressed light data
     */
    const std::vector<CompressedLightData>& getAllLightData() const { return m_lightData; }
    
    /**
     * @brief Get light count
     */
    size_t getLightCount() const { return m_lightData.size(); }
    
    /**
     * @brief Clear all lights
     */
    void clear();
    
    /**
     * @brief Compact storage (remove gaps)
     */
    void compact();
    
    /**
     * @brief Get storage statistics
     */
    struct Statistics {
        size_t lightCount;          ///< Number of lights
        size_t dataSize;            ///< Total data size
        size_t memoryUsed;          ///< Memory used
        float compressionRatio;     ///< Compression ratio
        float cacheEfficiency;      ///< Cache efficiency estimate
        
        Statistics() : lightCount(0), dataSize(0), memoryUsed(0), 
                      compressionRatio(1.0f), cacheEfficiency(1.0f) {}
    };
    
    Statistics getStatistics() const;

private:
    std::shared_ptr<LightMemoryPool> m_memoryPool;
    std::vector<CompressedLightData> m_lightData;       ///< Compressed light data array
    std::unordered_map<uint32_t, size_t> m_lightIdMap; ///< Light ID to index mapping
    std::vector<size_t> m_freeSlots;                    ///< Free slots for reuse
    uint32_t m_nextLightId;                             ///< Next available light ID
    
    // Compression helpers
    CompressedLightData compressLight(std::shared_ptr<Light> light) const;
    CompressedLightType getLightType(std::shared_ptr<Light> light) const;
    uint32_t getLightFlags(std::shared_ptr<Light> light) const;
    
    // Memory management
    size_t allocateSlot();
    void deallocateSlot(size_t slot);
};

/**
 * @brief Light memory manager - high-level interface
 */
class LightMemoryManager {
public:
    /**
     * @brief Constructor
     */
    LightMemoryManager();
    
    /**
     * @brief Destructor
     */
    ~LightMemoryManager();
    
    /**
     * @brief Initialize memory manager
     * 
     * @param poolSize Initial pool size
     * @param enableCompression Enable light data compression
     */
    void initialize(size_t poolSize = 1024 * 1024, bool enableCompression = true);
    
    /**
     * @brief Register light for memory management
     * 
     * @param light Light to register
     * @return Light handle for future operations
     */
    uint32_t registerLight(std::shared_ptr<Light> light);
    
    /**
     * @brief Unregister light
     * 
     * @param lightHandle Light handle
     */
    void unregisterLight(uint32_t lightHandle);
    
    /**
     * @brief Update light data
     * 
     * @param lightHandle Light handle
     * @param light Updated light
     */
    void updateLight(uint32_t lightHandle, std::shared_ptr<Light> light);
    
    /**
     * @brief Get light data for rendering
     * 
     * @param lightHandle Light handle
     * @return Compressed light data
     */
    const CompressedLightData* getLightData(uint32_t lightHandle) const;
    
    /**
     * @brief Get all lights for batch processing
     * 
     * @return Vector of all compressed light data
     */
    std::vector<CompressedLightData> getAllLights() const;
    
    /**
     * @brief Optimize memory layout
     */
    void optimize();
    
    /**
     * @brief Get memory statistics
     */
    struct Statistics {
        LightMemoryPool::Statistics poolStats;
        LightDataStorage::Statistics storageStats;
        size_t totalMemoryUsed;
        float overallEfficiency;
        
        Statistics() : totalMemoryUsed(0), overallEfficiency(1.0f) {}
    };
    
    Statistics getStatistics() const;
    
    /**
     * @brief Clear all lights and reset memory
     */
    void clear();

private:
    std::shared_ptr<LightMemoryPool> m_memoryPool;
    std::unique_ptr<LightDataStorage> m_storage;
    bool m_compressionEnabled;
    
    // Performance monitoring
    mutable Statistics m_lastStats;
    mutable std::chrono::steady_clock::time_point m_lastStatsUpdate;
    
    void updateStatistics() const;
};

/**
 * @brief Utility functions for memory optimization
 */
namespace LightMemoryUtils {
    /**
     * @brief Calculate optimal pool size based on light count
     * 
     * @param lightCount Expected number of lights
     * @return Optimal pool size in bytes
     */
    size_t calculateOptimalPoolSize(size_t lightCount);
    
    /**
     * @brief Estimate memory usage for light
     * 
     * @param light Light to estimate
     * @return Estimated memory usage in bytes
     */
    size_t estimateLightMemoryUsage(std::shared_ptr<Light> light);
    
    /**
     * @brief Check if light data is cache-friendly
     * 
     * @param data Light data array
     * @return Cache efficiency score [0, 1]
     */
    float calculateCacheEfficiency(const std::vector<CompressedLightData>& data);
}

} // namespace photon
