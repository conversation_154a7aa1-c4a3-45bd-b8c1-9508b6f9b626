// src/core/light/photometric_light.hpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Photometric Light with IES profile integration

#pragma once

#include "../scene/light.hpp"
#include "ies_profile.hpp"
#include "../math/vec3.hpp"
#include "../math/matrix4.hpp"
#include <memory>
#include <string>
#include <cmath>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

namespace photon {

// Forward declarations
class Scene;
class Sampler;

/**
 * @brief Photometric light types
 */
enum class PhotometricLightType {
    POINT,          ///< Point light with IES distribution
    SPOT,           ///< Spot light with IES distribution
    LINEAR,         ///< Linear light with IES distribution
    AREA            ///< Area light with IES distribution
};

/**
 * @brief Professional Photometric Light
 * 
 * Implements realistic lighting using IES photometric data files.
 * Supports various light geometries with accurate light distribution.
 */
class PhotometricLight : public Light {
public:
    /**
     * @brief Constructor
     * 
     * @param position Light position
     * @param direction Light direction (for directional types)
     * @param intensity Light intensity multiplier
     * @param type Photometric light type
     */
    PhotometricLight(const Vec3& position, const Vec3& direction, const Color3& intensity,
                     PhotometricLightType type = PhotometricLightType::POINT);
    
    /**
     * @brief Destructor
     */
    virtual ~PhotometricLight() = default;
    
    // Light interface implementation
    LightSample sample(const Intersection& isect, Sampler& sampler) const override;
    Color3 Li(const Intersection& isect, const Vec3& wi) const override;
    float pdf(const Intersection& isect, const Vec3& wi) const override;
    Color3 power() const override;
    bool isDelta() const override;
    std::string getName() const override { return "PhotometricLight"; }
    void preprocess(const Scene& scene) override;
    
    /**
     * @brief Load IES profile from file
     * 
     * @param filePath Path to IES file
     * @return True if loaded successfully
     */
    bool loadIESProfile(const std::string& filePath);
    
    /**
     * @brief Set IES profile
     * 
     * @param profile IES profile to use
     */
    void setIESProfile(std::shared_ptr<IESProfile> profile);
    
    /**
     * @brief Get IES profile
     */
    std::shared_ptr<IESProfile> getIESProfile() const { return m_iesProfile; }
    
    /**
     * @brief Set light position
     */
    void setPosition(const Vec3& position) { m_position = position; }
    
    /**
     * @brief Get light position
     */
    const Vec3& getPosition() const { return m_position; }
    
    /**
     * @brief Set light direction
     */
    void setDirection(const Vec3& direction) { 
        m_direction = direction.normalized();
        updateTransform();
    }
    
    /**
     * @brief Get light direction
     */
    const Vec3& getDirection() const { return m_direction; }
    
    /**
     * @brief Set light intensity
     */
    void setIntensity(const Color3& intensity) { m_intensity = intensity; }
    
    /**
     * @brief Get light intensity
     */
    const Color3& getIntensity() const { return m_intensity; }
    
    /**
     * @brief Set photometric light type
     */
    void setType(PhotometricLightType type) { m_type = type; }
    
    /**
     * @brief Get photometric light type
     */
    PhotometricLightType getType() const { return m_type; }
    
    /**
     * @brief Set light transform
     */
    void setTransform(const Transform& transform) { 
        m_transform = transform;
        updateCachedValues();
    }
    
    /**
     * @brief Get light transform
     */
    const Transform& getTransform() const { return m_transform; }
    
    /**
     * @brief Enable/disable light
     */
    void setEnabled(bool enabled) { m_enabled = enabled; }
    
    /**
     * @brief Check if light is enabled
     */
    bool isEnabled() const { return m_enabled; }
    
    /**
     * @brief Set luminous efficacy (lumens per watt)
     */
    void setLuminousEfficacy(float efficacy) { m_luminousEfficacy = efficacy; }
    
    /**
     * @brief Get luminous efficacy
     */
    float getLuminousEfficacy() const { return m_luminousEfficacy; }
    
    /**
     * @brief Set electrical power in watts
     */
    void setElectricalPower(float watts) { m_electricalPower = watts; }
    
    /**
     * @brief Get electrical power
     */
    float getElectricalPower() const { return m_electricalPower; }
    
    /**
     * @brief Get total luminous flux in lumens
     */
    float getLuminousFlux() const;
    
    /**
     * @brief Get photometric statistics
     */
    struct PhotometricStatistics {
        float totalLumens = 0.0f;
        float maxIntensity = 0.0f;
        float beamAngle = 0.0f;
        float fieldAngle = 0.0f;
        Vec3 primaryDirection;
        bool hasIESProfile = false;
        std::string iesFileName;
    };
    
    PhotometricStatistics getStatistics() const;

protected:
    Vec3 m_position;                            ///< Light position
    Vec3 m_direction;                           ///< Light direction
    Color3 m_intensity;                         ///< Light intensity multiplier
    PhotometricLightType m_type;                ///< Light type
    Transform m_transform;                      ///< Light transform
    bool m_enabled = true;                      ///< Light enabled state
    
    // Photometric properties
    std::shared_ptr<IESProfile> m_iesProfile;   ///< IES photometric profile
    float m_luminousEfficacy = 100.0f;          ///< Lumens per watt
    float m_electricalPower = 100.0f;           ///< Electrical power in watts
    
    // Cached values for performance
    Transform m_worldToLight;                   ///< World to light transform
    Transform m_lightToWorld;                   ///< Light to world transform
    float m_totalLumens = 0.0f;                 ///< Total luminous flux
    bool m_cacheValid = false;                  ///< Cache validity flag
    
    /**
     * @brief Update transform matrices
     */
    void updateTransform();
    
    /**
     * @brief Update cached values
     */
    void updateCachedValues();
    
    /**
     * @brief Evaluate IES profile in light space
     * 
     * @param direction Direction in light space
     * @return Intensity factor [0,1]
     */
    float evaluateIESProfile(const Vec3& direction) const;
    
    /**
     * @brief Transform direction from world to light space
     * 
     * @param worldDirection Direction in world space
     * @return Direction in light space
     */
    Vec3 worldToLightDirection(const Vec3& worldDirection) const;
    
    /**
     * @brief Compute effective intensity at direction
     * 
     * @param direction Direction from light (world space)
     * @return Effective intensity
     */
    Color3 computeEffectiveIntensity(const Vec3& direction) const;
    
    /**
     * @brief Check if light type is delta (point-like)
     */
    bool isTypeDelta() const;
};

/**
 * @brief Photometric light utility functions
 */
namespace PhotometricLightUtils {
    
    /**
     * @brief Create photometric light from IES file
     * 
     * @param iesFilePath Path to IES file
     * @param position Light position
     * @param direction Light direction
     * @param intensity Intensity multiplier
     * @param type Light type
     * @return Photometric light or nullptr if failed
     */
    std::shared_ptr<PhotometricLight> createFromIES(
        const std::string& iesFilePath,
        const Vec3& position,
        const Vec3& direction = Vec3(0, 0, -1),
        const Color3& intensity = Color3(1.0f),
        PhotometricLightType type = PhotometricLightType::POINT);
    
    /**
     * @brief Create standard photometric light presets
     * 
     * @param preset Preset name ("downlight", "floodlight", "streetlight", etc.)
     * @param position Light position
     * @param direction Light direction
     * @param intensity Intensity multiplier
     * @return Photometric light with preset configuration
     */
    std::shared_ptr<PhotometricLight> createPreset(
        const std::string& preset,
        const Vec3& position,
        const Vec3& direction = Vec3(0, 0, -1),
        const Color3& intensity = Color3(1.0f));
    
    /**
     * @brief Convert between photometric units
     * 
     * @param lumens Luminous flux in lumens
     * @param solidAngle Solid angle in steradians
     * @return Luminous intensity in candela
     */
    float lumensToCandelaPerSteradian(float lumens, float solidAngle);
    
    /**
     * @brief Calculate beam angle from IES profile
     * 
     * @param profile IES profile
     * @param threshold Intensity threshold (0.5 for 50% beam angle)
     * @return Beam angle in radians
     */
    float calculateBeamAngle(const IESProfile& profile, float threshold = 0.5f);
}

} // namespace photon
