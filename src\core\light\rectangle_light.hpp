// src/core/light/rectangle_light.hpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Rectangle Area Light implementation

#pragma once

#include "area_light_base.hpp"
#include "../math/vec3.hpp"
#include "../math/vec2.hpp"
#include "../math/color3.hpp"

namespace photon {

/**
 * @brief Rectangle Area Light
 */
class RectangleLight : public AreaLightBase {
public:
    /**
     * @brief Constructor with corner and edges
     * @param corner One corner of the rectangle
     * @param edge1 First edge vector
     * @param edge2 Second edge vector
     * @param emission Emission color
     * @param intensity Intensity multiplier
     * @param twoSided Two-sided emission
     */
    RectangleLight(const Vec3& corner, const Vec3& edge1, const Vec3& edge2,
                   const Color3& emission, float intensity = 1.0f, bool twoSided = false);
    
    /**
     * @brief Constructor with center and size
     * @param center Rectangle center
     * @param normal Rectangle normal
     * @param width Rectangle width
     * @param height Rectangle height
     * @param emission Emission color
     * @param intensity Intensity multiplier
     * @param twoSided Two-sided emission
     */
    RectangleLight(const Vec3& center, const Vec3& normal, float width, float height,
                   const Color3& emission, float intensity = 1.0f, bool twoSided = false);
    
    /**
     * @brief Destructor
     */
    virtual ~RectangleLight() = default;
    
    // AreaLightBase interface implementation
    void sampleSurface(Sampler& sampler, Vec3& point, Vec3& normal, float& pdf) const override;
    float getArea() const override;
    AreaLightShape getShape() const override { return AreaLightShape::RECTANGLE; }
    bool intersect(const Ray& ray, float& t, Vec3& point, Vec3& normal) const override;
    std::string getName() const override { return "Rectangle"; }
    
    /**
     * @brief Get rectangle corner
     */
    const Vec3& getCorner() const { return m_corner; }
    
    /**
     * @brief Get first edge vector
     */
    const Vec3& getEdge1() const { return m_edge1; }
    
    /**
     * @brief Get second edge vector
     */
    const Vec3& getEdge2() const { return m_edge2; }
    
    /**
     * @brief Get rectangle normal
     */
    const Vec3& getNormal() const { return m_normal; }
    
    /**
     * @brief Get rectangle center
     */
    Vec3 getCenter() const { return m_corner + 0.5f * (m_edge1 + m_edge2); }
    
    /**
     * @brief Get rectangle width
     */
    float getWidth() const { return m_edge1.length(); }
    
    /**
     * @brief Get rectangle height
     */
    float getHeight() const { return m_edge2.length(); }
    
    /**
     * @brief Get rectangle corners (4 corners)
     */
    void getCorners(Vec3 corners[4]) const;
    
    /**
     * @brief Set rectangle geometry
     * @param corner New corner position
     * @param edge1 New first edge
     * @param edge2 New second edge
     */
    void setGeometry(const Vec3& corner, const Vec3& edge1, const Vec3& edge2);
    
    /**
     * @brief Set rectangle from center and size
     * @param center Rectangle center
     * @param normal Rectangle normal
     * @param width Rectangle width
     * @param height Rectangle height
     */
    void setGeometry(const Vec3& center, const Vec3& normal, float width, float height);
    
    /**
     * @brief Sample point using uniform distribution
     * @param u Random sample [0,1]^2
     * @return Sampled point on rectangle
     */
    Vec3 sampleUniform(const Vec2& u) const;
    
    /**
     * @brief Sample point using cosine-weighted distribution
     * @param u Random sample [0,1]^2
     * @param fromPoint Point from which to sample
     * @return Sampled point and PDF
     */
    std::pair<Vec3, float> sampleCosineWeighted(const Vec2& u, const Vec3& fromPoint) const;
    
    /**
     * @brief Get solid angle subtended by rectangle from point
     * @param point Point from which to compute solid angle
     * @return Solid angle in steradians
     */
    float getSolidAngle(const Vec3& point) const;

private:
    Vec3 m_corner;      // Rectangle corner
    Vec3 m_edge1;       // First edge vector
    Vec3 m_edge2;       // Second edge vector
    Vec3 m_normal;      // Rectangle normal
    float m_area;       // Cached area
    
    /**
     * @brief Update cached values
     */
    void updateCachedValues();
    
    /**
     * @brief Check if point is inside rectangle (in local coordinates)
     * @param localPoint Point in rectangle's local coordinate system
     * @return True if inside
     */
    bool isInsideRectangle(const Vec2& localPoint) const;
    
    /**
     * @brief Convert world point to rectangle local coordinates
     * @param worldPoint World space point
     * @return Local coordinates [0,1]^2
     */
    Vec2 worldToLocal(const Vec3& worldPoint) const;
    
    /**
     * @brief Convert rectangle local coordinates to world point
     * @param localPoint Local coordinates [0,1]^2
     * @return World space point
     */
    Vec3 localToWorld(const Vec2& localPoint) const;
};

/**
 * @brief Rectangle Light factory functions
 */
namespace RectangleLightFactory {
    /**
     * @brief Create rectangle light from corner and edges
     */
    std::shared_ptr<RectangleLight> fromCornerAndEdges(
        const Vec3& corner, const Vec3& edge1, const Vec3& edge2,
        const Color3& emission, float intensity = 1.0f, bool twoSided = false);
    
    /**
     * @brief Create rectangle light from center and size
     */
    std::shared_ptr<RectangleLight> fromCenterAndSize(
        const Vec3& center, const Vec3& normal, float width, float height,
        const Color3& emission, float intensity = 1.0f, bool twoSided = false);
    
    /**
     * @brief Create square light
     */
    std::shared_ptr<RectangleLight> createSquare(
        const Vec3& center, const Vec3& normal, float size,
        const Color3& emission, float intensity = 1.0f, bool twoSided = false);
    
    /**
     * @brief Create ceiling light (horizontal rectangle facing down)
     */
    std::shared_ptr<RectangleLight> createCeiling(
        const Vec3& center, float width, float height,
        const Color3& emission, float intensity = 1.0f);
    
    /**
     * @brief Create wall light (vertical rectangle)
     */
    std::shared_ptr<RectangleLight> createWall(
        const Vec3& center, const Vec3& wallNormal, float width, float height,
        const Color3& emission, float intensity = 1.0f);
}

} // namespace photon
