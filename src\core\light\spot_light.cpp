// src/core/light/spot_light.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Advanced Spot Light implementation

#include "spot_light.hpp"
#include "../scene/scene.hpp"
#include "../scene/intersection.hpp"
#include "../sampler/sampler.hpp"
#include <algorithm>
#include <cmath>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

namespace photon {

SpotLight::SpotLight(const Vec3& position, const Vec3& direction, const Color3& intensity,
                     float innerAngle, float outerAngle, SpotLightFalloff falloff)
    : m_position(position), m_direction(direction.normalized()), m_intensity(intensity),
      m_innerAngle(innerAngle), m_outerAngle(outerAngle), m_falloffPattern(falloff) {

    // Validate angles
    m_innerAngle = std::max(0.0f, std::min(m_innerAngle, (float)M_PI));
    m_outerAngle = std::max(m_innerAngle, std::min(m_outerAngle, (float)M_PI));

    updateCachedValues();
}

void SpotLight::setConeAngles(float innerAngle, float outerAngle) {
    m_innerAngle = std::max(0.0f, std::min(innerAngle, (float)M_PI));
    m_outerAngle = std::max(m_innerAngle, std::min(outerAngle, (float)M_PI));
    updateCachedValues();
}

void SpotLight::updateCachedValues() {
    m_cosInner = std::cos(m_innerAngle);
    m_cosOuter = std::cos(m_outerAngle);
    m_falloffScale = 1.0f / (m_cosInner - m_cosOuter);
    m_solidAngle = 2.0f * M_PI * (1.0f - m_cosOuter);
}

LightSample SpotLight::sample(const Intersection& isect, Sampler& sampler) const {
    // Compute direction from surface to light
    Vec3 wi = m_position - isect.p;
    float distance = wi.length();
    wi /= distance; // Normalize
    
    // Check if point is within spot cone
    Vec3 lightToSurface = -wi;
    if (!isWithinCone(lightToSurface)) {
        return LightSample(); // Outside cone, no contribution
    }
    
    // Compute falloff factor
    float falloffFactor = computeFalloff(lightToSurface);
    if (falloffFactor <= 0.0f) {
        return LightSample(); // No contribution
    }
    
    // Apply inverse square law and falloff
    Color3 Li = m_intensity * falloffFactor / (distance * distance);
    
    return LightSample(Li, wi, 1.0f, distance, true);
}

Color3 SpotLight::Li(const Intersection& isect, const Vec3& wi) const {
    // Check if direction points towards light
    Vec3 toLight = (m_position - isect.p).normalized();
    if (wi.dot(toLight) < 0.999f) {
        return Color3(0); // Not pointing at light
    }
    
    // Check if point is within spot cone
    Vec3 lightToSurface = -wi;
    if (!isWithinCone(lightToSurface)) {
        return Color3(0); // Outside cone
    }
    
    // Compute falloff factor
    float falloffFactor = computeFalloff(lightToSurface);
    if (falloffFactor <= 0.0f) {
        return Color3(0); // No contribution
    }
    
    // Apply inverse square law and falloff
    float distance = (m_position - isect.p).length();
    return m_intensity * falloffFactor / (distance * distance);
}

float SpotLight::pdf(const Intersection& isect, const Vec3& wi) const {
    return 0.0f; // Delta light has zero PDF
}

Color3 SpotLight::power() const {
    // Integrate intensity over solid angle of cone
    return m_intensity * m_solidAngle;
}

void SpotLight::preprocess(const Scene& scene) {
    // No preprocessing needed for spot lights
}

bool SpotLight::isWithinCone(const Vec3& wi) const {
    float cosTheta = wi.dot(m_direction);
    return cosTheta >= m_cosOuter;
}

float SpotLight::computeFalloff(const Vec3& wi) const {
    float cosTheta = wi.dot(m_direction);
    
    // Outside outer cone
    if (cosTheta < m_cosOuter) {
        return 0.0f;
    }
    
    // Inside inner cone (full intensity)
    if (cosTheta >= m_cosInner) {
        return 1.0f;
    }
    
    // In falloff region - apply selected pattern
    switch (m_falloffPattern) {
        case SpotLightFalloff::LINEAR:
            return linearFalloff(cosTheta);
        case SpotLightFalloff::QUADRATIC:
            return quadraticFalloff(cosTheta);
        case SpotLightFalloff::CUBIC:
            return cubicFalloff(cosTheta);
        case SpotLightFalloff::SMOOTH_STEP:
            return smoothStepFalloff(cosTheta);
        case SpotLightFalloff::CUSTOM:
            return quadraticFalloff(cosTheta); // Default to quadratic
        default:
            return quadraticFalloff(cosTheta);
    }
}

float SpotLight::linearFalloff(float cosTheta) const {
    float t = (cosTheta - m_cosOuter) * m_falloffScale;
    return std::max(0.0f, std::min(1.0f, t));
}

float SpotLight::quadraticFalloff(float cosTheta) const {
    float t = (cosTheta - m_cosOuter) * m_falloffScale;
    t = std::max(0.0f, std::min(1.0f, t));
    return t * t;
}

float SpotLight::cubicFalloff(float cosTheta) const {
    float t = (cosTheta - m_cosOuter) * m_falloffScale;
    t = std::max(0.0f, std::min(1.0f, t));
    return t * t * t;
}

float SpotLight::smoothStepFalloff(float cosTheta) const {
    float t = (cosTheta - m_cosOuter) * m_falloffScale;
    t = std::max(0.0f, std::min(1.0f, t));
    // Smooth step: 3t² - 2t³
    return t * t * (3.0f - 2.0f * t);
}

} // namespace photon
