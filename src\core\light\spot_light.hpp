// src/core/light/spot_light.hpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Advanced Spot Light implementation with professional falloff patterns

#pragma once

#include "../scene/light.hpp"
#include "../math/vec3.hpp"
#include "../math/matrix4.hpp"
#include <memory>
#include <cmath>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

namespace photon {

// Forward declarations
class Scene;
class Sampler;

/**
 * @brief Spot light falloff patterns
 */
enum class SpotLightFalloff {
    LINEAR,         ///< Linear falloff
    QUADRATIC,      ///< Quadratic falloff (default)
    CUBIC,          ///< Cubic falloff
    SMOOTH_STEP,    ///< Smooth step falloff
    CUSTOM          ///< Custom falloff curve
};

/**
 * @brief Advanced Spot Light implementation
 * 
 * Professional spot light with sophisticated falloff patterns,
 * barn doors, and photometric accuracy.
 */
class SpotLight : public Light {
public:
    /**
     * @brief Constructor
     * 
     * @param position Light position
     * @param direction Light direction (normalized)
     * @param intensity Light intensity (power per solid angle)
     * @param innerAngle Inner cone angle in radians (full intensity)
     * @param outerAngle Outer cone angle in radians (zero intensity)
     * @param falloff Falloff pattern type
     */
    SpotLight(const Vec3& position, const Vec3& direction, const Color3& intensity,
              float innerAngle, float outerAngle, 
              SpotLightFalloff falloff = SpotLightFalloff::QUADRATIC);
    
    /**
     * @brief Destructor
     */
    virtual ~SpotLight() = default;
    
    // Light interface implementation
    LightSample sample(const Intersection& isect, Sampler& sampler) const override;
    Color3 Li(const Intersection& isect, const Vec3& wi) const override;
    float pdf(const Intersection& isect, const Vec3& wi) const override;
    Color3 power() const override;
    bool isDelta() const override { return true; }
    std::string getName() const override { return "SpotLight"; }
    void preprocess(const Scene& scene) override;
    
    /**
     * @brief Set light position
     */
    void setPosition(const Vec3& position) { m_position = position; }
    
    /**
     * @brief Get light position
     */
    const Vec3& getPosition() const { return m_position; }
    
    /**
     * @brief Set light direction
     */
    void setDirection(const Vec3& direction) { 
        m_direction = direction.normalized(); 
        updateCachedValues();
    }
    
    /**
     * @brief Get light direction
     */
    const Vec3& getDirection() const { return m_direction; }
    
    /**
     * @brief Set light intensity
     */
    void setIntensity(const Color3& intensity) { m_intensity = intensity; }
    
    /**
     * @brief Get light intensity
     */
    const Color3& getIntensity() const { return m_intensity; }
    
    /**
     * @brief Set cone angles
     * @param innerAngle Inner cone angle in radians
     * @param outerAngle Outer cone angle in radians
     */
    void setConeAngles(float innerAngle, float outerAngle);
    
    /**
     * @brief Get inner cone angle
     */
    float getInnerAngle() const { return m_innerAngle; }
    
    /**
     * @brief Get outer cone angle
     */
    float getOuterAngle() const { return m_outerAngle; }
    
    /**
     * @brief Set falloff pattern
     */
    void setFalloffPattern(SpotLightFalloff falloff) { 
        m_falloffPattern = falloff; 
        updateCachedValues();
    }
    
    /**
     * @brief Get falloff pattern
     */
    SpotLightFalloff getFalloffPattern() const { return m_falloffPattern; }

protected:
    Vec3 m_position;                    ///< Light position
    Vec3 m_direction;                   ///< Light direction (normalized)
    Color3 m_intensity;                 ///< Light intensity
    float m_innerAngle;                 ///< Inner cone angle (radians)
    float m_outerAngle;                 ///< Outer cone angle (radians)
    SpotLightFalloff m_falloffPattern;  ///< Falloff pattern
    
    // Cached values for performance
    float m_cosInner;                   ///< Cosine of inner angle
    float m_cosOuter;                   ///< Cosine of outer angle
    float m_falloffScale;               ///< Falloff scaling factor
    float m_solidAngle;                 ///< Solid angle of cone
    
    /**
     * @brief Update cached values after parameter changes
     */
    void updateCachedValues();
    
    /**
     * @brief Compute falloff factor for given direction
     * @param wi Direction from light to surface (normalized)
     * @return Falloff factor [0,1]
     */
    float computeFalloff(const Vec3& wi) const;
    
    /**
     * @brief Linear falloff pattern
     */
    float linearFalloff(float cosTheta) const;
    
    /**
     * @brief Quadratic falloff pattern
     */
    float quadraticFalloff(float cosTheta) const;
    
    /**
     * @brief Cubic falloff pattern
     */
    float cubicFalloff(float cosTheta) const;
    
    /**
     * @brief Smooth step falloff pattern
     */
    float smoothStepFalloff(float cosTheta) const;
    
    /**
     * @brief Check if direction is within spot cone
     * @param wi Direction from light to surface
     * @return True if within cone
     */
    bool isWithinCone(const Vec3& wi) const;
};

} // namespace photon
