// src/core/material/disney_brdf.hpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Disney Principled BRDF Implementation
// Based on Disney's 2012 "Physically Based Shading at Disney" paper

#pragma once

#include <algorithm>  // For std::clamp
#include <memory>
#include "../math/vec3.hpp"
#include "../math/color3.hpp"
#include "../common.hpp"

namespace photon {

// Forward declarations
class Sampler;
struct Intersection;

/**
 * @brief Disney Principled BRDF Parameters
 * 
 * Implements the Disney Principled BRDF model from the 2012 paper
 * "Physically Based Shading at Disney" by <PERSON>
 */
struct DisneyBRDFParams {
    // Core parameters
    Color3 baseColor = Color3(0.8f);        // Base color (albedo)
    float metallic = 0.0f;                  // Metallic parameter [0,1]
    float roughness = 0.5f;                 // Surface roughness [0,1]
    float specular = 0.5f;                  // Specular reflection strength [0,1]
    float specularTint = 0.0f;              // Specular color tint [0,1]
    
    // Extended parameters
    float anisotropic = 0.0f;               // Anisotropic reflection [0,1]
    float sheen = 0.0f;                     // Fabric-like sheen [0,1]
    float sheenTint = 0.5f;                 // Sheen color tint [0,1]
    float clearcoat = 0.0f;                 // Clear coat layer [0,1]
    float clearcoatGloss = 1.0f;            // Clear coat roughness [0,1]
    float subsurface = 0.0f;                // Subsurface scattering [0,1]
    
    /**
     * @brief Validate parameters and clamp to valid ranges
     */
    void validate() {
        metallic = std::clamp(metallic, 0.0f, 1.0f);
        roughness = std::clamp(roughness, 0.0f, 1.0f);
        specular = std::clamp(specular, 0.0f, 1.0f);
        specularTint = std::clamp(specularTint, 0.0f, 1.0f);
        anisotropic = std::clamp(anisotropic, 0.0f, 1.0f);
        sheen = std::clamp(sheen, 0.0f, 1.0f);
        sheenTint = std::clamp(sheenTint, 0.0f, 1.0f);
        clearcoat = std::clamp(clearcoat, 0.0f, 1.0f);
        clearcoatGloss = std::clamp(clearcoatGloss, 0.0f, 1.0f);
        subsurface = std::clamp(subsurface, 0.0f, 1.0f);
    }
};

/**
 * @brief Disney Principled BRDF Implementation
 * 
 * This class implements the Disney Principled BRDF model which combines:
 * - Diffuse reflection (modified Lambert)
 * - Specular reflection (GGX/Trowbridge-Reitz)
 * - Sheen (fabric-like reflection)
 * - Clearcoat (additional specular layer)
 * - Subsurface scattering (approximation)
 */
class DisneyBRDF {
public:
    /**
     * @brief Constructor with default parameters
     */
    DisneyBRDF();
    
    /**
     * @brief Constructor with custom parameters
     * @param params Disney BRDF parameters
     */
    explicit DisneyBRDF(const DisneyBRDFParams& params);
    
    /**
     * @brief Set BRDF parameters
     * @param params New parameters
     */
    void setParameters(const DisneyBRDFParams& params);
    
    /**
     * @brief Get current parameters
     * @return Current BRDF parameters
     */
    const DisneyBRDFParams& getParameters() const { return m_params; }
    
    /**
     * @brief Evaluate BRDF
     * @param wo Outgoing direction (towards viewer)
     * @param wi Incident direction (towards light)
     * @param n Surface normal
     * @return BRDF value
     */
    Color3 eval(const Vec3& wo, const Vec3& wi, const Vec3& n) const;
    
    /**
     * @brief Sample BRDF direction
     * @param wo Outgoing direction (towards viewer)
     * @param n Surface normal
     * @param sampler Random number sampler
     * @param wi Output: sampled incident direction
     * @param pdf Output: probability density function value
     * @return BRDF value for sampled direction
     */
    Color3 sample(const Vec3& wo, const Vec3& n, Sampler& sampler, Vec3& wi, float& pdf) const;
    
    /**
     * @brief Evaluate probability density function
     * @param wo Outgoing direction (towards viewer)
     * @param wi Incident direction (towards light)
     * @param n Surface normal
     * @return PDF value
     */
    float pdf(const Vec3& wo, const Vec3& wi, const Vec3& n) const;
    
    /**
     * @brief Check if BRDF is purely specular (delta distribution)
     * @return True if delta distribution
     */
    bool isDelta() const { return false; }
    
    /**
     * @brief Get albedo for energy conservation
     * @return Effective albedo
     */
    Color3 getAlbedo() const;

private:
    DisneyBRDFParams m_params;
    
    // Internal BRDF component evaluation
    Color3 evalDiffuse(const Vec3& wo, const Vec3& wi, const Vec3& n) const;
    Color3 evalSpecular(const Vec3& wo, const Vec3& wi, const Vec3& n) const;
    Color3 evalSheen(const Vec3& wo, const Vec3& wi, const Vec3& n) const;
    Color3 evalClearcoat(const Vec3& wo, const Vec3& wi, const Vec3& n) const;
    Color3 evalSubsurface(const Vec3& wo, const Vec3& wi, const Vec3& n) const;
    
    // Sampling functions
    Vec3 sampleDiffuse(const Vec3& wo, const Vec3& n, Sampler& sampler, float& pdf) const;
    Vec3 sampleSpecular(const Vec3& wo, const Vec3& n, Sampler& sampler, float& pdf) const;
    Vec3 sampleClearcoat(const Vec3& wo, const Vec3& n, Sampler& sampler, float& pdf) const;
    Vec3 sampleSubsurface(const Vec3& wo, const Vec3& n, Sampler& sampler, float& pdf) const;
    
    // PDF functions
    float pdfDiffuse(const Vec3& wo, const Vec3& wi, const Vec3& n) const;
    float pdfSpecular(const Vec3& wo, const Vec3& wi, const Vec3& n) const;
    float pdfClearcoat(const Vec3& wo, const Vec3& wi, const Vec3& n) const;
    float pdfSubsurface(const Vec3& wo, const Vec3& wi, const Vec3& n) const;
    
    // Utility functions
    float fresnelSchlick(float cosTheta, float F0) const;
    Color3 fresnelSchlick(float cosTheta, const Color3& F0) const;
    float distributionGGX(const Vec3& h, const Vec3& n, float roughness) const;
    float geometrySmith(const Vec3& wo, const Vec3& wi, const Vec3& n, float roughness) const;
    float geometrySchlickGGX(float cosTheta, float roughness) const;
    
    // Disney-specific functions
    float disneyDiffuse(float cosTheta_o, float cosTheta_i, float cosTheta_d, float roughness) const;
    float disneyFresnel(float cosTheta, float eta, float k) const;
    
    // Coordinate system utilities
    void buildCoordinateSystem(const Vec3& n, Vec3& tangent, Vec3& bitangent) const;
    Vec3 toLocal(const Vec3& v, const Vec3& n, const Vec3& tangent, const Vec3& bitangent) const;
    Vec3 toWorld(const Vec3& v, const Vec3& n, const Vec3& tangent, const Vec3& bitangent) const;
    
    // Component weight calculation
    void calculateComponentWeights(const Vec3& wo, const Vec3& n,
                                 float& diffuseWeight, float& specularWeight,
                                 float& sheenWeight, float& clearcoatWeight,
                                 float& subsurfaceWeight) const;

    // Subsurface scattering utilities
    float subsurfaceProfile(float distance, float scatteringDistance) const;
    Color3 subsurfaceApproximation(const Vec3& wo, const Vec3& wi, const Vec3& n) const;
    float subsurfaceFresnelWeight(float cosTheta) const;
};

/**
 * @brief Disney BRDF Material Factory
 * 
 * Provides common material presets based on Disney BRDF
 */
class DisneyMaterialPresets {
public:
    // Common material presets
    static DisneyBRDFParams createPlastic(const Color3& color = Color3(0.8f));
    static DisneyBRDFParams createMetal(const Color3& color = Color3(0.7f));
    static DisneyBRDFParams createGlass(const Color3& color = Color3(0.9f));
    static DisneyBRDFParams createWood(const Color3& color = Color3(0.6f, 0.4f, 0.2f));
    static DisneyBRDFParams createFabric(const Color3& color = Color3(0.5f));
    static DisneyBRDFParams createSkin(const Color3& color = Color3(0.8f, 0.6f, 0.5f));
    static DisneyBRDFParams createCeramic(const Color3& color = Color3(0.9f));
    static DisneyBRDFParams createRubber(const Color3& color = Color3(0.3f));

    // Subsurface scattering materials
    static DisneyBRDFParams createWax(const Color3& color = Color3(0.9f, 0.8f, 0.6f));
    static DisneyBRDFParams createMarble(const Color3& color = Color3(0.95f, 0.95f, 0.9f));
    static DisneyBRDFParams createJade(const Color3& color = Color3(0.3f, 0.7f, 0.4f));
    
    // Validation and energy conservation
    static bool validateEnergyConservation(const DisneyBRDFParams& params);
    static DisneyBRDFParams enforceEnergyConservation(const DisneyBRDFParams& params);
};

} // namespace photon
