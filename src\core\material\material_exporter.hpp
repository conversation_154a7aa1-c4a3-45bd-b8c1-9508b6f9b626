// src/core/material/material_exporter.hpp
// PhotonRender - Material Export System
// Sistema di export materiali per interoperabilità cross-platform

#ifndef PHOTON_MATERIAL_EXPORTER_HPP
#define PHOTON_MATERIAL_EXPORTER_HPP

#include "../math/vec3.hpp"
#include "disney_brdf.hpp"
#include <string>
#include <vector>
#include <memory>
#include <unordered_map>
#include <functional>

namespace photon {

// Forward declarations
class Material;
class PBRMaterial;
class Texture;

/**
 * @brief Supported export formats
 */
enum class ExportFormat {
    MTL,            // Wavefront MTL format
    GLTF,           // glTF 2.0 material format
    JSON,           // PhotonRender JSON format
    OBJ_MTL,        // OBJ + MTL combined
    BLENDER,        // Blender material format
    MAYA,           // Maya material format
    MAX,            // 3ds Max material format
    SUBSTANCE,      // Substance Designer format
    UNREAL,         // Unreal Engine material format
    UNITY           // Unity material format
};

/**
 * @brief Export options
 */
struct ExportOptions {
    ExportFormat format = ExportFormat::JSON;           // Target export format
    std::string outputPath;                             // Output file/directory path
    bool includeTextures = true;                        // Include texture files
    bool embedTextures = false;                         // Embed textures in file
    bool optimizeTextures = true;                       // Optimize texture sizes
    bool preserveQuality = true;                        // Preserve material quality
    bool includeMetadata = true;                        // Include metadata
    bool compressOutput = false;                        // Compress output files
    
    // Format-specific options
    std::unordered_map<std::string, std::string> formatOptions;
    
    // Texture options
    int maxTextureSize = 2048;                          // Maximum texture size
    std::string textureFormat = "png";                  // Texture output format
    float textureQuality = 0.9f;                       // Texture compression quality
    
    // Path options
    bool useRelativePaths = true;                       // Use relative texture paths
    std::string textureSubdirectory = "textures";      // Texture subdirectory name
};

/**
 * @brief Export result
 */
struct ExportResult {
    bool success = false;                               // Export success status
    std::string outputPath;                             // Final output path
    std::vector<std::string> exportedFiles;            // List of exported files
    std::vector<std::string> warnings;                 // Export warnings
    std::vector<std::string> errors;                   // Export errors
    size_t materialCount = 0;                          // Number of materials exported
    size_t textureCount = 0;                           // Number of textures exported
    double exportTime = 0.0;                           // Export time in seconds
    
    /**
     * @brief Get export summary
     * @return Summary string
     */
    std::string getSummary() const;
    
    /**
     * @brief Check if export has warnings
     * @return True if has warnings
     */
    bool hasWarnings() const { return !warnings.empty(); }
    
    /**
     * @brief Check if export has errors
     * @return True if has errors
     */
    bool hasErrors() const { return !errors.empty(); }
};

/**
 * @brief Material export data
 */
struct MaterialExportData {
    std::string name;                                   // Material name
    std::string id;                                     // Material ID
    DisneyBRDFParams brdfParams;                        // Disney BRDF parameters
    std::unordered_map<std::string, std::string> texturePaths; // Texture file paths
    std::unordered_map<std::string, std::string> metadata;     // Material metadata
    
    // Texture assignments
    std::string diffuseTexture;
    std::string normalTexture;
    std::string roughnessTexture;
    std::string metallicTexture;
    std::string specularTexture;
    std::string emissionTexture;
    std::string opacityTexture;
    std::string displacementTexture;
    std::string ambientOcclusionTexture;
    std::string subsurfaceTexture;
    std::string clearcoatTexture;
    std::string clearcoatNormalTexture;
    std::string anisotropyTexture;
    std::string sheenTexture;
    std::string environmentTexture;
};

/**
 * @brief Material Exporter
 * 
 * Comprehensive system for exporting materials to various formats
 * with support for textures, metadata, and cross-platform compatibility
 */
class MaterialExporter {
public:
    /**
     * @brief Constructor
     */
    MaterialExporter();
    
    /**
     * @brief Destructor
     */
    ~MaterialExporter();
    
    /**
     * @brief Export single material
     * @param material Material to export
     * @param options Export options
     * @return Export result
     */
    ExportResult exportMaterial(const std::shared_ptr<Material>& material, 
                               const ExportOptions& options);
    
    /**
     * @brief Export multiple materials
     * @param materials Materials to export
     * @param options Export options
     * @return Export result
     */
    ExportResult exportMaterials(const std::vector<std::shared_ptr<Material>>& materials, 
                                const ExportOptions& options);
    
    /**
     * @brief Export material library
     * @param libraryPath Path to material library
     * @param options Export options
     * @return Export result
     */
    ExportResult exportMaterialLibrary(const std::string& libraryPath, 
                                      const ExportOptions& options);
    
    /**
     * @brief Export to MTL format
     * @param materials Materials to export
     * @param options Export options
     * @return Export result
     */
    ExportResult exportToMTL(const std::vector<MaterialExportData>& materials, 
                            const ExportOptions& options);
    
    /**
     * @brief Export to glTF format
     * @param materials Materials to export
     * @param options Export options
     * @return Export result
     */
    ExportResult exportToGLTF(const std::vector<MaterialExportData>& materials, 
                             const ExportOptions& options);
    
    /**
     * @brief Export to JSON format
     * @param materials Materials to export
     * @param options Export options
     * @return Export result
     */
    ExportResult exportToJSON(const std::vector<MaterialExportData>& materials, 
                             const ExportOptions& options);
    
    /**
     * @brief Export to OBJ+MTL format
     * @param materials Materials to export
     * @param options Export options
     * @return Export result
     */
    ExportResult exportToOBJMTL(const std::vector<MaterialExportData>& materials, 
                               const ExportOptions& options);
    
    /**
     * @brief Export textures
     * @param materials Materials with textures
     * @param options Export options
     * @return List of exported texture paths
     */
    std::vector<std::string> exportTextures(const std::vector<MaterialExportData>& materials, 
                                           const ExportOptions& options);
    
    /**
     * @brief Convert material to export data
     * @param material Material to convert
     * @return Material export data
     */
    MaterialExportData convertMaterialToExportData(const std::shared_ptr<Material>& material);
    
    /**
     * @brief Get supported export formats
     * @return Vector of supported formats
     */
    std::vector<ExportFormat> getSupportedFormats() const;
    
    /**
     * @brief Get format name
     * @param format Export format
     * @return Format name string
     */
    std::string getFormatName(ExportFormat format) const;
    
    /**
     * @brief Get format extension
     * @param format Export format
     * @return File extension string
     */
    std::string getFormatExtension(ExportFormat format) const;
    
    /**
     * @brief Set progress callback
     * @param callback Progress callback function
     */
    void setProgressCallback(std::function<void(float, const std::string&)> callback);
    
    /**
     * @brief Get export statistics
     * @return Export statistics as string
     */
    std::string getExportStats() const;

private:
    std::function<void(float, const std::string&)> m_progressCallback;
    
    // Statistics
    mutable size_t m_totalExports = 0;
    mutable size_t m_successfulExports = 0;
    mutable size_t m_failedExports = 0;
    mutable double m_totalExportTime = 0.0;
    
    /**
     * @brief Create output directory
     * @param path Directory path
     * @return True if successful
     */
    bool createOutputDirectory(const std::string& path);
    
    /**
     * @brief Copy texture file
     * @param sourcePath Source texture path
     * @param targetPath Target texture path
     * @param options Export options
     * @return True if successful
     */
    bool copyTextureFile(const std::string& sourcePath, 
                        const std::string& targetPath, 
                        const ExportOptions& options);
    
    /**
     * @brief Optimize texture
     * @param texturePath Texture file path
     * @param options Export options
     * @return True if successful
     */
    bool optimizeTexture(const std::string& texturePath, 
                        const ExportOptions& options);
    
    /**
     * @brief Generate relative path
     * @param fromPath Base path
     * @param toPath Target path
     * @return Relative path string
     */
    std::string generateRelativePath(const std::string& fromPath, 
                                   const std::string& toPath);
    
    /**
     * @brief Validate export options
     * @param options Export options
     * @return True if valid
     */
    bool validateExportOptions(const ExportOptions& options);
    
    /**
     * @brief Update progress
     * @param progress Progress value (0-1)
     * @param message Progress message
     */
    void updateProgress(float progress, const std::string& message);
    
    /**
     * @brief Convert Disney BRDF to MTL parameters
     * @param params Disney BRDF parameters
     * @return MTL parameter map
     */
    std::unordered_map<std::string, std::string> convertToMTLParams(const DisneyBRDFParams& params);
    
    /**
     * @brief Convert Disney BRDF to glTF parameters
     * @param params Disney BRDF parameters
     * @return glTF parameter map
     */
    std::unordered_map<std::string, std::string> convertToGLTFParams(const DisneyBRDFParams& params);
    
    /**
     * @brief Generate material ID
     * @param materialName Material name
     * @return Unique material ID
     */
    std::string generateMaterialId(const std::string& materialName);
    
    /**
     * @brief Sanitize filename
     * @param filename Original filename
     * @return Sanitized filename
     */
    std::string sanitizeFilename(const std::string& filename);
    
    /**
     * @brief Write MTL file
     * @param materials Material export data
     * @param filePath Output file path
     * @param options Export options
     * @return True if successful
     */
    bool writeMTLFile(const std::vector<MaterialExportData>& materials, 
                     const std::string& filePath, 
                     const ExportOptions& options);
    
    /**
     * @brief Write glTF file
     * @param materials Material export data
     * @param filePath Output file path
     * @param options Export options
     * @return True if successful
     */
    bool writeGLTFFile(const std::vector<MaterialExportData>& materials, 
                      const std::string& filePath, 
                      const ExportOptions& options);
    
    /**
     * @brief Write JSON file
     * @param materials Material export data
     * @param filePath Output file path
     * @param options Export options
     * @return True if successful
     */
    bool writeJSONFile(const std::vector<MaterialExportData>& materials, 
                      const std::string& filePath, 
                      const ExportOptions& options);
};

} // namespace photon

#endif // PHOTON_MATERIAL_EXPORTER_HPP
