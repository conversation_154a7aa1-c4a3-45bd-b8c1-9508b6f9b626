// src/core/material/material_importer.hpp
// PhotonRender - Material Import System
// Sistema di import materiali per interoperabilità cross-platform

#ifndef PHOTON_MATERIAL_IMPORTER_HPP
#define PHOTON_MATERIAL_IMPORTER_HPP

#include "../math/vec3.hpp"
#include "disney_brdf.hpp"
#include "material_exporter.hpp"
#include <string>
#include <vector>
#include <memory>
#include <unordered_map>
#include <functional>

namespace photon {

// Forward declarations
class Material;
class PBRMaterial;

/**
 * @brief Supported import formats
 */
enum class ImportFormat {
    AUTO_DETECT,    // Auto-detect format from file extension
    MTL,            // Wavefront MTL format
    GLTF,           // glTF 2.0 material format
    JSON,           // PhotonRender JSON format
    OBJ_MTL,        // OBJ + MTL combined
    BLENDER,        // Blender material format
    MAYA,           // Maya material format
    MAX,            // 3ds Max material format
    SUBSTANCE,      // Substance Designer format
    UNREAL,         // Unreal Engine material format
    UNITY           // Unity material format
};

/**
 * @brief Import options
 */
struct ImportOptions {
    ImportFormat format = ImportFormat::AUTO_DETECT;   // Source import format
    std::string inputPath;                              // Input file/directory path
    bool importTextures = true;                         // Import texture files
    bool copyTextures = true;                           // Copy textures to local directory
    bool optimizeTextures = false;                      // Optimize imported textures
    bool validateMaterials = true;                      // Validate imported materials
    bool overwriteExisting = false;                     // Overwrite existing materials
    bool preserveNames = true;                          // Preserve original material names
    
    // Conversion options
    bool convertToDisneyBRDF = true;                    // Convert to Disney BRDF
    bool approximateUnsupported = true;                 // Approximate unsupported features
    bool generateMissingTextures = false;               // Generate missing texture placeholders
    
    // Path options
    std::string textureDirectory = "textures";         // Target texture directory
    bool useRelativePaths = true;                       // Use relative texture paths
    
    // Format-specific options
    std::unordered_map<std::string, std::string> formatOptions;
};

/**
 * @brief Import result
 */
struct ImportResult {
    bool success = false;                               // Import success status
    std::string inputPath;                              // Source input path
    std::vector<std::shared_ptr<Material>> materials;  // Imported materials
    std::vector<std::string> importedFiles;            // List of imported files
    std::vector<std::string> warnings;                 // Import warnings
    std::vector<std::string> errors;                   // Import errors
    size_t materialCount = 0;                          // Number of materials imported
    size_t textureCount = 0;                           // Number of textures imported
    double importTime = 0.0;                           // Import time in seconds
    
    /**
     * @brief Get import summary
     * @return Summary string
     */
    std::string getSummary() const;
    
    /**
     * @brief Check if import has warnings
     * @return True if has warnings
     */
    bool hasWarnings() const { return !warnings.empty(); }
    
    /**
     * @brief Check if import has errors
     * @return True if has errors
     */
    bool hasErrors() const { return !errors.empty(); }
};

/**
 * @brief Material import data
 */
struct MaterialImportData {
    std::string name;                                   // Material name
    std::string id;                                     // Material ID
    ImportFormat sourceFormat;                         // Source format
    DisneyBRDFParams brdfParams;                        // Converted Disney BRDF parameters
    std::unordered_map<std::string, std::string> texturePaths; // Texture file paths
    std::unordered_map<std::string, std::string> metadata;     // Material metadata
    std::unordered_map<std::string, std::string> originalParams; // Original parameters
    
    // Conversion quality
    float conversionQuality = 1.0f;                     // Conversion quality score (0-1)
    std::vector<std::string> conversionWarnings;       // Conversion warnings
};

/**
 * @brief Material Importer
 * 
 * Comprehensive system for importing materials from various formats
 * with automatic conversion to Disney BRDF and texture management
 */
class MaterialImporter {
public:
    /**
     * @brief Constructor
     */
    MaterialImporter();
    
    /**
     * @brief Destructor
     */
    ~MaterialImporter();
    
    /**
     * @brief Import materials from file
     * @param filePath Path to material file
     * @param options Import options
     * @return Import result
     */
    ImportResult importMaterials(const std::string& filePath, 
                                const ImportOptions& options);
    
    /**
     * @brief Import material library
     * @param libraryPath Path to material library directory
     * @param options Import options
     * @return Import result
     */
    ImportResult importMaterialLibrary(const std::string& libraryPath, 
                                      const ImportOptions& options);
    
    /**
     * @brief Import from MTL format
     * @param filePath MTL file path
     * @param options Import options
     * @return Import result
     */
    ImportResult importFromMTL(const std::string& filePath, 
                              const ImportOptions& options);
    
    /**
     * @brief Import from glTF format
     * @param filePath glTF file path
     * @param options Import options
     * @return Import result
     */
    ImportResult importFromGLTF(const std::string& filePath, 
                               const ImportOptions& options);
    
    /**
     * @brief Import from JSON format
     * @param filePath JSON file path
     * @param options Import options
     * @return Import result
     */
    ImportResult importFromJSON(const std::string& filePath, 
                               const ImportOptions& options);
    
    /**
     * @brief Import from OBJ+MTL format
     * @param filePath OBJ file path
     * @param options Import options
     * @return Import result
     */
    ImportResult importFromOBJMTL(const std::string& filePath, 
                                 const ImportOptions& options);
    
    /**
     * @brief Auto-detect file format
     * @param filePath File path
     * @return Detected import format
     */
    ImportFormat detectFormat(const std::string& filePath);
    
    /**
     * @brief Convert import data to material
     * @param importData Material import data
     * @return Created material
     */
    std::shared_ptr<Material> convertToMaterial(const MaterialImportData& importData);
    
    /**
     * @brief Import textures
     * @param materials Materials with texture references
     * @param options Import options
     * @return List of imported texture paths
     */
    std::vector<std::string> importTextures(const std::vector<MaterialImportData>& materials, 
                                           const ImportOptions& options);
    
    /**
     * @brief Get supported import formats
     * @return Vector of supported formats
     */
    std::vector<ImportFormat> getSupportedFormats() const;
    
    /**
     * @brief Get format name
     * @param format Import format
     * @return Format name string
     */
    std::string getFormatName(ImportFormat format) const;
    
    /**
     * @brief Get format extensions
     * @param format Import format
     * @return Vector of file extensions
     */
    std::vector<std::string> getFormatExtensions(ImportFormat format) const;
    
    /**
     * @brief Set progress callback
     * @param callback Progress callback function
     */
    void setProgressCallback(std::function<void(float, const std::string&)> callback);
    
    /**
     * @brief Get import statistics
     * @return Import statistics as string
     */
    std::string getImportStats() const;

private:
    std::function<void(float, const std::string&)> m_progressCallback;
    
    // Statistics
    mutable size_t m_totalImports = 0;
    mutable size_t m_successfulImports = 0;
    mutable size_t m_failedImports = 0;
    mutable double m_totalImportTime = 0.0;
    
    /**
     * @brief Validate import options
     * @param options Import options
     * @return True if valid
     */
    bool validateImportOptions(const ImportOptions& options);
    
    /**
     * @brief Update progress
     * @param progress Progress value (0-1)
     * @param message Progress message
     */
    void updateProgress(float progress, const std::string& message);
    
    /**
     * @brief Parse MTL file
     * @param filePath MTL file path
     * @return Vector of material import data
     */
    std::vector<MaterialImportData> parseMTLFile(const std::string& filePath);
    
    /**
     * @brief Parse glTF file
     * @param filePath glTF file path
     * @return Vector of material import data
     */
    std::vector<MaterialImportData> parseGLTFFile(const std::string& filePath);
    
    /**
     * @brief Parse JSON file
     * @param filePath JSON file path
     * @return Vector of material import data
     */
    std::vector<MaterialImportData> parseJSONFile(const std::string& filePath);
    
    /**
     * @brief Convert MTL parameters to Disney BRDF
     * @param mtlParams MTL parameters
     * @return Disney BRDF parameters
     */
    DisneyBRDFParams convertMTLToDisneyBRDF(const std::unordered_map<std::string, std::string>& mtlParams);
    
    /**
     * @brief Convert glTF parameters to Disney BRDF
     * @param gltfParams glTF parameters
     * @return Disney BRDF parameters
     */
    DisneyBRDFParams convertGLTFToDisneyBRDF(const std::unordered_map<std::string, std::string>& gltfParams);
    
    /**
     * @brief Copy texture file
     * @param sourcePath Source texture path
     * @param targetPath Target texture path
     * @param options Import options
     * @return True if successful
     */
    bool copyTextureFile(const std::string& sourcePath, 
                        const std::string& targetPath, 
                        const ImportOptions& options);
    
    /**
     * @brief Resolve texture path
     * @param texturePath Original texture path
     * @param basePath Base path for relative resolution
     * @return Resolved absolute path
     */
    std::string resolveTexturePath(const std::string& texturePath, 
                                  const std::string& basePath);
    
    /**
     * @brief Calculate conversion quality
     * @param importData Material import data
     * @return Quality score (0-1)
     */
    float calculateConversionQuality(const MaterialImportData& importData);
    
    /**
     * @brief Generate material name
     * @param baseName Base material name
     * @param index Material index
     * @return Unique material name
     */
    std::string generateMaterialName(const std::string& baseName, int index);
    
    /**
     * @brief Parse color string
     * @param colorStr Color string (e.g., "1.0 0.5 0.2")
     * @return Color3 value
     */
    Color3 parseColorString(const std::string& colorStr);
    
    /**
     * @brief Parse float string
     * @param floatStr Float string
     * @return Float value
     */
    float parseFloatString(const std::string& floatStr);
};

} // namespace photon

#endif // PHOTON_MATERIAL_IMPORTER_HPP
