// src/core/material/material_library.hpp
// PhotonRender - Material Library Management System
// Sistema di gestione libreria materiali con categorizzazione e ricerca

#ifndef PHOTON_MATERIAL_LIBRARY_HPP
#define PHOTON_MATERIAL_LIBRARY_HPP

#include "../math/vec3.hpp"
#include "../math/vec2.hpp"
#include "material.hpp"
#include "disney_brdf.hpp"
#include <memory>
#include <vector>
#include <string>
#include <unordered_map>
#include <functional>

namespace photon {

// Forward declarations
class Texture;
class Image;

/**
 * @brief Material categories for organization
 */
enum class MaterialCategory {
    METALS,         // Metal materials
    PLASTICS,       // Plastic and synthetic materials
    GLASS,          // Glass and transparent materials
    WOOD,           // Wood and natural materials
    FABRIC,         // Fabric and textile materials
    STONE,          // Stone and concrete materials
    ORGANIC,        // Organic materials (skin, food, etc.)
    AUTOMOTIVE,     // Car paint and automotive materials
    ARCHITECTURAL,  // Building and architectural materials
    CUSTOM,         // User-defined custom materials
    ALL             // All categories (for filtering)
};

/**
 * @brief Material tags for advanced filtering
 */
enum class MaterialTag {
    ROUGH,          // Rough surface materials
    SMOOTH,         // Smooth surface materials
    REFLECTIVE,     // Highly reflective materials
    TRANSLUCENT,    // Translucent materials
    EMISSIVE,       // Light-emitting materials
    WEATHERED,      // Aged/weathered materials
    POLISHED,       // Polished/finished materials
    MATTE,          // Matte finish materials
    GLOSSY,         // Glossy finish materials
    TEXTURED        // Textured surface materials
};

/**
 * @brief Material metadata for library management
 */
struct MaterialMetadata {
    std::string id;                     // Unique material ID
    std::string name;                   // Display name
    std::string description;            // Material description
    std::string author;                 // Material creator
    std::string version;                // Material version
    MaterialCategory category;          // Primary category
    std::vector<MaterialTag> tags;      // Associated tags
    std::string thumbnailPath;          // Thumbnail image path
    std::string previewPath;            // Preview render path
    float rating = 0.0f;                // User rating (0-5)
    int downloadCount = 0;              // Download/usage count
    std::string createdDate;            // Creation date
    std::string modifiedDate;           // Last modification date
    std::unordered_map<std::string, std::string> customProperties; // Custom metadata
};

/**
 * @brief Material library entry
 */
struct MaterialEntry {
    MaterialMetadata metadata;          // Material metadata
    std::shared_ptr<Material> material; // Material instance
    std::shared_ptr<Image> thumbnail;   // Thumbnail image
    std::shared_ptr<Image> preview;     // Preview render
    bool isLoaded = false;              // Material loaded in memory
    bool isDirty = false;               // Material modified since last save
};

/**
 * @brief Search and filter criteria
 */
struct MaterialSearchCriteria {
    std::string nameFilter;             // Name/description text filter
    MaterialCategory category = MaterialCategory::ALL; // Category filter
    std::vector<MaterialTag> requiredTags; // Required tags
    std::vector<MaterialTag> excludedTags; // Excluded tags
    float minRating = 0.0f;             // Minimum rating
    float maxRoughness = 1.0f;          // Maximum roughness
    float minMetallic = 0.0f;           // Minimum metallic value
    float maxMetallic = 1.0f;           // Maximum metallic value
    bool customOnly = false;            // Show only custom materials
    bool recentOnly = false;            // Show only recently used
};

/**
 * @brief Material Library Management System
 * 
 * Comprehensive system for managing material libraries with
 * categorization, search, filtering, and import/export capabilities
 */
class MaterialLibrary {
public:
    /**
     * @brief Constructor
     */
    MaterialLibrary();
    
    /**
     * @brief Destructor
     */
    ~MaterialLibrary();
    
    /**
     * @brief Initialize material library
     * @param libraryPath Path to material library directory
     * @return True if initialization successful
     */
    bool initialize(const std::string& libraryPath);
    
    /**
     * @brief Shutdown material library
     */
    void shutdown();
    
    /**
     * @brief Load material library from disk
     * @return True if load successful
     */
    bool loadLibrary();
    
    /**
     * @brief Save material library to disk
     * @return True if save successful
     */
    bool saveLibrary();
    
    /**
     * @brief Add material to library
     * @param material Material to add
     * @param metadata Material metadata
     * @return Material ID if successful, empty string if failed
     */
    std::string addMaterial(std::shared_ptr<Material> material, const MaterialMetadata& metadata);
    
    /**
     * @brief Remove material from library
     * @param materialId Material ID to remove
     * @return True if removal successful
     */
    bool removeMaterial(const std::string& materialId);
    
    /**
     * @brief Get material by ID
     * @param materialId Material ID
     * @return Material entry or nullptr if not found
     */
    std::shared_ptr<MaterialEntry> getMaterial(const std::string& materialId);
    
    /**
     * @brief Update material in library
     * @param materialId Material ID
     * @param material Updated material
     * @param metadata Updated metadata
     * @return True if update successful
     */
    bool updateMaterial(const std::string& materialId, 
                       std::shared_ptr<Material> material, 
                       const MaterialMetadata& metadata);
    
    /**
     * @brief Search materials by criteria
     * @param criteria Search criteria
     * @return Vector of matching material IDs
     */
    std::vector<std::string> searchMaterials(const MaterialSearchCriteria& criteria);
    
    /**
     * @brief Get materials by category
     * @param category Material category
     * @return Vector of material IDs in category
     */
    std::vector<std::string> getMaterialsByCategory(MaterialCategory category);
    
    /**
     * @brief Get materials by tag
     * @param tag Material tag
     * @return Vector of material IDs with tag
     */
    std::vector<std::string> getMaterialsByTag(MaterialTag tag);
    
    /**
     * @brief Get all material IDs
     * @return Vector of all material IDs
     */
    std::vector<std::string> getAllMaterialIds();
    
    /**
     * @brief Get material count
     * @return Total number of materials in library
     */
    size_t getMaterialCount() const;
    
    /**
     * @brief Get material count by category
     * @param category Material category
     * @return Number of materials in category
     */
    size_t getMaterialCountByCategory(MaterialCategory category) const;
    
    /**
     * @brief Import material from file
     * @param filePath Path to material file
     * @return Material ID if successful, empty string if failed
     */
    std::string importMaterial(const std::string& filePath);
    
    /**
     * @brief Export material to file
     * @param materialId Material ID to export
     * @param filePath Output file path
     * @return True if export successful
     */
    bool exportMaterial(const std::string& materialId, const std::string& filePath);
    
    /**
     * @brief Import material library from directory
     * @param libraryPath Path to library directory
     * @return Number of materials imported
     */
    int importLibrary(const std::string& libraryPath);
    
    /**
     * @brief Export material library to directory
     * @param libraryPath Output library directory
     * @return Number of materials exported
     */
    int exportLibrary(const std::string& libraryPath);
    
    /**
     * @brief Generate thumbnail for material
     * @param materialId Material ID
     * @param size Thumbnail size (square)
     * @return True if generation successful
     */
    bool generateThumbnail(const std::string& materialId, int size = 128);
    
    /**
     * @brief Generate preview render for material
     * @param materialId Material ID
     * @param size Preview size (square)
     * @return True if generation successful
     */
    bool generatePreview(const std::string& materialId, int size = 256);
    
    /**
     * @brief Set material rating
     * @param materialId Material ID
     * @param rating Rating value (0-5)
     */
    void setMaterialRating(const std::string& materialId, float rating);
    
    /**
     * @brief Increment material usage count
     * @param materialId Material ID
     */
    void incrementUsageCount(const std::string& materialId);
    
    /**
     * @brief Get recently used materials
     * @param count Number of recent materials to return
     * @return Vector of recently used material IDs
     */
    std::vector<std::string> getRecentMaterials(int count = 10);
    
    /**
     * @brief Get popular materials
     * @param count Number of popular materials to return
     * @return Vector of popular material IDs
     */
    std::vector<std::string> getPopularMaterials(int count = 10);
    
    /**
     * @brief Get library statistics
     * @return Statistics as string
     */
    std::string getLibraryStats() const;
    
    /**
     * @brief Validate library integrity
     * @return True if library is valid
     */
    bool validateLibrary();
    
    /**
     * @brief Cleanup unused files
     * @return Number of files cleaned up
     */
    int cleanupLibrary();
    
    /**
     * @brief Set progress callback for long operations
     * @param callback Progress callback function
     */
    void setProgressCallback(std::function<void(float, const std::string&)> callback);

private:
    std::string m_libraryPath;
    std::unordered_map<std::string, std::shared_ptr<MaterialEntry>> m_materials;
    std::vector<std::string> m_recentMaterials;
    std::function<void(float, const std::string&)> m_progressCallback;
    
    bool m_initialized = false;
    bool m_dirty = false;
    
    /**
     * @brief Generate unique material ID
     * @return Unique material ID
     */
    std::string generateMaterialId();
    
    /**
     * @brief Load material from file
     * @param filePath Material file path
     * @return Material entry or nullptr if failed
     */
    std::shared_ptr<MaterialEntry> loadMaterialFromFile(const std::string& filePath);
    
    /**
     * @brief Save material to file
     * @param entry Material entry to save
     * @param filePath Output file path
     * @return True if save successful
     */
    bool saveMaterialToFile(const std::shared_ptr<MaterialEntry>& entry, const std::string& filePath);
    
    /**
     * @brief Update progress
     * @param progress Progress value (0-1)
     * @param message Progress message
     */
    void updateProgress(float progress, const std::string& message);
    
    /**
     * @brief Get category string
     * @param category Material category
     * @return Category name as string
     */
    std::string getCategoryString(MaterialCategory category) const;
    
    /**
     * @brief Get tag string
     * @param tag Material tag
     * @return Tag name as string
     */
    std::string getTagString(MaterialTag tag) const;
    
    /**
     * @brief Parse category from string
     * @param categoryStr Category string
     * @return Material category
     */
    MaterialCategory parseCategoryFromString(const std::string& categoryStr) const;
    
    /**
     * @brief Parse tag from string
     * @param tagStr Tag string
     * @return Material tag
     */
    MaterialTag parseTagFromString(const std::string& tagStr) const;
};

} // namespace photon

#endif // PHOTON_MATERIAL_LIBRARY_HPP
