// src/core/material/material_validator.hpp
// PhotonRender - Material Validation System
// Sistema di validazione materiali con energy conservation e parameter validation

#ifndef PHOTON_MATERIAL_VALIDATOR_HPP
#define PHOTON_MATERIAL_VALIDATOR_HPP

#include "../math/vec3.hpp"
#include "disney_brdf.hpp"
#include <string>
#include <vector>
#include <memory>
#include <functional>

namespace photon {

// Forward declarations
class Material;
class PBRMaterial;

/**
 * @brief Validation severity levels
 */
enum class ValidationSeverity {
    INFO,       // Informational message
    WARNING,    // Warning that should be addressed
    ERROR,      // Error that must be fixed
    CRITICAL    // Critical error that breaks rendering
};

/**
 * @brief Validation categories
 */
enum class ValidationCategory {
    ENERGY_CONSERVATION,    // Energy conservation violations
    PARAMETER_RANGE,        // Parameter out of valid range
    PARAMETER_COMBINATION,  // Invalid parameter combinations
    TEXTURE_COMPATIBILITY,  // Texture compatibility issues
    PERFORMANCE,           // Performance warnings
    PHYSICAL_PLAUSIBILITY, // Physical plausibility issues
    WORKFLOW              // Workflow recommendations
};

/**
 * @brief Validation issue
 */
struct ValidationIssue {
    ValidationSeverity severity;        // Issue severity
    ValidationCategory category;        // Issue category
    std::string parameter;              // Parameter name causing issue
    std::string message;                // Human-readable message
    std::string suggestion;             // Suggested fix
    float currentValue = 0.0f;          // Current parameter value
    float suggestedValue = 0.0f;        // Suggested parameter value
    bool autoFixable = false;           // Can be automatically fixed
    
    /**
     * @brief Get severity string
     * @return Severity as string
     */
    std::string getSeverityString() const;
    
    /**
     * @brief Get category string
     * @return Category as string
     */
    std::string getCategoryString() const;
    
    /**
     * @brief Get formatted message
     * @return Formatted message with details
     */
    std::string getFormattedMessage() const;
};

/**
 * @brief Validation result
 */
struct ValidationResult {
    bool isValid = true;                        // Overall validation result
    std::vector<ValidationIssue> issues;       // List of validation issues
    float energyConservationScore = 1.0f;      // Energy conservation score (0-1)
    float physicalPlausibilityScore = 1.0f;    // Physical plausibility score (0-1)
    float performanceScore = 1.0f;             // Performance score (0-1)
    
    /**
     * @brief Get issues by severity
     * @param severity Target severity
     * @return Vector of issues with specified severity
     */
    std::vector<ValidationIssue> getIssuesBySeverity(ValidationSeverity severity) const;
    
    /**
     * @brief Get issues by category
     * @param category Target category
     * @return Vector of issues with specified category
     */
    std::vector<ValidationIssue> getIssuesByCategory(ValidationCategory category) const;
    
    /**
     * @brief Get auto-fixable issues
     * @return Vector of auto-fixable issues
     */
    std::vector<ValidationIssue> getAutoFixableIssues() const;
    
    /**
     * @brief Get validation summary
     * @return Summary string
     */
    std::string getSummary() const;
    
    /**
     * @brief Check if has errors
     * @return True if has error or critical issues
     */
    bool hasErrors() const;
    
    /**
     * @brief Check if has warnings
     * @return True if has warning issues
     */
    bool hasWarnings() const;
};

/**
 * @brief Validation configuration
 */
struct ValidationConfig {
    bool enableEnergyConservation = true;      // Enable energy conservation checks
    bool enableParameterRange = true;          // Enable parameter range checks
    bool enableParameterCombination = true;    // Enable parameter combination checks
    bool enableTextureCompatibility = true;    // Enable texture compatibility checks
    bool enablePerformanceWarnings = true;     // Enable performance warnings
    bool enablePhysicalPlausibility = true;    // Enable physical plausibility checks
    bool enableWorkflowSuggestions = true;     // Enable workflow suggestions
    
    float energyConservationTolerance = 0.05f; // Energy conservation tolerance
    float performanceThreshold = 0.8f;         // Performance warning threshold
    bool strictMode = false;                    // Strict validation mode
    bool autoFix = false;                       // Enable automatic fixes
};

/**
 * @brief Material Validator
 * 
 * Comprehensive system for validating material parameters with
 * energy conservation checks, parameter validation, and visual feedback
 */
class MaterialValidator {
public:
    /**
     * @brief Constructor
     */
    MaterialValidator();
    
    /**
     * @brief Destructor
     */
    ~MaterialValidator();
    
    /**
     * @brief Set validation configuration
     * @param config Validation configuration
     */
    void setConfig(const ValidationConfig& config);
    
    /**
     * @brief Get validation configuration
     * @return Current validation configuration
     */
    ValidationConfig getConfig() const;
    
    /**
     * @brief Validate material
     * @param material Material to validate
     * @return Validation result
     */
    ValidationResult validateMaterial(const std::shared_ptr<Material>& material);
    
    /**
     * @brief Validate PBR material
     * @param material PBR material to validate
     * @return Validation result
     */
    ValidationResult validatePBRMaterial(const std::shared_ptr<PBRMaterial>& material);
    
    /**
     * @brief Validate Disney BRDF parameters
     * @param params Disney BRDF parameters
     * @return Validation result
     */
    ValidationResult validateDisneyBRDF(const DisneyBRDFParams& params);
    
    /**
     * @brief Validate parameter value
     * @param paramName Parameter name
     * @param value Parameter value
     * @param minValue Minimum valid value
     * @param maxValue Maximum valid value
     * @return Validation issues for this parameter
     */
    std::vector<ValidationIssue> validateParameter(const std::string& paramName, 
                                                   float value, 
                                                   float minValue, 
                                                   float maxValue);
    
    /**
     * @brief Check energy conservation
     * @param params Disney BRDF parameters
     * @return Energy conservation validation issues
     */
    std::vector<ValidationIssue> checkEnergyConservation(const DisneyBRDFParams& params);
    
    /**
     * @brief Check parameter combinations
     * @param params Disney BRDF parameters
     * @return Parameter combination validation issues
     */
    std::vector<ValidationIssue> checkParameterCombinations(const DisneyBRDFParams& params);
    
    /**
     * @brief Check physical plausibility
     * @param params Disney BRDF parameters
     * @return Physical plausibility validation issues
     */
    std::vector<ValidationIssue> checkPhysicalPlausibility(const DisneyBRDFParams& params);
    
    /**
     * @brief Check performance implications
     * @param params Disney BRDF parameters
     * @return Performance validation issues
     */
    std::vector<ValidationIssue> checkPerformance(const DisneyBRDFParams& params);
    
    /**
     * @brief Auto-fix validation issues
     * @param params Disney BRDF parameters (will be modified)
     * @param issues Validation issues to fix
     * @return Number of issues fixed
     */
    int autoFixIssues(DisneyBRDFParams& params, const std::vector<ValidationIssue>& issues);
    
    /**
     * @brief Get workflow suggestions
     * @param params Disney BRDF parameters
     * @return Workflow suggestion issues
     */
    std::vector<ValidationIssue> getWorkflowSuggestions(const DisneyBRDFParams& params);
    
    /**
     * @brief Calculate energy conservation score
     * @param params Disney BRDF parameters
     * @return Energy conservation score (0-1, 1 = perfect conservation)
     */
    float calculateEnergyConservationScore(const DisneyBRDFParams& params);
    
    /**
     * @brief Calculate physical plausibility score
     * @param params Disney BRDF parameters
     * @return Physical plausibility score (0-1, 1 = perfectly plausible)
     */
    float calculatePhysicalPlausibilityScore(const DisneyBRDFParams& params);
    
    /**
     * @brief Calculate performance score
     * @param params Disney BRDF parameters
     * @return Performance score (0-1, 1 = optimal performance)
     */
    float calculatePerformanceScore(const DisneyBRDFParams& params);
    
    /**
     * @brief Set validation callback for real-time feedback
     * @param callback Validation callback function
     */
    void setValidationCallback(std::function<void(const ValidationResult&)> callback);
    
    /**
     * @brief Enable real-time validation
     * @param enable Enable real-time validation
     */
    void setRealTimeValidation(bool enable);
    
    /**
     * @brief Get validation statistics
     * @return Validation statistics as string
     */
    std::string getValidationStats() const;

private:
    ValidationConfig m_config;
    std::function<void(const ValidationResult&)> m_validationCallback;
    bool m_realTimeValidation = false;
    
    // Statistics
    mutable size_t m_validationCount = 0;
    mutable size_t m_issueCount = 0;
    mutable size_t m_autoFixCount = 0;
    
    /**
     * @brief Create validation issue
     * @param severity Issue severity
     * @param category Issue category
     * @param parameter Parameter name
     * @param message Issue message
     * @param suggestion Suggested fix
     * @param currentValue Current value
     * @param suggestedValue Suggested value
     * @param autoFixable Can be auto-fixed
     * @return Validation issue
     */
    ValidationIssue createIssue(ValidationSeverity severity,
                               ValidationCategory category,
                               const std::string& parameter,
                               const std::string& message,
                               const std::string& suggestion = "",
                               float currentValue = 0.0f,
                               float suggestedValue = 0.0f,
                               bool autoFixable = false) const;
    
    /**
     * @brief Check if metallic and specular are compatible
     * @param metallic Metallic value
     * @param specular Specular value
     * @return True if compatible
     */
    bool isMetallicSpecularCompatible(float metallic, float specular) const;
    
    /**
     * @brief Check if subsurface and metallic are compatible
     * @param subsurface Subsurface value
     * @param metallic Metallic value
     * @return True if compatible
     */
    bool isSubsurfaceMetallicCompatible(float subsurface, float metallic) const;
    
    /**
     * @brief Check if clearcoat parameters are valid
     * @param clearcoat Clearcoat value
     * @param clearcoatGloss Clearcoat gloss value
     * @return True if valid
     */
    bool isClearcoatValid(float clearcoat, float clearcoatGloss) const;
    
    /**
     * @brief Estimate rendering complexity
     * @param params Disney BRDF parameters
     * @return Complexity score (0-1, 1 = most complex)
     */
    float estimateRenderingComplexity(const DisneyBRDFParams& params) const;
    
    /**
     * @brief Trigger validation callback
     * @param result Validation result
     */
    void triggerCallback(const ValidationResult& result) const;
};

} // namespace photon

#endif // PHOTON_MATERIAL_VALIDATOR_HPP
