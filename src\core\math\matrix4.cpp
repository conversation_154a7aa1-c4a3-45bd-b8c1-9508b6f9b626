// src/core/math/matrix4.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// 4x4 Matrix implementation

#include "matrix4.hpp"
#include "ray.hpp"
#include <cmath>
#include <algorithm>
#include <iomanip>

namespace photon {

// Matrix4 basic operations
void Matrix4::setIdentity() {
    for (int i = 0; i < 4; ++i) {
        for (int j = 0; j < 4; ++j) {
            m[i][j] = (i == j) ? 1.0f : 0.0f;
        }
    }
}

void Matrix4::setZero() {
    for (int i = 0; i < 4; ++i) {
        for (int j = 0; j < 4; ++j) {
            m[i][j] = 0.0f;
        }
    }
}

Matrix4 Matrix4::identity() {
    Matrix4 result;
    result.setIdentity();
    return result;
}

Matrix4 Matrix4::zero() {
    Matrix4 result;
    result.setZero();
    return result;
}

// Matrix arithmetic
Matrix4 Matrix4::operator+(const Matrix4& other) const {
    Matrix4 result;
    for (int i = 0; i < 4; ++i) {
        for (int j = 0; j < 4; ++j) {
            result.m[i][j] = m[i][j] + other.m[i][j];
        }
    }
    return result;
}

Matrix4 Matrix4::operator-(const Matrix4& other) const {
    Matrix4 result;
    for (int i = 0; i < 4; ++i) {
        for (int j = 0; j < 4; ++j) {
            result.m[i][j] = m[i][j] - other.m[i][j];
        }
    }
    return result;
}

Matrix4 Matrix4::operator*(const Matrix4& other) const {
    Matrix4 result;
    result.setZero();
    
    for (int i = 0; i < 4; ++i) {
        for (int j = 0; j < 4; ++j) {
            for (int k = 0; k < 4; ++k) {
                result.m[i][j] += m[i][k] * other.m[k][j];
            }
        }
    }
    return result;
}

Matrix4 Matrix4::operator*(float scalar) const {
    Matrix4 result;
    for (int i = 0; i < 4; ++i) {
        for (int j = 0; j < 4; ++j) {
            result.m[i][j] = m[i][j] * scalar;
        }
    }
    return result;
}

// Vector transformations
Vec3 Matrix4::transformPoint(const Vec3& point) const {
    float x = m[0][0] * point.x + m[1][0] * point.y + m[2][0] * point.z + m[3][0];
    float y = m[0][1] * point.x + m[1][1] * point.y + m[2][1] * point.z + m[3][1];
    float z = m[0][2] * point.x + m[1][2] * point.y + m[2][2] * point.z + m[3][2];
    float w = m[0][3] * point.x + m[1][3] * point.y + m[2][3] * point.z + m[3][3];
    
    if (w != 0.0f && w != 1.0f) {
        return Vec3(x / w, y / w, z / w);
    }
    return Vec3(x, y, z);
}

Vec3 Matrix4::transformVector(const Vec3& vector) const {
    float x = m[0][0] * vector.x + m[1][0] * vector.y + m[2][0] * vector.z;
    float y = m[0][1] * vector.x + m[1][1] * vector.y + m[2][1] * vector.z;
    float z = m[0][2] * vector.x + m[1][2] * vector.y + m[2][2] * vector.z;
    return Vec3(x, y, z);
}

Vec3 Matrix4::transformNormal(const Vec3& normal) const {
    // Transform normal using inverse transpose
    Matrix4 invTranspose = inverse().transpose();
    return invTranspose.transformVector(normal).normalized();
}

// Matrix transpose
Matrix4 Matrix4::transpose() const {
    Matrix4 result;
    for (int i = 0; i < 4; ++i) {
        for (int j = 0; j < 4; ++j) {
            result.m[i][j] = m[j][i];
        }
    }
    return result;
}

// Static factory methods
Matrix4 Matrix4::translation(const Vec3& t) {
    return translation(t.x, t.y, t.z);
}

Matrix4 Matrix4::translation(float x, float y, float z) {
    Matrix4 result = identity();
    result.m[3][0] = x;
    result.m[3][1] = y;
    result.m[3][2] = z;
    return result;
}

Matrix4 Matrix4::scale(const Vec3& s) {
    return scale(s.x, s.y, s.z);
}

Matrix4 Matrix4::scale(float x, float y, float z) {
    Matrix4 result = identity();
    result.m[0][0] = x;
    result.m[1][1] = y;
    result.m[2][2] = z;
    return result;
}

Matrix4 Matrix4::scale(float uniform) {
    return scale(uniform, uniform, uniform);
}

Matrix4 Matrix4::rotationX(float angle) {
    float c = std::cos(angle);
    float s = std::sin(angle);
    
    Matrix4 result = identity();
    result.m[1][1] = c;  result.m[2][1] = -s;
    result.m[1][2] = s;  result.m[2][2] = c;
    return result;
}

Matrix4 Matrix4::rotationY(float angle) {
    float c = std::cos(angle);
    float s = std::sin(angle);
    
    Matrix4 result = identity();
    result.m[0][0] = c;   result.m[2][0] = s;
    result.m[0][2] = -s;  result.m[2][2] = c;
    return result;
}

Matrix4 Matrix4::rotationZ(float angle) {
    float c = std::cos(angle);
    float s = std::sin(angle);
    
    Matrix4 result = identity();
    result.m[0][0] = c;  result.m[1][0] = -s;
    result.m[0][1] = s;  result.m[1][1] = c;
    return result;
}

Matrix4 Matrix4::rotation(const Vec3& axis, float angle) {
    Vec3 a = axis.normalized();
    float c = std::cos(angle);
    float s = std::sin(angle);
    float t = 1.0f - c;
    
    return Matrix4(
        t*a.x*a.x + c,     t*a.x*a.y - s*a.z, t*a.x*a.z + s*a.y, 0,
        t*a.x*a.y + s*a.z, t*a.y*a.y + c,     t*a.y*a.z - s*a.x, 0,
        t*a.x*a.z - s*a.y, t*a.y*a.z + s*a.x, t*a.z*a.z + c,     0,
        0,                 0,                 0,                 1
    );
}

Matrix4 Matrix4::lookAt(const Vec3& eye, const Vec3& target, const Vec3& up) {
    Vec3 forward = (target - eye).normalized();
    Vec3 right = forward.cross(up.normalized()).normalized();
    Vec3 newUp = right.cross(forward);
    
    Matrix4 result = identity();
    result.m[0][0] = right.x;   result.m[1][0] = right.y;   result.m[2][0] = right.z;
    result.m[0][1] = newUp.x;   result.m[1][1] = newUp.y;   result.m[2][1] = newUp.z;
    result.m[0][2] = -forward.x; result.m[1][2] = -forward.y; result.m[2][2] = -forward.z;
    result.m[3][0] = -right.dot(eye);
    result.m[3][1] = -newUp.dot(eye);
    result.m[3][2] = forward.dot(eye);
    
    return result;
}

// Comparison operators
bool Matrix4::operator==(const Matrix4& other) const {
    for (int i = 0; i < 4; ++i) {
        for (int j = 0; j < 4; ++j) {
            if (m[i][j] != other.m[i][j]) return false;
        }
    }
    return true;
}

bool Matrix4::operator!=(const Matrix4& other) const {
    return !(*this == other);
}

// Utility functions
bool Matrix4::hasNaN() const {
    for (int i = 0; i < 4; ++i) {
        for (int j = 0; j < 4; ++j) {
            if (std::isnan(m[i][j])) return true;
        }
    }
    return false;
}

bool Matrix4::hasInf() const {
    for (int i = 0; i < 4; ++i) {
        for (int j = 0; j < 4; ++j) {
            if (std::isinf(m[i][j])) return true;
        }
    }
    return false;
}

// Non-member operators
Matrix4 operator*(float scalar, const Matrix4& matrix) {
    return matrix * scalar;
}

// Stream operator
std::ostream& operator<<(std::ostream& os, const Matrix4& matrix) {
    os << std::fixed << std::setprecision(3);
    for (int i = 0; i < 4; ++i) {
        os << "[ ";
        for (int j = 0; j < 4; ++j) {
            os << std::setw(8) << matrix.m[j][i];
            if (j < 3) os << ", ";
        }
        os << " ]";
        if (i < 3) os << "\n";
    }
    return os;
}

// Transform class implementation
Ray Transform::transformRay(const Ray& ray) const {
    Point3 o = transformPoint(ray.o);
    Vec3 d = transformVector(ray.d);
    return Ray(o, d, ray.tMin, ray.tMax);
}

Ray Transform::inverseTransformRay(const Ray& ray) const {
    Point3 o = inverseTransformPoint(ray.o);
    Vec3 d = inverseTransformVector(ray.d);
    return Ray(o, d, ray.tMin, ray.tMax);
}

Transform Transform::operator*(const Transform& other) const {
    return Transform(m_matrix * other.m_matrix, other.m_inverse * m_inverse);
}

bool Transform::isIdentity() const {
    return m_matrix == Matrix4::identity();
}

// Transform static factory methods
Transform Transform::identity() {
    return Transform();
}

Transform Transform::translation(const Vec3& t) {
    Matrix4 m = Matrix4::translation(t);
    Matrix4 inv = Matrix4::translation(-t);
    return Transform(m, inv);
}

Transform Transform::scale(const Vec3& s) {
    Matrix4 m = Matrix4::scale(s);
    Matrix4 inv = Matrix4::scale(1.0f/s.x, 1.0f/s.y, 1.0f/s.z);
    return Transform(m, inv);
}

Transform Transform::scale(float uniform) {
    return scale(Vec3(uniform));
}

Transform Transform::rotationX(float angle) {
    Matrix4 m = Matrix4::rotationX(angle);
    Matrix4 inv = Matrix4::rotationX(-angle);
    return Transform(m, inv);
}

Transform Transform::rotationY(float angle) {
    Matrix4 m = Matrix4::rotationY(angle);
    Matrix4 inv = Matrix4::rotationY(-angle);
    return Transform(m, inv);
}

Transform Transform::rotationZ(float angle) {
    Matrix4 m = Matrix4::rotationZ(angle);
    Matrix4 inv = Matrix4::rotationZ(-angle);
    return Transform(m, inv);
}

Transform Transform::rotation(const Vec3& axis, float angle) {
    Matrix4 m = Matrix4::rotation(axis, angle);
    Matrix4 inv = Matrix4::rotation(axis, -angle);
    return Transform(m, inv);
}

Transform Transform::lookAt(const Vec3& eye, const Vec3& target, const Vec3& up) {
    Matrix4 m = Matrix4::lookAt(eye, target, up);
    return Transform(m, m.inverse());
}

// Matrix4 determinant and inverse implementation
float Matrix4::determinant() const {
    float det = 0.0f;
    for (int i = 0; i < 4; ++i) {
        det += m[i][0] * cofactor(0, i);
    }
    return det;
}

float Matrix4::minor(int row, int col) const {
    float submatrix[3][3];
    int sub_i = 0;

    for (int i = 0; i < 4; ++i) {
        if (i == row) continue;
        int sub_j = 0;
        for (int j = 0; j < 4; ++j) {
            if (j == col) continue;
            submatrix[sub_i][sub_j] = m[j][i];
            sub_j++;
        }
        sub_i++;
    }

    // 3x3 determinant
    return submatrix[0][0] * (submatrix[1][1] * submatrix[2][2] - submatrix[1][2] * submatrix[2][1])
         - submatrix[0][1] * (submatrix[1][0] * submatrix[2][2] - submatrix[1][2] * submatrix[2][0])
         + submatrix[0][2] * (submatrix[1][0] * submatrix[2][1] - submatrix[1][1] * submatrix[2][0]);
}

float Matrix4::cofactor(int row, int col) const {
    float minorValue = minor(row, col);
    return ((row + col) % 2 == 0) ? minorValue : -minorValue;
}

Matrix4 Matrix4::adjugate() const {
    Matrix4 result;
    for (int i = 0; i < 4; ++i) {
        for (int j = 0; j < 4; ++j) {
            result.m[j][i] = cofactor(i, j); // Transpose while computing
        }
    }
    return result;
}

Matrix4 Matrix4::inverse() const {
    float det = determinant();
    if (std::abs(det) < 1e-6f) {
        // Matrix is not invertible, return identity
        return identity();
    }

    Matrix4 adj = adjugate();
    return adj * (1.0f / det);
}

bool Matrix4::isInvertible() const {
    return std::abs(determinant()) > 1e-6f;
}

} // namespace photon
