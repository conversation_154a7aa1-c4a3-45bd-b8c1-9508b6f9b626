// src/core/math/ray.hpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Ray class for ray-tracing operations

#pragma once

#include "vec3.hpp"
#include <limits>

namespace photon {

/**
 * @brief Ray class for ray-tracing operations
 * 
 * Represents a ray with origin, direction, and parameter bounds.
 * Used for ray-scene intersection calculations.
 */
class Ray {
public:
    Point3 o;  // Origin
    Vec3 d;    // Direction (should be normalized)
    float tMin; // Minimum parameter value
    float tMax; // Maximum parameter value
    
    // Constructors
    Ray() : o(0), d(0, 0, 1), tMin(0.001f), tMax(std::numeric_limits<float>::infinity()) {}
    
    Ray(const Point3& origin, const Vec3& direction) 
        : o(origin), d(direction), tMin(0.001f), tMax(std::numeric_limits<float>::infinity()) {}
    
    Ray(const Point3& origin, const Vec3& direction, float tMin, float tMax)
        : o(origin), d(direction), tMin(tMin), tMax(tMax) {}
    
    // Copy constructor and assignment
    Ray(const Ray& ray) = default;
    Ray& operator=(const Ray& ray) = default;
    
    // Ray evaluation
    Point3 operator()(float t) const { return o + d * t; }
    Point3 at(float t) const { return o + d * t; }
    
    // Ray properties
    bool hasNaN() const { return o.hasNaN() || d.hasNaN(); }
    bool hasInf() const { return o.hasInf() || d.hasInf(); }
    
    // Ray transformations
    Ray transformed(const class Transform& transform) const;
    
    // Utility functions
    void normalize() { d.normalize(); }
    
    // Static utility functions
    static Ray between(const Point3& p1, const Point3& p2) {
        Vec3 dir = p2 - p1;
        float length = dir.length();
        return Ray(p1, dir / length, 0.001f, length - 0.001f);
    }

    // Compatibility methods for origin/direction access
    const Point3& origin() const { return o; }
    Point3& origin() { return o; }
    const Vec3& direction() const { return d; }
    Vec3& direction() { return d; }
};

/**
 * @brief Ray differential for texture filtering
 * 
 * Stores additional rays for computing texture derivatives.
 * Used for anti-aliasing in texture sampling.
 */
class RayDifferential : public Ray {
public:
    bool hasDifferentials;
    Point3 rxOrigin, ryOrigin;
    Vec3 rxDirection, ryDirection;
    
    RayDifferential() : Ray(), hasDifferentials(false) {}
    
    RayDifferential(const Point3& origin, const Vec3& direction)
        : Ray(origin, direction), hasDifferentials(false) {}
    
    RayDifferential(const Ray& ray) 
        : Ray(ray), hasDifferentials(false) {}
    
    void scaleDifferentials(float s) {
        rxOrigin = o + (rxOrigin - o) * s;
        ryOrigin = o + (ryOrigin - o) * s;
        rxDirection = d + (rxDirection - d) * s;
        ryDirection = d + (ryDirection - d) * s;
    }
};

// Stream operator
inline std::ostream& operator<<(std::ostream& os, const Ray& ray) {
    return os << "Ray(o=" << ray.o << ", d=" << ray.d 
              << ", tMin=" << ray.tMin << ", tMax=" << ray.tMax << ")";
}

} // namespace photon
