// src/core/math/vec2.hpp
// PhotonRender - Professional Rendering Engine for SketchUp
// 2D Vector class for UV coordinates and texture mapping

#pragma once

#include <algorithm>  // For std::clamp, std::min, std::max
#include <cmath>
#include <iostream>

namespace photon {

/**
 * @brief 2D vector class for UV coordinates
 */
struct Vec2 {
    float x, y;
    
    /**
     * @brief Default constructor (zero vector)
     */
    Vec2() : x(0.0f), y(0.0f) {}
    
    /**
     * @brief Constructor with components
     */
    Vec2(float x, float y) : x(x), y(y) {}
    
    /**
     * @brief Constructor with single value (both components)
     */
    explicit Vec2(float value) : x(value), y(value) {}
    
    // Arithmetic operators
    Vec2 operator+(const Vec2& v) const { return Vec2(x + v.x, y + v.y); }
    Vec2 operator-(const Vec2& v) const { return Vec2(x - v.x, y - v.y); }
    Vec2 operator*(float s) const { return Vec2(x * s, y * s); }
    Vec2 operator*(const Vec2& v) const { return Vec2(x * v.x, y * v.y); }
    Vec2 operator/(float s) const { return Vec2(x / s, y / s); }
    Vec2 operator/(const Vec2& v) const { return Vec2(x / v.x, y / v.y); }
    
    // Assignment operators
    Vec2& operator+=(const Vec2& v) { x += v.x; y += v.y; return *this; }
    Vec2& operator-=(const Vec2& v) { x -= v.x; y -= v.y; return *this; }
    Vec2& operator*=(float s) { x *= s; y *= s; return *this; }
    Vec2& operator*=(const Vec2& v) { x *= v.x; y *= v.y; return *this; }
    Vec2& operator/=(float s) { x /= s; y /= s; return *this; }
    Vec2& operator/=(const Vec2& v) { x /= v.x; y /= v.y; return *this; }
    
    // Unary operators
    Vec2 operator-() const { return Vec2(-x, -y); }
    
    // Comparison operators
    bool operator==(const Vec2& v) const { return x == v.x && y == v.y; }
    bool operator!=(const Vec2& v) const { return !(*this == v); }
    
    // Array access
    float& operator[](int i) { return (&x)[i]; }
    const float& operator[](int i) const { return (&x)[i]; }
    
    /**
     * @brief Dot product
     */
    float dot(const Vec2& v) const { return x * v.x + y * v.y; }
    
    /**
     * @brief Cross product (returns scalar for 2D)
     */
    float cross(const Vec2& v) const { return x * v.y - y * v.x; }
    
    /**
     * @brief Vector length (magnitude)
     */
    float length() const { return std::sqrt(x * x + y * y); }
    
    /**
     * @brief Squared length (faster than length)
     */
    float lengthSquared() const { return x * x + y * y; }
    
    /**
     * @brief Normalize vector (make unit length)
     */
    Vec2 normalized() const {
        float len = length();
        return len > 0.0f ? *this / len : Vec2(0.0f);
    }
    
    /**
     * @brief Normalize vector in place
     */
    Vec2& normalize() {
        float len = length();
        if (len > 0.0f) {
            x /= len;
            y /= len;
        }
        return *this;
    }
    
    /**
     * @brief Check if vector is zero
     */
    bool isZero() const { return x == 0.0f && y == 0.0f; }
    
    /**
     * @brief Check if vector is unit length
     */
    bool isUnit() const { return std::abs(lengthSquared() - 1.0f) < 1e-6f; }
    
    /**
     * @brief Distance to another point
     */
    float distance(const Vec2& v) const { return (*this - v).length(); }
    
    /**
     * @brief Squared distance to another point
     */
    float distanceSquared(const Vec2& v) const { return (*this - v).lengthSquared(); }
    
    /**
     * @brief Linear interpolation
     */
    Vec2 lerp(const Vec2& v, float t) const {
        return *this * (1.0f - t) + v * t;
    }
    
    /**
     * @brief Clamp components to range [min, max]
     */
    Vec2 clamp(float minVal, float maxVal) const {
        return Vec2(
            std::clamp(x, minVal, maxVal),
            std::clamp(y, minVal, maxVal)
        );
    }
    
    /**
     * @brief Clamp components to range [min, max] per component
     */
    Vec2 clamp(const Vec2& minVec, const Vec2& maxVec) const {
        return Vec2(
            std::clamp(x, minVec.x, maxVec.x),
            std::clamp(y, minVec.y, maxVec.y)
        );
    }
    
    /**
     * @brief Absolute value of components
     */
    Vec2 abs() const { return Vec2(std::abs(x), std::abs(y)); }
    
    /**
     * @brief Floor of components
     */
    Vec2 floor() const { return Vec2(std::floor(x), std::floor(y)); }
    
    /**
     * @brief Ceiling of components
     */
    Vec2 ceil() const { return Vec2(std::ceil(x), std::ceil(y)); }
    
    /**
     * @brief Round components to nearest integer
     */
    Vec2 round() const { return Vec2(std::round(x), std::round(y)); }
    
    /**
     * @brief Fractional part of components
     */
    Vec2 fract() const { return *this - floor(); }
    
    /**
     * @brief Reflect vector across normal
     */
    Vec2 reflect(const Vec2& normal) const {
        return *this - normal * (2.0f * dot(normal));
    }
    
    /**
     * @brief Rotate vector by angle (radians)
     */
    Vec2 rotate(float angle) const {
        float cos_a = std::cos(angle);
        float sin_a = std::sin(angle);
        return Vec2(x * cos_a - y * sin_a, x * sin_a + y * cos_a);
    }
    
    /**
     * @brief Get angle of vector (radians)
     */
    float angle() const { return std::atan2(y, x); }
    
    /**
     * @brief Get perpendicular vector (90 degrees counter-clockwise)
     */
    Vec2 perpendicular() const { return Vec2(-y, x); }
    
    // Static utility functions
    
    /**
     * @brief Zero vector
     */
    static Vec2 zero() { return Vec2(0.0f, 0.0f); }
    
    /**
     * @brief Unit vector in X direction
     */
    static Vec2 unitX() { return Vec2(1.0f, 0.0f); }
    
    /**
     * @brief Unit vector in Y direction
     */
    static Vec2 unitY() { return Vec2(0.0f, 1.0f); }
    
    /**
     * @brief Vector with all components set to 1
     */
    static Vec2 one() { return Vec2(1.0f, 1.0f); }
    
    /**
     * @brief Minimum of two vectors (component-wise)
     */
    static Vec2 min(const Vec2& a, const Vec2& b) {
        return Vec2(std::min(a.x, b.x), std::min(a.y, b.y));
    }
    
    /**
     * @brief Maximum of two vectors (component-wise)
     */
    static Vec2 max(const Vec2& a, const Vec2& b) {
        return Vec2(std::max(a.x, b.x), std::max(a.y, b.y));
    }
    
    /**
     * @brief Linear interpolation between two vectors
     */
    static Vec2 lerp(const Vec2& a, const Vec2& b, float t) {
        return a.lerp(b, t);
    }
    
    /**
     * @brief Distance between two points
     */
    static float distance(const Vec2& a, const Vec2& b) {
        return a.distance(b);
    }
    
    /**
     * @brief Angle between two vectors (radians)
     */
    static float angle(const Vec2& a, const Vec2& b) {
        return std::acos(std::clamp(a.normalized().dot(b.normalized()), -1.0f, 1.0f));
    }
};

// Global operators for scalar multiplication
inline Vec2 operator*(float s, const Vec2& v) { return v * s; }

// Stream operators for debugging
inline std::ostream& operator<<(std::ostream& os, const Vec2& v) {
    return os << "Vec2(" << v.x << ", " << v.y << ")";
}

inline std::istream& operator>>(std::istream& is, Vec2& v) {
    return is >> v.x >> v.y;
}

} // namespace photon
