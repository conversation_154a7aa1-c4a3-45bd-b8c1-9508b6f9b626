// src/core/memory/advanced_memory_manager.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Advanced Memory Management System Implementation

#include "advanced_memory_manager.hpp"
#include "../profiling/performance_profiler.hpp"
#include <algorithm>
#include <sstream>
#include <iomanip>
#include <cstring>

#ifdef _WIN32
#include <windows.h>
#else
#include <sys/mman.h>
#include <unistd.h>
#endif

namespace photon {

// AdvancedMemoryPool Implementation
AdvancedMemoryPool::AdvancedMemoryPool(PoolType type, const PoolConfiguration& config)
    : m_type(type), m_config(config) {
    
    // Pre-allocate initial blocks
    for (size_t i = 0; i < m_config.initial_blocks; i++) {
        expandPool();
    }
    
    PHOTON_PROFILE_MEMORY("MemoryPool_" + std::to_string(static_cast<int>(type)), 
                         m_config.initial_blocks * m_config.block_size);
}

AdvancedMemoryPool::~AdvancedMemoryPool() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    // Free all blocks
    for (auto& block : m_blocks) {
        if (block.ptr) {
#ifdef _WIN32
            VirtualFree(block.ptr, 0, MEM_RELEASE);
#else
            munmap(block.ptr, block.size);
#endif
        }
    }
    
    m_blocks.clear();
    m_free_list.clear();
}

void* AdvancedMemoryPool::allocate(size_t size, size_t alignment) {
    PHOTON_PROFILE_SCOPE("MemoryPool::allocate");
    
    if (size == 0) return nullptr;
    
    std::lock_guard<std::mutex> lock(m_mutex);
    
    // Align size
    size_t aligned_size = (size + alignment - 1) & ~(alignment - 1);
    
    // Find suitable block
    MemoryBlock* block = findFreeBlock(aligned_size, alignment);
    
    if (!block) {
        // Try to expand pool
        if (m_blocks.size() < m_config.max_blocks) {
            expandPool();
            block = findFreeBlock(aligned_size, alignment);
        }
        
        if (!block) {
            m_stats.pool_misses++;
            return nullptr; // Pool exhausted
        }
    }
    
    // Mark block as used
    block->in_use = true;
    block->ref_count++;
    block->last_access = std::chrono::steady_clock::now();
    
    // Update statistics
    m_stats.allocation_count++;
    m_stats.current_usage += aligned_size;
    m_stats.total_allocated += aligned_size;
    m_stats.pool_hits++;
    
    if (m_stats.current_usage > m_stats.peak_usage) {
        m_stats.peak_usage = m_stats.current_usage;
    }
    
    return block->ptr;
}

void AdvancedMemoryPool::deallocate(void* ptr) {
    if (!ptr) return;
    
    PHOTON_PROFILE_SCOPE("MemoryPool::deallocate");
    
    std::lock_guard<std::mutex> lock(m_mutex);
    
    // Find the block
    auto it = std::find_if(m_blocks.begin(), m_blocks.end(),
                          [ptr](const MemoryBlock& block) {
                              return block.ptr == ptr;
                          });
    
    if (it != m_blocks.end()) {
        it->in_use = false;
        it->ref_count = std::max(0, static_cast<int>(it->ref_count) - 1);
        it->last_access = std::chrono::steady_clock::now();
        
        // Update statistics
        m_stats.deallocation_count++;
        m_stats.current_usage -= it->size;
        m_stats.total_freed += it->size;
        
        // Add to free list
        m_free_list.push_back(ptr);
    }
}

bool AdvancedMemoryPool::owns(void* ptr) const {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    return std::any_of(m_blocks.begin(), m_blocks.end(),
                      [ptr](const MemoryBlock& block) {
                          return block.ptr <= ptr && 
                                 ptr < static_cast<char*>(block.ptr) + block.size;
                      });
}

MemoryStatistics AdvancedMemoryPool::getStatistics() const {
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_stats;
}

size_t AdvancedMemoryPool::garbageCollect() {
    PHOTON_PROFILE_SCOPE("MemoryPool::garbageCollect");
    
    std::lock_guard<std::mutex> lock(m_mutex);
    
    size_t reclaimed = 0;
    auto now = std::chrono::steady_clock::now();
    
    // Remove expired blocks from free list
    m_free_list.erase(
        std::remove_if(m_free_list.begin(), m_free_list.end(),
                      [this, now, &reclaimed](void* ptr) {
                          auto it = std::find_if(m_blocks.begin(), m_blocks.end(),
                                               [ptr](const MemoryBlock& block) {
                                                   return block.ptr == ptr;
                                               });
                          
                          if (it != m_blocks.end() && isBlockExpired(*it)) {
                              reclaimed += it->size;
                              return true;
                          }
                          return false;
                      }),
        m_free_list.end());
    
    // Coalesce adjacent free blocks
    coalesceBlocks();
    
    m_stats.gc_runs++;
    m_stats.bytes_reclaimed += reclaimed;
    
    return reclaimed;
}

size_t AdvancedMemoryPool::shrink() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_config.auto_shrink) return 0;
    
    size_t shrunk = 0;
    auto now = std::chrono::steady_clock::now();
    
    // Remove unused blocks that have been idle for too long
    m_blocks.erase(
        std::remove_if(m_blocks.begin(), m_blocks.end(),
                      [this, now, &shrunk](const MemoryBlock& block) {
                          if (!block.in_use && isBlockExpired(block)) {
                              if (block.ptr) {
#ifdef _WIN32
                                  VirtualFree(block.ptr, 0, MEM_RELEASE);
#else
                                  munmap(block.ptr, block.size);
#endif
                              }
                              shrunk += block.size;
                              return true;
                          }
                          return false;
                      }),
        m_blocks.end());
    
    return shrunk;
}

void AdvancedMemoryPool::expandPool() {
    void* ptr = nullptr;
    
#ifdef _WIN32
    ptr = VirtualAlloc(nullptr, m_config.block_size, MEM_COMMIT | MEM_RESERVE, PAGE_READWRITE);
#else
    ptr = mmap(nullptr, m_config.block_size, PROT_READ | PROT_WRITE, MAP_PRIVATE | MAP_ANONYMOUS, -1, 0);
    if (ptr == MAP_FAILED) ptr = nullptr;
#endif
    
    if (ptr) {
        m_blocks.emplace_back(ptr, m_config.block_size, 16, m_type);
        m_free_list.push_back(ptr);
    }
}

MemoryBlock* AdvancedMemoryPool::findFreeBlock(size_t size, size_t alignment) {
    for (auto& block : m_blocks) {
        if (!block.in_use && block.size >= size) {
            // Check alignment
            uintptr_t addr = reinterpret_cast<uintptr_t>(block.ptr);
            uintptr_t aligned_addr = (addr + alignment - 1) & ~(alignment - 1);
            
            if (aligned_addr + size <= addr + block.size) {
                return &block;
            }
        }
    }
    return nullptr;
}

void AdvancedMemoryPool::coalesceBlocks() {
    // Sort blocks by address
    std::sort(m_blocks.begin(), m_blocks.end(),
             [](const MemoryBlock& a, const MemoryBlock& b) {
                 return a.ptr < b.ptr;
             });
    
    // Coalesce adjacent free blocks
    for (size_t i = 0; i < m_blocks.size() - 1; ++i) {
        if (!m_blocks[i].in_use && !m_blocks[i + 1].in_use) {
            char* end_of_first = static_cast<char*>(m_blocks[i].ptr) + m_blocks[i].size;
            if (end_of_first == m_blocks[i + 1].ptr) {
                // Merge blocks
                m_blocks[i].size += m_blocks[i + 1].size;
                m_blocks.erase(m_blocks.begin() + i + 1);
                --i; // Check this block again
            }
        }
    }
}

bool AdvancedMemoryPool::isBlockExpired(const MemoryBlock& block) const {
    auto now = std::chrono::steady_clock::now();
    auto idle_time = std::chrono::duration_cast<std::chrono::milliseconds>(now - block.last_access);
    return idle_time > m_config.gc_interval;
}

// GarbageCollector Implementation
GarbageCollector::GarbageCollector() = default;

GarbageCollector::~GarbageCollector() {
    shutdown();
}

void GarbageCollector::initialize(GCStrategy strategy) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_running) return;
    
    m_strategy = strategy;
    m_running = true;
    
    // Start GC thread
    m_gc_thread = std::thread(&GarbageCollector::gcThreadLoop, this);
    
    PHOTON_PROFILE_COUNTER("GarbageCollector_Initialized");
}

void GarbageCollector::shutdown() {
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        m_running = false;
    }
    
    if (m_gc_thread.joinable()) {
        m_gc_thread.join();
    }
}

void GarbageCollector::registerPool(std::shared_ptr<AdvancedMemoryPool> pool) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_pools.push_back(pool);
}

void GarbageCollector::unregisterPool(std::shared_ptr<AdvancedMemoryPool> pool) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    m_pools.erase(
        std::remove_if(m_pools.begin(), m_pools.end(),
                      [&pool](const std::weak_ptr<AdvancedMemoryPool>& weak_pool) {
                          return weak_pool.lock() == pool;
                      }),
        m_pools.end());
}

size_t GarbageCollector::collect() {
    PHOTON_PROFILE_SCOPE("GarbageCollector::collect");
    
    auto start_time = std::chrono::steady_clock::now();
    size_t total_reclaimed = 0;
    
    switch (m_strategy) {
        case GCStrategy::MARK_AND_SWEEP:
            total_reclaimed = runMarkAndSweep();
            break;
        case GCStrategy::GENERATIONAL:
            total_reclaimed = runGenerational();
            break;
        case GCStrategy::INCREMENTAL:
            total_reclaimed = runIncremental();
            break;
        case GCStrategy::CONCURRENT:
            total_reclaimed = runConcurrent();
            break;
        case GCStrategy::ADAPTIVE:
            total_reclaimed = runAdaptive();
            break;
    }
    
    auto end_time = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    // Update statistics
    m_stats.total_collections++;
    m_stats.bytes_reclaimed += total_reclaimed;
    m_stats.total_time += duration;
    m_stats.average_time = m_stats.total_time / m_stats.total_collections;
    m_stats.efficiency = m_stats.bytes_reclaimed > 0 ? 
                        (double)total_reclaimed / duration.count() : 0.0;
    
    PHOTON_PROFILE_GAUGE("GC_BytesReclaimed", total_reclaimed);
    PHOTON_PROFILE_GAUGE("GC_Duration", duration.count());
    
    return total_reclaimed;
}

void GarbageCollector::gcThreadLoop() {
    while (m_running) {
        std::this_thread::sleep_for(m_interval);
        
        if (shouldRunGC()) {
            collect();
        }
    }
}

size_t GarbageCollector::runMarkAndSweep() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    size_t total_reclaimed = 0;
    
    // Run GC on all registered pools
    for (auto it = m_pools.begin(); it != m_pools.end();) {
        if (auto pool = it->lock()) {
            total_reclaimed += pool->garbageCollect();
            ++it;
        } else {
            // Remove expired weak_ptr
            it = m_pools.erase(it);
        }
    }
    
    return total_reclaimed;
}

size_t GarbageCollector::runGenerational() {
    // Simplified generational GC - focus on newer allocations
    return runMarkAndSweep(); // For now, same as mark and sweep
}

size_t GarbageCollector::runIncremental() {
    // Incremental GC - process a subset of pools each time
    std::lock_guard<std::mutex> lock(m_mutex);
    
    static size_t pool_index = 0;
    size_t total_reclaimed = 0;
    
    if (!m_pools.empty()) {
        pool_index = pool_index % m_pools.size();
        
        if (auto pool = m_pools[pool_index].lock()) {
            total_reclaimed = pool->garbageCollect();
        }
        
        pool_index++;
    }
    
    return total_reclaimed;
}

size_t GarbageCollector::runConcurrent() {
    // Concurrent GC - run in parallel (simplified implementation)
    return runMarkAndSweep();
}

size_t GarbageCollector::runAdaptive() {
    // Adaptive strategy based on memory pressure
    double memory_pressure = 0.0;
    
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        size_t total_used = 0;
        size_t total_available = 0;
        
        for (auto& weak_pool : m_pools) {
            if (auto pool = weak_pool.lock()) {
                auto stats = pool->getStatistics();
                total_used += stats.current_usage;
                total_available += stats.total_allocated;
            }
        }
        
        memory_pressure = total_available > 0 ? (double)total_used / total_available : 0.0;
    }
    
    // Choose strategy based on memory pressure
    if (memory_pressure > 0.9) {
        return runMarkAndSweep(); // Aggressive collection
    } else if (memory_pressure > 0.7) {
        return runIncremental(); // Moderate collection
    } else {
        return 0; // No collection needed
    }
}

bool GarbageCollector::shouldRunGC() const {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    size_t total_used = 0;
    size_t total_available = 0;
    
    for (auto& weak_pool : m_pools) {
        if (auto pool = weak_pool.lock()) {
            auto stats = pool->getStatistics();
            total_used += stats.current_usage;
            total_available += stats.total_allocated;
        }
    }
    
    double usage_ratio = total_available > 0 ? (double)total_used / total_available : 0.0;
    return usage_ratio > m_memory_threshold;
}

// VRAMManager Implementation
VRAMManager& VRAMManager::getInstance() {
    static VRAMManager instance;
    return instance;
}

bool VRAMManager::initialize(size_t total_vram_mb) {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (m_initialized) return true;

    if (total_vram_mb == 0) {
        detectVRAMSize();
    } else {
        m_total_vram = total_vram_mb * 1024 * 1024;
    }

    m_initialized = true;

    PHOTON_PROFILE_MEMORY("VRAM_Total", m_total_vram);

    return true;
}

void VRAMManager::shutdown() {
    std::lock_guard<std::mutex> lock(m_mutex);

    // Free all VRAM allocations
    for (auto& pair : m_allocations) {
        if (pair.first) {
#ifdef CUDA_ENABLED
            cudaFree(pair.first);
#endif
        }
    }

    m_allocations.clear();
    m_vram_blocks.clear();
    m_initialized = false;
}

void* VRAMManager::allocateVRAM(size_t size, const std::string& tag) {
    if (!m_initialized || size == 0) return nullptr;

    PHOTON_PROFILE_SCOPE("VRAMManager::allocateVRAM");

    std::lock_guard<std::mutex> lock(m_mutex);

    void* ptr = nullptr;

#ifdef CUDA_ENABLED
    cudaError_t error = cudaMalloc(&ptr, size);
    if (error == cudaSuccess) {
        m_allocations[ptr] = size;
        m_vram_blocks.emplace_back(ptr, size);

        PHOTON_PROFILE_MEMORY("VRAM_" + tag, size);
        checkMemoryPressure();
    }
#endif

    return ptr;
}

void VRAMManager::deallocateVRAM(void* ptr) {
    if (!ptr || !m_initialized) return;

    PHOTON_PROFILE_SCOPE("VRAMManager::deallocateVRAM");

    std::lock_guard<std::mutex> lock(m_mutex);

    auto it = m_allocations.find(ptr);
    if (it != m_allocations.end()) {
#ifdef CUDA_ENABLED
        cudaFree(ptr);
#endif
        m_allocations.erase(it);

        // Remove from blocks
        m_vram_blocks.erase(
            std::remove_if(m_vram_blocks.begin(), m_vram_blocks.end(),
                          [ptr](const MemoryBlock& block) {
                              return block.ptr == ptr;
                          }),
            m_vram_blocks.end());
    }
}

VRAMManager::VRAMStatistics VRAMManager::getVRAMStatistics() const {
    std::lock_guard<std::mutex> lock(m_mutex);

    VRAMStatistics stats;
    stats.total_vram = m_total_vram;
    stats.used_vram = 0;

    for (const auto& pair : m_allocations) {
        stats.used_vram += pair.second;
    }

    stats.free_vram = stats.total_vram - stats.used_vram;
    stats.usage_percentage = stats.total_vram > 0 ?
                            (double)stats.used_vram / stats.total_vram * 100.0 : 0.0;

    return stats;
}

void VRAMManager::detectVRAMSize() {
#ifdef CUDA_ENABLED
    size_t free_mem, total_mem;
    cudaError_t error = cudaMemGetInfo(&free_mem, &total_mem);
    if (error == cudaSuccess) {
        m_total_vram = total_mem;
    } else {
        m_total_vram = 2ULL * 1024 * 1024 * 1024; // Default 2GB
    }
#else
    m_total_vram = 2ULL * 1024 * 1024 * 1024; // Default 2GB
#endif
}

void VRAMManager::checkMemoryPressure() {
    auto stats = getVRAMStatistics();
    double pressure = stats.usage_percentage / 100.0;

    if (m_pressure_callback && pressure > 0.8) {
        m_pressure_callback(pressure);
    }
}

// AdvancedMemoryManager Implementation
AdvancedMemoryManager& AdvancedMemoryManager::getInstance() {
    static AdvancedMemoryManager instance;
    return instance;
}

bool AdvancedMemoryManager::initialize() {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (m_initialized) return true;

    // Initialize pools
    initializePools();

    // Initialize garbage collector
    m_gc = std::make_unique<GarbageCollector>();
    m_gc->initialize(GCStrategy::ADAPTIVE);

    // Register pools with GC
    for (auto& pair : m_pools) {
        m_gc->registerPool(pair.second);
    }

    m_initialized = true;

    PHOTON_PROFILE_COUNTER("AdvancedMemoryManager_Initialized");

    return true;
}

void AdvancedMemoryManager::shutdown() {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_initialized) return;

    // Shutdown garbage collector
    if (m_gc) {
        m_gc->shutdown();
        m_gc.reset();
    }

    // Clear pools
    m_pools.clear();
    m_allocation_map.clear();

    m_initialized = false;
}

void* AdvancedMemoryManager::allocate(size_t size, size_t alignment, const std::string& tag) {
    if (!m_initialized || size == 0) return nullptr;

    PHOTON_PROFILE_SCOPE("AdvancedMemoryManager::allocate");

    PoolType pool_type = selectOptimalPool(size);
    void* ptr = allocateFromPool(pool_type, size, alignment);

    if (ptr) {
        std::lock_guard<std::mutex> lock(m_mutex);
        m_allocation_map[ptr] = pool_type;

        // Update global statistics
        m_global_stats.allocation_count++;
        m_global_stats.current_usage += size;
        m_global_stats.total_allocated += size;

        if (m_global_stats.current_usage > m_global_stats.peak_usage) {
            m_global_stats.peak_usage = m_global_stats.current_usage;
        }

        if (!tag.empty()) {
            PHOTON_PROFILE_MEMORY(tag, size);
        }
    }

    return ptr;
}

void AdvancedMemoryManager::deallocate(void* ptr) {
    if (!ptr || !m_initialized) return;

    PHOTON_PROFILE_SCOPE("AdvancedMemoryManager::deallocate");

    std::lock_guard<std::mutex> lock(m_mutex);

    auto it = m_allocation_map.find(ptr);
    if (it != m_allocation_map.end()) {
        PoolType pool_type = it->second;
        m_allocation_map.erase(it);

        auto pool_it = m_pools.find(pool_type);
        if (pool_it != m_pools.end()) {
            pool_it->second->deallocate(ptr);
        }

        m_global_stats.deallocation_count++;
    }
}

void* AdvancedMemoryManager::allocateFromPool(PoolType pool_type, size_t size, size_t alignment) {
    std::lock_guard<std::mutex> lock(m_mutex);

    auto it = m_pools.find(pool_type);
    if (it != m_pools.end()) {
        return it->second->allocate(size, alignment);
    }

    return nullptr;
}

PoolType AdvancedMemoryManager::selectOptimalPool(size_t size) const {
    if (size <= 1024) {
        return PoolType::SMALL_OBJECTS;
    } else if (size <= 64 * 1024) {
        return PoolType::MEDIUM_OBJECTS;
    } else if (size <= 1024 * 1024) {
        return PoolType::LARGE_OBJECTS;
    } else {
        return PoolType::HUGE_OBJECTS;
    }
}

void AdvancedMemoryManager::initializePools() {
    // Small objects pool (< 1KB)
    m_pools[PoolType::SMALL_OBJECTS] = std::make_shared<AdvancedMemoryPool>(
        PoolType::SMALL_OBJECTS, PoolConfiguration(4096, 32, 512));

    // Medium objects pool (1KB - 64KB)
    m_pools[PoolType::MEDIUM_OBJECTS] = std::make_shared<AdvancedMemoryPool>(
        PoolType::MEDIUM_OBJECTS, PoolConfiguration(64 * 1024, 16, 256));

    // Large objects pool (64KB - 1MB)
    m_pools[PoolType::LARGE_OBJECTS] = std::make_shared<AdvancedMemoryPool>(
        PoolType::LARGE_OBJECTS, PoolConfiguration(1024 * 1024, 8, 128));

    // Huge objects pool (> 1MB)
    m_pools[PoolType::HUGE_OBJECTS] = std::make_shared<AdvancedMemoryPool>(
        PoolType::HUGE_OBJECTS, PoolConfiguration(16 * 1024 * 1024, 4, 64));

    // Specialized pools
    m_pools[PoolType::TEXTURE_DATA] = std::make_shared<AdvancedMemoryPool>(
        PoolType::TEXTURE_DATA, PoolConfiguration(4 * 1024 * 1024, 8, 64));

    m_pools[PoolType::GEOMETRY_DATA] = std::make_shared<AdvancedMemoryPool>(
        PoolType::GEOMETRY_DATA, PoolConfiguration(2 * 1024 * 1024, 16, 128));

    m_pools[PoolType::LIGHT_DATA] = std::make_shared<AdvancedMemoryPool>(
        PoolType::LIGHT_DATA, PoolConfiguration(256 * 1024, 8, 64));

    m_pools[PoolType::TEMPORARY] = std::make_shared<AdvancedMemoryPool>(
        PoolType::TEMPORARY, PoolConfiguration(1024 * 1024, 16, 256));
}

MemoryStatistics AdvancedMemoryManager::getStatistics() const {
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_global_stats;
}

std::unordered_map<PoolType, MemoryStatistics> AdvancedMemoryManager::getPoolStatistics() const {
    std::lock_guard<std::mutex> lock(m_mutex);

    std::unordered_map<PoolType, MemoryStatistics> pool_stats;

    for (const auto& pair : m_pools) {
        pool_stats[pair.first] = pair.second->getStatistics();
    }

    return pool_stats;
}

size_t AdvancedMemoryManager::runGarbageCollection() {
    if (!m_gc) return 0;

    return m_gc->collect();
}

std::string AdvancedMemoryManager::generateMemoryReport() const {
    std::stringstream ss;

    ss << "=== Advanced Memory Manager Report ===\n";
    ss << "Global Statistics:\n";

    auto global_stats = getStatistics();
    ss << "  Total Allocated: " << (global_stats.total_allocated / 1024 / 1024) << " MB\n";
    ss << "  Current Usage: " << (global_stats.current_usage / 1024 / 1024) << " MB\n";
    ss << "  Peak Usage: " << (global_stats.peak_usage / 1024 / 1024) << " MB\n";
    ss << "  Allocations: " << global_stats.allocation_count << "\n";
    ss << "  Deallocations: " << global_stats.deallocation_count << "\n";
    ss << "  Hit Ratio: " << (global_stats.getHitRatio() * 100.0) << "%\n";
    ss << "  Fragmentation: " << (global_stats.getFragmentation() * 100.0) << "%\n\n";

    ss << "Pool Statistics:\n";
    auto pool_stats = getPoolStatistics();

    const char* pool_names[] = {
        "Small Objects", "Medium Objects", "Large Objects", "Huge Objects",
        "Texture Data", "Geometry Data", "Light Data", "Temporary"
    };

    int i = 0;
    for (const auto& pair : pool_stats) {
        const auto& stats = pair.second;
        ss << "  " << pool_names[i++] << ":\n";
        ss << "    Current Usage: " << (stats.current_usage / 1024) << " KB\n";
        ss << "    Peak Usage: " << (stats.peak_usage / 1024) << " KB\n";
        ss << "    Allocations: " << stats.allocation_count << "\n";
        ss << "    Hit Ratio: " << (stats.getHitRatio() * 100.0) << "%\n";
    }

    return ss.str();
}

} // namespace photon
