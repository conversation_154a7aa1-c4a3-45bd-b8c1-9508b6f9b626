// src/core/memory/advanced_memory_manager.hpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Advanced Memory Management System

#pragma once

#include "../math/vec3.hpp"
#include <memory>
#include <unordered_map>
#include <vector>
#include <mutex>
#include <atomic>
#include <chrono>
#include <functional>
#include <thread>

namespace photon {

/**
 * @brief Memory allocation strategies
 */
enum class AllocationStrategy {
    POOL_BASED,         ///< Use memory pools for frequent allocations
    DIRECT,             ///< Direct allocation for large/infrequent allocations
    HYBRID,             ///< Automatic selection based on size and frequency
    STREAMING           ///< Streaming allocation for large datasets
};

/**
 * @brief Memory pool types
 */
enum class PoolType {
    SMALL_OBJECTS,      ///< Small objects (< 1KB)
    MEDIUM_OBJECTS,     ///< Medium objects (1KB - 64KB)
    LARGE_OBJECTS,      ///< Large objects (64KB - 1MB)
    HUGE_OBJECTS,       ///< Huge objects (> 1MB)
    TEXTURE_DATA,       ///< Texture-specific pool
    GEOMETRY_DATA,      ///< Geometry-specific pool
    LIGHT_DATA,         ///< Light-specific pool
    TEMPORARY           ///< Temporary allocations
};

/**
 * @brief Memory block metadata
 */
struct MemoryBlock {
    void* ptr;
    size_t size;
    size_t alignment;
    bool in_use;
    std::chrono::steady_clock::time_point last_access;
    size_t ref_count;
    PoolType pool_type;
    
    MemoryBlock(void* p, size_t s, size_t a = 16, PoolType type = PoolType::SMALL_OBJECTS)
        : ptr(p), size(s), alignment(a), in_use(false), ref_count(0), pool_type(type),
          last_access(std::chrono::steady_clock::now()) {}
};

/**
 * @brief Memory allocation statistics
 */
struct MemoryStatistics {
    std::atomic<size_t> total_allocated{0};
    std::atomic<size_t> total_freed{0};
    std::atomic<size_t> current_usage{0};
    std::atomic<size_t> peak_usage{0};
    std::atomic<size_t> allocation_count{0};
    std::atomic<size_t> deallocation_count{0};
    std::atomic<size_t> pool_hits{0};
    std::atomic<size_t> pool_misses{0};
    std::atomic<size_t> gc_runs{0};
    std::atomic<size_t> bytes_reclaimed{0};
    
    void reset() {
        total_allocated = 0;
        total_freed = 0;
        current_usage = 0;
        peak_usage = 0;
        allocation_count = 0;
        deallocation_count = 0;
        pool_hits = 0;
        pool_misses = 0;
        gc_runs = 0;
        bytes_reclaimed = 0;
    }
    
    double getFragmentation() const {
        return total_allocated > 0 ? 
               (double)(total_allocated - current_usage) / total_allocated : 0.0;
    }
    
    double getHitRatio() const {
        size_t total_requests = pool_hits + pool_misses;
        return total_requests > 0 ? (double)pool_hits / total_requests : 0.0;
    }
};

/**
 * @brief Memory pool configuration
 */
struct PoolConfiguration {
    size_t block_size;
    size_t initial_blocks;
    size_t max_blocks;
    size_t growth_factor;
    bool auto_shrink;
    std::chrono::milliseconds gc_interval;
    
    PoolConfiguration(size_t bs = 4096, size_t ib = 16, size_t mb = 1024, 
                     size_t gf = 2, bool as = true, 
                     std::chrono::milliseconds gi = std::chrono::milliseconds(5000))
        : block_size(bs), initial_blocks(ib), max_blocks(mb), 
          growth_factor(gf), auto_shrink(as), gc_interval(gi) {}
};

/**
 * @brief Advanced Memory Pool
 */
class AdvancedMemoryPool {
public:
    AdvancedMemoryPool(PoolType type, const PoolConfiguration& config);
    ~AdvancedMemoryPool();
    
    /**
     * @brief Allocate memory from pool
     */
    void* allocate(size_t size, size_t alignment = 16);
    
    /**
     * @brief Deallocate memory back to pool
     */
    void deallocate(void* ptr);
    
    /**
     * @brief Check if pointer belongs to this pool
     */
    bool owns(void* ptr) const;
    
    /**
     * @brief Get pool statistics
     */
    MemoryStatistics getStatistics() const;
    
    /**
     * @brief Run garbage collection
     */
    size_t garbageCollect();
    
    /**
     * @brief Shrink pool if possible
     */
    size_t shrink();
    
    /**
     * @brief Get pool type
     */
    PoolType getType() const { return m_type; }
    
    /**
     * @brief Get configuration
     */
    const PoolConfiguration& getConfiguration() const { return m_config; }

private:
    PoolType m_type;
    PoolConfiguration m_config;
    std::vector<MemoryBlock> m_blocks;
    std::vector<void*> m_free_list;
    mutable std::mutex m_mutex;
    MemoryStatistics m_stats;
    
    void expandPool();
    MemoryBlock* findFreeBlock(size_t size, size_t alignment);
    void coalesceBlocks();
    bool isBlockExpired(const MemoryBlock& block) const;
};

/**
 * @brief Garbage Collection strategies
 */
enum class GCStrategy {
    MARK_AND_SWEEP,     ///< Traditional mark and sweep
    GENERATIONAL,       ///< Generational garbage collection
    INCREMENTAL,        ///< Incremental collection
    CONCURRENT,         ///< Concurrent collection
    ADAPTIVE            ///< Adaptive strategy based on usage patterns
};

/**
 * @brief Garbage Collector
 */
class GarbageCollector {
public:
    GarbageCollector();
    ~GarbageCollector();
    
    /**
     * @brief Initialize garbage collector
     */
    void initialize(GCStrategy strategy = GCStrategy::ADAPTIVE);
    
    /**
     * @brief Shutdown garbage collector
     */
    void shutdown();
    
    /**
     * @brief Register memory pool for GC
     */
    void registerPool(std::shared_ptr<AdvancedMemoryPool> pool);
    
    /**
     * @brief Unregister memory pool
     */
    void unregisterPool(std::shared_ptr<AdvancedMemoryPool> pool);
    
    /**
     * @brief Run garbage collection manually
     */
    size_t collect();
    
    /**
     * @brief Set GC parameters
     */
    void setGCInterval(std::chrono::milliseconds interval);
    void setMemoryThreshold(double threshold);
    void setStrategy(GCStrategy strategy);
    
    /**
     * @brief Get GC statistics
     */
    struct GCStatistics {
        size_t total_collections;
        size_t bytes_reclaimed;
        std::chrono::milliseconds total_time;
        std::chrono::milliseconds average_time;
        double efficiency;
    };
    
    GCStatistics getStatistics() const;

private:
    GCStrategy m_strategy;
    std::vector<std::weak_ptr<AdvancedMemoryPool>> m_pools;
    std::thread m_gc_thread;
    std::atomic<bool> m_running{false};
    std::chrono::milliseconds m_interval{5000};
    double m_memory_threshold{0.8};
    mutable std::mutex m_mutex;
    GCStatistics m_stats;
    
    void gcThreadLoop();
    size_t runMarkAndSweep();
    size_t runGenerational();
    size_t runIncremental();
    size_t runConcurrent();
    size_t runAdaptive();
    bool shouldRunGC() const;
};

/**
 * @brief VRAM (GPU Memory) Manager
 */
class VRAMManager {
public:
    static VRAMManager& getInstance();
    
    /**
     * @brief Initialize VRAM manager
     */
    bool initialize(size_t total_vram_mb = 0); // 0 = auto-detect
    
    /**
     * @brief Shutdown VRAM manager
     */
    void shutdown();
    
    /**
     * @brief Allocate VRAM
     */
    void* allocateVRAM(size_t size, const std::string& tag = "");
    
    /**
     * @brief Deallocate VRAM
     */
    void deallocateVRAM(void* ptr);
    
    /**
     * @brief Stream data to VRAM
     */
    bool streamToVRAM(void* dst, const void* src, size_t size);
    
    /**
     * @brief Stream data from VRAM
     */
    bool streamFromVRAM(void* dst, const void* src, size_t size);
    
    /**
     * @brief Get VRAM usage statistics
     */
    struct VRAMStatistics {
        size_t total_vram;
        size_t used_vram;
        size_t free_vram;
        double usage_percentage;
        size_t largest_free_block;
        size_t fragmentation_bytes;
    };
    
    VRAMStatistics getVRAMStatistics() const;
    
    /**
     * @brief Defragment VRAM
     */
    size_t defragmentVRAM();
    
    /**
     * @brief Set memory pressure callback
     */
    void setMemoryPressureCallback(std::function<void(double)> callback);

private:
    VRAMManager() = default;
    ~VRAMManager() = default;
    
    bool m_initialized{false};
    size_t m_total_vram{0};
    std::vector<MemoryBlock> m_vram_blocks;
    std::unordered_map<void*, size_t> m_allocations;
    mutable std::mutex m_mutex;
    std::function<void(double)> m_pressure_callback;
    
    void detectVRAMSize();
    void checkMemoryPressure();
};

/**
 * @brief Advanced Memory Manager (Main Interface)
 */
class AdvancedMemoryManager {
public:
    static AdvancedMemoryManager& getInstance();
    
    /**
     * @brief Initialize memory manager
     */
    bool initialize();
    
    /**
     * @brief Shutdown memory manager
     */
    void shutdown();
    
    /**
     * @brief Allocate memory with automatic pool selection
     */
    void* allocate(size_t size, size_t alignment = 16, const std::string& tag = "");
    
    /**
     * @brief Deallocate memory
     */
    void deallocate(void* ptr);
    
    /**
     * @brief Allocate from specific pool
     */
    void* allocateFromPool(PoolType pool_type, size_t size, size_t alignment = 16);
    
    /**
     * @brief Typed allocation
     */
    template<typename T>
    T* allocateTyped(size_t count = 1, const std::string& tag = "") {
        return static_cast<T*>(allocate(count * sizeof(T), alignof(T), tag));
    }
    
    /**
     * @brief Typed deallocation
     */
    template<typename T>
    void deallocateTyped(T* ptr) {
        deallocate(ptr);
    }
    
    /**
     * @brief Get memory statistics
     */
    MemoryStatistics getStatistics() const;
    
    /**
     * @brief Get pool statistics
     */
    std::unordered_map<PoolType, MemoryStatistics> getPoolStatistics() const;
    
    /**
     * @brief Run garbage collection
     */
    size_t runGarbageCollection();
    
    /**
     * @brief Configure pool
     */
    void configurePool(PoolType type, const PoolConfiguration& config);
    
    /**
     * @brief Set allocation strategy
     */
    void setAllocationStrategy(AllocationStrategy strategy);
    
    /**
     * @brief Enable/disable automatic GC
     */
    void setAutoGC(bool enabled);
    
    /**
     * @brief Generate memory report
     */
    std::string generateMemoryReport() const;

private:
    AdvancedMemoryManager() = default;
    ~AdvancedMemoryManager() = default;
    
    bool m_initialized{false};
    AllocationStrategy m_strategy{AllocationStrategy::HYBRID};
    std::unordered_map<PoolType, std::shared_ptr<AdvancedMemoryPool>> m_pools;
    std::unique_ptr<GarbageCollector> m_gc;
    std::unordered_map<void*, PoolType> m_allocation_map;
    mutable std::mutex m_mutex;
    MemoryStatistics m_global_stats;
    
    PoolType selectOptimalPool(size_t size) const;
    void initializePools();
    void updateGlobalStatistics();
};

/**
 * @brief RAII Memory Guard
 */
template<typename T>
class MemoryGuard {
public:
    explicit MemoryGuard(size_t count = 1, const std::string& tag = "")
        : m_ptr(AdvancedMemoryManager::getInstance().allocateTyped<T>(count, tag)), 
          m_count(count) {}
    
    ~MemoryGuard() {
        if (m_ptr) {
            AdvancedMemoryManager::getInstance().deallocateTyped(m_ptr);
        }
    }
    
    // Non-copyable, movable
    MemoryGuard(const MemoryGuard&) = delete;
    MemoryGuard& operator=(const MemoryGuard&) = delete;
    
    MemoryGuard(MemoryGuard&& other) noexcept 
        : m_ptr(other.m_ptr), m_count(other.m_count) {
        other.m_ptr = nullptr;
        other.m_count = 0;
    }
    
    MemoryGuard& operator=(MemoryGuard&& other) noexcept {
        if (this != &other) {
            if (m_ptr) {
                AdvancedMemoryManager::getInstance().deallocateTyped(m_ptr);
            }
            m_ptr = other.m_ptr;
            m_count = other.m_count;
            other.m_ptr = nullptr;
            other.m_count = 0;
        }
        return *this;
    }
    
    T* get() const { return m_ptr; }
    T& operator*() const { return *m_ptr; }
    T* operator->() const { return m_ptr; }
    T& operator[](size_t index) const { return m_ptr[index]; }
    
    T* release() {
        T* ptr = m_ptr;
        m_ptr = nullptr;
        m_count = 0;
        return ptr;
    }
    
    size_t size() const { return m_count; }

private:
    T* m_ptr;
    size_t m_count;
};

// Convenience macros
#define PHOTON_ALLOC(size) photon::AdvancedMemoryManager::getInstance().allocate(size)
#define PHOTON_ALLOC_TYPED(type, count) photon::AdvancedMemoryManager::getInstance().allocateTyped<type>(count)
#define PHOTON_FREE(ptr) photon::AdvancedMemoryManager::getInstance().deallocate(ptr)
#define PHOTON_MEMORY_GUARD(type, count) photon::MemoryGuard<type>(count)

} // namespace photon
