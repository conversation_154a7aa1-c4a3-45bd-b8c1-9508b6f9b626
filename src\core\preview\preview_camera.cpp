// src/core/preview/preview_camera.cpp
// PhotonRender - Preview Camera System Implementation
// Implementazione sistema camera per preview materiali

#include "preview_camera.hpp"
#include "../camera/perspective_camera.hpp"
#include "../math/ray.hpp"
#include <cmath>
#include <algorithm>
#include <iostream>

namespace photon {

PreviewCamera::PreviewCamera() {
    // Initialize with default values
    m_camera = std::make_shared<PerspectiveCamera>();
}

PreviewCamera::~PreviewCamera() {
    // Cleanup handled by smart pointers
}

bool PreviewCamera::initialize(const PreviewSettings& settings) {
    try {
        // Update settings
        updateSettings(settings);
        
        // Initialize camera position
        updateCameraPosition();
        
        std::cout << "PreviewCamera initialized successfully" << std::endl;
        std::cout << "FOV: " << m_fov << " degrees" << std::endl;
        std::cout << "Aspect ratio: " << m_aspectRatio << std::endl;
        std::cout << "Distance: " << m_distance << std::endl;
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "PreviewCamera initialization failed: " << e.what() << std::endl;
        return false;
    }
}

void PreviewCamera::updateSettings(const PreviewSettings& settings) {
    m_distance = settings.cameraDistance;
    m_angleX = settings.cameraAngleX;
    m_angleY = settings.cameraAngleY;
    m_fov = settings.fov;
    
    // Calculate aspect ratio from resolution
    m_aspectRatio = static_cast<float>(settings.width) / static_cast<float>(settings.height);
    
    // Apply constraints
    applyConstraints();
    
    // Update camera
    if (m_camera) {
        m_camera->setFieldOfView(m_fov);
        m_camera->setAspectRatio(m_aspectRatio);
        m_camera->setNearFar(m_nearPlane, m_farPlane);
        updateCameraPosition();
    }
}

void PreviewCamera::updatePosition(float distance, float angleX, float angleY) {
    m_distance = distance;
    m_angleX = angleX;
    m_angleY = angleY;
    
    applyConstraints();
    updateCameraPosition();
}

void PreviewCamera::setTarget(const Vec3& target) {
    m_target = target;
    updateCameraPosition();
}

void PreviewCamera::setFieldOfView(float fov) {
    m_fov = std::clamp(fov, 10.0f, 120.0f);
    
    if (m_camera) {
        m_camera->setFieldOfView(m_fov);
    }
}

void PreviewCamera::setAspectRatio(float aspect) {
    m_aspectRatio = std::max(0.1f, aspect);
    
    if (m_camera) {
        m_camera->setAspectRatio(m_aspectRatio);
    }
}

void PreviewCamera::setClippingPlanes(float near, float far) {
    m_nearPlane = std::max(0.001f, near);
    m_farPlane = std::max(m_nearPlane + 0.001f, far);
    
    if (m_camera) {
        m_camera->setNearFar(m_nearPlane, m_farPlane);
    }
}

void PreviewCamera::reset() {
    m_distance = 3.0f;
    m_angleX = 0.0f;
    m_angleY = 0.0f;
    m_target = Vec3(0, 0, 0);
    
    updateCameraPosition();
}

void PreviewCamera::animateTo(float distance, float angleX, float angleY, float duration) {
    if (m_animating) {
        stopAnimation();
    }
    
    // Store current values as start
    m_startDistance = m_distance;
    m_startAngleX = m_angleX;
    m_startAngleY = m_angleY;
    
    // Store target values
    m_targetDistance = distance;
    m_targetAngleX = angleX;
    m_targetAngleY = angleY;
    
    // Setup animation
    m_animationDuration = std::max(0.1f, duration);
    m_animationTime = 0.0f;
    m_animating = true;
    
    std::cout << "Starting camera animation to distance=" << distance 
              << ", angleX=" << angleX << ", angleY=" << angleY 
              << " over " << duration << "s" << std::endl;
}

bool PreviewCamera::updateAnimation(float deltaTime) {
    if (!m_animating) {
        return false;
    }
    
    m_animationTime += deltaTime;
    
    if (m_animationTime >= m_animationDuration) {
        // Animation complete
        m_distance = m_targetDistance;
        m_angleX = m_targetAngleX;
        m_angleY = m_targetAngleY;
        m_animating = false;
        
        applyConstraints();
        updateCameraPosition();
        
        std::cout << "Camera animation completed" << std::endl;
        return false;
    }
    
    // Calculate interpolation factor with smooth step
    float t = m_animationTime / m_animationDuration;
    t = smoothStep(t);
    
    // Interpolate values
    m_distance = lerp(m_startDistance, m_targetDistance, t);
    m_angleX = lerp(m_startAngleX, m_targetAngleX, t);
    m_angleY = lerp(m_startAngleY, m_targetAngleY, t);
    
    applyConstraints();
    updateCameraPosition();
    
    return true;
}

void PreviewCamera::stopAnimation() {
    m_animating = false;
    m_animationTime = 0.0f;
}

void PreviewCamera::setConstraints(float minDistance, float maxDistance, 
                                  float minAngleX, float maxAngleX) {
    m_minDistance = std::max(0.1f, minDistance);
    m_maxDistance = std::max(m_minDistance + 0.1f, maxDistance);
    m_minAngleX = std::clamp(minAngleX, -90.0f, 90.0f);
    m_maxAngleX = std::clamp(maxAngleX, m_minAngleX, 90.0f);
}

Matrix4 PreviewCamera::getViewMatrix() const {
    if (m_camera) {
        return m_camera->getViewMatrix();
    }
    return Matrix4::lookAt(m_position, m_target, m_up);
}

Matrix4 PreviewCamera::getProjectionMatrix() const {
    if (m_camera) {
        return m_camera->getProjectionMatrix();
    }
    return Matrix4::perspective(m_fov, m_aspectRatio, m_nearPlane, m_farPlane);
}

Ray PreviewCamera::screenToWorldRay(float screenX, float screenY) const {
    if (m_camera) {
        return m_camera->generateRay(screenX, screenY);
    }
    
    // Fallback implementation
    Vec3 direction = Vec3(
        (screenX - 0.5f) * 2.0f * std::tan(m_fov * M_PI / 360.0f) * m_aspectRatio,
        (0.5f - screenY) * 2.0f * std::tan(m_fov * M_PI / 360.0f),
        -1.0f
    ).normalized();
    
    return Ray(m_position, direction);
}

void PreviewCamera::updateCameraPosition() {
    // Convert spherical coordinates to Cartesian
    float radX = m_angleX * M_PI / 180.0f;
    float radY = m_angleY * M_PI / 180.0f;
    
    float cosX = std::cos(radX);
    float sinX = std::sin(radX);
    float cosY = std::cos(radY);
    float sinY = std::sin(radY);
    
    // Calculate position relative to target
    Vec3 offset(
        m_distance * cosX * sinY,
        m_distance * sinX,
        m_distance * cosX * cosY
    );
    
    m_position = m_target + offset;
    
    // Update camera
    if (m_camera) {
        m_camera->setPosition(m_position);
        m_camera->setTarget(m_target);
        m_camera->setUp(m_up);
    }
}

void PreviewCamera::applyConstraints() {
    if (!m_constraintsEnabled) {
        return;
    }
    
    // Constrain distance
    m_distance = std::clamp(m_distance, m_minDistance, m_maxDistance);
    
    // Constrain X angle (elevation)
    m_angleX = std::clamp(m_angleX, m_minAngleX, m_maxAngleX);
    
    // Normalize Y angle (azimuth) to [0, 360)
    while (m_angleY < 0.0f) {
        m_angleY += 360.0f;
    }
    while (m_angleY >= 360.0f) {
        m_angleY -= 360.0f;
    }
}

float PreviewCamera::lerp(float start, float end, float t) const {
    return start + t * (end - start);
}

float PreviewCamera::smoothStep(float t) const {
    // Smooth step function: 3t² - 2t³
    return t * t * (3.0f - 2.0f * t);
}

} // namespace photon
