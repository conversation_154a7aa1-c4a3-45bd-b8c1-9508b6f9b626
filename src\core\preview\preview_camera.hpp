// src/core/preview/preview_camera.hpp
// PhotonRender - Preview Camera System
// Sistema camera per preview materiali

#ifndef PHOTON_PREVIEW_CAMERA_HPP
#define PHOTON_PREVIEW_CAMERA_HPP

#include "../math/vec3.hpp"
#include "../math/matrix4.hpp"
#include "../camera/camera.hpp"
#include "preview_renderer.hpp"
#include <memory>

namespace photon {

// Forward declarations
class PerspectiveCamera;

/**
 * @brief Preview Camera Controller
 * 
 * Specialized camera controller for material preview with orbital
 * controls and smooth animations
 */
class PreviewCamera {
public:
    /**
     * @brief Constructor
     */
    PreviewCamera();
    
    /**
     * @brief Destructor
     */
    ~PreviewCamera();
    
    /**
     * @brief Initialize preview camera
     * @param settings Preview settings
     * @return True if initialization successful
     */
    bool initialize(const PreviewSettings& settings);
    
    /**
     * @brief Update camera settings
     * @param settings New settings
     */
    void updateSettings(const PreviewSettings& settings);
    
    /**
     * @brief Update camera position
     * @param distance Distance from target
     * @param angleX Rotation around X axis (degrees)
     * @param angleY Rotation around Y axis (degrees)
     */
    void updatePosition(float distance, float angleX, float angleY);
    
    /**
     * @brief Set camera target point
     * @param target Target position
     */
    void setTarget(const Vec3& target);
    
    /**
     * @brief Set field of view
     * @param fov Field of view in degrees
     */
    void setFieldOfView(float fov);
    
    /**
     * @brief Set aspect ratio
     * @param aspect Aspect ratio (width/height)
     */
    void setAspectRatio(float aspect);
    
    /**
     * @brief Set near and far clipping planes
     * @param near Near clipping distance
     * @param far Far clipping distance
     */
    void setClippingPlanes(float near, float far);
    
    /**
     * @brief Get camera instance
     * @return Camera for rendering
     */
    std::shared_ptr<Camera> getCamera() const { return m_camera; }
    
    /**
     * @brief Get current camera position
     * @return Camera position
     */
    Vec3 getPosition() const { return m_position; }
    
    /**
     * @brief Get current camera target
     * @return Camera target
     */
    Vec3 getTarget() const { return m_target; }
    
    /**
     * @brief Get current distance from target
     * @return Distance
     */
    float getDistance() const { return m_distance; }
    
    /**
     * @brief Get current X angle
     * @return X rotation angle in degrees
     */
    float getAngleX() const { return m_angleX; }
    
    /**
     * @brief Get current Y angle
     * @return Y rotation angle in degrees
     */
    float getAngleY() const { return m_angleY; }
    
    /**
     * @brief Reset camera to default position
     */
    void reset();
    
    /**
     * @brief Animate camera to new position
     * @param distance Target distance
     * @param angleX Target X angle
     * @param angleY Target Y angle
     * @param duration Animation duration in seconds
     */
    void animateTo(float distance, float angleX, float angleY, float duration = 1.0f);
    
    /**
     * @brief Update animation (call per frame)
     * @param deltaTime Time since last update
     * @return True if animation is active
     */
    bool updateAnimation(float deltaTime);
    
    /**
     * @brief Check if animation is active
     * @return True if animating
     */
    bool isAnimating() const { return m_animating; }
    
    /**
     * @brief Stop current animation
     */
    void stopAnimation();
    
    /**
     * @brief Set camera movement constraints
     * @param minDistance Minimum distance from target
     * @param maxDistance Maximum distance from target
     * @param minAngleX Minimum X angle (degrees)
     * @param maxAngleX Maximum X angle (degrees)
     */
    void setConstraints(float minDistance, float maxDistance, 
                       float minAngleX, float maxAngleX);
    
    /**
     * @brief Enable/disable constraints
     * @param enabled Constraints enabled
     */
    void setConstraintsEnabled(bool enabled) { m_constraintsEnabled = enabled; }
    
    /**
     * @brief Get view matrix
     * @return View transformation matrix
     */
    Matrix4 getViewMatrix() const;
    
    /**
     * @brief Get projection matrix
     * @return Projection transformation matrix
     */
    Matrix4 getProjectionMatrix() const;
    
    /**
     * @brief Convert screen coordinates to world ray
     * @param screenX Screen X coordinate [0,1]
     * @param screenY Screen Y coordinate [0,1]
     * @return World space ray
     */
    Ray screenToWorldRay(float screenX, float screenY) const;

private:
    std::shared_ptr<PerspectiveCamera> m_camera;
    
    // Camera parameters
    Vec3 m_position = Vec3(0, 0, 3);
    Vec3 m_target = Vec3(0, 0, 0);
    Vec3 m_up = Vec3(0, 1, 0);
    
    // Orbital parameters
    float m_distance = 3.0f;
    float m_angleX = 0.0f;  // Elevation angle
    float m_angleY = 0.0f;  // Azimuth angle
    
    // Camera settings
    float m_fov = 45.0f;
    float m_aspectRatio = 1.0f;
    float m_nearPlane = 0.1f;
    float m_farPlane = 100.0f;
    
    // Animation state
    bool m_animating = false;
    float m_animationTime = 0.0f;
    float m_animationDuration = 1.0f;
    
    // Animation targets
    float m_startDistance, m_targetDistance;
    float m_startAngleX, m_targetAngleX;
    float m_startAngleY, m_targetAngleY;
    
    // Constraints
    bool m_constraintsEnabled = true;
    float m_minDistance = 1.0f;
    float m_maxDistance = 10.0f;
    float m_minAngleX = -80.0f;
    float m_maxAngleX = 80.0f;
    
    /**
     * @brief Update camera position from orbital parameters
     */
    void updateCameraPosition();
    
    /**
     * @brief Apply constraints to parameters
     */
    void applyConstraints();
    
    /**
     * @brief Interpolate between two values
     * @param start Start value
     * @param end End value
     * @param t Interpolation factor [0,1]
     * @return Interpolated value
     */
    float lerp(float start, float end, float t) const;
    
    /**
     * @brief Smooth step interpolation
     * @param t Input value [0,1]
     * @return Smoothed value [0,1]
     */
    float smoothStep(float t) const;
};

} // namespace photon

#endif // PHOTON_PREVIEW_CAMERA_HPP
