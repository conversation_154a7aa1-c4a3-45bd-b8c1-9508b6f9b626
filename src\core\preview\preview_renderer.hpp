// src/core/preview/preview_renderer.hpp
// PhotonRender - Real-time Material Preview System
// Sistema di rendering real-time per preview materiali

#ifndef PHOTON_PREVIEW_RENDERER_HPP
#define PHOTON_PREVIEW_RENDERER_HPP

#include "../math/vec3.hpp"
#include "../math/vec2.hpp"
#include "../math/ray.hpp"
#include "../math/matrix4.hpp"
#include "../material/material.hpp"
#include "../scene/scene.hpp"
#include "../camera/camera.hpp"
#include "../integrator/integrator.hpp"
#include "../sampler/sampler.hpp"
#include <memory>
#include <vector>
#include <string>

namespace photon {

// Forward declarations
class PreviewScene;
class PreviewCamera;
class PreviewLighting;
class Image;

/**
 * @brief Preview geometry types
 */
enum class PreviewGeometry {
    SPHERE,     // Sphere for material preview
    CUBE,       // Cube for material preview
    PLANE,      // Plane for texture preview
    CYLINDER,   // Cylinder for material preview
    TORUS       // Torus for complex material preview
};

/**
 * @brief Preview lighting presets
 */
enum class PreviewLighting {
    STUDIO,     // Studio lighting setup
    OUTDOOR,    // Outdoor HDRI lighting
    INDOOR,     // Indoor lighting setup
    DRAMATIC,   // Dramatic lighting
    SOFT        // Soft diffuse lighting
};

/**
 * @brief Preview render settings
 */
struct PreviewSettings {
    int width = 256;                    // Preview image width
    int height = 256;                   // Preview image height
    int samples = 16;                   // Samples per pixel
    int maxDepth = 8;                   // Maximum ray depth
    PreviewGeometry geometry = PreviewGeometry::SPHERE;
    PreviewLighting lighting = PreviewLighting::STUDIO;
    bool enableDenoising = true;        // AI denoising enabled
    bool enableToneMapping = true;      // Tone mapping enabled
    float exposure = 1.0f;              // Exposure adjustment
    float gamma = 2.2f;                 // Gamma correction
    
    // Camera settings
    float cameraDistance = 3.0f;       // Camera distance from object
    float cameraAngleX = 0.0f;          // Camera rotation X (degrees)
    float cameraAngleY = 0.0f;          // Camera rotation Y (degrees)
    float fov = 45.0f;                  // Field of view (degrees)
    
    // Animation settings
    bool enableRotation = false;        // Auto-rotate object
    float rotationSpeed = 30.0f;        // Rotation speed (degrees/sec)
};

/**
 * @brief Real-time Material Preview Renderer
 * 
 * Specialized renderer for real-time material preview with optimized
 * performance for interactive material editing
 */
class PreviewRenderer {
public:
    /**
     * @brief Constructor
     */
    PreviewRenderer();
    
    /**
     * @brief Destructor
     */
    ~PreviewRenderer();
    
    /**
     * @brief Initialize preview renderer
     * @param settings Preview render settings
     * @return True if initialization successful
     */
    bool initialize(const PreviewSettings& settings);
    
    /**
     * @brief Shutdown preview renderer
     */
    void shutdown();
    
    /**
     * @brief Set material to preview
     * @param material Material to preview
     */
    void setMaterial(std::shared_ptr<Material> material);
    
    /**
     * @brief Set preview geometry
     * @param geometry Geometry type
     */
    void setGeometry(PreviewGeometry geometry);
    
    /**
     * @brief Set preview lighting
     * @param lighting Lighting preset
     */
    void setLighting(PreviewLighting lighting);
    
    /**
     * @brief Update preview settings
     * @param settings New settings
     */
    void updateSettings(const PreviewSettings& settings);
    
    /**
     * @brief Render preview image
     * @return Rendered preview image
     */
    std::shared_ptr<Image> render();
    
    /**
     * @brief Render preview image asynchronously
     * @param callback Callback when render complete
     */
    void renderAsync(std::function<void(std::shared_ptr<Image>)> callback);
    
    /**
     * @brief Check if async render is in progress
     * @return True if rendering
     */
    bool isRendering() const;
    
    /**
     * @brief Cancel current async render
     */
    void cancelRender();
    
    /**
     * @brief Get current render progress (0.0 to 1.0)
     * @return Render progress
     */
    float getRenderProgress() const;
    
    /**
     * @brief Update camera position
     * @param distance Camera distance
     * @param angleX Camera rotation X (degrees)
     * @param angleY Camera rotation Y (degrees)
     */
    void updateCamera(float distance, float angleX, float angleY);
    
    /**
     * @brief Update lighting intensity
     * @param intensity Lighting intensity multiplier
     */
    void updateLightingIntensity(float intensity);
    
    /**
     * @brief Update exposure
     * @param exposure Exposure value
     */
    void updateExposure(float exposure);
    
    /**
     * @brief Enable/disable auto-rotation
     * @param enabled Auto-rotation enabled
     * @param speed Rotation speed (degrees/sec)
     */
    void setAutoRotation(bool enabled, float speed = 30.0f);
    
    /**
     * @brief Update auto-rotation (call per frame)
     * @param deltaTime Time since last update (seconds)
     */
    void updateAutoRotation(float deltaTime);
    
    /**
     * @brief Get current preview settings
     * @return Current settings
     */
    const PreviewSettings& getSettings() const { return m_settings; }
    
    /**
     * @brief Get render statistics
     * @return Render stats as string
     */
    std::string getRenderStats() const;
    
    /**
     * @brief Reset camera to default position
     */
    void resetCamera();
    
    /**
     * @brief Save preview image to file
     * @param filename Output filename
     * @return True if save successful
     */
    bool savePreview(const std::string& filename);

private:
    PreviewSettings m_settings;
    std::shared_ptr<PreviewScene> m_scene;
    std::shared_ptr<PreviewCamera> m_camera;
    std::shared_ptr<Integrator> m_integrator;
    std::shared_ptr<Sampler> m_sampler;
    std::shared_ptr<Material> m_material;
    
    // Render state
    bool m_initialized = false;
    bool m_rendering = false;
    float m_renderProgress = 0.0f;
    std::thread m_renderThread;
    std::mutex m_renderMutex;
    std::atomic<bool> m_cancelRender{false};
    
    // Animation state
    float m_currentRotation = 0.0f;
    
    // Performance tracking
    mutable std::chrono::high_resolution_clock::time_point m_lastRenderTime;
    mutable float m_lastRenderDuration = 0.0f;
    mutable int m_totalRays = 0;
    
    /**
     * @brief Create preview scene with geometry
     */
    void createScene();
    
    /**
     * @brief Setup preview lighting
     */
    void setupLighting();
    
    /**
     * @brief Setup preview camera
     */
    void setupCamera();
    
    /**
     * @brief Internal render function
     * @return Rendered image
     */
    std::shared_ptr<Image> renderInternal();
    
    /**
     * @brief Apply post-processing to rendered image
     * @param image Input image
     * @return Post-processed image
     */
    std::shared_ptr<Image> applyPostProcessing(std::shared_ptr<Image> image);
    
    /**
     * @brief Update render progress
     * @param progress Progress value (0.0 to 1.0)
     */
    void updateRenderProgress(float progress);
};

} // namespace photon

#endif // PHOTON_PREVIEW_RENDERER_HPP
