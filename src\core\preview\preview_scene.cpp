// src/core/preview/preview_scene.cpp
// PhotonRender - Preview Scene Management Implementation
// Implementazione sistema di gestione scene per preview materiali

#include "preview_scene.hpp"
#include "../mesh/mesh.hpp"
#include "../light/point_light.hpp"
#include "../light/directional_light.hpp"
#include "../light/environment_light.hpp"
#include "../material/material.hpp"
#include <cmath>
#include <iostream>

namespace photon {

PreviewScene::PreviewScene() {
    // Initialize default ground material
    m_groundMaterial = std::make_shared<DiffuseMaterial>(Color3(0.8f, 0.8f, 0.8f));
}

PreviewScene::~PreviewScene() {
    clearLights();
}

bool PreviewScene::initialize(PreviewGeometry geometry) {
    try {
        // Create initial geometry
        createGeometry(geometry);
        
        // Create ground plane
        if (m_groundPlaneEnabled) {
            m_groundPlane = createGroundPlane();
            if (m_groundPlane) {
                m_groundPlane->setMaterial(m_groundMaterial);
                addMesh(m_groundPlane);
            }
        }
        
        // Setup default lighting
        setupLighting(PreviewLighting::STUDIO);
        
        std::cout << "PreviewScene initialized successfully" << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "PreviewScene initialization failed: " << e.what() << std::endl;
        return false;
    }
}

void PreviewScene::createGeometry(PreviewGeometry geometry) {
    // Remove existing preview object
    if (m_previewObject) {
        removeMesh(m_previewObject);
    }
    
    // Create new geometry
    switch (geometry) {
        case PreviewGeometry::SPHERE:
            m_previewObject = createSphere();
            break;
        case PreviewGeometry::CUBE:
            m_previewObject = createCube();
            break;
        case PreviewGeometry::PLANE:
            m_previewObject = createPlane();
            break;
        case PreviewGeometry::CYLINDER:
            m_previewObject = createCylinder();
            break;
        case PreviewGeometry::TORUS:
            m_previewObject = createTorus();
            break;
        default:
            m_previewObject = createSphere();
            break;
    }
    
    m_currentGeometry = geometry;
    
    // Apply current material
    if (m_previewObject && m_previewMaterial) {
        m_previewObject->setMaterial(m_previewMaterial);
    }
    
    // Add to scene
    if (m_previewObject) {
        addMesh(m_previewObject);
        updateObjectTransform();
    }
    
    std::cout << "Created preview geometry: " << static_cast<int>(geometry) << std::endl;
}

void PreviewScene::setMaterial(std::shared_ptr<Material> material) {
    m_previewMaterial = material;
    
    if (m_previewObject && material) {
        m_previewObject->setMaterial(material);
    }
}

void PreviewScene::setupLighting(PreviewLighting lighting) {
    clearLights();
    m_currentLighting = lighting;
    
    switch (lighting) {
        case PreviewLighting::STUDIO:
            setupStudioLighting();
            break;
        case PreviewLighting::OUTDOOR:
            setupOutdoorLighting();
            break;
        case PreviewLighting::INDOOR:
            setupIndoorLighting();
            break;
        case PreviewLighting::DRAMATIC:
            setupDramaticLighting();
            break;
        case PreviewLighting::SOFT:
            setupSoftLighting();
            break;
        default:
            setupStudioLighting();
            break;
    }
    
    std::cout << "Setup lighting: " << static_cast<int>(lighting) << std::endl;
}

void PreviewScene::setLightingIntensity(float intensity) {
    m_lightingIntensity = std::max(0.0f, intensity);
    
    // Update all lights
    for (auto& light : m_lights) {
        if (auto pointLight = std::dynamic_pointer_cast<PointLight>(light)) {
            pointLight->setIntensity(pointLight->getBaseIntensity() * m_lightingIntensity);
        } else if (auto dirLight = std::dynamic_pointer_cast<DirectionalLight>(light)) {
            dirLight->setIntensity(dirLight->getBaseIntensity() * m_lightingIntensity);
        }
    }
    
    if (m_environmentLight) {
        m_environmentLight->setIntensity(m_environmentLight->getBaseIntensity() * m_lightingIntensity);
    }
}

void PreviewScene::setObjectRotation(float angleY) {
    m_currentRotation = angleY;
    updateObjectTransform();
}

BoundingBox PreviewScene::getObjectBounds() const {
    if (m_previewObject) {
        return m_previewObject->getBounds();
    }
    return BoundingBox();
}

void PreviewScene::setGroundPlaneEnabled(bool enabled) {
    if (m_groundPlaneEnabled == enabled) return;
    
    m_groundPlaneEnabled = enabled;
    
    if (enabled) {
        if (!m_groundPlane) {
            m_groundPlane = createGroundPlane();
            if (m_groundPlane) {
                m_groundPlane->setMaterial(m_groundMaterial);
            }
        }
        if (m_groundPlane) {
            addMesh(m_groundPlane);
        }
    } else {
        if (m_groundPlane) {
            removeMesh(m_groundPlane);
        }
    }
}

void PreviewScene::setGroundPlaneMaterial(std::shared_ptr<Material> material) {
    m_groundMaterial = material;
    
    if (m_groundPlane && material) {
        m_groundPlane->setMaterial(material);
    }
}

void PreviewScene::setEnvironmentEnabled(bool enabled) {
    m_environmentEnabled = enabled;
    
    if (enabled && !m_environmentLight) {
        // Create default environment light
        m_environmentLight = std::make_shared<EnvironmentLight>();
        addLight(m_environmentLight);
    } else if (!enabled && m_environmentLight) {
        removeLight(m_environmentLight);
        m_environmentLight.reset();
    }
}

void PreviewScene::setEnvironmentMap(std::shared_ptr<Texture> envMap) {
    if (m_environmentLight) {
        m_environmentLight->setEnvironmentMap(envMap);
    }
}

std::shared_ptr<Mesh> PreviewScene::createSphere(float radius, int segments) {
    auto mesh = std::make_shared<Mesh>();
    
    // Generate sphere vertices
    for (int lat = 0; lat <= segments; ++lat) {
        float theta = lat * M_PI / segments;
        float sinTheta = std::sin(theta);
        float cosTheta = std::cos(theta);
        
        for (int lon = 0; lon <= segments; ++lon) {
            float phi = lon * 2 * M_PI / segments;
            float sinPhi = std::sin(phi);
            float cosPhi = std::cos(phi);
            
            Vec3 position(radius * sinTheta * cosPhi,
                         radius * cosTheta,
                         radius * sinTheta * sinPhi);
            
            Vec3 normal = position.normalized();
            Vec2 uv(static_cast<float>(lon) / segments,
                    static_cast<float>(lat) / segments);
            
            mesh->addVertex(position, normal, uv);
        }
    }
    
    // Generate sphere indices
    for (int lat = 0; lat < segments; ++lat) {
        for (int lon = 0; lon < segments; ++lon) {
            int current = lat * (segments + 1) + lon;
            int next = current + segments + 1;
            
            // First triangle
            mesh->addTriangle(current, next, current + 1);
            // Second triangle
            mesh->addTriangle(current + 1, next, next + 1);
        }
    }
    
    mesh->buildBVH();
    return mesh;
}

std::shared_ptr<Mesh> PreviewScene::createCube(float size) {
    auto mesh = std::make_shared<Mesh>();
    float half = size * 0.5f;
    
    // Cube vertices (6 faces, 4 vertices each)
    Vec3 vertices[24] = {
        // Front face
        Vec3(-half, -half,  half), Vec3( half, -half,  half),
        Vec3( half,  half,  half), Vec3(-half,  half,  half),
        // Back face
        Vec3(-half, -half, -half), Vec3(-half,  half, -half),
        Vec3( half,  half, -half), Vec3( half, -half, -half),
        // Top face
        Vec3(-half,  half, -half), Vec3(-half,  half,  half),
        Vec3( half,  half,  half), Vec3( half,  half, -half),
        // Bottom face
        Vec3(-half, -half, -half), Vec3( half, -half, -half),
        Vec3( half, -half,  half), Vec3(-half, -half,  half),
        // Right face
        Vec3( half, -half, -half), Vec3( half,  half, -half),
        Vec3( half,  half,  half), Vec3( half, -half,  half),
        // Left face
        Vec3(-half, -half, -half), Vec3(-half, -half,  half),
        Vec3(-half,  half,  half), Vec3(-half,  half, -half)
    };
    
    Vec3 normals[6] = {
        Vec3( 0,  0,  1), Vec3( 0,  0, -1), Vec3( 0,  1,  0),
        Vec3( 0, -1,  0), Vec3( 1,  0,  0), Vec3(-1,  0,  0)
    };
    
    Vec2 uvs[4] = {
        Vec2(0, 0), Vec2(1, 0), Vec2(1, 1), Vec2(0, 1)
    };
    
    // Add vertices
    for (int face = 0; face < 6; ++face) {
        for (int vert = 0; vert < 4; ++vert) {
            mesh->addVertex(vertices[face * 4 + vert], normals[face], uvs[vert]);
        }
    }
    
    // Add triangles (2 per face)
    for (int face = 0; face < 6; ++face) {
        int base = face * 4;
        mesh->addTriangle(base, base + 1, base + 2);
        mesh->addTriangle(base, base + 2, base + 3);
    }
    
    mesh->buildBVH();
    return mesh;
}

std::shared_ptr<Mesh> PreviewScene::createPlane(float width, float height) {
    auto mesh = std::make_shared<Mesh>();
    float halfW = width * 0.5f;
    float halfH = height * 0.5f;

    // Plane vertices
    mesh->addVertex(Vec3(-halfW, 0, -halfH), Vec3(0, 1, 0), Vec2(0, 0));
    mesh->addVertex(Vec3( halfW, 0, -halfH), Vec3(0, 1, 0), Vec2(1, 0));
    mesh->addVertex(Vec3( halfW, 0,  halfH), Vec3(0, 1, 0), Vec2(1, 1));
    mesh->addVertex(Vec3(-halfW, 0,  halfH), Vec3(0, 1, 0), Vec2(0, 1));

    // Plane triangles
    mesh->addTriangle(0, 1, 2);
    mesh->addTriangle(0, 2, 3);

    mesh->buildBVH();
    return mesh;
}

std::shared_ptr<Mesh> PreviewScene::createCylinder(float radius, float height, int segments) {
    auto mesh = std::make_shared<Mesh>();
    float halfHeight = height * 0.5f;

    // Create cylinder vertices
    for (int i = 0; i <= segments; ++i) {
        float angle = 2.0f * M_PI * i / segments;
        float x = radius * std::cos(angle);
        float z = radius * std::sin(angle);
        float u = static_cast<float>(i) / segments;

        // Bottom vertex
        mesh->addVertex(Vec3(x, -halfHeight, z), Vec3(x/radius, 0, z/radius), Vec2(u, 0));
        // Top vertex
        mesh->addVertex(Vec3(x, halfHeight, z), Vec3(x/radius, 0, z/radius), Vec2(u, 1));
    }

    // Create side triangles
    for (int i = 0; i < segments; ++i) {
        int bottom1 = i * 2;
        int top1 = i * 2 + 1;
        int bottom2 = (i + 1) * 2;
        int top2 = (i + 1) * 2 + 1;

        mesh->addTriangle(bottom1, bottom2, top1);
        mesh->addTriangle(top1, bottom2, top2);
    }

    // Add center vertices for caps
    int centerBottom = mesh->getVertexCount();
    mesh->addVertex(Vec3(0, -halfHeight, 0), Vec3(0, -1, 0), Vec2(0.5f, 0.5f));
    int centerTop = mesh->getVertexCount();
    mesh->addVertex(Vec3(0, halfHeight, 0), Vec3(0, 1, 0), Vec2(0.5f, 0.5f));

    // Create cap triangles
    for (int i = 0; i < segments; ++i) {
        int bottom1 = i * 2;
        int top1 = i * 2 + 1;
        int bottom2 = ((i + 1) % segments) * 2;
        int top2 = ((i + 1) % segments) * 2 + 1;

        // Bottom cap
        mesh->addTriangle(centerBottom, bottom2, bottom1);
        // Top cap
        mesh->addTriangle(centerTop, top1, top2);
    }

    mesh->buildBVH();
    return mesh;
}

std::shared_ptr<Mesh> PreviewScene::createTorus(float majorRadius, float minorRadius,
                                               int majorSegments, int minorSegments) {
    auto mesh = std::make_shared<Mesh>();

    for (int i = 0; i <= majorSegments; ++i) {
        float u = static_cast<float>(i) / majorSegments;
        float majorAngle = u * 2.0f * M_PI;

        for (int j = 0; j <= minorSegments; ++j) {
            float v = static_cast<float>(j) / minorSegments;
            float minorAngle = v * 2.0f * M_PI;

            float x = (majorRadius + minorRadius * std::cos(minorAngle)) * std::cos(majorAngle);
            float y = minorRadius * std::sin(minorAngle);
            float z = (majorRadius + minorRadius * std::cos(minorAngle)) * std::sin(majorAngle);

            Vec3 position(x, y, z);

            // Calculate normal
            Vec3 center(majorRadius * std::cos(majorAngle), 0, majorRadius * std::sin(majorAngle));
            Vec3 normal = (position - center).normalized();

            mesh->addVertex(position, normal, Vec2(u, v));
        }
    }

    // Create triangles
    for (int i = 0; i < majorSegments; ++i) {
        for (int j = 0; j < minorSegments; ++j) {
            int current = i * (minorSegments + 1) + j;
            int next = current + minorSegments + 1;

            mesh->addTriangle(current, next, current + 1);
            mesh->addTriangle(current + 1, next, next + 1);
        }
    }

    mesh->buildBVH();
    return mesh;
}

std::shared_ptr<Mesh> PreviewScene::createGroundPlane(float size) {
    auto mesh = std::make_shared<Mesh>();
    float half = size * 0.5f;

    // Ground plane vertices
    mesh->addVertex(Vec3(-half, -1.5f, -half), Vec3(0, 1, 0), Vec2(0, 0));
    mesh->addVertex(Vec3( half, -1.5f, -half), Vec3(0, 1, 0), Vec2(size, 0));
    mesh->addVertex(Vec3( half, -1.5f,  half), Vec3(0, 1, 0), Vec2(size, size));
    mesh->addVertex(Vec3(-half, -1.5f,  half), Vec3(0, 1, 0), Vec2(0, size));

    // Ground plane triangles
    mesh->addTriangle(0, 1, 2);
    mesh->addTriangle(0, 2, 3);

    mesh->buildBVH();
    return mesh;
}

void PreviewScene::setupStudioLighting() {
    // Key light (main light)
    auto keyLight = std::make_shared<PointLight>(
        Vec3(2.0f, 3.0f, 2.0f),     // Position
        Color3(1.0f, 1.0f, 1.0f),   // Color
        10.0f                        // Intensity
    );
    addLight(keyLight);

    // Fill light (softer, opposite side)
    auto fillLight = std::make_shared<PointLight>(
        Vec3(-1.5f, 2.0f, 1.0f),    // Position
        Color3(0.8f, 0.9f, 1.0f),   // Slightly blue
        5.0f                         // Lower intensity
    );
    addLight(fillLight);

    // Rim light (back light for edge definition)
    auto rimLight = std::make_shared<PointLight>(
        Vec3(0.0f, 2.0f, -3.0f),    // Behind object
        Color3(1.0f, 0.9f, 0.8f),   // Slightly warm
        3.0f                         // Low intensity
    );
    addLight(rimLight);
}

void PreviewScene::setupOutdoorLighting() {
    // Sun light (directional)
    auto sunLight = std::make_shared<DirectionalLight>(
        Vec3(-0.3f, -0.7f, -0.2f).normalized(),  // Direction
        Color3(1.0f, 0.95f, 0.8f),               // Warm sunlight
        5.0f                                      // Intensity
    );
    addLight(sunLight);

    // Sky light (ambient)
    auto skyLight = std::make_shared<PointLight>(
        Vec3(0.0f, 10.0f, 0.0f),    // High above
        Color3(0.5f, 0.7f, 1.0f),   // Blue sky
        2.0f                         // Ambient level
    );
    addLight(skyLight);
}

void PreviewScene::setupIndoorLighting() {
    // Ceiling light
    auto ceilingLight = std::make_shared<PointLight>(
        Vec3(0.0f, 4.0f, 0.0f),     // Above
        Color3(1.0f, 0.95f, 0.9f),  // Warm white
        8.0f                         // Intensity
    );
    addLight(ceilingLight);

    // Window light
    auto windowLight = std::make_shared<DirectionalLight>(
        Vec3(0.5f, -0.3f, 0.8f).normalized(),   // From window
        Color3(0.9f, 0.95f, 1.0f),              // Cool daylight
        3.0f                                     // Intensity
    );
    addLight(windowLight);
}

void PreviewScene::setupDramaticLighting() {
    // Single strong light from side
    auto dramaticLight = std::make_shared<PointLight>(
        Vec3(3.0f, 1.0f, 0.0f),     // Side position
        Color3(1.0f, 0.8f, 0.6f),   // Warm dramatic
        15.0f                        // High intensity
    );
    addLight(dramaticLight);

    // Subtle fill from opposite
    auto fillLight = std::make_shared<PointLight>(
        Vec3(-2.0f, 0.5f, 1.0f),    // Opposite side
        Color3(0.3f, 0.4f, 0.6f),   // Cool fill
        1.0f                         // Very low intensity
    );
    addLight(fillLight);
}

void PreviewScene::setupSoftLighting() {
    // Multiple soft lights for even illumination
    auto light1 = std::make_shared<PointLight>(
        Vec3(1.5f, 2.0f, 1.5f), Color3(1.0f, 1.0f, 1.0f), 4.0f);
    auto light2 = std::make_shared<PointLight>(
        Vec3(-1.5f, 2.0f, 1.5f), Color3(1.0f, 1.0f, 1.0f), 4.0f);
    auto light3 = std::make_shared<PointLight>(
        Vec3(0.0f, 2.0f, -1.5f), Color3(1.0f, 1.0f, 1.0f), 3.0f);
    auto light4 = std::make_shared<PointLight>(
        Vec3(0.0f, 4.0f, 0.0f), Color3(1.0f, 1.0f, 1.0f), 2.0f);

    addLight(light1);
    addLight(light2);
    addLight(light3);
    addLight(light4);
}

void PreviewScene::clearLights() {
    for (auto& light : m_lights) {
        removeLight(light);
    }
    m_lights.clear();

    if (m_environmentLight) {
        removeLight(m_environmentLight);
        m_environmentLight.reset();
    }
}

void PreviewScene::addLight(std::shared_ptr<Light> light) {
    if (light) {
        m_lights.push_back(light);
        Scene::addLight(light);
    }
}

void PreviewScene::updateObjectTransform() {
    if (!m_previewObject) return;

    // Create rotation matrix
    float radians = m_currentRotation * M_PI / 180.0f;
    m_objectTransform = Matrix4::rotationY(radians);

    // Apply transform to object
    m_previewObject->setTransform(m_objectTransform);
}

} // namespace photon
