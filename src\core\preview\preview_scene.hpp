// src/core/preview/preview_scene.hpp
// PhotonRender - Preview Scene Management
// Sistema di gestione scene per preview materiali

#ifndef PHOTON_PREVIEW_SCENE_HPP
#define PHOTON_PREVIEW_SCENE_HPP

#include "../math/vec3.hpp"
#include "../math/vec2.hpp"
#include "../math/ray.hpp"
#include "../math/matrix4.hpp"
#include "../material/material.hpp"
#include "../scene/scene.hpp"
#include "../light/light.hpp"
#include "preview_renderer.hpp"
#include <memory>
#include <vector>

namespace photon {

// Forward declarations
class Mesh;
class PointLight;
class DirectionalLight;
class EnvironmentLight;

/**
 * @brief Preview Scene for Material Testing
 * 
 * Specialized scene class for material preview with optimized
 * geometry and lighting setups
 */
class PreviewScene : public Scene {
public:
    /**
     * @brief Constructor
     */
    PreviewScene();
    
    /**
     * @brief Destructor
     */
    ~PreviewScene();
    
    /**
     * @brief Initialize preview scene
     * @param geometry Initial geometry type
     * @return True if initialization successful
     */
    bool initialize(PreviewGeometry geometry);
    
    /**
     * @brief Create preview geometry
     * @param geometry Geometry type to create
     */
    void createGeometry(PreviewGeometry geometry);
    
    /**
     * @brief Set material for preview object
     * @param material Material to apply
     */
    void setMaterial(std::shared_ptr<Material> material);
    
    /**
     * @brief Setup lighting configuration
     * @param lighting Lighting preset
     */
    void setupLighting(PreviewLighting lighting);
    
    /**
     * @brief Set lighting intensity
     * @param intensity Intensity multiplier
     */
    void setLightingIntensity(float intensity);
    
    /**
     * @brief Set object rotation
     * @param angleY Rotation angle in degrees
     */
    void setObjectRotation(float angleY);
    
    /**
     * @brief Get current geometry type
     * @return Current geometry
     */
    PreviewGeometry getCurrentGeometry() const { return m_currentGeometry; }
    
    /**
     * @brief Get current lighting type
     * @return Current lighting
     */
    PreviewLighting getCurrentLighting() const { return m_currentLighting; }
    
    /**
     * @brief Get preview object bounds
     * @return Bounding box of preview object
     */
    BoundingBox getObjectBounds() const;
    
    /**
     * @brief Enable/disable ground plane
     * @param enabled Ground plane enabled
     */
    void setGroundPlaneEnabled(bool enabled);
    
    /**
     * @brief Set ground plane material
     * @param material Ground plane material
     */
    void setGroundPlaneMaterial(std::shared_ptr<Material> material);
    
    /**
     * @brief Enable/disable environment background
     * @param enabled Environment background enabled
     */
    void setEnvironmentEnabled(bool enabled);
    
    /**
     * @brief Set environment map
     * @param envMap Environment map texture
     */
    void setEnvironmentMap(std::shared_ptr<Texture> envMap);

private:
    PreviewGeometry m_currentGeometry = PreviewGeometry::SPHERE;
    PreviewLighting m_currentLighting = PreviewLighting::STUDIO;
    
    // Scene objects
    std::shared_ptr<Mesh> m_previewObject;
    std::shared_ptr<Mesh> m_groundPlane;
    std::shared_ptr<Material> m_previewMaterial;
    std::shared_ptr<Material> m_groundMaterial;
    
    // Lighting
    std::vector<std::shared_ptr<Light>> m_lights;
    std::shared_ptr<EnvironmentLight> m_environmentLight;
    float m_lightingIntensity = 1.0f;
    
    // Transform
    Matrix4 m_objectTransform = Matrix4::identity();
    float m_currentRotation = 0.0f;
    
    // Settings
    bool m_groundPlaneEnabled = true;
    bool m_environmentEnabled = false;
    
    /**
     * @brief Create sphere geometry
     * @param radius Sphere radius
     * @param segments Number of segments
     * @return Sphere mesh
     */
    std::shared_ptr<Mesh> createSphere(float radius = 1.0f, int segments = 32);
    
    /**
     * @brief Create cube geometry
     * @param size Cube size
     * @return Cube mesh
     */
    std::shared_ptr<Mesh> createCube(float size = 1.0f);
    
    /**
     * @brief Create plane geometry
     * @param width Plane width
     * @param height Plane height
     * @return Plane mesh
     */
    std::shared_ptr<Mesh> createPlane(float width = 2.0f, float height = 2.0f);
    
    /**
     * @brief Create cylinder geometry
     * @param radius Cylinder radius
     * @param height Cylinder height
     * @param segments Number of segments
     * @return Cylinder mesh
     */
    std::shared_ptr<Mesh> createCylinder(float radius = 1.0f, float height = 2.0f, int segments = 24);
    
    /**
     * @brief Create torus geometry
     * @param majorRadius Major radius
     * @param minorRadius Minor radius
     * @param majorSegments Major segments
     * @param minorSegments Minor segments
     * @return Torus mesh
     */
    std::shared_ptr<Mesh> createTorus(float majorRadius = 1.0f, float minorRadius = 0.3f, 
                                     int majorSegments = 24, int minorSegments = 16);
    
    /**
     * @brief Create ground plane
     * @param size Ground plane size
     * @return Ground plane mesh
     */
    std::shared_ptr<Mesh> createGroundPlane(float size = 10.0f);
    
    /**
     * @brief Setup studio lighting
     */
    void setupStudioLighting();
    
    /**
     * @brief Setup outdoor lighting
     */
    void setupOutdoorLighting();
    
    /**
     * @brief Setup indoor lighting
     */
    void setupIndoorLighting();
    
    /**
     * @brief Setup dramatic lighting
     */
    void setupDramaticLighting();
    
    /**
     * @brief Setup soft lighting
     */
    void setupSoftLighting();
    
    /**
     * @brief Clear all lights
     */
    void clearLights();
    
    /**
     * @brief Add light to scene
     * @param light Light to add
     */
    void addLight(std::shared_ptr<Light> light);
    
    /**
     * @brief Update object transform
     */
    void updateObjectTransform();
    
    /**
     * @brief Calculate UV coordinates for geometry
     * @param geometry Geometry type
     * @param vertex Vertex position
     * @return UV coordinates
     */
    Vec2 calculateUV(PreviewGeometry geometry, const Vec3& vertex);
    
    /**
     * @brief Calculate normal for geometry
     * @param geometry Geometry type
     * @param vertex Vertex position
     * @return Normal vector
     */
    Vec3 calculateNormal(PreviewGeometry geometry, const Vec3& vertex);
    
    /**
     * @brief Calculate tangent for geometry
     * @param geometry Geometry type
     * @param vertex Vertex position
     * @param normal Normal vector
     * @return Tangent vector
     */
    Vec3 calculateTangent(PreviewGeometry geometry, const Vec3& vertex, const Vec3& normal);
};

} // namespace photon

#endif // PHOTON_PREVIEW_SCENE_HPP
