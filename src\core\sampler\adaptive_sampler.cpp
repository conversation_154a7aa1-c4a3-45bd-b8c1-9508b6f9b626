// src/core/sampler/adaptive_sampler.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Adaptive Sampling System Implementation

#include "adaptive_sampler.hpp"
#include "../math/math_utils.hpp"
#include <algorithm>
#include <cmath>
#include <numeric>

namespace photon {

// PixelVarianceStats Implementation
void PixelVarianceStats::updateWithSample(const Color3& sample) {
    sampleCount++;
    
    if (sampleCount == 1) {
        mean = sample;
        variance = Color3(0.0f, 0.0f, 0.0f);
        luminanceVariance = 0.0f;
        return;
    }
    
    // <PERSON><PERSON>ord's online algorithm for variance calculation
    Color3 delta = sample - mean;
    mean = mean + delta / static_cast<float>(sampleCount);
    Color3 delta2 = sample - mean;
    variance = variance + Color3(delta.r * delta2.r, delta.g * delta2.g, delta.b * delta2.b);
    
    // Update luminance variance
    float sampleLuminance = sample.luminance();
    float meanLuminance = mean.luminance();
    float deltaLum = sampleLuminance - meanLuminance;
    luminanceVariance += deltaLum * deltaLum;
}

bool PixelVarianceStats::checkConvergence(float threshold, float varianceThreshold) const {
    if (sampleCount < 4) return false;
    
    // Check relative error
    float relativeError = getRelativeError();
    if (relativeError > threshold) return false;
    
    // Check variance threshold
    float normalizedVariance = luminanceVariance / static_cast<float>(sampleCount - 1);
    float meanLuminance = mean.luminance();
    if (meanLuminance > 0.0f) {
        float relativeVariance = std::sqrt(normalizedVariance) / meanLuminance;
        if (relativeVariance > varianceThreshold) return false;
    }
    
    return true;
}

float PixelVarianceStats::getRelativeError() const {
    if (sampleCount < 2) return 1.0f;
    
    float normalizedVariance = luminanceVariance / static_cast<float>(sampleCount - 1);
    float standardError = std::sqrt(normalizedVariance / static_cast<float>(sampleCount));
    float meanLuminance = mean.luminance();
    
    if (meanLuminance > 0.0f) {
        return standardError / meanLuminance;
    }
    
    return standardError;
}

void PixelVarianceStats::reset() {
    mean = Color3(0.0f, 0.0f, 0.0f);
    variance = Color3(0.0f, 0.0f, 0.0f);
    luminanceVariance = 0.0f;
    sampleCount = 0;
    converged = false;
    convergenceConfidence = 0.0f;
}

// NoiseAnalysis Implementation
void NoiseAnalysis::updateNoise(float variance, int sampleCount) {
    if (sampleCount < 2) return;
    
    float newNoiseLevel = std::sqrt(variance / static_cast<float>(sampleCount));
    
    if (sampleCount > 2) {
        // Calculate noise reduction rate
        float oldNoise = noiseLevel;
        noiseReduction = (oldNoise > 0.0f) ? (oldNoise - newNoiseLevel) / oldNoise : 0.0f;
        isConverging = noiseReduction > 0.0f;
    }
    
    noiseLevel = newNoiseLevel;
    samplesForTarget = estimateSamplesNeeded(variance, sampleCount);
}

int NoiseAnalysis::estimateSamplesNeeded(float currentVariance, int currentSamples) const {
    if (currentVariance <= 0.0f || targetNoise <= 0.0f) return currentSamples;
    
    // Noise scales as 1/sqrt(samples), so variance scales as 1/samples
    float targetVariance = targetNoise * targetNoise;
    float samplesNeeded = currentVariance / targetVariance * static_cast<float>(currentSamples);
    
    return static_cast<int>(std::ceil(samplesNeeded));
}

// AdaptiveSampler Implementation
AdaptiveSampler::AdaptiveSampler() 
    : m_width(0), m_height(0) {
}

AdaptiveSampler::~AdaptiveSampler() = default;

void AdaptiveSampler::initialize(int width, int height, const AdaptiveSamplingParams& params) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    m_width = width;
    m_height = height;
    m_params = params;
    
    // Allocate per-pixel data
    int totalPixels = width * height;
    m_pixelStats.resize(totalPixels);
    m_noiseAnalysis.resize(totalPixels);
    
    // Reset statistics
    m_convergedPixels = 0;
    m_totalSamples = 0;
    
    // Initialize noise analysis
    for (auto& noise : m_noiseAnalysis) {
        noise.targetNoise = params.convergenceThreshold;
    }
}

void AdaptiveSampler::reset() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    // Reset all pixel statistics
    for (auto& stats : m_pixelStats) {
        stats.reset();
    }
    
    // Reset noise analysis
    for (auto& noise : m_noiseAnalysis) {
        noise = NoiseAnalysis();
        noise.targetNoise = m_params.convergenceThreshold;
    }
    
    m_convergedPixels = 0;
    m_totalSamples = 0;
}

bool AdaptiveSampler::updatePixel(int x, int y, const Color3& sample) {
    if (!isValidPixel(x, y)) return false;
    
    std::lock_guard<std::mutex> lock(m_mutex);
    
    int index = getPixelIndex(x, y);
    PixelVarianceStats& stats = m_pixelStats[index];
    
    // Update pixel statistics
    bool wasConverged = stats.converged;
    stats.updateWithSample(sample);
    
    // Update noise analysis
    updateNoiseAnalysis(x, y, stats);
    
    // Check convergence
    bool isConverged = analyzeConvergence(x, y);
    stats.converged = isConverged;
    stats.convergenceConfidence = calculateConfidence(stats);
    
    // Update global statistics
    if (isConverged && !wasConverged) {
        m_convergedPixels++;
    } else if (!isConverged && wasConverged) {
        m_convergedPixels--;
    }
    
    m_totalSamples++;
    
    // Return true if pixel needs more samples
    return !isConverged && stats.sampleCount < m_params.maxSamples;
}

bool AdaptiveSampler::hasConverged(int x, int y) const {
    if (!isValidPixel(x, y)) return true;
    
    std::lock_guard<std::mutex> lock(m_mutex);
    int index = getPixelIndex(x, y);
    return m_pixelStats[index].converged;
}

int AdaptiveSampler::getOptimalSampleCount(int x, int y) const {
    if (!isValidPixel(x, y)) return m_params.minSamples;
    
    std::lock_guard<std::mutex> lock(m_mutex);
    int index = getPixelIndex(x, y);
    const PixelVarianceStats& stats = m_pixelStats[index];
    const NoiseAnalysis& noise = m_noiseAnalysis[index];
    
    if (stats.converged) {
        return stats.sampleCount;
    }
    
    // Estimate based on noise analysis
    int noiseBased = noise.samplesForTarget;
    
    // Estimate based on variance
    float relativeError = stats.getRelativeError();
    int varianceBased = stats.sampleCount;
    if (relativeError > m_params.convergenceThreshold) {
        float ratio = relativeError / m_params.convergenceThreshold;
        varianceBased = static_cast<int>(stats.sampleCount * ratio * ratio);
    }
    
    // Take the minimum of estimates, but respect bounds
    int optimal = std::min(noiseBased, varianceBased);
    optimal = std::max(optimal, m_params.minSamples);
    optimal = std::min(optimal, m_params.maxSamples);
    
    return optimal;
}

int AdaptiveSampler::getCurrentSampleCount(int x, int y) const {
    if (!isValidPixel(x, y)) return 0;
    
    std::lock_guard<std::mutex> lock(m_mutex);
    int index = getPixelIndex(x, y);
    return m_pixelStats[index].sampleCount;
}

const PixelVarianceStats& AdaptiveSampler::getPixelStats(int x, int y) const {
    static PixelVarianceStats defaultStats;
    if (!isValidPixel(x, y)) return defaultStats;
    
    std::lock_guard<std::mutex> lock(m_mutex);
    int index = getPixelIndex(x, y);
    return m_pixelStats[index];
}

const NoiseAnalysis& AdaptiveSampler::getNoiseAnalysis(int x, int y) const {
    static NoiseAnalysis defaultNoise;
    if (!isValidPixel(x, y)) return defaultNoise;
    
    std::lock_guard<std::mutex> lock(m_mutex);
    int index = getPixelIndex(x, y);
    return m_noiseAnalysis[index];
}

float AdaptiveSampler::getConvergenceProgress() const {
    if (m_width * m_height == 0) return 0.0f;
    return static_cast<float>(m_convergedPixels) / static_cast<float>(m_width * m_height);
}

float AdaptiveSampler::getAverageSamplesPerPixel() const {
    if (m_width * m_height == 0) return 0.0f;
    
    std::lock_guard<std::mutex> lock(m_mutex);
    long long totalSamples = 0;
    for (const auto& stats : m_pixelStats) {
        totalSamples += stats.sampleCount;
    }
    
    return static_cast<float>(totalSamples) / static_cast<float>(m_width * m_height);
}

float AdaptiveSampler::getEfficiency() const {
    float avgSPP = getAverageSamplesPerPixel();
    if (avgSPP == 0.0f) return 1.0f;
    
    // Compare to fixed sampling at max samples
    return static_cast<float>(m_params.maxSamples) / avgSPP;
}

void AdaptiveSampler::setParameters(const AdaptiveSamplingParams& params) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_params = params;
    
    // Update noise analysis target
    for (auto& noise : m_noiseAnalysis) {
        noise.targetNoise = params.convergenceThreshold;
    }
}

// Private method implementations
float AdaptiveSampler::analyzeSpatialCoherence(int x, int y) const {
    if (!m_params.spatialAdaptation) return 0.0f;

    std::vector<int> neighbors = getSpatialNeighbors(x, y);
    if (neighbors.empty()) return 0.0f;

    int index = getPixelIndex(x, y);
    const PixelVarianceStats& currentStats = m_pixelStats[index];

    float coherence = 0.0f;
    int validNeighbors = 0;

    for (int neighborIndex : neighbors) {
        const PixelVarianceStats& neighborStats = m_pixelStats[neighborIndex];
        if (neighborStats.sampleCount > 0) {
            // Calculate color similarity
            Color3 colorDiff = currentStats.mean - neighborStats.mean;
            float similarity = 1.0f / (1.0f + colorDiff.length());
            coherence += similarity;
            validNeighbors++;
        }
    }

    return validNeighbors > 0 ? coherence / static_cast<float>(validNeighbors) : 0.0f;
}

std::vector<int> AdaptiveSampler::getSpatialNeighbors(int x, int y) const {
    std::vector<int> neighbors;
    int radius = m_params.spatialRadius;

    for (int dy = -radius; dy <= radius; dy++) {
        for (int dx = -radius; dx <= radius; dx++) {
            if (dx == 0 && dy == 0) continue; // Skip center pixel

            int nx = x + dx;
            int ny = y + dy;

            if (isValidPixel(nx, ny)) {
                neighbors.push_back(getPixelIndex(nx, ny));
            }
        }
    }

    return neighbors;
}

bool AdaptiveSampler::analyzeConvergence(int x, int y) const {
    int index = getPixelIndex(x, y);
    const PixelVarianceStats& stats = m_pixelStats[index];

    // Minimum samples check
    if (stats.sampleCount < m_params.minSamples) return false;

    // Check convergence interval
    if (stats.sampleCount % m_params.checkInterval != 0) {
        return stats.converged; // Keep previous state
    }

    // Variance-based convergence
    bool varianceConverged = true;
    if (m_params.useVarianceAnalysis) {
        varianceConverged = stats.checkConvergence(m_params.convergenceThreshold,
                                                  m_params.varianceThreshold);
    }

    // Noise-based convergence
    bool noiseConverged = true;
    if (m_params.useNoiseAnalysis) {
        const NoiseAnalysis& noise = m_noiseAnalysis[index];
        noiseConverged = noise.noiseLevel <= m_params.convergenceThreshold;
    }

    // Spatial coherence check
    float spatialCoherence = analyzeSpatialCoherence(x, y);
    bool spatiallyStable = spatialCoherence > 0.8f || !m_params.spatialAdaptation;

    // Confidence check
    float confidence = calculateConfidence(stats);
    bool confidentConvergence = confidence >= m_params.confidenceThreshold;

    return varianceConverged && noiseConverged && spatiallyStable && confidentConvergence;
}

float AdaptiveSampler::calculateConfidence(const PixelVarianceStats& stats) const {
    if (stats.sampleCount < 4) return 0.0f;

    // Base confidence on sample count and variance stability
    float sampleConfidence = std::min(1.0f, static_cast<float>(stats.sampleCount) /
                                     static_cast<float>(m_params.minSamples * 2));

    // Variance stability confidence
    float relativeError = stats.getRelativeError();
    float varianceConfidence = 1.0f / (1.0f + relativeError * 10.0f);

    // Combine confidences
    return (sampleConfidence + varianceConfidence) * 0.5f;
}

void AdaptiveSampler::updateNoiseAnalysis(int x, int y, const PixelVarianceStats& stats) {
    int index = getPixelIndex(x, y);
    NoiseAnalysis& noise = m_noiseAnalysis[index];

    if (stats.sampleCount >= 2) {
        float variance = stats.luminanceVariance / static_cast<float>(stats.sampleCount - 1);
        noise.updateNoise(variance, stats.sampleCount);
    }
}

float AdaptiveSampler::estimateNoiseLevel(const PixelVarianceStats& stats) const {
    if (stats.sampleCount < 2) return 1.0f;

    float variance = stats.luminanceVariance / static_cast<float>(stats.sampleCount - 1);
    return std::sqrt(variance / static_cast<float>(stats.sampleCount));
}

// Utility functions implementation
namespace AdaptiveSamplingUtils {

int calculateOptimalSamples(float variance, float targetVariance, int currentSamples) {
    if (targetVariance <= 0.0f || variance <= targetVariance) {
        return currentSamples;
    }

    // Variance scales as 1/samples
    float ratio = variance / targetVariance;
    return static_cast<int>(std::ceil(static_cast<float>(currentSamples) * ratio));
}

float estimateConvergenceRate(const std::vector<float>& samples) {
    if (samples.size() < 3) return 0.0f;

    // Simple linear regression on log scale
    float n = static_cast<float>(samples.size());
    float sumX = 0.0f, sumY = 0.0f, sumXY = 0.0f, sumX2 = 0.0f;

    for (size_t i = 0; i < samples.size(); i++) {
        float x = static_cast<float>(i + 1);
        float y = std::log(std::max(samples[i], 1e-6f));

        sumX += x;
        sumY += y;
        sumXY += x * y;
        sumX2 += x * x;
    }

    float slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
    return -slope; // Negative slope indicates convergence
}

float calculateNoiseReduction(float oldVariance, float newVariance) {
    if (oldVariance <= 0.0f) return 0.0f;
    return std::max(0.0f, (oldVariance - newVariance) / oldVariance);
}

} // namespace AdaptiveSamplingUtils

} // namespace photon
