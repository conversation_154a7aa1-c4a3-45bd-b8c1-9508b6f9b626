// src/core/sampler/adaptive_sampler.hpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Adaptive Sampling System for intelligent SPP optimization

#pragma once

#include "../math/vec3.hpp"
#include "../math/vec2.hpp"
#include <vector>
#include <memory>
#include <atomic>
#include <mutex>

namespace photon {

// Forward declarations
class Sampler;
class Integrator;
class Scene;
class Camera;

/**
 * @brief Pixel variance statistics for adaptive sampling
 */
struct PixelVarianceStats {
    Color3 mean{0.0f, 0.0f, 0.0f};           ///< Running mean of pixel color
    Color3 variance{0.0f, 0.0f, 0.0f};       ///< Running variance of pixel color
    float luminanceVariance = 0.0f;          ///< Luminance variance for faster checks
    int sampleCount = 0;                     ///< Number of samples taken
    bool converged = false;                  ///< Whether pixel has converged
    float convergenceConfidence = 0.0f;      ///< Confidence in convergence [0,1]
    
    /**
     * @brief Update statistics with new sample
     */
    void updateWithSample(const Color3& sample);
    
    /**
     * @brief Check if pixel has converged
     */
    bool checkConvergence(float threshold, float varianceThreshold) const;
    
    /**
     * @brief Get relative error estimate
     */
    float getRelativeError() const;
    
    /**
     * @brief Reset statistics
     */
    void reset();
};

/**
 * @brief Noise analysis for adaptive sampling
 */
struct NoiseAnalysis {
    float noiseLevel = 0.0f;                 ///< Current noise level [0,1]
    float noiseReduction = 0.0f;             ///< Noise reduction rate
    float targetNoise = 0.01f;               ///< Target noise level
    int samplesForTarget = 0;                ///< Estimated samples needed for target
    bool isConverging = false;               ///< Whether noise is decreasing
    
    /**
     * @brief Update noise analysis with new variance
     */
    void updateNoise(float variance, int sampleCount);
    
    /**
     * @brief Estimate samples needed for target noise
     */
    int estimateSamplesNeeded(float currentVariance, int currentSamples) const;
};

/**
 * @brief Adaptive sampling parameters
 */
struct AdaptiveSamplingParams {
    bool enabled = true;                     ///< Enable adaptive sampling
    float convergenceThreshold = 0.01f;     ///< Convergence threshold
    float varianceThreshold = 0.05f;        ///< Variance threshold
    int minSamples = 16;                     ///< Minimum samples per pixel
    int maxSamples = 1024;                   ///< Maximum samples per pixel
    int checkInterval = 8;                   ///< Check convergence every N samples
    bool useVarianceAnalysis = true;         ///< Use variance for convergence
    bool useNoiseAnalysis = true;            ///< Use noise analysis
    float confidenceThreshold = 0.95f;      ///< Confidence threshold for convergence
    float earlyTerminationFactor = 0.8f;    ///< Factor for early termination
    
    // Advanced parameters
    bool spatialAdaptation = true;           ///< Use spatial coherence
    float spatialWeight = 0.3f;              ///< Weight for spatial information
    int spatialRadius = 2;                   ///< Radius for spatial analysis
    bool temporalAdaptation = false;         ///< Use temporal coherence (for animation)
    float temporalWeight = 0.2f;             ///< Weight for temporal information
};

/**
 * @brief Adaptive Sampling System
 * 
 * Intelligently determines optimal number of samples per pixel based on:
 * - Variance detection and analysis
 * - Noise level estimation
 * - Convergence confidence
 * - Spatial coherence
 */
class AdaptiveSampler {
public:
    /**
     * @brief Constructor
     */
    AdaptiveSampler();
    
    /**
     * @brief Destructor
     */
    ~AdaptiveSampler();
    
    /**
     * @brief Initialize adaptive sampler
     * 
     * @param width Image width
     * @param height Image height
     * @param params Adaptive sampling parameters
     */
    void initialize(int width, int height, const AdaptiveSamplingParams& params);
    
    /**
     * @brief Reset for new render
     */
    void reset();
    
    /**
     * @brief Update pixel with new sample
     * 
     * @param x Pixel x coordinate
     * @param y Pixel y coordinate
     * @param sample Color sample
     * @return True if pixel needs more samples
     */
    bool updatePixel(int x, int y, const Color3& sample);
    
    /**
     * @brief Check if pixel has converged
     * 
     * @param x Pixel x coordinate
     * @param y Pixel y coordinate
     * @return True if pixel has converged
     */
    bool hasConverged(int x, int y) const;
    
    /**
     * @brief Get optimal sample count for pixel
     * 
     * @param x Pixel x coordinate
     * @param y Pixel y coordinate
     * @return Optimal number of samples
     */
    int getOptimalSampleCount(int x, int y) const;
    
    /**
     * @brief Get current sample count for pixel
     * 
     * @param x Pixel x coordinate
     * @param y Pixel y coordinate
     * @return Current sample count
     */
    int getCurrentSampleCount(int x, int y) const;
    
    /**
     * @brief Get pixel variance statistics
     * 
     * @param x Pixel x coordinate
     * @param y Pixel y coordinate
     * @return Variance statistics
     */
    const PixelVarianceStats& getPixelStats(int x, int y) const;
    
    /**
     * @brief Get noise analysis for pixel
     * 
     * @param x Pixel x coordinate
     * @param y Pixel y coordinate
     * @return Noise analysis
     */
    const NoiseAnalysis& getNoiseAnalysis(int x, int y) const;
    
    /**
     * @brief Get convergence progress [0,1]
     * 
     * @return Overall convergence progress
     */
    float getConvergenceProgress() const;
    
    /**
     * @brief Get average samples per pixel
     * 
     * @return Average SPP across all pixels
     */
    float getAverageSamplesPerPixel() const;
    
    /**
     * @brief Get efficiency metrics
     * 
     * @return Efficiency compared to fixed sampling
     */
    float getEfficiency() const;
    
    /**
     * @brief Set adaptive sampling parameters
     * 
     * @param params New parameters
     */
    void setParameters(const AdaptiveSamplingParams& params);
    
    /**
     * @brief Get current parameters
     * 
     * @return Current adaptive sampling parameters
     */
    const AdaptiveSamplingParams& getParameters() const { return m_params; }

private:
    // Core data
    int m_width, m_height;
    AdaptiveSamplingParams m_params;
    
    // Per-pixel statistics
    std::vector<PixelVarianceStats> m_pixelStats;
    std::vector<NoiseAnalysis> m_noiseAnalysis;
    
    // Thread safety
    mutable std::mutex m_mutex;
    
    // Statistics
    std::atomic<int> m_convergedPixels{0};
    std::atomic<long long> m_totalSamples{0};
    
    // Internal methods
    int getPixelIndex(int x, int y) const { return y * m_width + x; }
    bool isValidPixel(int x, int y) const { return x >= 0 && x < m_width && y >= 0 && y < m_height; }
    
    // Spatial analysis
    float analyzeSpatialCoherence(int x, int y) const;
    std::vector<int> getSpatialNeighbors(int x, int y) const;
    
    // Convergence analysis
    bool analyzeConvergence(int x, int y) const;
    float calculateConfidence(const PixelVarianceStats& stats) const;
    
    // Noise analysis
    void updateNoiseAnalysis(int x, int y, const PixelVarianceStats& stats);
    float estimateNoiseLevel(const PixelVarianceStats& stats) const;
};

/**
 * @brief Utility functions for adaptive sampling
 */
namespace AdaptiveSamplingUtils {
    /**
     * @brief Calculate optimal sample count based on variance
     * 
     * @param variance Current variance
     * @param targetVariance Target variance
     * @param currentSamples Current sample count
     * @return Optimal sample count
     */
    int calculateOptimalSamples(float variance, float targetVariance, int currentSamples);
    
    /**
     * @brief Estimate convergence rate
     * 
     * @param samples Sample history
     * @return Convergence rate estimate
     */
    float estimateConvergenceRate(const std::vector<float>& samples);
    
    /**
     * @brief Calculate noise reduction factor
     * 
     * @param oldVariance Previous variance
     * @param newVariance Current variance
     * @return Noise reduction factor
     */
    float calculateNoiseReduction(float oldVariance, float newVariance);
}

} // namespace photon
