// src/core/sampler/sampler.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Random sampling implementation

#include "sampler.hpp"
#include "../common.hpp"
#include <algorithm>

namespace photon {

// RandomSampler implementation
RandomSampler::RandomSampler(uint32_t seed) : m_dist(0.0f, 1.0f) {
    if (seed == 0) {
        std::random_device rd;
        seed = rd();
    }
    m_rng.seed(seed);
}

float RandomSampler::get1D() {
    return m_dist(m_rng);
}

Vec3 RandomSampler::get2D() {
    return Vec3(get1D(), get1D(), 0);
}

std::unique_ptr<Sampler> RandomSampler::clone() const {
    return std::make_unique<RandomSampler>(m_rng());
}

void RandomSampler::seed(uint32_t seed) {
    m_rng.seed(seed);
}

// StratifiedSampler implementation
StratifiedSampler::StratifiedSampler(int xSamples, int ySamples, bool jitter)
    : m_xSamples(xSamples), m_ySamples(ySamples), m_jitter(jitter),
      m_currentSample(0), m_dist(0.0f, 1.0f), m_current1D(0), m_current2D(0) {
    
    std::random_device rd;
    m_rng.seed(rd());
    generateSamples();
}

void StratifiedSampler::generateSamples() {
    int totalSamples = m_xSamples * m_ySamples;
    
    // Generate 1D samples
    m_samples1D.clear();
    m_samples1D.reserve(totalSamples);
    for (int i = 0; i < totalSamples; ++i) {
        float sample = (i + (m_jitter ? m_dist(m_rng) : 0.5f)) / totalSamples;
        m_samples1D.push_back(sample);
    }
    
    // Generate 2D samples
    m_samples2D.clear();
    m_samples2D.reserve(totalSamples);
    for (int y = 0; y < m_ySamples; ++y) {
        for (int x = 0; x < m_xSamples; ++x) {
            float u = (x + (m_jitter ? m_dist(m_rng) : 0.5f)) / m_xSamples;
            float v = (y + (m_jitter ? m_dist(m_rng) : 0.5f)) / m_ySamples;
            m_samples2D.push_back(Vec3(u, v, 0));
        }
    }
    
    // Shuffle for better distribution
    std::shuffle(m_samples1D.begin(), m_samples1D.end(), m_rng);
    std::shuffle(m_samples2D.begin(), m_samples2D.end(), m_rng);
}

float StratifiedSampler::get1D() {
    if (m_current1D >= m_samples1D.size()) {
        return m_dist(m_rng); // Fallback to random
    }
    return m_samples1D[m_current1D++];
}

Vec3 StratifiedSampler::get2D() {
    if (m_current2D >= m_samples2D.size()) {
        return Vec3(m_dist(m_rng), m_dist(m_rng), 0); // Fallback to random
    }
    return m_samples2D[m_current2D++];
}

void StratifiedSampler::startPixel() {
    m_current1D = 0;
    m_current2D = 0;
    generateSamples();
}

std::unique_ptr<Sampler> StratifiedSampler::clone() const {
    return std::make_unique<StratifiedSampler>(m_xSamples, m_ySamples, m_jitter);
}

void StratifiedSampler::seed(uint32_t seed) {
    m_rng.seed(seed);
    generateSamples();
}

// HaltonSampler implementation
HaltonSampler::HaltonSampler(int samplesPerPixel)
    : m_samplesPerPixel(samplesPerPixel), m_currentSample(0), m_pixelX(0), m_pixelY(0) {
}

float HaltonSampler::get1D() {
    int index = m_pixelY * 1024 + m_pixelX; // Assume max 1024x1024 for now
    return halton(index + m_currentSample, 2);
}

Vec3 HaltonSampler::get2D() {
    int index = m_pixelY * 1024 + m_pixelX;
    float u = halton(index + m_currentSample, 2);
    float v = halton(index + m_currentSample, 3);
    m_currentSample++;
    return Vec3(u, v, 0);
}

void HaltonSampler::startPixel(int pixelX, int pixelY) {
    m_pixelX = pixelX;
    m_pixelY = pixelY;
    m_currentSample = 0;
}

float HaltonSampler::halton(int index, int base) const {
    return radicalInverse(index, base);
}

float HaltonSampler::radicalInverse(int n, int base) const {
    float result = 0.0f;
    float fraction = 1.0f / base;
    
    while (n > 0) {
        result += (n % base) * fraction;
        n /= base;
        fraction /= base;
    }
    
    return result;
}

std::unique_ptr<Sampler> HaltonSampler::clone() const {
    return std::make_unique<HaltonSampler>(m_samplesPerPixel);
}

void HaltonSampler::seed(uint32_t seed) {
    // Halton sequence is deterministic, seed doesn't apply
}

// Sampling utility functions
namespace sampling {

Vec3 sampleUniformHemisphere(float u1, float u2) {
    float z = u1;
    float r = std::sqrt(std::max(0.0f, 1.0f - z * z));
    float phi = 2.0f * M_PI * u2;
    return Vec3(r * std::cos(phi), r * std::sin(phi), z);
}

Vec3 sampleCosineHemisphere(float u1, float u2) {
    float cosTheta = std::sqrt(u1);
    float sinTheta = std::sqrt(1.0f - u1);
    float phi = 2.0f * M_PI * u2;
    return Vec3(sinTheta * std::cos(phi), sinTheta * std::sin(phi), cosTheta);
}

Vec3 sampleUniformSphere(float u1, float u2) {
    float z = 1.0f - 2.0f * u1;
    float r = std::sqrt(std::max(0.0f, 1.0f - z * z));
    float phi = 2.0f * M_PI * u2;
    return Vec3(r * std::cos(phi), r * std::sin(phi), z);
}

Vec3 sampleUniformDisk(float u1, float u2) {
    float r = std::sqrt(u1);
    float theta = 2.0f * M_PI * u2;
    return Vec3(r * std::cos(theta), r * std::sin(theta), 0);
}

Vec3 sampleUniformTriangle(float u1, float u2) {
    float su1 = std::sqrt(u1);
    return Vec3(1.0f - su1, u2 * su1, 0);
}

Vec3 uniformSampleToHemisphere(float u1, float u2, const Normal3& normal) {
    Vec3 sample = sampleUniformHemisphere(u1, u2);
    
    // Create coordinate system around normal
    Vec3 nt = (std::abs(normal.x) > 0.1f) ? Vec3(0, 1, 0) : Vec3(1, 0, 0);
    Vec3 tangent = normal.cross(nt).normalized();
    Vec3 bitangent = normal.cross(tangent);
    
    return sample.x * tangent + sample.y * bitangent + sample.z * normal;
}

Vec3 cosineSampleToHemisphere(float u1, float u2, const Normal3& normal) {
    Vec3 sample = sampleCosineHemisphere(u1, u2);
    
    // Create coordinate system around normal
    Vec3 nt = (std::abs(normal.x) > 0.1f) ? Vec3(0, 1, 0) : Vec3(1, 0, 0);
    Vec3 tangent = normal.cross(nt).normalized();
    Vec3 bitangent = normal.cross(tangent);
    
    return sample.x * tangent + sample.y * bitangent + sample.z * normal;
}

float uniformHemispherePdf() {
    return 1.0f / (2.0f * M_PI);
}

float cosineHemispherePdf(float cosTheta) {
    return cosTheta / M_PI;
}

float powerHeuristic(int nf, float fPdf, int ng, float gPdf) {
    float f = nf * fPdf;
    float g = ng * gPdf;
    return (f * f) / (f * f + g * g);
}

float balanceHeuristic(int nf, float fPdf, int ng, float gPdf) {
    return (nf * fPdf) / (nf * fPdf + ng * gPdf);
}

} // namespace sampling

} // namespace photon
