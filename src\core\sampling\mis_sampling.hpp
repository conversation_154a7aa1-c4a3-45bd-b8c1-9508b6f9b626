// src/core/sampling/mis_sampling.hpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Multiple Importance Sampling framework

#pragma once

#include "../math/vec3.hpp"
#include "../math/ray.hpp"
#include "../scene/scene.hpp"
#include "../material/material.hpp"
#include "../sampler/sampler.hpp"
#include <memory>
#include <vector>

namespace photon {

// Forward declarations
class Scene;
class Light;
class Material;
class Intersection;

/**
 * @brief MIS sample result structure
 * 
 * Contains the combined result of light and BSDF sampling with MIS weights
 */
struct MISSample {
    Color3 Li;              ///< Combined incident radiance
    Vec3 wi;                ///< Incident direction
    float pdf;              ///< Combined PDF
    float weight;           ///< MIS weight
    bool isValid;           ///< Sample validity
    
    MISSample() : Li(0), wi(0), pdf(0), weight(0), isValid(false) {}
    MISSample(const Color3& Li, const Vec3& wi, float pdf, float weight)
        : Li(Li), wi(wi), pdf(pdf), weight(weight), isValid(true) {}
};

/**
 * @brief MIS strategy enumeration
 */
enum class MISStrategy {
    POWER_HEURISTIC,        ///< Power heuristic (β=2)
    BALANCE_HEURISTIC,      ///< Balance heuristic (β=1)
    OPTIMAL_HEURISTIC       ///< Optimal heuristic (adaptive)
};

/**
 * @brief Multiple Importance Sampling framework
 * 
 * Combines light sampling and BSDF sampling using various heuristics
 * to reduce variance in Monte Carlo integration.
 */
class MISSampling {
public:
    /**
     * @brief Constructor
     * 
     * @param strategy MIS strategy to use
     * @param lightSamples Number of light samples per intersection
     * @param bsdfSamples Number of BSDF samples per intersection
     */
    MISSampling(MISStrategy strategy = MISStrategy::POWER_HEURISTIC, 
                int lightSamples = 1, int bsdfSamples = 1);
    
    /**
     * @brief Destructor
     */
    ~MISSampling() = default;
    
    // Non-copyable
    MISSampling(const MISSampling&) = delete;
    MISSampling& operator=(const MISSampling&) = delete;
    
    /**
     * @brief Sample direct lighting using MIS
     * 
     * Combines light sampling and BSDF sampling with optimal weighting
     * 
     * @param isect Surface intersection point
     * @param scene Scene containing lights
     * @param sampler Random number sampler
     * @param wo Outgoing direction (from surface)
     * @return MIS sample result
     */
    MISSample sampleDirectLighting(const Intersection& isect, const Scene& scene,
                                  Sampler& sampler, const Vec3& wo) const;
    
    /**
     * @brief Sample single light with MIS weighting
     * 
     * @param isect Surface intersection point
     * @param light Light source to sample
     * @param scene Scene for visibility testing
     * @param sampler Random number sampler
     * @param wo Outgoing direction
     * @return Light sample with MIS weight
     */
    MISSample sampleLight(const Intersection& isect, const Light& light, 
                         const Scene& scene, Sampler& sampler, const Vec3& wo) const;
    
    /**
     * @brief Sample BSDF with MIS weighting
     * 
     * @param isect Surface intersection point
     * @param scene Scene for light evaluation
     * @param sampler Random number sampler
     * @param wo Outgoing direction
     * @return BSDF sample with MIS weight
     */
    MISSample sampleBSDF(const Intersection& isect, const Scene& scene,
                        Sampler& sampler, const Vec3& wo) const;
    
    /**
     * @brief Calculate MIS weight using power heuristic
     * 
     * @param nf Number of samples from technique f
     * @param fPdf PDF of technique f
     * @param ng Number of samples from technique g  
     * @param gPdf PDF of technique g
     * @param beta Power parameter (default: 2)
     * @return MIS weight for technique f
     */
    static float powerHeuristic(int nf, float fPdf, int ng, float gPdf, float beta = 2.0f);
    
    /**
     * @brief Calculate MIS weight using balance heuristic
     * 
     * @param nf Number of samples from technique f
     * @param fPdf PDF of technique f
     * @param ng Number of samples from technique g
     * @param gPdf PDF of technique g
     * @return MIS weight for technique f
     */
    static float balanceHeuristic(int nf, float fPdf, int ng, float gPdf);
    
    /**
     * @brief Calculate optimal MIS weight (adaptive)
     * 
     * Automatically selects best heuristic based on PDF ratios
     * 
     * @param nf Number of samples from technique f
     * @param fPdf PDF of technique f
     * @param ng Number of samples from technique g
     * @param gPdf PDF of technique g
     * @return Optimal MIS weight for technique f
     */
    static float optimalHeuristic(int nf, float fPdf, int ng, float gPdf);
    
    /**
     * @brief Set MIS strategy
     */
    void setStrategy(MISStrategy strategy) { m_strategy = strategy; }
    
    /**
     * @brief Get current MIS strategy
     */
    MISStrategy getStrategy() const { return m_strategy; }
    
    /**
     * @brief Set number of light samples
     */
    void setLightSamples(int samples) { m_lightSamples = samples; }
    
    /**
     * @brief Set number of BSDF samples
     */
    void setBSDFSamples(int samples) { m_bsdfSamples = samples; }
    
    /**
     * @brief Get performance statistics
     */
    struct Statistics {
        float avgNoiseReduction;    ///< Average noise reduction percentage
        float avgMISOverhead;       ///< Average MIS overhead in nanoseconds
        int totalSamples;           ///< Total samples processed
        int lightSamples;           ///< Light samples taken
        int bsdfSamples;            ///< BSDF samples taken
    };
    
    /**
     * @brief Get performance statistics
     */
    Statistics getStatistics() const { return m_stats; }
    
    /**
     * @brief Reset performance statistics
     */
    void resetStatistics();

private:
    MISStrategy m_strategy;         ///< MIS strategy to use
    int m_lightSamples;             ///< Number of light samples
    int m_bsdfSamples;              ///< Number of BSDF samples
    mutable Statistics m_stats;     ///< Performance statistics
    
    /**
     * @brief Calculate MIS weight based on current strategy
     */
    float calculateMISWeight(int nf, float fPdf, int ng, float gPdf) const;
    
    /**
     * @brief Evaluate light contribution at intersection
     */
    Color3 evaluateLightContribution(const Intersection& isect, const Light& light,
                                    const Vec3& wi, const Vec3& wo) const;
    
    /**
     * @brief Check visibility between two points
     */
    bool isVisible(const Scene& scene, const Vec3& p1, const Vec3& p2) const;
    
    /**
     * @brief Update performance statistics
     */
    void updateStatistics(float noiseReduction, float overhead) const;

    /**
     * @brief Select light based on importance sampling
     */
    int selectLightImportance(const std::vector<std::shared_ptr<Light>>& lights,
                             const Intersection& isect, Sampler& sampler) const;

    /**
     * @brief Calculate noise reduction percentage
     */
    float calculateNoiseReduction(const Color3& result, float totalWeight, int numSamples) const;
};

} // namespace photon
