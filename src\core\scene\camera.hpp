// src/core/scene/camera.hpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Camera system for ray generation

#pragma once

#include "../math/vec3.hpp"
#include "../math/ray.hpp"
#include <memory>

namespace photon {

// Forward declarations
class Sampler;
class Transform;

/**
 * @brief Abstract base camera class
 */
class Camera {
public:
    Camera() = default;
    virtual ~Camera() = default;
    
    // Non-copyable
    Camera(const Camera&) = delete;
    Camera& operator=(const Camera&) = delete;
    
    /**
     * @brief Generate camera ray for given screen coordinates
     * 
     * @param u Screen coordinate U [0,1]
     * @param v Screen coordinate V [0,1]  
     * @param sampler Random sampler for anti-aliasing
     * @return Generated camera ray
     */
    virtual Ray generateRay(float u, float v, Sampler& sampler) const = 0;
    
    /**
     * @brief Generate ray differential for texture filtering
     */
    virtual RayDifferential generateRayDifferential(float u, float v, Sampler& sampler) const;
    
    /**
     * @brief Get camera position in world space
     */
    virtual Point3 getPosition() const = 0;
    
    /**
     * @brief Get camera forward direction
     */
    virtual Vec3 getForward() const = 0;
    
    /**
     * @brief Get camera up direction
     */
    virtual Vec3 getUp() const = 0;
    
    /**
     * @brief Get camera right direction
     */
    virtual Vec3 getRight() const = 0;
    
    /**
     * @brief Set camera transform
     */
    virtual void setTransform(const Point3& position, const Point3& target, const Vec3& up) = 0;
    
    /**
     * @brief Get field of view in degrees
     */
    virtual float getFOV() const = 0;
    
    /**
     * @brief Set field of view in degrees
     */
    virtual void setFOV(float fov) = 0;
    
    /**
     * @brief Get aspect ratio (width/height)
     */
    virtual float getAspectRatio() const = 0;
    
    /**
     * @brief Set aspect ratio (width/height)
     */
    virtual void setAspectRatio(float aspect) = 0;
};

/**
 * @brief Perspective camera implementation
 */
class PerspectiveCamera : public Camera {
public:
    /**
     * @brief Constructor
     * 
     * @param position Camera position
     * @param target Look-at target
     * @param up Up vector
     * @param fov Field of view in degrees
     * @param aspect Aspect ratio (width/height)
     * @param nearPlane Near clipping plane
     * @param farPlane Far clipping plane
     */
    PerspectiveCamera(const Point3& position = Point3(0, 0, 1),
                     const Point3& target = Point3(0, 0, 0),
                     const Vec3& up = Vec3(0, 1, 0),
                     float fov = 45.0f,
                     float aspect = 16.0f / 9.0f,
                     float nearPlane = 0.1f,
                     float farPlane = 1000.0f);
    
    // Camera interface implementation
    Ray generateRay(float u, float v, Sampler& sampler) const override;
    RayDifferential generateRayDifferential(float u, float v, Sampler& sampler) const override;
    
    Point3 getPosition() const override { return m_position; }
    Vec3 getForward() const override { return m_forward; }
    Vec3 getUp() const override { return m_up; }
    Vec3 getRight() const override { return m_right; }
    
    void setTransform(const Point3& position, const Point3& target, const Vec3& up) override;
    
    float getFOV() const override { return m_fov; }
    void setFOV(float fov) override;
    
    float getAspectRatio() const override { return m_aspectRatio; }
    void setAspectRatio(float aspect) override;
    
    /**
     * @brief Get near clipping plane distance
     */
    float getNearPlane() const { return m_nearPlane; }
    
    /**
     * @brief Set near clipping plane distance
     */
    void setNearPlane(float nearPlane) { m_nearPlane = nearPlane; }
    
    /**
     * @brief Get far clipping plane distance
     */
    float getFarPlane() const { return m_farPlane; }
    
    /**
     * @brief Set far clipping plane distance
     */
    void setFarPlane(float farPlane) { m_farPlane = farPlane; }
    
    /**
     * @brief Enable/disable depth of field
     */
    void setDepthOfField(float focalDistance, float aperture);
    
    /**
     * @brief Disable depth of field
     */
    void disableDepthOfField();
    
    /**
     * @brief Check if depth of field is enabled
     */
    bool hasDepthOfField() const { return m_aperture > 0.0f; }

private:
    // Camera parameters
    Point3 m_position;
    Point3 m_target;
    Vec3 m_up, m_right, m_forward;
    
    float m_fov;           // Field of view in degrees
    float m_aspectRatio;   // Width / height
    float m_nearPlane;     // Near clipping plane
    float m_farPlane;      // Far clipping plane
    
    // Depth of field parameters
    float m_focalDistance = 0.0f;
    float m_aperture = 0.0f;
    
    // Cached values for ray generation
    float m_tanHalfFOV;
    float m_imageWidth, m_imageHeight;
    
    /**
     * @brief Update internal camera matrices and cached values
     */
    void updateCameraVectors();
    
    /**
     * @brief Update cached projection values
     */
    void updateProjection();
    
    /**
     * @brief Sample point on aperture for depth of field
     */
    Point3 sampleAperture(Sampler& sampler) const;
};

/**
 * @brief Orthographic camera implementation
 */
class OrthographicCamera : public Camera {
public:
    /**
     * @brief Constructor
     */
    OrthographicCamera(const Point3& position = Point3(0, 0, 1),
                      const Point3& target = Point3(0, 0, 0),
                      const Vec3& up = Vec3(0, 1, 0),
                      float width = 2.0f,
                      float height = 2.0f,
                      float nearPlane = 0.1f,
                      float farPlane = 1000.0f);
    
    // Camera interface implementation
    Ray generateRay(float u, float v, Sampler& sampler) const override;
    
    Point3 getPosition() const override { return m_position; }
    Vec3 getForward() const override { return m_forward; }
    Vec3 getUp() const override { return m_up; }
    Vec3 getRight() const override { return m_right; }
    
    void setTransform(const Point3& position, const Point3& target, const Vec3& up) override;
    
    float getFOV() const override { return 0.0f; } // Not applicable for orthographic
    void setFOV(float fov) override {} // Not applicable
    
    float getAspectRatio() const override { return m_width / m_height; }
    void setAspectRatio(float aspect) override;
    
    /**
     * @brief Get orthographic width
     */
    float getWidth() const { return m_width; }
    
    /**
     * @brief Set orthographic width
     */
    void setWidth(float width) { m_width = width; }
    
    /**
     * @brief Get orthographic height
     */
    float getHeight() const { return m_height; }
    
    /**
     * @brief Set orthographic height
     */
    void setHeight(float height) { m_height = height; }

private:
    Point3 m_position;
    Point3 m_target;
    Vec3 m_up, m_right, m_forward;
    
    float m_width, m_height;
    float m_nearPlane, m_farPlane;
    
    void updateCameraVectors();
};

} // namespace photon
