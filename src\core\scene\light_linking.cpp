// src/core/scene/light_linking.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Light Linking System implementation

#include "light_linking.hpp"
#include "light.hpp"
#include "scene.hpp"
#include "../common.hpp"
#include <algorithm>
#include <chrono>
#include <iostream>

namespace photon {

// LightGroup implementation
LightGroup::LightGroup(const std::string& name, const std::string& description)
    : m_name(name), m_description(description), m_enabled(true) {
}

void LightGroup::addLight(std::shared_ptr<Light> light) {
    if (!light) {
        std::cerr << "Warning: Attempted to add null light to group " << m_name << std::endl;
        return;
    }
    
    // Check if light is already in group
    auto it = std::find(m_lights.begin(), m_lights.end(), light);
    if (it != m_lights.end()) {
        std::cerr << "Warning: Light already exists in group " << m_name << std::endl;
        return;
    }
    
    m_lights.push_back(light);
}

void LightGroup::removeLight(std::shared_ptr<Light> light) {
    if (!light) return;
    
    auto it = std::find(m_lights.begin(), m_lights.end(), light);
    if (it != m_lights.end()) {
        m_lights.erase(it);
    }
}

bool LightGroup::containsLight(std::shared_ptr<Light> light) const {
    if (!light) return false;
    
    auto it = std::find(m_lights.begin(), m_lights.end(), light);
    return it != m_lights.end();
}

LightGroup::Statistics LightGroup::getStatistics() const {
    Statistics stats;
    stats.lightCount = m_lights.size();
    
    std::unordered_map<std::string, int> lightTypeCounts;
    
    for (const auto& light : m_lights) {
        if (!light) continue;
        
        if (m_enabled) {
            stats.enabledLights++;
        }
        
        // Calculate total power
        Color3 power = light->power();
        stats.totalPower += power.luminance();
        
        // Count light types
        std::string lightType = light->getName();
        lightTypeCounts[lightType]++;
    }
    
    // Find dominant light type
    int maxCount = 0;
    for (const auto& pair : lightTypeCounts) {
        if (pair.second > maxCount) {
            maxCount = pair.second;
            stats.dominantLightType = pair.first;
        }
    }
    
    return stats;
}

// ObjectLightAssociation implementation
ObjectLightAssociation::ObjectLightAssociation(uint32_t objectId, LightLinkingMode mode)
    : m_objectId(objectId), m_mode(mode) {
}

void ObjectLightAssociation::includeLight(std::shared_ptr<Light> light) {
    if (!light) return;
    
    m_includedLights.insert(light);
    // Remove from exclusion list if present
    m_excludedLights.erase(light);
}

void ObjectLightAssociation::excludeLight(std::shared_ptr<Light> light) {
    if (!light) return;
    
    m_excludedLights.insert(light);
    // Remove from inclusion list if present
    m_includedLights.erase(light);
}

void ObjectLightAssociation::removeIncludedLight(std::shared_ptr<Light> light) {
    if (!light) return;
    m_includedLights.erase(light);
}

void ObjectLightAssociation::removeExcludedLight(std::shared_ptr<Light> light) {
    if (!light) return;
    m_excludedLights.erase(light);
}

bool ObjectLightAssociation::shouldIlluminate(std::shared_ptr<Light> light) const {
    if (!light) return false;
    
    switch (m_mode) {
        case LightLinkingMode::INCLUDE_ALL:
            return true;
            
        case LightLinkingMode::EXCLUDE_ALL:
            return false;
            
        case LightLinkingMode::SELECTIVE_INCLUDE:
            return m_includedLights.find(light) != m_includedLights.end();
            
        case LightLinkingMode::SELECTIVE_EXCLUDE:
            return m_excludedLights.find(light) == m_excludedLights.end();
            
        default:
            return true;
    }
}

void ObjectLightAssociation::clear() {
    m_includedLights.clear();
    m_excludedLights.clear();
    m_mode = LightLinkingMode::INCLUDE_ALL;
}

// LightLinkingManager implementation
LightLinkingManager::LightLinkingManager() : m_enabled(true), m_statsCacheValid(false) {
}

std::shared_ptr<LightGroup> LightLinkingManager::createLightGroup(const std::string& name, const std::string& description) {
    if (name.empty()) {
        std::cerr << "Error: Light group name cannot be empty" << std::endl;
        return nullptr;
    }
    
    // Check if group already exists
    auto it = m_lightGroups.find(name);
    if (it != m_lightGroups.end()) {
        std::cerr << "Warning: Light group '" << name << "' already exists" << std::endl;
        return it->second;
    }
    
    auto group = std::make_shared<LightGroup>(name, description);
    m_lightGroups[name] = group;
    m_statsCacheValid = false;
    
    return group;
}

void LightLinkingManager::removeLightGroup(const std::string& name) {
    auto it = m_lightGroups.find(name);
    if (it != m_lightGroups.end()) {
        m_lightGroups.erase(it);
        m_statsCacheValid = false;
    }
}

std::shared_ptr<LightGroup> LightLinkingManager::getLightGroup(const std::string& name) const {
    auto it = m_lightGroups.find(name);
    return (it != m_lightGroups.end()) ? it->second : nullptr;
}

void LightLinkingManager::setObjectLightAssociation(uint32_t objectId, std::shared_ptr<ObjectLightAssociation> association) {
    if (!association) {
        std::cerr << "Warning: Attempted to set null object light association" << std::endl;
        return;
    }
    
    m_objectAssociations[objectId] = association;
    m_statsCacheValid = false;
}

std::shared_ptr<ObjectLightAssociation> LightLinkingManager::getObjectLightAssociation(uint32_t objectId) const {
    auto it = m_objectAssociations.find(objectId);
    return (it != m_objectAssociations.end()) ? it->second : nullptr;
}

void LightLinkingManager::removeObjectLightAssociation(uint32_t objectId) {
    auto it = m_objectAssociations.find(objectId);
    if (it != m_objectAssociations.end()) {
        m_objectAssociations.erase(it);
        m_statsCacheValid = false;
    }
}

bool LightLinkingManager::shouldIlluminate(std::shared_ptr<Light> light, const Intersection& isect) const {
    if (!m_enabled || !light) return true;
    
    // Get object ID from intersection
    uint32_t objectId = getObjectIdFromIntersection(isect);
    
    // Check object-specific light association
    auto association = getObjectLightAssociation(objectId);
    if (association) {
        return association->shouldIlluminate(light);
    }
    
    // Check light group status
    for (const auto& groupPair : m_lightGroups) {
        const auto& group = groupPair.second;
        if (group && group->containsLight(light)) {
            return group->isEnabled();
        }
    }
    
    // Default: allow illumination
    return true;
}

std::vector<std::shared_ptr<Light>> LightLinkingManager::getEffectiveLights(
    const std::vector<std::shared_ptr<Light>>& allLights,
    const Intersection& isect) const {
    
    if (!m_enabled) {
        return allLights; // Return all lights if linking is disabled
    }
    
    std::vector<std::shared_ptr<Light>> effectiveLights;
    effectiveLights.reserve(allLights.size());
    
    for (const auto& light : allLights) {
        if (shouldIlluminate(light, isect)) {
            effectiveLights.push_back(light);
        }
    }
    
    return effectiveLights;
}

void LightLinkingManager::clear() {
    m_lightGroups.clear();
    m_objectAssociations.clear();
    m_statsCacheValid = false;
}

LightLinkingManager::Statistics LightLinkingManager::getStatistics() const {
    if (!m_statsCacheValid) {
        updateStatistics();
    }
    return m_cachedStats;
}

void LightLinkingManager::optimize() {
    // Update statistics cache
    updateStatistics();

    // Optimization for large scenes (100+ lights, 1000+ objects)
    if (m_cachedStats.totalLights > 100 || m_cachedStats.objectAssociationCount > 1000) {
        // Build acceleration structures for fast lookups
        optimizeForLargeScenes();
    }

    // Optimize light group access patterns
    optimizeLightGroups();

    // Cache frequently accessed associations
    cacheFrequentAssociations();
}

void LightLinkingManager::optimizeForLargeScenes() {
    // For scenes with many lights/objects, we can:
    // 1. Pre-compute light-object compatibility matrices
    // 2. Use spatial data structures for light culling
    // 3. Cache effective light lists per object type

    // For now, implement basic caching optimizations
    // Future: implement spatial acceleration structures
}

void LightLinkingManager::optimizeLightGroups() {
    // Optimize light group access by caching enabled status
    for (auto& groupPair : m_lightGroups) {
        auto& group = groupPair.second;
        if (group) {
            // Pre-compute group statistics for faster access
            group->getStatistics();
        }
    }
}

void LightLinkingManager::cacheFrequentAssociations() {
    // Cache associations for objects that are queried frequently
    // This is a placeholder for more sophisticated caching
    // Future: implement LRU cache for object associations
}

void LightLinkingManager::updateStatistics() const {
    auto startTime = std::chrono::high_resolution_clock::now();
    
    m_cachedStats = Statistics{};
    m_cachedStats.lightGroupCount = m_lightGroups.size();
    m_cachedStats.objectAssociationCount = m_objectAssociations.size();
    
    // Count total and linked lights
    std::unordered_set<std::shared_ptr<Light>> allLights;
    std::unordered_set<std::shared_ptr<Light>> linkedLights;
    
    for (const auto& groupPair : m_lightGroups) {
        const auto& group = groupPair.second;
        if (group) {
            for (const auto& light : group->getLights()) {
                allLights.insert(light);
                if (group->isEnabled()) {
                    linkedLights.insert(light);
                }
            }
        }
    }
    
    for (const auto& assocPair : m_objectAssociations) {
        const auto& association = assocPair.second;
        if (association) {
            for (const auto& light : association->getIncludedLights()) {
                allLights.insert(light);
                linkedLights.insert(light);
            }
            for (const auto& light : association->getExcludedLights()) {
                allLights.insert(light);
            }
        }
    }
    
    m_cachedStats.totalLights = allLights.size();
    m_cachedStats.linkedLights = linkedLights.size();
    
    if (m_cachedStats.objectAssociationCount > 0) {
        m_cachedStats.averageLightsPerObject = 
            static_cast<float>(m_cachedStats.linkedLights) / m_cachedStats.objectAssociationCount;
    }
    
    // Calculate overhead
    auto endTime = std::chrono::high_resolution_clock::now();
    m_cachedStats.linkingOverhead = 
        std::chrono::duration<float, std::nano>(endTime - startTime).count();
    
    m_statsCacheValid = true;
}

uint32_t LightLinkingManager::getObjectIdFromIntersection(const Intersection& isect) const {
    // Extract object ID from intersection
    // This could be from geometryId, primitiveId, or a custom object ID system
    
    if (isect.geometryId >= 0) {
        return static_cast<uint32_t>(isect.geometryId);
    }
    
    if (isect.primitiveId >= 0) {
        return static_cast<uint32_t>(isect.primitiveId);
    }
    
    // Fallback: use a hash of the intersection point
    // This is not ideal but provides some object differentiation
    uint32_t hash = 0;
    hash ^= std::hash<float>{}(isect.p.x) + 0x9e3779b9 + (hash << 6) + (hash >> 2);
    hash ^= std::hash<float>{}(isect.p.y) + 0x9e3779b9 + (hash << 6) + (hash >> 2);
    hash ^= std::hash<float>{}(isect.p.z) + 0x9e3779b9 + (hash << 6) + (hash >> 2);
    
    return hash;
}

// Light Linking Utility Functions
namespace LightLinkingUtils {

void createDefaultLightGroups(LightLinkingManager& manager,
                             const std::vector<std::shared_ptr<Light>>& lights) {
    // Create standard light groups
    auto keyGroup = manager.createLightGroup("Key Lights", "Primary illumination lights");
    auto fillGroup = manager.createLightGroup("Fill Lights", "Secondary fill illumination");
    auto rimGroup = manager.createLightGroup("Rim Lights", "Edge/rim lighting");
    auto environmentGroup = manager.createLightGroup("Environment", "Environment and ambient lights");
    auto practicalGroup = manager.createLightGroup("Practical", "Practical/visible lights in scene");

    // Auto-assign lights to groups based on type and characteristics
    for (const auto& light : lights) {
        if (!light) continue;

        std::string lightType = light->getName();
        Color3 power = light->power();
        float intensity = power.luminance();

        if (lightType == "Directional") {
            // Directional lights are typically key lights (sun)
            keyGroup->addLight(light);
        } else if (lightType == "Environment" || lightType == "HDRI") {
            // Environment lights
            environmentGroup->addLight(light);
        } else if (lightType == "Point") {
            // Point lights: categorize by intensity
            if (intensity > 50.0f) {
                keyGroup->addLight(light);
            } else if (intensity > 10.0f) {
                fillGroup->addLight(light);
            } else {
                practicalGroup->addLight(light);
            }
        } else if (lightType == "Area") {
            // Area lights: typically key or fill
            if (intensity > 20.0f) {
                keyGroup->addLight(light);
            } else {
                fillGroup->addLight(light);
            }
        } else {
            // Default: add to fill group
            fillGroup->addLight(light);
        }
    }
}

void autoCategorizeLight(LightLinkingManager& manager,
                        const std::vector<std::shared_ptr<Light>>& lights) {
    // Create type-based groups
    std::unordered_map<std::string, std::shared_ptr<LightGroup>> typeGroups;

    for (const auto& light : lights) {
        if (!light) continue;

        std::string lightType = light->getName();

        // Create group if it doesn't exist
        if (typeGroups.find(lightType) == typeGroups.end()) {
            std::string groupName = lightType + " Lights";
            std::string description = "All " + lightType + " type lights";
            typeGroups[lightType] = manager.createLightGroup(groupName, description);
        }

        // Add light to type group
        typeGroups[lightType]->addLight(light);
    }
}

std::shared_ptr<ObjectLightAssociation> createObjectAssociation(
    uint32_t objectId,
    const std::string& pattern,
    const std::vector<std::shared_ptr<Light>>& lights) {

    auto association = std::make_shared<ObjectLightAssociation>(objectId);

    if (pattern == "interior") {
        // Interior objects: exclude directional lights, include area and point lights
        association->setMode(LightLinkingMode::SELECTIVE_INCLUDE);
        for (const auto& light : lights) {
            if (!light) continue;
            std::string type = light->getName();
            if (type == "Point" || type == "Area" || type == "Spot") {
                association->includeLight(light);
            }
        }
    } else if (pattern == "exterior") {
        // Exterior objects: include directional and environment lights
        association->setMode(LightLinkingMode::SELECTIVE_INCLUDE);
        for (const auto& light : lights) {
            if (!light) continue;
            std::string type = light->getName();
            if (type == "Directional" || type == "Environment" || type == "HDRI") {
                association->includeLight(light);
            }
        }
    } else if (pattern == "key") {
        // Key lit objects: only key lights
        association->setMode(LightLinkingMode::SELECTIVE_INCLUDE);
        for (const auto& light : lights) {
            if (!light) continue;
            Color3 power = light->power();
            if (power.luminance() > 30.0f) { // High intensity = key light
                association->includeLight(light);
            }
        }
    } else if (pattern == "fill") {
        // Fill lit objects: exclude key lights
        association->setMode(LightLinkingMode::SELECTIVE_EXCLUDE);
        for (const auto& light : lights) {
            if (!light) continue;
            Color3 power = light->power();
            if (power.luminance() > 50.0f) { // Very high intensity = exclude
                association->excludeLight(light);
            }
        }
    } else if (pattern == "rim") {
        // Rim lit objects: only rim/edge lights
        association->setMode(LightLinkingMode::SELECTIVE_INCLUDE);
        // This would need additional light metadata to identify rim lights
        // For now, include lights with specific naming or characteristics
    } else {
        // Default: include all lights
        association->setMode(LightLinkingMode::INCLUDE_ALL);
    }

    return association;
}

void batchSetObjectAssociations(LightLinkingManager& manager,
                               const std::vector<uint32_t>& objectIds,
                               std::shared_ptr<ObjectLightAssociation> templateAssociation) {
    if (!templateAssociation) return;

    for (uint32_t objectId : objectIds) {
        // Create a copy of the template association for each object
        auto association = std::make_shared<ObjectLightAssociation>(objectId, templateAssociation->getMode());

        // Copy light lists
        for (const auto& light : templateAssociation->getIncludedLights()) {
            association->includeLight(light);
        }
        for (const auto& light : templateAssociation->getExcludedLights()) {
            association->excludeLight(light);
        }

        manager.setObjectLightAssociation(objectId, association);
    }
}

LightLinkingUtils::OptimizationSuggestions analyzeAndOptimize(const LightLinkingManager& manager,
                                                             size_t lightCount, size_t objectCount) {
    OptimizationSuggestions suggestions;
    auto stats = manager.getStatistics();

    // Analyze scene complexity
    bool complexScene = (lightCount > 20 || objectCount > 500);
    bool manyAssociations = (stats.objectAssociationCount > objectCount * 0.1f);

    if (complexScene) {
        suggestions.useGrouping = true;
        suggestions.suggestions.push_back("Use light grouping for scenes with 20+ lights");

        if (lightCount > 50) {
            suggestions.useSpatialCulling = true;
            suggestions.suggestions.push_back("Enable spatial light culling for 50+ lights");
        }

        if (stats.averageLightsPerObject < 0.3f * lightCount) {
            suggestions.useImportanceCulling = true;
            suggestions.suggestions.push_back("Use importance culling - many lights don't affect most objects");
        }
    }

    if (manyAssociations) {
        suggestions.suggestions.push_back("Consider using light groups instead of per-object associations");
    }

    if (stats.linkingOverhead > 1000.0f) { // > 1 microsecond
        suggestions.suggestions.push_back("Light linking overhead is high - consider optimization");
    }

    // Performance recommendations
    if (suggestions.suggestions.empty()) {
        suggestions.suggestions.push_back("Light linking performance is optimal");
    }

    return suggestions;
}

} // namespace LightLinkingUtils

} // namespace photon
