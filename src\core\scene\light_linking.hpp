// src/core/scene/light_linking.hpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Light Linking System for selective illumination control

#pragma once

#include "../math/vec3.hpp"
#include <memory>
#include <vector>
#include <unordered_set>
#include <unordered_map>
#include <string>

namespace photon {

// Forward declarations
class Light;
class Mesh;
class Scene;
struct Intersection;

/**
 * @brief Light linking mode enumeration
 */
enum class LightLinkingMode {
    INCLUDE_ALL,        ///< Include all lights (default)
    EXCLUDE_ALL,        ///< Exclude all lights
    SELECTIVE_INCLUDE,  ///< Include only specified lights
    SELECTIVE_EXCLUDE   ///< Exclude only specified lights
};

/**
 * @brief Light group for organizing lights
 */
class LightGroup {
public:
    /**
     * @brief Constructor
     * 
     * @param name Group name
     * @param description Optional description
     */
    LightGroup(const std::string& name, const std::string& description = "");
    
    /**
     * @brief Destructor
     */
    ~LightGroup() = default;
    
    // Non-copyable
    LightGroup(const LightGroup&) = delete;
    LightGroup& operator=(const LightGroup&) = delete;
    
    /**
     * @brief Add light to group
     */
    void addLight(std::shared_ptr<Light> light);
    
    /**
     * @brief Remove light from group
     */
    void removeLight(std::shared_ptr<Light> light);
    
    /**
     * @brief Check if light is in group
     */
    bool containsLight(std::shared_ptr<Light> light) const;
    
    /**
     * @brief Get all lights in group
     */
    const std::vector<std::shared_ptr<Light>>& getLights() const { return m_lights; }
    
    /**
     * @brief Get group name
     */
    const std::string& getName() const { return m_name; }
    
    /**
     * @brief Get group description
     */
    const std::string& getDescription() const { return m_description; }
    
    /**
     * @brief Set group enabled/disabled
     */
    void setEnabled(bool enabled) { m_enabled = enabled; }
    
    /**
     * @brief Check if group is enabled
     */
    bool isEnabled() const { return m_enabled; }
    
    /**
     * @brief Get group statistics
     */
    struct Statistics {
        size_t lightCount = 0;
        size_t enabledLights = 0;
        float totalPower = 0.0f;
        std::string dominantLightType;
    };
    
    Statistics getStatistics() const;

private:
    std::string m_name;
    std::string m_description;
    std::vector<std::shared_ptr<Light>> m_lights;
    bool m_enabled = true;
};

/**
 * @brief Object light association for per-object light control
 */
class ObjectLightAssociation {
public:
    /**
     * @brief Constructor
     * 
     * @param objectId Unique object identifier
     * @param mode Light linking mode
     */
    ObjectLightAssociation(uint32_t objectId, LightLinkingMode mode = LightLinkingMode::INCLUDE_ALL);
    
    /**
     * @brief Set light linking mode
     */
    void setMode(LightLinkingMode mode) { m_mode = mode; }
    
    /**
     * @brief Get light linking mode
     */
    LightLinkingMode getMode() const { return m_mode; }
    
    /**
     * @brief Add light to inclusion list
     */
    void includeLight(std::shared_ptr<Light> light);
    
    /**
     * @brief Add light to exclusion list
     */
    void excludeLight(std::shared_ptr<Light> light);
    
    /**
     * @brief Remove light from inclusion list
     */
    void removeIncludedLight(std::shared_ptr<Light> light);
    
    /**
     * @brief Remove light from exclusion list
     */
    void removeExcludedLight(std::shared_ptr<Light> light);
    
    /**
     * @brief Check if light should illuminate this object
     * 
     * @param light Light to check
     * @return True if light should illuminate object
     */
    bool shouldIlluminate(std::shared_ptr<Light> light) const;
    
    /**
     * @brief Get object ID
     */
    uint32_t getObjectId() const { return m_objectId; }
    
    /**
     * @brief Get included lights
     */
    const std::unordered_set<std::shared_ptr<Light>>& getIncludedLights() const { return m_includedLights; }
    
    /**
     * @brief Get excluded lights
     */
    const std::unordered_set<std::shared_ptr<Light>>& getExcludedLights() const { return m_excludedLights; }
    
    /**
     * @brief Clear all light associations
     */
    void clear();

private:
    uint32_t m_objectId;
    LightLinkingMode m_mode;
    std::unordered_set<std::shared_ptr<Light>> m_includedLights;
    std::unordered_set<std::shared_ptr<Light>> m_excludedLights;
};

/**
 * @brief Light linking manager for the entire scene
 */
class LightLinkingManager {
public:
    /**
     * @brief Constructor
     */
    LightLinkingManager();
    
    /**
     * @brief Destructor
     */
    ~LightLinkingManager() = default;
    
    // Non-copyable
    LightLinkingManager(const LightLinkingManager&) = delete;
    LightLinkingManager& operator=(const LightLinkingManager&) = delete;
    
    /**
     * @brief Create light group
     */
    std::shared_ptr<LightGroup> createLightGroup(const std::string& name, const std::string& description = "");
    
    /**
     * @brief Remove light group
     */
    void removeLightGroup(const std::string& name);
    
    /**
     * @brief Get light group by name
     */
    std::shared_ptr<LightGroup> getLightGroup(const std::string& name) const;
    
    /**
     * @brief Get all light groups
     */
    const std::unordered_map<std::string, std::shared_ptr<LightGroup>>& getLightGroups() const { return m_lightGroups; }
    
    /**
     * @brief Set object light association
     */
    void setObjectLightAssociation(uint32_t objectId, std::shared_ptr<ObjectLightAssociation> association);
    
    /**
     * @brief Get object light association
     */
    std::shared_ptr<ObjectLightAssociation> getObjectLightAssociation(uint32_t objectId) const;
    
    /**
     * @brief Remove object light association
     */
    void removeObjectLightAssociation(uint32_t objectId);
    
    /**
     * @brief Check if light should illuminate object at intersection
     * 
     * @param light Light to check
     * @param isect Intersection information
     * @return True if light should illuminate the intersected object
     */
    bool shouldIlluminate(std::shared_ptr<Light> light, const Intersection& isect) const;
    
    /**
     * @brief Get effective lights for intersection
     * 
     * Filters scene lights based on light linking rules
     * 
     * @param allLights All lights in scene
     * @param isect Intersection information
     * @return Vector of lights that should illuminate the intersection
     */
    std::vector<std::shared_ptr<Light>> getEffectiveLights(
        const std::vector<std::shared_ptr<Light>>& allLights,
        const Intersection& isect) const;
    
    /**
     * @brief Enable/disable light linking globally
     */
    void setEnabled(bool enabled) { m_enabled = enabled; }
    
    /**
     * @brief Check if light linking is enabled
     */
    bool isEnabled() const { return m_enabled; }
    
    /**
     * @brief Clear all light linking data
     */
    void clear();
    
    /**
     * @brief Get light linking statistics
     */
    struct Statistics {
        size_t lightGroupCount = 0;
        size_t objectAssociationCount = 0;
        size_t totalLights = 0;
        size_t linkedLights = 0;
        float averageLightsPerObject = 0.0f;
        float linkingOverhead = 0.0f; // in nanoseconds
    };
    
    Statistics getStatistics() const;
    
    /**
     * @brief Optimize light linking for performance
     * 
     * Builds acceleration structures for fast light queries
     */
    void optimize();

private:
    bool m_enabled = true;
    std::unordered_map<std::string, std::shared_ptr<LightGroup>> m_lightGroups;
    std::unordered_map<uint32_t, std::shared_ptr<ObjectLightAssociation>> m_objectAssociations;
    
    // Performance optimization
    mutable Statistics m_cachedStats;
    mutable bool m_statsCacheValid = false;
    
    /**
     * @brief Update statistics cache
     */
    void updateStatistics() const;
    
    /**
     * @brief Get object ID from intersection
     */
    uint32_t getObjectIdFromIntersection(const Intersection& isect) const;

    /**
     * @brief Optimize for large scenes (100+ lights, 1000+ objects)
     */
    void optimizeForLargeScenes();

    /**
     * @brief Optimize light group access patterns
     */
    void optimizeLightGroups();

    /**
     * @brief Cache frequently accessed associations
     */
    void cacheFrequentAssociations();
};

/**
 * @brief Light linking utility functions
 */
namespace LightLinkingUtils {

    /**
     * @brief Create default light groups for common scenarios
     *
     * @param manager Light linking manager
     * @param lights All lights in scene
     */
    void createDefaultLightGroups(LightLinkingManager& manager,
                                 const std::vector<std::shared_ptr<Light>>& lights);

    /**
     * @brief Auto-categorize lights into groups based on type
     *
     * @param manager Light linking manager
     * @param lights All lights in scene
     */
    void autoCategorizeLight(LightLinkingManager& manager,
                            const std::vector<std::shared_ptr<Light>>& lights);

    /**
     * @brief Create object association with common patterns
     *
     * @param objectId Object identifier
     * @param pattern Association pattern ("interior", "exterior", "key", "fill", "rim")
     * @param lights Available lights
     * @return Configured object light association
     */
    std::shared_ptr<ObjectLightAssociation> createObjectAssociation(
        uint32_t objectId,
        const std::string& pattern,
        const std::vector<std::shared_ptr<Light>>& lights);

    /**
     * @brief Batch set object associations for multiple objects
     *
     * @param manager Light linking manager
     * @param objectIds List of object IDs
     * @param association Association to apply to all objects
     */
    void batchSetObjectAssociations(LightLinkingManager& manager,
                                   const std::vector<uint32_t>& objectIds,
                                   std::shared_ptr<ObjectLightAssociation> association);

    /**
     * @brief Optimize light linking for performance
     *
     * Analyzes scene and suggests optimizations
     *
     * @param manager Light linking manager
     * @param sceneStats Scene statistics
     * @return Optimization suggestions
     */
    struct OptimizationSuggestions {
        bool useGrouping = false;
        bool useSpatialCulling = false;
        bool useImportanceCulling = false;
        std::vector<std::string> suggestions;
    };

    OptimizationSuggestions analyzeAndOptimize(const LightLinkingManager& manager,
                                              size_t lightCount, size_t objectCount);
}

} // namespace photon
