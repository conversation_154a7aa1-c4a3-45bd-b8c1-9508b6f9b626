// src/core/scene/scene.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Scene management implementation

#include "scene.hpp"
#include "../common.hpp"
#include <iostream>
#include <fstream>
#include <algorithm>
#include <limits>

namespace photon {

// Note: Mesh implementation is now in geometry/mesh.cpp
// This file only contains Scene implementation

// Scene implementation
Scene::Scene() {
    m_bounds.min = Point3(std::numeric_limits<float>::max());
    m_bounds.max = Point3(std::numeric_limits<float>::lowest());
}

Scene::~Scene() {
    clear();
}

void Scene::addMesh(std::shared_ptr<Mesh> mesh) {
    if (!mesh || !mesh->validate()) {
        std::cerr << "Warning: Attempted to add invalid mesh to scene" << std::endl;
        return;
    }

    m_meshes.push_back(mesh);
    m_accelerationBuilt = false;
    updateBounds();
}

void Scene::addMaterial(const std::string& name, std::shared_ptr<Material> material) {
    if (!material) {
        std::cerr << "Warning: Attempted to add null material: " << name << std::endl;
        return;
    }
    
    m_materials[name] = material;
}

void Scene::addLight(std::shared_ptr<Light> light) {
    if (!light) {
        std::cerr << "Warning: Attempted to add null light to scene" << std::endl;
        return;
    }
    
    m_lights.push_back(light);
}

std::vector<std::shared_ptr<Light>> Scene::getEffectiveLights(const Intersection& isect) const {
    return m_lightLinkingManager.getEffectiveLights(m_lights, isect);
}

std::shared_ptr<Material> Scene::getMaterial(const std::string& name) const {
    auto it = m_materials.find(name);
    return (it != m_materials.end()) ? it->second : nullptr;
}

bool Scene::intersect(const Ray& ray, Intersection& isect) const {
    if (!m_embreeScene) {
        std::cerr << "Error: Embree scene not built" << std::endl;
        return false;
    }
    
    // Create Embree ray
    RTCRayHit rayhit;
    rayhit.ray.org_x = ray.o.x;
    rayhit.ray.org_y = ray.o.y;
    rayhit.ray.org_z = ray.o.z;
    rayhit.ray.dir_x = ray.d.x;
    rayhit.ray.dir_y = ray.d.y;
    rayhit.ray.dir_z = ray.d.z;
    rayhit.ray.tnear = ray.tMin;
    rayhit.ray.tfar = ray.tMax;
    rayhit.ray.mask = 0xFFFFFFFF;
    rayhit.ray.flags = 0;
    rayhit.hit.geomID = RTC_INVALID_GEOMETRY_ID;
    rayhit.hit.instID[0] = RTC_INVALID_GEOMETRY_ID;
    
    // Perform intersection
    rtcIntersect1(m_embreeScene, &rayhit);
    
    if (rayhit.hit.geomID == RTC_INVALID_GEOMETRY_ID) {
        isect.hit = false;
        return false;
    }
    
    // Fill intersection data
    isect.hit = true;
    isect.t = rayhit.ray.tfar;
    isect.p = ray.at(isect.t);
    isect.n = Normal3(rayhit.hit.Ng_x, rayhit.hit.Ng_y, rayhit.hit.Ng_z).normalized();
    isect.u = rayhit.hit.u;
    isect.v = rayhit.hit.v;
    isect.primitiveId = rayhit.hit.primID;
    isect.geometryId = rayhit.hit.geomID;
    
    // Get material from mesh (TODO: implement material assignment per mesh)
    if (isect.geometryId < m_meshes.size()) {
        // For now, use default material
        isect.material = nullptr;
    }
    
    return true;
}

bool Scene::intersectShadow(const Ray& ray) const {
    if (!m_embreeScene) return false;
    
    // Create Embree ray for occlusion test
    RTCRay rtcRay;
    rtcRay.org_x = ray.o.x;
    rtcRay.org_y = ray.o.y;
    rtcRay.org_z = ray.o.z;
    rtcRay.dir_x = ray.d.x;
    rtcRay.dir_y = ray.d.y;
    rtcRay.dir_z = ray.d.z;
    rtcRay.tnear = ray.tMin;
    rtcRay.tfar = ray.tMax;
    rtcRay.mask = 0xFFFFFFFF;
    rtcRay.flags = 0;
    
    // Perform occlusion test
    rtcOccluded1(m_embreeScene, &rtcRay);
    
    return rtcRay.tfar < 0.0f; // Ray was occluded
}

Scene::Bounds Scene::getBounds() const {
    return m_bounds;
}

void Scene::updateBounds() {
    m_bounds.min = Point3(std::numeric_limits<float>::max());
    m_bounds.max = Point3(std::numeric_limits<float>::lowest());

    for (const auto& mesh : m_meshes) {
        for (const auto& vertex : mesh->getVertices()) {
            m_bounds.min = Point3(
                std::min(m_bounds.min.x, vertex.position.x),
                std::min(m_bounds.min.y, vertex.position.y),
                std::min(m_bounds.min.z, vertex.position.z)
            );
            m_bounds.max = Point3(
                std::max(m_bounds.max.x, vertex.position.x),
                std::max(m_bounds.max.y, vertex.position.y),
                std::max(m_bounds.max.z, vertex.position.z)
            );
        }
    }

    // Handle empty scene
    if (m_meshes.empty()) {
        m_bounds.min = Point3(0);
        m_bounds.max = Point3(0);
    }
}

void Scene::clear() {
    m_meshes.clear();
    m_materials.clear();
    m_lights.clear();
    
    if (m_embreeScene) {
        rtcReleaseScene(m_embreeScene);
        m_embreeScene = nullptr;
    }
    
    m_accelerationBuilt = false;
    updateBounds();
}

Scene::Statistics Scene::getStatistics() const {
    Statistics stats;
    stats.meshCount = m_meshes.size();
    stats.materialCount = m_materials.size();
    stats.lightCount = m_lights.size();

    for (const auto& mesh : m_meshes) {
        stats.triangleCount += mesh->getTriangleCount();
        stats.vertexCount += mesh->getVertexCount();
    }

    return stats;
}

void Scene::buildAccelerationStructure(RTCDevice device) {
    if (m_embreeScene) {
        rtcReleaseScene(m_embreeScene);
    }

    m_embreeScene = rtcNewScene(device);

    // Add geometry to Embree scene
    for (size_t i = 0; i < m_meshes.size(); ++i) {
        auto& mesh = m_meshes[i];

        RTCGeometry geom = rtcNewGeometry(device, RTC_GEOMETRY_TYPE_TRIANGLE);

        // Set vertex buffer using new Embree 4 API
        const auto& vertices = mesh->getVertices();
        float* vertexBuffer = (float*)rtcSetNewGeometryBuffer(geom,
            RTC_BUFFER_TYPE_VERTEX, 0, RTC_FORMAT_FLOAT3,
            3 * sizeof(float), vertices.size());

        for (size_t j = 0; j < vertices.size(); ++j) {
            vertexBuffer[j * 3 + 0] = vertices[j].position.x;
            vertexBuffer[j * 3 + 1] = vertices[j].position.y;
            vertexBuffer[j * 3 + 2] = vertices[j].position.z;
        }

        // Set index buffer using new Embree 4 API
        const auto& triangles = mesh->getTriangles();
        unsigned* indexBuffer = (unsigned*)rtcSetNewGeometryBuffer(geom,
            RTC_BUFFER_TYPE_INDEX, 0, RTC_FORMAT_UINT3,
            3 * sizeof(unsigned), triangles.size());

        for (size_t j = 0; j < triangles.size(); ++j) {
            indexBuffer[j * 3 + 0] = triangles[j].v0;
            indexBuffer[j * 3 + 1] = triangles[j].v1;
            indexBuffer[j * 3 + 2] = triangles[j].v2;
        }

        rtcCommitGeometry(geom);
        unsigned geometryId = rtcAttachGeometry(m_embreeScene, geom);
        rtcReleaseGeometry(geom);

        // Store geometry ID for later reference (TODO: add to mesh interface)
        // mesh->setGeometryId(geometryId);
    }

    rtcCommitScene(m_embreeScene);
    m_accelerationBuilt = true;
}

bool Scene::loadFromFile(const std::string& filename) {
    // TODO: Implement scene loading from JSON/other formats
    std::cerr << "Scene loading not yet implemented: " << filename << std::endl;
    return false;
}

bool Scene::saveToFile(const std::string& filename) const {
    // TODO: Implement scene saving
    std::cerr << "Scene saving not yet implemented: " << filename << std::endl;
    return false;
}

} // namespace photon
