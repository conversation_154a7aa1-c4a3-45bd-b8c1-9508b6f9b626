// src/core/scene/scene_loader.hpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Scene loading from JSON/XML files

#pragma once

#include "../common.hpp"
#include "scene.hpp"
#include <string>
#include <memory>
#include <map>
#include <vector>
#include <iostream>

namespace photon {

/**
 * @brief Scene file format enumeration
 */
enum class SceneFormat {
    JSON,
    XML,
    AUTO_DETECT  // Detect from file extension
};

/**
 * @brief Scene loading configuration
 */
struct SceneLoadConfig {
    bool loadMaterials = true;      ///< Load material definitions
    bool loadLights = true;         ///< Load light definitions
    bool loadCamera = true;         ///< Load camera settings
    bool loadGeometry = true;       ///< Load mesh geometry
    bool validateScene = true;      ///< Validate scene after loading
    float scaleFactor = 1.0f;       ///< Scale factor for geometry
    std::string basePath = "";      ///< Base path for relative file references
};

/**
 * @brief Scene loading statistics
 */
struct SceneLoadStats {
    int meshesLoaded = 0;
    int materialsLoaded = 0;
    int lightsLoaded = 0;
    int texturesLoaded = 0;
    float loadTimeMs = 0.0f;
    std::vector<std::string> warnings;
    std::vector<std::string> errors;
    
    void addWarning(const std::string& warning) {
        warnings.push_back(warning);
    }
    
    void addError(const std::string& error) {
        errors.push_back(error);
    }
    
    bool hasErrors() const {
        return !errors.empty();
    }
    
    void print() const {
        std::cout << "Scene Load Statistics:" << std::endl;
        std::cout << "  Meshes: " << meshesLoaded << std::endl;
        std::cout << "  Materials: " << materialsLoaded << std::endl;
        std::cout << "  Lights: " << lightsLoaded << std::endl;
        std::cout << "  Textures: " << texturesLoaded << std::endl;
        std::cout << "  Load Time: " << loadTimeMs << "ms" << std::endl;
        
        if (!warnings.empty()) {
            std::cout << "  Warnings:" << std::endl;
            for (const auto& warning : warnings) {
                std::cout << "    - " << warning << std::endl;
            }
        }
        
        if (!errors.empty()) {
            std::cout << "  Errors:" << std::endl;
            for (const auto& error : errors) {
                std::cout << "    - " << error << std::endl;
            }
        }
    }
};

/**
 * @brief Scene loader class
 */
class SceneLoader {
public:
    /**
     * @brief Load scene from file
     * 
     * @param filename Scene file path
     * @param config Loading configuration
     * @param stats Output loading statistics
     * @return Loaded scene or nullptr on failure
     */
    static std::shared_ptr<Scene> loadFromFile(const std::string& filename,
                                              const SceneLoadConfig& config = SceneLoadConfig(),
                                              SceneLoadStats* stats = nullptr);
    
    /**
     * @brief Load scene from JSON string
     * 
     * @param jsonString JSON scene description
     * @param config Loading configuration
     * @param stats Output loading statistics
     * @return Loaded scene or nullptr on failure
     */
    static std::shared_ptr<Scene> loadFromJSON(const std::string& jsonString,
                                              const SceneLoadConfig& config = SceneLoadConfig(),
                                              SceneLoadStats* stats = nullptr);
    
    /**
     * @brief Save scene to file
     * 
     * @param scene Scene to save
     * @param filename Output file path
     * @param format Output format (auto-detect from extension if AUTO_DETECT)
     * @return true if successful, false otherwise
     */
    static bool saveToFile(const Scene& scene, const std::string& filename,
                          SceneFormat format = SceneFormat::AUTO_DETECT);
    
    /**
     * @brief Save scene to JSON string
     * 
     * @param scene Scene to save
     * @return JSON string representation
     */
    static std::string saveToJSON(const Scene& scene);
    
    /**
     * @brief Detect scene format from file extension
     * 
     * @param filename File path
     * @return Detected format
     */
    static SceneFormat detectFormat(const std::string& filename);
    
    /**
     * @brief Validate scene file without loading
     * 
     * @param filename Scene file path
     * @return true if valid, false otherwise
     */
    static bool validateFile(const std::string& filename);
    
    /**
     * @brief Create a simple test scene
     * 
     * @return Test scene with Cornell Box setup
     */
    static std::shared_ptr<Scene> createTestScene();
    
    /**
     * @brief Create an empty scene with default settings
     * 
     * @return Empty scene
     */
    static std::shared_ptr<Scene> createEmptyScene();

private:
    // JSON parsing helpers
    static std::shared_ptr<Scene> parseJSONScene(const std::string& jsonString,
                                                 const SceneLoadConfig& config,
                                                 SceneLoadStats* stats);
    
    // Component loading functions
    static bool loadCamera(Scene& scene, const std::string& cameraData,
                          const SceneLoadConfig& config, SceneLoadStats* stats);
    
    static bool loadMaterials(Scene& scene, const std::string& materialsData,
                             const SceneLoadConfig& config, SceneLoadStats* stats);
    
    static bool loadLights(Scene& scene, const std::string& lightsData,
                          const SceneLoadConfig& config, SceneLoadStats* stats);
    
    static bool loadGeometry(Scene& scene, const std::string& geometryData,
                            const SceneLoadConfig& config, SceneLoadStats* stats);
    
    // Utility functions
    static std::string readFileToString(const std::string& filename);
    static std::string getFileExtension(const std::string& filename);
    static std::string getBasePath(const std::string& filename);
    static bool fileExists(const std::string& filename);
    
    // JSON generation helpers
    static std::string generateCameraJSON(const Scene& scene);
    static std::string generateMaterialsJSON(const Scene& scene);
    static std::string generateLightsJSON(const Scene& scene);
    static std::string generateGeometryJSON(const Scene& scene);
};

/**
 * @brief Simple JSON parser for scene loading
 * Note: This is a minimal JSON parser for basic scene loading.
 * For production use, consider using a full JSON library like nlohmann/json
 */
class SimpleJSONParser {
public:
    struct JSONValue {
        enum Type { STRING, NUMBER, BOOLEAN, OBJECT, ARRAY, NULL_VALUE };
        Type type;
        std::string stringValue;
        double numberValue;
        bool boolValue;
        std::map<std::string, std::shared_ptr<JSONValue>> objectValue;
        std::vector<std::shared_ptr<JSONValue>> arrayValue;
        
        JSONValue(Type t = NULL_VALUE) : type(t), numberValue(0), boolValue(false) {}
        
        bool isString() const { return type == STRING; }
        bool isNumber() const { return type == NUMBER; }
        bool isBool() const { return type == BOOLEAN; }
        bool isObject() const { return type == OBJECT; }
        bool isArray() const { return type == ARRAY; }
        bool isNull() const { return type == NULL_VALUE; }
        
        std::string asString() const { return stringValue; }
        double asNumber() const { return numberValue; }
        float asFloat() const { return static_cast<float>(numberValue); }
        int asInt() const { return static_cast<int>(numberValue); }
        bool asBool() const { return boolValue; }
        
        std::shared_ptr<JSONValue> get(const std::string& key) const {
            auto it = objectValue.find(key);
            return (it != objectValue.end()) ? it->second : nullptr;
        }
        
        std::shared_ptr<JSONValue> operator[](size_t index) const {
            return (index < arrayValue.size()) ? arrayValue[index] : nullptr;
        }
        
        size_t size() const {
            return isArray() ? arrayValue.size() : objectValue.size();
        }
    };
    
    static std::shared_ptr<JSONValue> parse(const std::string& json);
    static std::string stringify(const JSONValue& value, int indent = 0);

private:
    static std::shared_ptr<JSONValue> parseValue(const std::string& json, size_t& pos);
    static std::shared_ptr<JSONValue> parseObject(const std::string& json, size_t& pos);
    static std::shared_ptr<JSONValue> parseArray(const std::string& json, size_t& pos);
    static std::shared_ptr<JSONValue> parseString(const std::string& json, size_t& pos);
    static std::shared_ptr<JSONValue> parseNumber(const std::string& json, size_t& pos);
    static void skipWhitespace(const std::string& json, size_t& pos);
};

} // namespace photon
