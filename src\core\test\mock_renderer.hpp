// src/core/test/mock_renderer.hpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Mock renderer for testing without Embree compilation

#pragma once

#ifdef PHOTON_SIMPLE_BUILD
#include "../common_simple.hpp"
#include "../image/image_io.hpp"
#else
#include "../common.hpp"
#include "../scene/scene.hpp"
#include "../image/image_io.hpp"
#endif
#include <memory>
#include <random>

namespace photon {

#ifdef PHOTON_SIMPLE_BUILD
// Forward declaration for simplified build
class Scene;
#endif

/**
 * @brief Mock renderer for testing purposes
 * 
 * This renderer creates test images without requiring full Embree compilation.
 * Useful for testing I/O, scene loading, and basic functionality.
 */
class MockRenderer {
public:
    MockRenderer();
    ~MockRenderer() = default;
    
    /**
     * @brief Set the scene to render
     */
    void setScene(std::shared_ptr<Scene> scene) { m_scene = scene; }
    
    /**
     * @brief Set render resolution
     */
    void setResolution(int width, int height) { 
        m_width = width; 
        m_height = height; 
    }
    
    /**
     * @brief Set number of samples per pixel
     */
    void setSamples(int samples) { m_samples = samples; }
    
    /**
     * @brief Start rendering
     */
    void render();
    
    /**
     * @brief Save rendered image
     */
    bool saveImage(const std::string& filename, int quality = 95);
    
    /**
     * @brief Get render statistics
     */
    struct MockStats {
        int totalPixels = 0;
        int totalSamples = 0;
        float renderTime = 0.0f;
        bool completed = false;
    };
    
    const MockStats& getStats() const { return m_stats; }
    
    /**
     * @brief Generate test patterns
     */
    enum class TestPattern {
        CHECKERBOARD,
        GRADIENT,
        NOISE,
        CORNELL_BOX,
        SPHERE_TEST
    };
    
    void setTestPattern(TestPattern pattern) { m_pattern = pattern; }

private:
    std::shared_ptr<Scene> m_scene;
    int m_width = 512;
    int m_height = 512;
    int m_samples = 16;
    TestPattern m_pattern = TestPattern::CORNELL_BOX;
    MockStats m_stats;
    ImageData m_image;
    std::mt19937 m_rng;
    
    // Pattern generation functions
    Color3 generateCheckerboard(int x, int y);
    Color3 generateGradient(int x, int y);
    Color3 generateNoise(int x, int y);
    Color3 generateCornellBox(int x, int y);
    Color3 generateSphereTest(int x, int y);
    
    // Utility functions
    Color3 raycast(const Vec3& origin, const Vec3& direction);
    bool intersectSphere(const Vec3& origin, const Vec3& direction, 
                        const Vec3& center, float radius, float& t);
    bool intersectPlane(const Vec3& origin, const Vec3& direction,
                       const Vec3& planePoint, const Vec3& planeNormal, float& t);
    Color3 sampleEnvironment(const Vec3& direction);
    float noise(float x, float y);
};

/**
 * @brief Test suite for PhotonRender functionality
 */
class PhotonTestSuite {
public:
    /**
     * @brief Run all tests
     */
    static bool runAllTests();
    
    /**
     * @brief Test math library
     */
    static bool testMathLibrary();
    
    /**
     * @brief Test scene loading
     */
    static bool testSceneLoading();
    
    /**
     * @brief Test mesh loading
     */
    static bool testMeshLoading();
    
    /**
     * @brief Test image I/O
     */
    static bool testImageIO();
    
    /**
     * @brief Test mock rendering
     */
    static bool testMockRendering();
    
    /**
     * @brief Generate test report
     */
    static void generateTestReport(const std::string& filename);

private:
    struct TestResult {
        std::string testName;
        bool passed;
        std::string details;
        float executionTime;
    };
    
    static std::vector<TestResult> s_testResults;
    static void addTestResult(const std::string& name, bool passed, 
                             const std::string& details = "", float time = 0.0f);
};

/**
 * @brief Performance benchmark suite
 */
class PhotonBenchmark {
public:
    /**
     * @brief Run performance benchmarks
     */
    static void runBenchmarks();
    
    /**
     * @brief Benchmark math operations
     */
    static void benchmarkMath();
    
    /**
     * @brief Benchmark scene operations
     */
    static void benchmarkScene();
    
    /**
     * @brief Benchmark image I/O
     */
    static void benchmarkImageIO();
    
    /**
     * @brief Generate benchmark report
     */
    static void generateBenchmarkReport(const std::string& filename);

private:
    struct BenchmarkResult {
        std::string benchmarkName;
        double averageTime;
        double minTime;
        double maxTime;
        int iterations;
        std::string units;
    };
    
    static std::vector<BenchmarkResult> s_benchmarkResults;
    static void addBenchmarkResult(const std::string& name, double avgTime, 
                                  double minTime, double maxTime, int iterations,
                                  const std::string& units = "ms");
};

} // namespace photon
