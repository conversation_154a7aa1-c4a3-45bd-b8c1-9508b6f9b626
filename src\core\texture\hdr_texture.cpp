// src/core/texture/hdr_texture.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// HDR texture implementation

#include "hdr_texture.hpp"
#include <iostream>
#include <fstream>
#include <algorithm>
#include <cmath>
#include <cassert>

namespace photon {

HDRTexture::HDRTexture() = default;

HDRTexture::~HDRTexture() = default;

bool HDRTexture::load(const std::string& filename, HDRFormat format) {
    if (format == HDRFormat::AUTO) {
        format = detectFormat(filename);
    }
    
    bool success = false;
    switch (format) {
        case HDRFormat::HDR:
            success = loadHDR(filename);
            break;
        case HDRFormat::EXR:
            success = loadEXR(filename);
            break;
        case HDRFormat::RGBE:
            success = loadRGBE(filename);
            break;
        default:
            std::cerr << "Unsupported HDR format for file: " << filename << std::endl;
            return false;
    }
    
    if (success) {
        computeStatistics();
        std::cout << "Loaded HDR texture: " << filename 
                  << " (" << m_width << "x" << m_height << ")" << std::endl;
    }
    
    return success;
}

bool HDRTexture::create(const std::vector<float>& data, int width, int height) {
    if (data.size() != width * height * 3) {
        std::cerr << "HDRTexture::create: Invalid data size" << std::endl;
        return false;
    }
    
    m_width = width;
    m_height = height;
    m_data = data;
    
    computeStatistics();
    return true;
}

Color3 HDRTexture::sample(const Vec2& uv) const {
    if (!isLoaded()) return Color3(0);
    
    Vec2 rotatedUV = applyRotation(uv);
    
    // Wrap UV coordinates
    float u = rotatedUV.x - std::floor(rotatedUV.x);
    float v = rotatedUV.y - std::floor(rotatedUV.y);
    
    // Convert to pixel coordinates
    int x = static_cast<int>(u * m_width) % m_width;
    int y = static_cast<int>(v * m_height) % m_height;
    
    return getPixel(x, y) * m_intensity;
}

Color3 HDRTexture::sampleFiltered(const Vec2& uv, bool bilinear) const {
    if (!isLoaded()) return Color3(0);
    
    if (!bilinear) {
        return sample(uv);
    }
    
    Vec2 rotatedUV = applyRotation(uv);
    
    // Wrap UV coordinates
    float u = rotatedUV.x - std::floor(rotatedUV.x);
    float v = rotatedUV.y - std::floor(rotatedUV.y);
    
    return bilinearSample(u, v) * m_intensity;
}

Vec2 HDRTexture::directionToUV(const Vec3& direction) const {
    // Convert direction to spherical coordinates
    float theta = std::atan2(direction.z, direction.x);
    float phi = std::acos(std::clamp(direction.y, -1.0f, 1.0f));
    
    // Convert to UV coordinates (equirectangular projection)
    float u = (theta + M_PI) / (2.0f * M_PI);
    float v = phi / M_PI;
    
    return Vec2(u, v);
}

Vec3 HDRTexture::uvToDirection(const Vec2& uv) const {
    // Convert UV to spherical coordinates
    float theta = uv.x * 2.0f * M_PI - M_PI;
    float phi = uv.y * M_PI;
    
    // Convert to Cartesian coordinates
    float sinPhi = std::sin(phi);
    float cosPhi = std::cos(phi);
    float sinTheta = std::sin(theta);
    float cosTheta = std::cos(theta);
    
    return Vec3(sinPhi * cosTheta, cosPhi, sinPhi * sinTheta);
}

Color3 HDRTexture::sampleDirection(const Vec3& direction) const {
    Vec2 uv = directionToUV(direction);
    return sampleFiltered(uv);
}

void HDRTexture::buildImportanceSampling() {
    if (!isLoaded()) return;
    
    // Build luminance map
    m_luminanceMap.resize(m_width * m_height);
    for (int i = 0; i < m_width * m_height; ++i) {
        Color3 color(m_data[i * 3], m_data[i * 3 + 1], m_data[i * 3 + 2]);
        m_luminanceMap[i] = color.luminance();
    }
    
    // Build row CDF
    m_rowCDF.resize(m_height + 1);
    m_rowCDF[0] = 0.0f;
    
    for (int y = 0; y < m_height; ++y) {
        float rowSum = 0.0f;
        for (int x = 0; x < m_width; ++x) {
            rowSum += m_luminanceMap[y * m_width + x];
        }
        m_rowCDF[y + 1] = m_rowCDF[y] + rowSum;
    }
    
    // Normalize row CDF
    float totalSum = m_rowCDF[m_height];
    if (totalSum > 0.0f) {
        for (float& cdf : m_rowCDF) {
            cdf /= totalSum;
        }
    }
    
    // Build column CDFs
    m_colCDF.resize(m_height);
    for (int y = 0; y < m_height; ++y) {
        m_colCDF[y].resize(m_width + 1);
        m_colCDF[y][0] = 0.0f;
        
        for (int x = 0; x < m_width; ++x) {
            m_colCDF[y][x + 1] = m_colCDF[y][x] + m_luminanceMap[y * m_width + x];
        }
        
        // Normalize column CDF
        float rowSum = m_colCDF[y][m_width];
        if (rowSum > 0.0f) {
            for (float& cdf : m_colCDF[y]) {
                cdf /= rowSum;
            }
        }
    }
    
    m_importanceSamplingBuilt = true;
    std::cout << "Built importance sampling for HDR texture" << std::endl;
}

Vec3 HDRTexture::sampleImportance(const Vec2& u, float& pdf) const {
    if (!m_importanceSamplingBuilt) {
        // Fallback to uniform sampling
        float theta = u.x * 2.0f * M_PI;
        float phi = std::acos(1.0f - 2.0f * u.y);
        
        Vec3 direction(
            std::sin(phi) * std::cos(theta),
            std::cos(phi),
            std::sin(phi) * std::sin(theta)
        );
        
        pdf = 1.0f / (4.0f * M_PI);
        return direction;
    }
    
    // Sample row using CDF
    int row = sampleCDF(m_rowCDF, u.y);
    row = std::clamp(row, 0, m_height - 1);
    
    // Sample column using CDF
    int col = sampleCDF(m_colCDF[row], u.x);
    col = std::clamp(col, 0, m_width - 1);
    
    // Convert to UV coordinates
    Vec2 uv((col + 0.5f) / m_width, (row + 0.5f) / m_height);
    
    // Convert to direction
    Vec3 direction = uvToDirection(uv);
    
    // Compute PDF
    float luminance = m_luminanceMap[row * m_width + col];
    float sinPhi = std::sin(uv.y * M_PI);
    pdf = (luminance * m_width * m_height) / (2.0f * M_PI * M_PI * sinPhi);
    
    if (sinPhi == 0.0f || pdf <= 0.0f) {
        pdf = 1.0f / (4.0f * M_PI);
    }
    
    return direction;
}

float HDRTexture::getImportancePDF(const Vec3& direction) const {
    if (!m_importanceSamplingBuilt) {
        return 1.0f / (4.0f * M_PI);
    }
    
    Vec2 uv = directionToUV(direction);
    
    int x = static_cast<int>(uv.x * m_width) % m_width;
    int y = static_cast<int>(uv.y * m_height) % m_height;
    
    float luminance = m_luminanceMap[y * m_width + x];
    float sinPhi = std::sin(uv.y * M_PI);
    
    if (sinPhi == 0.0f) {
        return 1.0f / (4.0f * M_PI);
    }
    
    float pdf = (luminance * m_width * m_height) / (2.0f * M_PI * M_PI * sinPhi);
    return std::max(pdf, 1e-8f);
}

HDRFormat HDRTexture::detectFormat(const std::string& filename) const {
    std::string ext = filename.substr(filename.find_last_of('.') + 1);
    std::transform(ext.begin(), ext.end(), ext.begin(), ::tolower);
    
    if (ext == "hdr") return HDRFormat::HDR;
    if (ext == "exr") return HDRFormat::EXR;
    if (ext == "rgbe") return HDRFormat::RGBE;
    
    return HDRFormat::HDR; // Default
}

void HDRTexture::computeStatistics() {
    if (!isLoaded()) return;
    
    float totalLuminance = 0.0f;
    m_maxLuminance = 0.0f;
    
    for (int i = 0; i < m_width * m_height; ++i) {
        Color3 color(m_data[i * 3], m_data[i * 3 + 1], m_data[i * 3 + 2]);
        float luminance = color.luminance();
        
        totalLuminance += luminance;
        m_maxLuminance = std::max(m_maxLuminance, luminance);
    }
    
    m_averageLuminance = totalLuminance / (m_width * m_height);
}

Vec2 HDRTexture::applyRotation(const Vec2& uv) const {
    if (m_rotation == 0.0f) return uv;
    
    float rotatedU = uv.x + m_rotation / (2.0f * M_PI);
    return Vec2(rotatedU, uv.y);
}

Color3 HDRTexture::bilinearSample(float u, float v) const {
    // Convert to pixel coordinates
    float fx = u * m_width - 0.5f;
    float fy = v * m_height - 0.5f;
    
    int x0 = static_cast<int>(std::floor(fx));
    int y0 = static_cast<int>(std::floor(fy));
    int x1 = x0 + 1;
    int y1 = y0 + 1;
    
    float wx = fx - x0;
    float wy = fy - y0;
    
    // Sample four corners
    Color3 c00 = getPixel(x0, y0);
    Color3 c10 = getPixel(x1, y0);
    Color3 c01 = getPixel(x0, y1);
    Color3 c11 = getPixel(x1, y1);
    
    // Bilinear interpolation
    Color3 c0 = c00 * (1.0f - wx) + c10 * wx;
    Color3 c1 = c01 * (1.0f - wx) + c11 * wx;
    
    return c0 * (1.0f - wy) + c1 * wy;
}

Color3 HDRTexture::getPixel(int x, int y) const {
    // Wrap coordinates
    x = ((x % m_width) + m_width) % m_width;
    y = ((y % m_height) + m_height) % m_height;
    
    int index = (y * m_width + x) * 3;
    return Color3(m_data[index], m_data[index + 1], m_data[index + 2]);
}

int HDRTexture::sampleCDF(const std::vector<float>& cdf, float u) const {
    auto it = std::lower_bound(cdf.begin(), cdf.end(), u);
    int index = static_cast<int>(it - cdf.begin()) - 1;
    return std::max(0, std::min(index, static_cast<int>(cdf.size()) - 2));
}

bool HDRTexture::loadHDR(const std::string& filename) {
    std::ifstream file(filename, std::ios::binary);
    if (!file.is_open()) {
        std::cerr << "Failed to open HDR file: " << filename << std::endl;
        return false;
    }

    // For now, create a simple procedural HDR texture
    // TODO: Implement actual HDR file loading
    std::cout << "HDR loading not fully implemented, creating procedural texture" << std::endl;

    m_width = 512;
    m_height = 256;
    m_data.resize(m_width * m_height * 3);

    // Create simple sky gradient
    for (int y = 0; y < m_height; ++y) {
        for (int x = 0; x < m_width; ++x) {
            float v = static_cast<float>(y) / (m_height - 1);
            float u = static_cast<float>(x) / (m_width - 1);

            // Sky gradient: blue at horizon, white at zenith
            Color3 horizon(0.5f, 0.7f, 1.0f);
            Color3 zenith(0.8f, 0.9f, 1.0f);
            Color3 color = horizon.lerp(zenith, v);

            // Add sun
            float theta = u * 2.0f * M_PI;
            float phi = v * M_PI;
            Vec3 dir(std::sin(phi) * std::cos(theta), std::cos(phi), std::sin(phi) * std::sin(theta));
            Vec3 sunDir(0.3f, 0.8f, 0.2f);
            sunDir = sunDir.normalized();

            float sunDot = std::max(0.0f, dir.dot(sunDir));
            if (sunDot > 0.99f) {
                color = color + Color3(10.0f, 8.0f, 6.0f) * std::pow(sunDot, 100.0f);
            }

            int index = (y * m_width + x) * 3;
            m_data[index] = color.r;
            m_data[index + 1] = color.g;
            m_data[index + 2] = color.b;
        }
    }

    return true;
}

bool HDRTexture::loadEXR(const std::string& filename) {
    // TODO: Implement EXR loading using OpenEXR library
    std::cout << "EXR loading not implemented, falling back to HDR" << std::endl;
    return loadHDR(filename);
}

bool HDRTexture::loadRGBE(const std::string& filename) {
    // TODO: Implement RGBE loading
    std::cout << "RGBE loading not implemented, falling back to HDR" << std::endl;
    return loadHDR(filename);
}

// HDRTextureFactory implementations
namespace HDRTextureFactory {

std::shared_ptr<HDRTexture> createSolid(const Color3& color, int width, int height) {
    auto texture = std::make_shared<HDRTexture>();

    std::vector<float> data(width * height * 3);
    for (int i = 0; i < width * height; ++i) {
        data[i * 3] = color.r;
        data[i * 3 + 1] = color.g;
        data[i * 3 + 2] = color.b;
    }

    texture->create(data, width, height);
    return texture;
}

std::shared_ptr<HDRTexture> createGradient(const Color3& top, const Color3& bottom,
                                          int width, int height) {
    auto texture = std::make_shared<HDRTexture>();

    std::vector<float> data(width * height * 3);
    for (int y = 0; y < height; ++y) {
        float t = static_cast<float>(y) / (height - 1);
        Color3 color = bottom.lerp(top, t);

        for (int x = 0; x < width; ++x) {
            int index = (y * width + x) * 3;
            data[index] = color.r;
            data[index + 1] = color.g;
            data[index + 2] = color.b;
        }
    }

    texture->create(data, width, height);
    return texture;
}

std::shared_ptr<HDRTexture> createSky(const Color3& zenith, const Color3& horizon,
                                     float sunIntensity, int width, int height) {
    auto texture = std::make_shared<HDRTexture>();

    std::vector<float> data(width * height * 3);
    for (int y = 0; y < height; ++y) {
        for (int x = 0; x < width; ++x) {
            float v = static_cast<float>(y) / (height - 1);
            float u = static_cast<float>(x) / (width - 1);

            // Sky gradient
            Color3 color = horizon.lerp(zenith, v);

            // Add sun
            float theta = u * 2.0f * M_PI;
            float phi = v * M_PI;
            Vec3 dir(std::sin(phi) * std::cos(theta), std::cos(phi), std::sin(phi) * std::sin(theta));
            Vec3 sunDir(0.3f, 0.8f, 0.2f);
            sunDir = sunDir.normalized();

            float sunDot = std::max(0.0f, dir.dot(sunDir));
            if (sunDot > 0.98f) {
                color = color + Color3(sunIntensity) * std::pow(sunDot, 50.0f);
            }

            int index = (y * width + x) * 3;
            data[index] = color.r;
            data[index + 1] = color.g;
            data[index + 2] = color.b;
        }
    }

    texture->create(data, width, height);
    texture->buildImportanceSampling();
    return texture;
}

} // namespace HDRTextureFactory

} // namespace photon
