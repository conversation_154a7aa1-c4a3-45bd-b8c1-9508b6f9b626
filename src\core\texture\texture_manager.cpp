// src/core/texture/texture_manager.cpp
// PhotonRender - Texture Management System Implementation
// Implementazione sistema di gestione texture

#include "texture_manager.hpp"
#include "../texture/texture.hpp"
#include "../image/image.hpp"
#include <filesystem>
#include <fstream>
#include <algorithm>
#include <chrono>
#include <iostream>
#include <sstream>
#include <iomanip>

namespace photon {

// UVTransform implementation
Vec2 UVTransform::transform(const Vec2& uv) const {
    // Translate to pivot
    Vec2 centered = uv - pivot;
    
    // Apply rotation
    if (rotation != 0.0f) {
        float cosR = std::cos(rotation);
        float sinR = std::sin(rotation);
        float x = centered.x * cosR - centered.y * sinR;
        float y = centered.x * sinR + centered.y * cosR;
        centered = Vec2(x, y);
    }
    
    // Apply scale
    centered.x *= scale.x;
    centered.y *= scale.y;
    
    // Translate back from pivot and apply offset
    return centered + pivot + offset;
}

Matrix4 UVTransform::getMatrix() const {
    Matrix4 result = Matrix4::identity();
    
    // Apply transformations in order: translate to pivot, rotate, scale, translate back, offset
    result = Matrix4::translation(Vec3(pivot.x + offset.x, pivot.y + offset.y, 0.0f)) * result;
    result = Matrix4::translation(Vec3(-pivot.x, -pivot.y, 0.0f)) * result;
    result = Matrix4::scale(Vec3(scale.x, scale.y, 1.0f)) * result;
    result = Matrix4::rotationZ(rotation) * result;
    result = Matrix4::translation(Vec3(pivot.x, pivot.y, 0.0f)) * result;
    
    return result;
}

void UVTransform::reset() {
    offset = Vec2(0.0f, 0.0f);
    scale = Vec2(1.0f, 1.0f);
    rotation = 0.0f;
    pivot = Vec2(0.5f, 0.5f);
}

// TextureManager implementation
TextureManager::TextureManager() {
    // Initialize with default state
}

TextureManager::~TextureManager() {
    shutdown();
}

bool TextureManager::initialize(const std::string& textureLibraryPath, size_t cacheSize) {
    if (m_initialized) {
        shutdown();
    }
    
    m_libraryPath = textureLibraryPath;
    m_maxCacheSize = cacheSize * 1024 * 1024; // Convert MB to bytes
    
    try {
        // Create texture library directory if it doesn't exist
        std::filesystem::create_directories(textureLibraryPath);
        
        // Create subdirectories
        std::filesystem::create_directories(textureLibraryPath + "/thumbnails");
        std::filesystem::create_directories(textureLibraryPath + "/cache");
        
        m_initialized = true;
        
        std::cout << "TextureManager initialized successfully" << std::endl;
        std::cout << "Library path: " << textureLibraryPath << std::endl;
        std::cout << "Cache size: " << cacheSize << " MB" << std::endl;
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "TextureManager initialization failed: " << e.what() << std::endl;
        return false;
    }
}

void TextureManager::shutdown() {
    if (!m_initialized) return;
    
    // Clear cache
    m_cache.clear();
    m_recentTextures.clear();
    
    m_currentCacheSize = 0;
    m_initialized = false;
    
    std::cout << "TextureManager shutdown complete" << std::endl;
}

std::string TextureManager::loadTexture(const std::string& filePath, bool generateThumbnail) {
    if (!m_initialized) {
        std::cerr << "TextureManager not initialized" << std::endl;
        return "";
    }
    
    if (!std::filesystem::exists(filePath)) {
        std::cerr << "Texture file not found: " << filePath << std::endl;
        return "";
    }
    
    // Generate texture ID
    std::string textureId = generateTextureId(filePath);
    
    // Check if already loaded
    if (m_cache.find(textureId) != m_cache.end()) {
        updateRecentTextures(textureId);
        return textureId;
    }
    
    updateProgress(0.0f, "Loading texture: " + std::filesystem::path(filePath).filename().string());
    
    try {
        // Load texture from file
        auto cacheEntry = loadTextureFromFile(filePath);
        if (!cacheEntry) {
            std::cerr << "Failed to load texture: " << filePath << std::endl;
            return "";
        }
        
        updateProgress(0.7f, "Processing texture...");
        
        // Generate thumbnail if requested
        if (generateThumbnail) {
            generateThumbnail(textureId, 128);
        }
        
        // Add to cache
        m_cache[textureId] = cacheEntry;
        updateCacheSize();
        
        // Update recent textures
        updateRecentTextures(textureId);
        
        updateProgress(1.0f, "Texture loaded successfully");
        
        std::cout << "Loaded texture: " << textureId << " (" << filePath << ")" << std::endl;
        return textureId;
        
    } catch (const std::exception& e) {
        std::cerr << "Error loading texture: " << e.what() << std::endl;
        return "";
    }
}

std::shared_ptr<Texture> TextureManager::getTexture(const std::string& textureId) {
    auto it = m_cache.find(textureId);
    if (it == m_cache.end()) {
        return nullptr;
    }
    
    auto& entry = it->second;
    
    // Update access tracking
    entry->lastAccessed = std::chrono::duration_cast<std::chrono::seconds>(
        std::chrono::system_clock::now().time_since_epoch()
    ).count();
    entry->accessCount++;
    
    // Update recent textures
    updateRecentTextures(textureId);
    
    return entry->texture;
}

TextureMetadata TextureManager::getTextureMetadata(const std::string& textureId) {
    auto it = m_cache.find(textureId);
    if (it != m_cache.end()) {
        return it->second->metadata;
    }
    
    // Return empty metadata if not found
    return TextureMetadata{};
}

std::shared_ptr<Image> TextureManager::getTextureThumbnail(const std::string& textureId) {
    auto it = m_cache.find(textureId);
    if (it != m_cache.end()) {
        return it->second->thumbnail;
    }
    
    return nullptr;
}

bool TextureManager::removeTexture(const std::string& textureId) {
    auto it = m_cache.find(textureId);
    if (it == m_cache.end()) {
        return false;
    }
    
    // Remove from cache
    m_cache.erase(it);
    
    // Remove from recent textures
    m_recentTextures.erase(
        std::remove(m_recentTextures.begin(), m_recentTextures.end(), textureId),
        m_recentTextures.end()
    );
    
    // Update cache size
    updateCacheSize();
    
    std::cout << "Removed texture from cache: " << textureId << std::endl;
    return true;
}

std::vector<std::string> TextureManager::searchTextures(const TextureSearchCriteria& criteria) {
    std::vector<std::string> results;
    
    for (const auto& pair : m_cache) {
        const auto& textureId = pair.first;
        const auto& entry = pair.second;
        const auto& metadata = entry->metadata;
        
        // Name filter
        if (!criteria.nameFilter.empty()) {
            std::string lowerName = metadata.name;
            std::string lowerFilter = criteria.nameFilter;
            std::transform(lowerName.begin(), lowerName.end(), lowerName.begin(), ::tolower);
            std::transform(lowerFilter.begin(), lowerFilter.end(), lowerFilter.begin(), ::tolower);
            
            if (lowerName.find(lowerFilter) == std::string::npos) {
                continue;
            }
        }
        
        // Format filter
        if (!criteria.formats.empty()) {
            if (std::find(criteria.formats.begin(), criteria.formats.end(), metadata.format) == criteria.formats.end()) {
                continue;
            }
        }
        
        // Size filters
        if (metadata.width < criteria.minWidth || metadata.width > criteria.maxWidth ||
            metadata.height < criteria.minHeight || metadata.height > criteria.maxHeight) {
            continue;
        }
        
        // HDR filter
        if (criteria.hdrOnly && !metadata.isHDR) {
            continue;
        }
        
        // Recent filter
        if (criteria.recentOnly) {
            if (std::find(m_recentTextures.begin(), m_recentTextures.end(), textureId) == m_recentTextures.end()) {
                continue;
            }
        }
        
        // Tag filter
        if (!criteria.tags.empty()) {
            bool hasAllTags = true;
            for (const auto& tag : criteria.tags) {
                if (std::find(metadata.tags.begin(), metadata.tags.end(), tag) == metadata.tags.end()) {
                    hasAllTags = false;
                    break;
                }
            }
            if (!hasAllTags) {
                continue;
            }
        }
        
        // Texture passed all filters
        results.push_back(textureId);
    }
    
    return results;
}

std::vector<std::string> TextureManager::getAllTextureIds() {
    std::vector<std::string> results;
    results.reserve(m_cache.size());
    
    for (const auto& pair : m_cache) {
        results.push_back(pair.first);
    }
    
    return results;
}

std::vector<std::string> TextureManager::getRecentTextures(int count) {
    std::vector<std::string> result;
    int actualCount = std::min(count, static_cast<int>(m_recentTextures.size()));
    
    for (int i = 0; i < actualCount; ++i) {
        result.push_back(m_recentTextures[i]);
    }
    
    return result;
}

TextureAssignment TextureManager::createAssignment(const std::string& textureId, TextureType type) {
    TextureAssignment assignment;
    assignment.textureId = textureId;
    assignment.type = type;
    assignment.uvTransform.reset();
    assignment.filter = TextureFilter::LINEAR;
    assignment.wrapU = TextureWrap::REPEAT;
    assignment.wrapV = TextureWrap::REPEAT;
    assignment.intensity = 1.0f;
    assignment.enabled = true;
    
    return assignment;
}

bool TextureManager::validateAssignment(const TextureAssignment& assignment) {
    // Check if texture exists
    if (m_cache.find(assignment.textureId) == m_cache.end()) {
        return false;
    }
    
    // Validate intensity range
    if (assignment.intensity < 0.0f || assignment.intensity > 10.0f) {
        return false;
    }
    
    // Validate UV transform
    if (assignment.uvTransform.scale.x <= 0.0f || assignment.uvTransform.scale.y <= 0.0f) {
        return false;
    }
    
    return true;
}

std::vector<std::string> TextureManager::getSupportedFormats() {
    return {
        "png", "jpg", "jpeg", "bmp", "tga", "tiff", "tif",
        "hdr", "exr", "pfm", "ppm", "pgm", "pbm"
    };
}

bool TextureManager::isFormatSupported(const std::string& format) {
    auto supportedFormats = getSupportedFormats();
    std::string lowerFormat = format;
    std::transform(lowerFormat.begin(), lowerFormat.end(), lowerFormat.begin(), ::tolower);
    
    return std::find(supportedFormats.begin(), supportedFormats.end(), lowerFormat) != supportedFormats.end();
}

std::string TextureManager::getCacheStats() {
    std::ostringstream stats;
    
    stats << "Texture Cache Statistics:\n";
    stats << "Cached textures: " << m_cache.size() << "\n";
    stats << "Cache size: " << std::fixed << std::setprecision(2) 
          << (m_currentCacheSize / (1024.0f * 1024.0f)) << " MB\n";
    stats << "Cache limit: " << std::fixed << std::setprecision(2) 
          << (m_maxCacheSize / (1024.0f * 1024.0f)) << " MB\n";
    stats << "Recent textures: " << m_recentTextures.size() << "\n";
    
    return stats.str();
}

void TextureManager::clearCache(bool keepRecent) {
    if (keepRecent && !m_recentTextures.empty()) {
        // Keep only recent textures
        std::unordered_map<std::string, std::shared_ptr<TextureCacheEntry>> newCache;
        
        for (const auto& textureId : m_recentTextures) {
            auto it = m_cache.find(textureId);
            if (it != m_cache.end()) {
                newCache[textureId] = it->second;
            }
        }
        
        m_cache = std::move(newCache);
    } else {
        m_cache.clear();
        m_recentTextures.clear();
    }
    
    updateCacheSize();
    
    std::cout << "Texture cache cleared" << std::endl;
}

void TextureManager::setCacheSize(size_t sizeInMB) {
    m_maxCacheSize = sizeInMB * 1024 * 1024;
    
    // Evict textures if over new limit
    if (m_currentCacheSize > m_maxCacheSize) {
        evictLRU(m_maxCacheSize);
    }
    
    std::cout << "Cache size set to " << sizeInMB << " MB" << std::endl;
}

std::string TextureManager::generateTextureId(const std::string& filePath) {
    // Generate ID based on file path and modification time
    std::filesystem::path path(filePath);
    std::string filename = path.filename().string();

    // Get file modification time
    auto ftime = std::filesystem::last_write_time(filePath);
    auto sctp = std::chrono::time_point_cast<std::chrono::system_clock::duration>(
        ftime - std::filesystem::file_time_type::clock::now() + std::chrono::system_clock::now()
    );
    auto time_t = std::chrono::system_clock::to_time_t(sctp);

    // Create hash-like ID
    std::ostringstream oss;
    oss << "tex_" << std::hash<std::string>{}(filename) << "_" << time_t;

    return oss.str();
}

std::shared_ptr<TextureCacheEntry> TextureManager::loadTextureFromFile(const std::string& filePath) {
    try {
        // Load image
        auto image = std::make_shared<Image>();
        if (!image->load(filePath)) {
            std::cerr << "Failed to load image: " << filePath << std::endl;
            return nullptr;
        }

        // Create texture from image
        auto texture = std::make_shared<Texture>();
        if (!texture->loadFromImage(image)) {
            std::cerr << "Failed to create texture from image: " << filePath << std::endl;
            return nullptr;
        }

        // Create cache entry
        auto entry = std::make_shared<TextureCacheEntry>();
        entry->texture = texture;
        entry->isLoaded = true;

        // Fill metadata
        std::filesystem::path path(filePath);
        entry->metadata.id = generateTextureId(filePath);
        entry->metadata.name = path.stem().string();
        entry->metadata.filePath = filePath;
        entry->metadata.format = path.extension().string().substr(1); // Remove dot
        entry->metadata.width = image->getWidth();
        entry->metadata.height = image->getHeight();
        entry->metadata.channels = image->getChannels();
        entry->metadata.fileSize = std::filesystem::file_size(filePath);

        // Set timestamps
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        std::stringstream ss;
        ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
        entry->metadata.createdDate = ss.str();
        entry->metadata.modifiedDate = ss.str();

        // Detect HDR format
        std::string lowerFormat = entry->metadata.format;
        std::transform(lowerFormat.begin(), lowerFormat.end(), lowerFormat.begin(), ::tolower);
        entry->metadata.isHDR = (lowerFormat == "hdr" || lowerFormat == "exr" || lowerFormat == "pfm");

        // Set access tracking
        entry->lastAccessed = std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::system_clock::now().time_since_epoch()
        ).count();
        entry->accessCount = 1;

        return entry;

    } catch (const std::exception& e) {
        std::cerr << "Error loading texture from file: " << e.what() << std::endl;
        return nullptr;
    }
}

bool TextureManager::generateThumbnail(const std::string& textureId, int size) {
    auto it = m_cache.find(textureId);
    if (it == m_cache.end()) {
        return false;
    }

    auto& entry = it->second;
    if (!entry->texture) {
        return false;
    }

    try {
        // Get original image
        auto originalImage = entry->texture->getImage();
        if (!originalImage) {
            return false;
        }

        // Create thumbnail
        auto thumbnail = std::make_shared<Image>();
        if (!thumbnail->createThumbnail(originalImage, size, size)) {
            return false;
        }

        // Save thumbnail to disk
        std::string thumbnailPath = m_libraryPath + "/thumbnails/" + textureId + ".png";
        if (!thumbnail->save(thumbnailPath)) {
            std::cerr << "Failed to save thumbnail: " << thumbnailPath << std::endl;
            return false;
        }

        // Store in cache entry
        entry->thumbnail = thumbnail;

        std::cout << "Generated thumbnail for texture: " << textureId << std::endl;
        return true;

    } catch (const std::exception& e) {
        std::cerr << "Error generating thumbnail: " << e.what() << std::endl;
        return false;
    }
}

void TextureManager::updateCacheSize() {
    m_currentCacheSize = 0;

    for (const auto& pair : m_cache) {
        const auto& entry = pair.second;
        if (entry->texture) {
            // Estimate texture memory usage
            int width = entry->metadata.width;
            int height = entry->metadata.height;
            int channels = entry->metadata.channels;
            int bytesPerChannel = entry->metadata.isHDR ? 4 : 1; // HDR uses float, LDR uses byte

            m_currentCacheSize += width * height * channels * bytesPerChannel;
        }
    }

    // Evict if over limit
    if (m_currentCacheSize > m_maxCacheSize) {
        evictLRU(m_maxCacheSize * 0.8f); // Evict to 80% of limit
    }
}

void TextureManager::evictLRU(size_t targetSize) {
    if (m_currentCacheSize <= targetSize) {
        return;
    }

    // Create vector of cache entries sorted by last access time
    std::vector<std::pair<std::string, std::shared_ptr<TextureCacheEntry>>> entries;
    for (const auto& pair : m_cache) {
        entries.push_back(pair);
    }

    // Sort by last accessed time (oldest first)
    std::sort(entries.begin(), entries.end(),
              [](const auto& a, const auto& b) {
                  return a.second->lastAccessed < b.second->lastAccessed;
              });

    // Evict oldest entries until under target size
    for (const auto& entry : entries) {
        if (m_currentCacheSize <= targetSize) {
            break;
        }

        // Don't evict recent textures
        if (std::find(m_recentTextures.begin(), m_recentTextures.end(), entry.first) != m_recentTextures.end()) {
            continue;
        }

        // Remove from cache
        m_cache.erase(entry.first);
        updateCacheSize();

        std::cout << "Evicted texture from cache: " << entry.first << std::endl;
    }
}

void TextureManager::updateRecentTextures(const std::string& textureId) {
    // Remove if already in list
    auto it = std::find(m_recentTextures.begin(), m_recentTextures.end(), textureId);
    if (it != m_recentTextures.end()) {
        m_recentTextures.erase(it);
    }

    // Add to front
    m_recentTextures.insert(m_recentTextures.begin(), textureId);

    // Keep only last 50 recent textures
    if (m_recentTextures.size() > 50) {
        m_recentTextures.resize(50);
    }
}

void TextureManager::updateProgress(float progress, const std::string& message) {
    if (m_progressCallback) {
        m_progressCallback(progress, message);
    }
}

void TextureManager::setProgressCallback(std::function<void(float, const std::string&)> callback) {
    m_progressCallback = callback;
}

std::string TextureManager::getTextureTypeString(TextureType type) {
    switch (type) {
        case TextureType::DIFFUSE: return "diffuse";
        case TextureType::NORMAL: return "normal";
        case TextureType::ROUGHNESS: return "roughness";
        case TextureType::METALLIC: return "metallic";
        case TextureType::SPECULAR: return "specular";
        case TextureType::EMISSION: return "emission";
        case TextureType::OPACITY: return "opacity";
        case TextureType::DISPLACEMENT: return "displacement";
        case TextureType::AMBIENT_OCCLUSION: return "ambient_occlusion";
        case TextureType::SUBSURFACE: return "subsurface";
        case TextureType::CLEARCOAT: return "clearcoat";
        case TextureType::CLEARCOAT_NORMAL: return "clearcoat_normal";
        case TextureType::ANISOTROPY: return "anisotropy";
        case TextureType::SHEEN: return "sheen";
        case TextureType::ENVIRONMENT: return "environment";
        default: return "unknown";
    }
}

TextureType TextureManager::parseTextureType(const std::string& typeStr) {
    if (typeStr == "diffuse") return TextureType::DIFFUSE;
    if (typeStr == "normal") return TextureType::NORMAL;
    if (typeStr == "roughness") return TextureType::ROUGHNESS;
    if (typeStr == "metallic") return TextureType::METALLIC;
    if (typeStr == "specular") return TextureType::SPECULAR;
    if (typeStr == "emission") return TextureType::EMISSION;
    if (typeStr == "opacity") return TextureType::OPACITY;
    if (typeStr == "displacement") return TextureType::DISPLACEMENT;
    if (typeStr == "ambient_occlusion") return TextureType::AMBIENT_OCCLUSION;
    if (typeStr == "subsurface") return TextureType::SUBSURFACE;
    if (typeStr == "clearcoat") return TextureType::CLEARCOAT;
    if (typeStr == "clearcoat_normal") return TextureType::CLEARCOAT_NORMAL;
    if (typeStr == "anisotropy") return TextureType::ANISOTROPY;
    if (typeStr == "sheen") return TextureType::SHEEN;
    if (typeStr == "environment") return TextureType::ENVIRONMENT;
    return TextureType::DIFFUSE; // Default
}

} // namespace photon
