// src/core/texture/texture_manager.hpp
// PhotonRender - Texture Management System
// Sistema di gestione texture con caricamento, cache e assignment

#ifndef PHOTON_TEXTURE_MANAGER_HPP
#define PHOTON_TEXTURE_MANAGER_HPP

#include "../math/vec2.hpp"
#include "../math/vec3.hpp"
#include "../math/matrix4.hpp"
#include <memory>
#include <string>
#include <unordered_map>
#include <vector>
#include <functional>

namespace photon {

// Forward declarations
class Texture;
class Image;

/**
 * @brief Texture types for material assignment
 */
enum class TextureType {
    DIFFUSE,        // Base color/albedo texture
    NORMAL,         // Normal map texture
    ROUGHNESS,      // Roughness map texture
    METALLIC,       // Metallic map texture
    SPECULAR,       // Specular map texture
    EMISSION,       // Emission/emissive texture
    OPACITY,        // Opacity/alpha texture
    DISPLACEMENT,   // Displacement/height texture
    AMBIENT_OCCLUSION, // Ambient occlusion texture
    SUBSURFACE,     // Subsurface scattering texture
    CLEARCOAT,      // Clearcoat texture
    CLEARCOAT_NORMAL, // Clearcoat normal texture
    ANISOTROPY,     // Anisotropy texture
    SHEEN,          // Sheen texture
    ENVIRONMENT     // Environment/HDRI texture
};

/**
 * @brief Texture filtering modes
 */
enum class TextureFilter {
    NEAREST,        // Nearest neighbor filtering
    LINEAR,         // Linear filtering
    TRILINEAR,      // Trilinear filtering
    ANISOTROPIC     // Anisotropic filtering
};

/**
 * @brief Texture wrapping modes
 */
enum class TextureWrap {
    REPEAT,         // Repeat texture
    CLAMP,          // Clamp to edge
    MIRROR,         // Mirror repeat
    BORDER          // Border color
};

/**
 * @brief UV transformation parameters
 */
struct UVTransform {
    Vec2 offset = Vec2(0.0f, 0.0f);     // UV offset
    Vec2 scale = Vec2(1.0f, 1.0f);     // UV scale
    float rotation = 0.0f;              // UV rotation (radians)
    Vec2 pivot = Vec2(0.5f, 0.5f);     // Rotation pivot point
    
    /**
     * @brief Apply transformation to UV coordinate
     * @param uv Input UV coordinate
     * @return Transformed UV coordinate
     */
    Vec2 transform(const Vec2& uv) const;
    
    /**
     * @brief Get transformation matrix
     * @return 3x3 transformation matrix
     */
    Matrix4 getMatrix() const;
    
    /**
     * @brief Reset to identity
     */
    void reset();
};

/**
 * @brief Texture assignment information
 */
struct TextureAssignment {
    std::string textureId;              // Texture ID
    TextureType type;                   // Texture type
    UVTransform uvTransform;            // UV transformation
    TextureFilter filter = TextureFilter::LINEAR; // Filtering mode
    TextureWrap wrapU = TextureWrap::REPEAT;      // U wrapping mode
    TextureWrap wrapV = TextureWrap::REPEAT;      // V wrapping mode
    float intensity = 1.0f;             // Texture intensity/strength
    bool enabled = true;                // Texture enabled
    
    // Channel mixing for packed textures
    int channelR = 0;                   // Red channel source (0=R, 1=G, 2=B, 3=A)
    int channelG = 1;                   // Green channel source
    int channelB = 2;                   // Blue channel source
    int channelA = 3;                   // Alpha channel source
    bool invertR = false;               // Invert red channel
    bool invertG = false;               // Invert green channel
    bool invertB = false;               // Invert blue channel
    bool invertA = false;               // Invert alpha channel
};

/**
 * @brief Texture metadata for management
 */
struct TextureMetadata {
    std::string id;                     // Unique texture ID
    std::string name;                   // Display name
    std::string filePath;               // File path
    std::string format;                 // Image format (PNG, JPG, etc.)
    int width = 0;                      // Image width
    int height = 0;                     // Image height
    int channels = 0;                   // Number of channels
    size_t fileSize = 0;                // File size in bytes
    std::string createdDate;            // Creation date
    std::string modifiedDate;           // Last modification date
    std::vector<std::string> tags;      // Texture tags
    bool isHDR = false;                 // High dynamic range
    bool isSRGB = true;                 // sRGB color space
    float gamma = 2.2f;                 // Gamma value
};

/**
 * @brief Texture cache entry
 */
struct TextureCacheEntry {
    std::shared_ptr<Texture> texture;   // Loaded texture
    std::shared_ptr<Image> thumbnail;   // Thumbnail image
    TextureMetadata metadata;           // Texture metadata
    size_t lastAccessed = 0;            // Last access timestamp
    size_t accessCount = 0;             // Access count
    bool isLoaded = false;              // Texture loaded in memory
};

/**
 * @brief Texture search criteria
 */
struct TextureSearchCriteria {
    std::string nameFilter;             // Name filter
    std::vector<std::string> tags;      // Required tags
    std::vector<std::string> formats;   // Allowed formats
    int minWidth = 0;                   // Minimum width
    int maxWidth = INT_MAX;             // Maximum width
    int minHeight = 0;                  // Minimum height
    int maxHeight = INT_MAX;            // Maximum height
    bool hdrOnly = false;               // HDR textures only
    bool recentOnly = false;            // Recently used only
};

/**
 * @brief Texture Manager
 * 
 * Comprehensive system for texture loading, caching, assignment
 * and management with support for multiple formats and UV controls
 */
class TextureManager {
public:
    /**
     * @brief Constructor
     */
    TextureManager();
    
    /**
     * @brief Destructor
     */
    ~TextureManager();
    
    /**
     * @brief Initialize texture manager
     * @param textureLibraryPath Path to texture library
     * @param cacheSize Maximum cache size in MB
     * @return True if initialization successful
     */
    bool initialize(const std::string& textureLibraryPath, size_t cacheSize = 512);
    
    /**
     * @brief Shutdown texture manager
     */
    void shutdown();
    
    /**
     * @brief Load texture from file
     * @param filePath Path to texture file
     * @param generateThumbnail Generate thumbnail
     * @return Texture ID if successful, empty string if failed
     */
    std::string loadTexture(const std::string& filePath, bool generateThumbnail = true);
    
    /**
     * @brief Get texture by ID
     * @param textureId Texture ID
     * @return Texture or nullptr if not found
     */
    std::shared_ptr<Texture> getTexture(const std::string& textureId);
    
    /**
     * @brief Get texture metadata
     * @param textureId Texture ID
     * @return Texture metadata
     */
    TextureMetadata getTextureMetadata(const std::string& textureId);
    
    /**
     * @brief Get texture thumbnail
     * @param textureId Texture ID
     * @return Thumbnail image or nullptr
     */
    std::shared_ptr<Image> getTextureThumbnail(const std::string& textureId);
    
    /**
     * @brief Remove texture from cache
     * @param textureId Texture ID
     * @return True if removed successfully
     */
    bool removeTexture(const std::string& textureId);
    
    /**
     * @brief Search textures by criteria
     * @param criteria Search criteria
     * @return Vector of matching texture IDs
     */
    std::vector<std::string> searchTextures(const TextureSearchCriteria& criteria);
    
    /**
     * @brief Get all texture IDs
     * @return Vector of all texture IDs
     */
    std::vector<std::string> getAllTextureIds();
    
    /**
     * @brief Get recently used textures
     * @param count Number of recent textures
     * @return Vector of recent texture IDs
     */
    std::vector<std::string> getRecentTextures(int count = 10);
    
    /**
     * @brief Create texture assignment
     * @param textureId Texture ID
     * @param type Texture type
     * @return Texture assignment
     */
    TextureAssignment createAssignment(const std::string& textureId, TextureType type);
    
    /**
     * @brief Validate texture assignment
     * @param assignment Texture assignment to validate
     * @return True if valid
     */
    bool validateAssignment(const TextureAssignment& assignment);
    
    /**
     * @brief Generate texture thumbnail
     * @param textureId Texture ID
     * @param size Thumbnail size
     * @return True if generation successful
     */
    bool generateThumbnail(const std::string& textureId, int size = 128);
    
    /**
     * @brief Get supported texture formats
     * @return Vector of supported formats
     */
    std::vector<std::string> getSupportedFormats();
    
    /**
     * @brief Check if format is supported
     * @param format File format
     * @return True if supported
     */
    bool isFormatSupported(const std::string& format);
    
    /**
     * @brief Get cache statistics
     * @return Cache stats as string
     */
    std::string getCacheStats();
    
    /**
     * @brief Clear texture cache
     * @param keepRecent Keep recently used textures
     */
    void clearCache(bool keepRecent = true);
    
    /**
     * @brief Set cache size limit
     * @param sizeInMB Cache size in megabytes
     */
    void setCacheSize(size_t sizeInMB);
    
    /**
     * @brief Set progress callback for loading operations
     * @param callback Progress callback function
     */
    void setProgressCallback(std::function<void(float, const std::string&)> callback);
    
    /**
     * @brief Get texture type string
     * @param type Texture type
     * @return Type name as string
     */
    static std::string getTextureTypeString(TextureType type);
    
    /**
     * @brief Parse texture type from string
     * @param typeStr Type string
     * @return Texture type
     */
    static TextureType parseTextureType(const std::string& typeStr);

private:
    std::string m_libraryPath;
    std::unordered_map<std::string, std::shared_ptr<TextureCacheEntry>> m_cache;
    std::vector<std::string> m_recentTextures;
    std::function<void(float, const std::string&)> m_progressCallback;
    
    size_t m_maxCacheSize = 512 * 1024 * 1024; // 512 MB default
    size_t m_currentCacheSize = 0;
    bool m_initialized = false;
    
    /**
     * @brief Generate unique texture ID
     * @param filePath File path
     * @return Unique texture ID
     */
    std::string generateTextureId(const std::string& filePath);
    
    /**
     * @brief Load texture from file
     * @param filePath File path
     * @return Texture cache entry
     */
    std::shared_ptr<TextureCacheEntry> loadTextureFromFile(const std::string& filePath);
    
    /**
     * @brief Update cache size tracking
     */
    void updateCacheSize();
    
    /**
     * @brief Evict least recently used textures
     * @param targetSize Target cache size
     */
    void evictLRU(size_t targetSize);
    
    /**
     * @brief Update recent textures list
     * @param textureId Texture ID
     */
    void updateRecentTextures(const std::string& textureId);
    
    /**
     * @brief Update progress
     * @param progress Progress value (0-1)
     * @param message Progress message
     */
    void updateProgress(float progress, const std::string& message);
};

} // namespace photon

#endif // PHOTON_TEXTURE_MANAGER_HPP
