// src/core/texture/texture_memory.hpp
// PhotonRender - Advanced Texture Memory Management System
// Provides texture cache, garbage collection, and memory monitoring

#pragma once

#include <memory>
#include <unordered_map>
#include <vector>
#include <string>
#include <chrono>
#include <mutex>
#include <atomic>
#include "texture.hpp"

namespace photon {

/**
 * @brief Memory usage statistics
 */
struct MemoryStats {
    size_t totalAllocated = 0;      ///< Total memory allocated
    size_t totalUsed = 0;           ///< Memory currently in use
    size_t totalCached = 0;         ///< Memory used by cache
    size_t peakUsage = 0;           ///< Peak memory usage
    size_t numTextures = 0;         ///< Number of textures
    size_t numCachedTextures = 0;   ///< Number of cached textures
    double fragmentationRatio = 0.0; ///< Memory fragmentation ratio
    
    /**
     * @brief Calculate memory efficiency
     */
    double getEfficiency() const {
        return totalAllocated > 0 ? static_cast<double>(totalUsed) / totalAllocated : 0.0;
    }
    
    /**
     * @brief Get cache hit ratio
     */
    double getCacheEfficiency() const {
        return totalUsed > 0 ? static_cast<double>(totalCached) / totalUsed : 0.0;
    }
};

/**
 * @brief Cache performance metrics
 */
struct CacheMetrics {
    uint64_t hits = 0;          ///< Cache hits
    uint64_t misses = 0;        ///< Cache misses
    uint64_t evictions = 0;     ///< Cache evictions
    uint64_t allocations = 0;   ///< Memory allocations
    uint64_t deallocations = 0; ///< Memory deallocations

    /**
     * @brief Get hit ratio
     */
    double getHitRatio() const {
        uint64_t total = hits + misses;
        return total > 0 ? static_cast<double>(hits) / total : 0.0;
    }

    /**
     * @brief Reset metrics
     */
    void reset() {
        hits = 0;
        misses = 0;
        evictions = 0;
        allocations = 0;
        deallocations = 0;
    }
};

/**
 * @brief Texture cache entry
 */
struct TextureCacheEntry {
    std::shared_ptr<Texture> texture;
    std::chrono::steady_clock::time_point lastAccess;
    std::chrono::steady_clock::time_point creationTime;
    size_t accessCount = 0;
    size_t memoryUsage = 0;
    bool isPinned = false;  ///< Pinned textures won't be evicted
    
    TextureCacheEntry(std::shared_ptr<Texture> tex)
        : texture(tex)
        , lastAccess(std::chrono::steady_clock::now())
        , creationTime(std::chrono::steady_clock::now())
        , memoryUsage(tex ? tex->getMemoryUsage() : 0) {}
    
    /**
     * @brief Update access time and count
     */
    void updateAccess() {
        lastAccess = std::chrono::steady_clock::now();
        accessCount++;
    }
    
    /**
     * @brief Get age in seconds
     */
    double getAge() const {
        auto now = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(now - creationTime);
        return duration.count() / 1000.0;
    }
    
    /**
     * @brief Get time since last access in seconds
     */
    double getTimeSinceLastAccess() const {
        auto now = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(now - lastAccess);
        return duration.count() / 1000.0;
    }
};

/**
 * @brief Cache eviction policy
 */
enum class EvictionPolicy {
    LRU,        ///< Least Recently Used
    LFU,        ///< Least Frequently Used
    FIFO,       ///< First In, First Out
    ADAPTIVE    ///< Adaptive policy based on usage patterns
};

/**
 * @brief Memory management settings
 */
struct MemorySettings {
    size_t maxCacheSize = 512 * 1024 * 1024;  ///< Max cache size (512MB)
    size_t maxTextureSize = 64 * 1024 * 1024; ///< Max single texture size (64MB)
    double gcThreshold = 0.8;                  ///< GC trigger threshold (80%)
    double gcTargetRatio = 0.6;               ///< GC target ratio (60%)
    EvictionPolicy evictionPolicy = EvictionPolicy::LRU;
    bool enableAutoGC = true;                  ///< Enable automatic garbage collection
    bool enableCompression = true;             ///< Enable automatic compression
    bool enableStreaming = true;               ///< Enable streaming for large textures
    double compressionThreshold = 0.5;        ///< Compression threshold (50% efficiency)
    
    /**
     * @brief Default settings
     */
    static MemorySettings defaultSettings() {
        return MemorySettings{};
    }
    
    /**
     * @brief Low memory settings
     */
    static MemorySettings lowMemorySettings() {
        MemorySettings settings;
        settings.maxCacheSize = 128 * 1024 * 1024;  // 128MB
        settings.maxTextureSize = 16 * 1024 * 1024; // 16MB
        settings.gcThreshold = 0.7;
        settings.gcTargetRatio = 0.5;
        settings.compressionThreshold = 0.3;
        return settings;
    }
    
    /**
     * @brief High performance settings
     */
    static MemorySettings highPerformanceSettings() {
        MemorySettings settings;
        settings.maxCacheSize = 1024 * 1024 * 1024; // 1GB
        settings.maxTextureSize = 128 * 1024 * 1024; // 128MB
        settings.gcThreshold = 0.9;
        settings.gcTargetRatio = 0.8;
        settings.enableCompression = false;
        settings.compressionThreshold = 0.8;
        return settings;
    }
};

/**
 * @brief Advanced texture memory manager
 */
class TextureMemoryManager {
public:
    /**
     * @brief Get singleton instance
     */
    static TextureMemoryManager& getInstance();
    
    /**
     * @brief Initialize memory manager
     */
    bool initialize(const MemorySettings& settings = MemorySettings::defaultSettings());
    
    /**
     * @brief Shutdown memory manager
     */
    void shutdown();
    
    /**
     * @brief Cache texture
     */
    bool cacheTexture(const std::string& key, std::shared_ptr<Texture> texture);
    
    /**
     * @brief Get cached texture
     */
    std::shared_ptr<Texture> getCachedTexture(const std::string& key);
    
    /**
     * @brief Remove texture from cache
     */
    bool removeCachedTexture(const std::string& key);
    
    /**
     * @brief Pin texture in cache (prevent eviction)
     */
    bool pinTexture(const std::string& key);
    
    /**
     * @brief Unpin texture
     */
    bool unpinTexture(const std::string& key);
    
    /**
     * @brief Force garbage collection
     */
    size_t forceGarbageCollection();
    
    /**
     * @brief Clear all cached textures
     */
    void clearCache();
    
    /**
     * @brief Get memory statistics
     */
    MemoryStats getMemoryStats() const;
    
    /**
     * @brief Get cache metrics
     */
    CacheMetrics getCacheMetrics() const;
    
    /**
     * @brief Set memory settings
     */
    void setMemorySettings(const MemorySettings& settings);
    
    /**
     * @brief Get memory settings
     */
    const MemorySettings& getMemorySettings() const { return m_settings; }
    
    /**
     * @brief Check if automatic GC is needed
     */
    bool needsGarbageCollection() const;
    
    /**
     * @brief Get cache usage ratio
     */
    double getCacheUsageRatio() const;
    
    /**
     * @brief Get number of cached textures
     */
    size_t getCachedTextureCount() const;
    
    /**
     * @brief Get total cache memory usage
     */
    size_t getTotalCacheMemory() const;

private:
    TextureMemoryManager() = default;
    ~TextureMemoryManager() = default;
    TextureMemoryManager(const TextureMemoryManager&) = delete;
    TextureMemoryManager& operator=(const TextureMemoryManager&) = delete;

    /**
     * @brief Internal atomic metrics for thread safety
     */
    struct AtomicMetrics {
        std::atomic<uint64_t> hits{0};
        std::atomic<uint64_t> misses{0};
        std::atomic<uint64_t> evictions{0};
        std::atomic<uint64_t> allocations{0};
        std::atomic<uint64_t> deallocations{0};

        void reset() {
            hits = 0;
            misses = 0;
            evictions = 0;
            allocations = 0;
            deallocations = 0;
        }
    };

    mutable std::mutex m_cacheMutex;
    std::unordered_map<std::string, TextureCacheEntry> m_cache;
    MemorySettings m_settings;
    mutable AtomicMetrics m_metrics;
    bool m_initialized = false;
    
    /**
     * @brief Perform automatic garbage collection if needed
     */
    void autoGarbageCollection();
    
    /**
     * @brief Evict textures based on policy
     */
    size_t evictTextures(size_t targetMemory);
    
    /**
     * @brief Calculate eviction score for LRU policy
     */
    double calculateLRUScore(const TextureCacheEntry& entry) const;
    
    /**
     * @brief Calculate eviction score for LFU policy
     */
    double calculateLFUScore(const TextureCacheEntry& entry) const;
    
    /**
     * @brief Calculate eviction score for adaptive policy
     */
    double calculateAdaptiveScore(const TextureCacheEntry& entry) const;
    
    /**
     * @brief Update memory statistics
     */
    void updateMemoryStats() const;
    
    /**
     * @brief Check if texture should be compressed
     */
    bool shouldCompress(const Texture* texture) const;
    
    /**
     * @brief Check if texture should be streamed
     */
    bool shouldStream(const Texture* texture) const;
};

} // namespace photon
