// src/core/texture/texture_streaming.cpp
// PhotonRender - Texture Streaming System Implementation
// Advanced texture loading and management for large scenes

#include "texture_streaming.hpp"
#include <algorithm>
#include <chrono>
#include <iostream>

namespace photon {

TextureStreamingManager::TextureStreamingManager(const StreamingParams& params)
    : m_params(params) {
}

TextureStreamingManager::~TextureStreamingManager() {
    shutdown();
}

bool TextureStreamingManager::initialize() {
    // Start background loading threads
    m_shouldStop = false;
    
    for (int i = 0; i < m_params.maxLoadingThreads; ++i) {
        m_loadingThreads.emplace_back(&TextureStreamingManager::loadingThreadFunction, this);
    }
    
    return true;
}

void TextureStreamingManager::shutdown() {
    // Stop background threads
    m_shouldStop = true;
    
    for (auto& thread : m_loadingThreads) {
        if (thread.joinable()) {
            thread.join();
        }
    }
    m_loadingThreads.clear();
    
    // Clear cache
    clearCache();
}

std::shared_ptr<ImageTexture> TextureStreamingManager::requestTexture(const TextureRequest& request) {
    std::lock_guard<std::mutex> lock(m_cacheMutex);
    
    auto it = m_textureCache.find(request.path);
    
    if (it != m_textureCache.end()) {
        // Texture found in cache
        auto& entry = it->second;
        
        if (entry.state == TextureLoadState::LOADED || entry.state == TextureLoadState::COMPRESSED) {
            // Texture is ready
            updateAccessTime(request.path);
            m_cacheHits++;
            return entry.texture;
        } else if (entry.state == TextureLoadState::LOADING) {
            // Already loading
            return nullptr;
        }
    }
    
    // Texture not in cache or failed to load
    m_cacheMisses++;
    
    // Create cache entry
    TextureCacheEntry entry;
    entry.state = TextureLoadState::LOADING;
    entry.priority = request.priority;
    entry.lastAccessTime = m_currentTime;
    m_textureCache[request.path] = entry;
    
    // Add to loading queue if not critical priority
    if (request.priority == TexturePriority::CRITICAL) {
        // Load immediately for critical textures
        auto texture = loadTextureFromFile(request.path);
        if (texture) {
            entry.texture = texture;
            entry.state = TextureLoadState::LOADED;
            entry.loadTime = m_currentTime;
            entry.memoryUsage = texture->getMemoryUsage();
            m_totalMemoryUsage += entry.memoryUsage;
            m_textureCache[request.path] = entry;
            
            // Compress if enabled
            if (m_params.enableCompression) {
                texture->compress();
                entry.state = TextureLoadState::COMPRESSED;
                entry.memoryUsage = texture->getMemoryUsage();
            }
            
            return texture;
        } else {
            entry.state = TextureLoadState::ERROR;
            m_textureCache[request.path] = entry;
        }
    } else {
        // Add to background loading queue
        std::lock_guard<std::mutex> queueLock(m_queueMutex);
        m_loadingQueue.push(request);
    }
    
    return nullptr;
}

std::shared_ptr<ImageTexture> TextureStreamingManager::getTexture(const std::string& path) {
    std::lock_guard<std::mutex> lock(m_cacheMutex);
    
    auto it = m_textureCache.find(path);
    if (it != m_textureCache.end() && 
        (it->second.state == TextureLoadState::LOADED || it->second.state == TextureLoadState::COMPRESSED)) {
        updateAccessTime(path);
        m_cacheHits++;
        return it->second.texture;
    }
    
    m_cacheMisses++;
    return nullptr;
}

void TextureStreamingManager::update(const Vec3& cameraPosition, float deltaTime) {
    m_cameraPosition = cameraPosition;
    m_currentTime += deltaTime;
    
    // Cleanup memory if needed
    if (m_totalMemoryUsage > m_params.maxMemoryMB * 1024 * 1024) {
        cleanupMemory();
    }
    
    // Unload distant textures
    if (m_params.unloadDistance > 0.0f) {
        unloadDistantTextures(cameraPosition);
    }
}

void TextureStreamingManager::preloadArea(const Vec3& center, float radius) {
    if (!m_params.enablePreloading) {
        return;
    }
    
    // This would typically iterate through a spatial data structure
    // For now, we'll just demonstrate the concept
    
    // Example: preload textures in area (would need scene integration)
    // for (auto& textureInfo : getTexturesInArea(center, radius)) {
    //     TextureRequest request(textureInfo.path, TexturePriority::BACKGROUND, textureInfo.position);
    //     requestTexture(request);
    // }
}

void TextureStreamingManager::unloadDistantTextures(const Vec3& cameraPosition) {
    std::lock_guard<std::mutex> lock(m_cacheMutex);
    
    std::vector<std::string> toUnload;
    
    for (const auto& pair : m_textureCache) {
        const auto& entry = pair.second;
        
        if (shouldUnloadTexture(entry)) {
            toUnload.push_back(pair.first);
        }
    }
    
    // Unload textures
    for (const auto& path : toUnload) {
        auto it = m_textureCache.find(path);
        if (it != m_textureCache.end()) {
            m_totalMemoryUsage -= it->second.memoryUsage;
            m_textureCache.erase(it);
        }
    }
}

void TextureStreamingManager::unloadTexture(const std::string& path) {
    std::lock_guard<std::mutex> lock(m_cacheMutex);
    
    auto it = m_textureCache.find(path);
    if (it != m_textureCache.end()) {
        m_totalMemoryUsage -= it->second.memoryUsage;
        m_textureCache.erase(it);
    }
}

void TextureStreamingManager::clearCache() {
    std::lock_guard<std::mutex> lock(m_cacheMutex);
    m_textureCache.clear();
    m_totalMemoryUsage = 0;
}

TextureStreamingManager::MemoryStats TextureStreamingManager::getMemoryStats() const {
    std::lock_guard<std::mutex> lock(m_cacheMutex);
    
    MemoryStats stats;
    stats.totalMemoryMB = m_params.maxMemoryMB;
    stats.usedMemoryMB = m_totalMemoryUsage / (1024 * 1024);
    stats.cachedTextures = m_textureCache.size();
    
    // Count loading textures
    stats.loadingTextures = 0;
    for (const auto& pair : m_textureCache) {
        if (pair.second.state == TextureLoadState::LOADING) {
            stats.loadingTextures++;
        }
    }
    
    // Calculate cache hit rate
    size_t totalAccesses = m_cacheHits + m_cacheMisses;
    stats.cacheHitRate = totalAccesses > 0 ? static_cast<float>(m_cacheHits) / totalAccesses : 0.0f;
    
    return stats;
}

void TextureStreamingManager::setStreamingParams(const StreamingParams& params) {
    m_params = params;
}

void TextureStreamingManager::loadingThreadFunction() {
    while (!m_shouldStop) {
        TextureRequest request("", TexturePriority::LOW, Vec3(0.0f));
        bool hasRequest = false;
        
        // Get next request from queue
        {
            std::lock_guard<std::mutex> lock(m_queueMutex);
            if (!m_loadingQueue.empty()) {
                request = m_loadingQueue.top();
                m_loadingQueue.pop();
                hasRequest = true;
            }
        }
        
        if (hasRequest) {
            // Load texture
            auto texture = loadTextureFromFile(request.path);
            
            // Update cache
            {
                std::lock_guard<std::mutex> lock(m_cacheMutex);
                auto it = m_textureCache.find(request.path);
                if (it != m_textureCache.end()) {
                    auto& entry = it->second;
                    
                    if (texture) {
                        entry.texture = texture;
                        entry.state = TextureLoadState::LOADED;
                        entry.loadTime = m_currentTime;
                        entry.memoryUsage = texture->getMemoryUsage();
                        m_totalMemoryUsage += entry.memoryUsage;
                        
                        // Compress if enabled
                        if (m_params.enableCompression) {
                            texture->compress();
                            entry.state = TextureLoadState::COMPRESSED;
                            entry.memoryUsage = texture->getMemoryUsage();
                        }
                        
                        // Call completion callback if provided
                        if (request.callback) {
                            request.callback(texture);
                        }
                    } else {
                        entry.state = TextureLoadState::ERROR;
                    }
                }
            }
        } else {
            // No requests, sleep briefly
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }
    }
}

std::shared_ptr<ImageTexture> TextureStreamingManager::loadTextureFromFile(const std::string& path) {
    auto texture = std::make_shared<ImageTexture>();
    
    if (texture->loadFromFile(path)) {
        return texture;
    }
    
    return nullptr;
}

TexturePriority TextureStreamingManager::calculatePriority(const Vec3& worldPos, float importance) const {
    float distance = (worldPos - m_cameraPosition).length();
    
    // Adjust priority based on distance and importance
    if (distance < m_params.preloadDistance * 0.5f && importance > 0.8f) {
        return TexturePriority::HIGH;
    } else if (distance < m_params.preloadDistance && importance > 0.5f) {
        return TexturePriority::MEDIUM;
    } else if (distance < m_params.unloadDistance) {
        return TexturePriority::LOW;
    } else {
        return TexturePriority::BACKGROUND;
    }
}

bool TextureStreamingManager::shouldUnloadTexture(const TextureCacheEntry& entry) const {
    // Don't unload if recently accessed
    if (m_currentTime - entry.lastAccessTime < 5.0f) {
        return false;
    }
    
    // Don't unload critical textures
    if (entry.priority == TexturePriority::CRITICAL) {
        return false;
    }
    
    // Unload if not accessed for a while
    return (m_currentTime - entry.lastAccessTime) > 30.0f;
}

void TextureStreamingManager::cleanupMemory() {
    std::lock_guard<std::mutex> lock(m_cacheMutex);
    
    // Create list of textures sorted by last access time
    std::vector<std::pair<std::string, float>> candidates;
    
    for (const auto& pair : m_textureCache) {
        const auto& entry = pair.second;
        
        // Skip critical textures and recently accessed ones
        if (entry.priority != TexturePriority::CRITICAL && 
            (m_currentTime - entry.lastAccessTime) > 1.0f) {
            candidates.emplace_back(pair.first, entry.lastAccessTime);
        }
    }
    
    // Sort by access time (oldest first)
    std::sort(candidates.begin(), candidates.end(), 
              [](const auto& a, const auto& b) { return a.second < b.second; });
    
    // Remove oldest textures until under memory limit
    size_t targetMemory = m_params.maxMemoryMB * 1024 * 1024 * 0.8f; // 80% of limit
    
    for (const auto& candidate : candidates) {
        if (m_totalMemoryUsage <= targetMemory) {
            break;
        }
        
        auto it = m_textureCache.find(candidate.first);
        if (it != m_textureCache.end()) {
            m_totalMemoryUsage -= it->second.memoryUsage;
            m_textureCache.erase(it);
        }
    }
}

void TextureStreamingManager::updateAccessTime(const std::string& path) {
    auto it = m_textureCache.find(path);
    if (it != m_textureCache.end()) {
        it->second.lastAccessTime = m_currentTime;
        it->second.accessCount++;
    }
}

} // namespace photon
