// src/core/texture/uv_mapping.hpp
// PhotonRender - Professional Rendering Engine for SketchUp
// UV Mapping Enhancement - Multiple UV sets and coordinate transformation

#pragma once

#include "../math/vec2.hpp"
#include "../math/vec3.hpp"
#include <cmath>

namespace photon {

/**
 * @brief UV coordinate transformation
 * 
 * Provides comprehensive UV coordinate transformation capabilities including
 * scaling, offset, rotation, and flipping operations.
 */
struct UVTransform {
    Vec2 scale = Vec2(1.0f);        ///< UV scale factors (default: no scaling)
    Vec2 offset = Vec2(0.0f);       ///< UV offset (default: no offset)
    float rotation = 0.0f;          ///< UV rotation in radians (default: no rotation)
    bool flipU = false;             ///< Flip U coordinate (default: no flip)
    bool flipV = false;             ///< Flip V coordinate (default: no flip)
    
    /**
     * @brief Default constructor (identity transform)
     */
    UVTransform() = default;
    
    /**
     * @brief Constructor with parameters
     */
    UVTransform(const Vec2& scale, const Vec2& offset = Vec2(0.0f), 
                float rotation = 0.0f, bool flipU = false, bool flipV = false)
        : scale(scale), offset(offset), rotation(rotation), flipU(flipU), flipV(flipV) {}
    
    /**
     * @brief Apply transformation to UV coordinates
     * 
     * Transformation order: flip -> scale -> rotate -> offset
     * This order ensures predictable and intuitive results.
     * 
     * @param uv Input UV coordinates
     * @return Transformed UV coordinates
     */
    Vec2 transform(const Vec2& uv) const;
    
    /**
     * @brief Apply inverse transformation to UV coordinates
     * 
     * Useful for reverse mapping operations.
     * 
     * @param uv Input UV coordinates
     * @return Inverse transformed UV coordinates
     */
    Vec2 inverseTransform(const Vec2& uv) const;
    
    /**
     * @brief Check if this is an identity transform
     */
    bool isIdentity() const;
    
    /**
     * @brief Combine with another transform
     */
    UVTransform combine(const UVTransform& other) const;
    
    // Static factory methods for common transforms
    
    /**
     * @brief Create identity transform
     */
    static UVTransform identity() { return UVTransform(); }
    
    /**
     * @brief Create scale transform
     */
    static UVTransform createScale(float scaleU, float scaleV) {
        return UVTransform(Vec2(scaleU, scaleV));
    }
    
    /**
     * @brief Create uniform scale transform
     */
    static UVTransform createScale(float scale) {
        return UVTransform(Vec2(scale));
    }
    
    /**
     * @brief Create offset transform
     */
    static UVTransform createOffset(float offsetU, float offsetV) {
        UVTransform t;
        t.offset = Vec2(offsetU, offsetV);
        return t;
    }
    
    /**
     * @brief Create rotation transform
     */
    static UVTransform createRotation(float angleRadians) {
        UVTransform t;
        t.rotation = angleRadians;
        return t;
    }
    
    /**
     * @brief Create flip transform
     */
    static UVTransform createFlip(bool flipU, bool flipV) {
        UVTransform t;
        t.flipU = flipU;
        t.flipV = flipV;
        return t;
    }
};

/**
 * @brief UV mapping modes for procedural coordinate generation
 */
enum class UVMappingMode {
    VERTEX_UV,      ///< Use vertex UV coordinates (default)
    PLANAR_XY,      ///< Planar mapping on XY plane (Z-axis projection)
    PLANAR_XZ,      ///< Planar mapping on XZ plane (Y-axis projection)
    PLANAR_YZ,      ///< Planar mapping on YZ plane (X-axis projection)
    CYLINDRICAL_Y,  ///< Cylindrical mapping around Y axis
    CYLINDRICAL_X,  ///< Cylindrical mapping around X axis
    CYLINDRICAL_Z,  ///< Cylindrical mapping around Z axis
    SPHERICAL,      ///< Spherical mapping
    CUBIC,          ///< Cubic mapping (box projection)
    TRIPLANAR       ///< Triplanar mapping (blend of 3 planar projections)
};

/**
 * @brief UV mapping generator for procedural coordinate generation
 * 
 * Provides comprehensive UV coordinate generation from 3D positions
 * using various projection methods.
 */
class UVMapping {
public:
    /**
     * @brief Constructor
     * @param mode UV mapping mode
     * @param transform UV transformation to apply
     */
    UVMapping(UVMappingMode mode = UVMappingMode::VERTEX_UV, 
              const UVTransform& transform = UVTransform::identity());
    
    /**
     * @brief Generate UV coordinates from 3D position
     * @param position 3D world position
     * @param normal Surface normal (optional, used for some mapping modes)
     * @return Generated UV coordinates
     */
    Vec2 generateUV(const Vec3& position, const Vec3& normal = Vec3(0, 1, 0)) const;
    
    /**
     * @brief Transform existing UV coordinates
     * @param uv Input UV coordinates
     * @return Transformed UV coordinates
     */
    Vec2 transformUV(const Vec2& uv) const;
    
    // Getters and setters
    
    /**
     * @brief Set mapping mode
     */
    void setMappingMode(UVMappingMode mode) { m_mode = mode; }
    
    /**
     * @brief Set UV transform
     */
    void setTransform(const UVTransform& transform) { m_transform = transform; }
    
    /**
     * @brief Set mapping scale (convenience method)
     */
    void setScale(float scaleU, float scaleV);
    
    /**
     * @brief Set mapping offset (convenience method)
     */
    void setOffset(float offsetU, float offsetV);
    
    /**
     * @brief Set mapping rotation (convenience method)
     */
    void setRotation(float angleRadians);
    
    /**
     * @brief Get mapping mode
     */
    UVMappingMode getMappingMode() const { return m_mode; }
    
    /**
     * @brief Get UV transform
     */
    const UVTransform& getTransform() const { return m_transform; }
    
    /**
     * @brief Get mapping mode name (for debugging)
     */
    const char* getMappingModeName() const;

private:
    UVMappingMode m_mode;
    UVTransform m_transform;
    
    // Internal mapping generation methods
    
    /**
     * @brief Generate planar UV coordinates
     * @param position 3D position
     * @param axis Primary axis (0=X, 1=Y, 2=Z)
     * @return Generated UV coordinates
     */
    Vec2 generatePlanarUV(const Vec3& position, int axis) const;
    
    /**
     * @brief Generate cylindrical UV coordinates
     * @param position 3D position
     * @param axis Cylinder axis (0=X, 1=Y, 2=Z)
     * @return Generated UV coordinates
     */
    Vec2 generateCylindricalUV(const Vec3& position, int axis) const;
    
    /**
     * @brief Generate spherical UV coordinates
     * @param position 3D position
     * @return Generated UV coordinates
     */
    Vec2 generateSphericalUV(const Vec3& position) const;
    
    /**
     * @brief Generate cubic UV coordinates
     * @param position 3D position
     * @param normal Surface normal
     * @return Generated UV coordinates
     */
    Vec2 generateCubicUV(const Vec3& position, const Vec3& normal) const;
    
    /**
     * @brief Generate triplanar UV coordinates
     * @param position 3D position
     * @param normal Surface normal
     * @return Generated UV coordinates
     */
    Vec2 generateTriplanarUV(const Vec3& position, const Vec3& normal) const;
};

/**
 * @brief Multiple UV sets mapping manager
 *
 * Manages multiple UV coordinate sets (UV0-UV3) with different mapping modes
 * and transformations for each set.
 */
class MultiUVMapping {
public:
    /**
     * @brief Constructor
     */
    MultiUVMapping() {
        // Initialize with default UV mapping for UV0
        m_uvMappings[0] = UVMapping(UVMappingMode::VERTEX_UV);
        m_uvMappings[1] = UVMapping(UVMappingMode::VERTEX_UV);
        m_uvMappings[2] = UVMapping(UVMappingMode::VERTEX_UV);
        m_uvMappings[3] = UVMapping(UVMappingMode::VERTEX_UV);
    }

    /**
     * @brief Set UV mapping for specific set
     * @param uvSet UV set index (0-3)
     * @param mapping UV mapping configuration
     */
    void setUVMapping(int uvSet, const UVMapping& mapping) {
        if (uvSet >= 0 && uvSet < 4) {
            m_uvMappings[uvSet] = mapping;
        }
    }

    /**
     * @brief Get UV mapping for specific set
     * @param uvSet UV set index (0-3)
     * @return UV mapping configuration
     */
    const UVMapping& getUVMapping(int uvSet) const {
        if (uvSet >= 0 && uvSet < 4) {
            return m_uvMappings[uvSet];
        }
        return m_uvMappings[0]; // Default to UV0
    }

    /**
     * @brief Generate UV coordinates for specific set
     * @param uvSet UV set index (0-3)
     * @param position 3D world position
     * @param normal Surface normal
     * @return Generated UV coordinates
     */
    Vec2 generateUV(int uvSet, const Vec3& position, const Vec3& normal = Vec3(0, 1, 0)) const {
        if (uvSet >= 0 && uvSet < 4) {
            return m_uvMappings[uvSet].generateUV(position, normal);
        }
        return m_uvMappings[0].generateUV(position, normal); // Default to UV0
    }

    /**
     * @brief Transform existing UV coordinates for specific set
     * @param uvSet UV set index (0-3)
     * @param uv Input UV coordinates
     * @return Transformed UV coordinates
     */
    Vec2 transformUV(int uvSet, const Vec2& uv) const {
        if (uvSet >= 0 && uvSet < 4) {
            return m_uvMappings[uvSet].transformUV(uv);
        }
        return m_uvMappings[0].transformUV(uv); // Default to UV0
    }

    /**
     * @brief Set mapping mode for specific UV set
     * @param uvSet UV set index (0-3)
     * @param mode UV mapping mode
     */
    void setMappingMode(int uvSet, UVMappingMode mode) {
        if (uvSet >= 0 && uvSet < 4) {
            m_uvMappings[uvSet].setMappingMode(mode);
        }
    }

    /**
     * @brief Set transform for specific UV set
     * @param uvSet UV set index (0-3)
     * @param transform UV transformation
     */
    void setTransform(int uvSet, const UVTransform& transform) {
        if (uvSet >= 0 && uvSet < 4) {
            m_uvMappings[uvSet].setTransform(transform);
        }
    }

private:
    UVMapping m_uvMappings[4]; ///< UV mappings for each set (UV0-UV3)
};

} // namespace photon
