// src/gpu/cuda/cuda_integration.cpp
// PhotonRender - CUDA Integration Wrapper
// Wrapper C++ per integrare CUDA con PhotonRender

#include "cuda_integration.h"
#include "cuda_renderer.h"
#include <iostream>
#include <chrono>
#include <vector>

namespace photon {
namespace gpu {

CudaRenderer::CudaRenderer() : initialized_(false) {
}

CudaRenderer::~CudaRenderer() {
    if (initialized_) {
        shutdown();
    }
}

bool CudaRenderer::initialize() {
    if (initialized_) {
        return true;
    }
    
    std::cout << "[CUDA] Initializing CUDA renderer..." << std::endl;
    
    if (!cuda_init()) {
        std::cerr << "[CUDA ERROR] Failed to initialize CUDA" << std::endl;
        return false;
    }
    
    initialized_ = true;
    std::cout << "[CUDA] CUDA renderer initialized successfully" << std::endl;
    return true;
}

void CudaRenderer::shutdown() {
    if (!initialized_) {
        return;
    }
    
    std::cout << "[CUDA] Shutting down CUDA renderer..." << std::endl;
    cuda_cleanup();
    initialized_ = false;
}

bool CudaRenderer::isAvailable() const {
    return initialized_;
}

bool CudaRenderer::renderTestScene(
    std::vector<float>& image_data,
    int width,
    int height,
    int samples_per_pixel
) {
    if (!initialized_) {
        std::cerr << "[CUDA ERROR] Renderer not initialized" << std::endl;
        return false;
    }
    
    // Prepara buffer immagine
    image_data.resize(width * height * 3);
    
    // Crea scena di test con alcune sfere
    std::vector<float> sphere_data = {
        // Sfera 1: centro(-1, 0, -1), raggio=0.5, colore rosso
        -1.0f, 0.0f, -1.0f, 0.5f, 1.0f, 0.2f, 0.2f,
        
        // Sfera 2: centro(1, 0, -1), raggio=0.5, colore verde  
        1.0f, 0.0f, -1.0f, 0.5f, 0.2f, 1.0f, 0.2f,
        
        // Sfera 3: centro(0, -100.5, -1), raggio=100, colore grigio (ground)
        0.0f, -100.5f, -1.0f, 100.0f, 0.5f, 0.5f, 0.5f,
        
        // Sfera 4: centro(0, 0.5, -1), raggio=0.3, colore blu
        0.0f, 0.5f, -1.0f, 0.3f, 0.2f, 0.2f, 1.0f
    };
    
    int num_spheres = sphere_data.size() / 7; // 7 valori per sfera
    
    std::cout << "[CUDA] Rendering " << width << "x" << height 
              << " @ " << samples_per_pixel << " SPP..." << std::endl;
    
    // Misura tempo di rendering
    auto start_time = std::chrono::high_resolution_clock::now();
    
    bool success = cuda_render(
        image_data.data(),
        width,
        height,
        samples_per_pixel,
        sphere_data.data(),
        num_spheres
    );
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    if (success) {
        std::cout << "[CUDA] Rendering completed in " << duration.count() << "ms" << std::endl;
        
        // Calcola statistiche performance
        long long total_samples = (long long)width * height * samples_per_pixel;
        double samples_per_second = total_samples / (duration.count() / 1000.0);
        
        std::cout << "[CUDA] Performance: " << (samples_per_second / 1000000.0) 
                  << " Msamples/sec" << std::endl;
    } else {
        std::cerr << "[CUDA ERROR] Rendering failed" << std::endl;
    }
    
    return success;
}

RenderStats CudaRenderer::getLastRenderStats() const {
    return last_stats_;
}

bool CudaRenderer::saveImagePNG(
    const std::vector<float>& image_data,
    int width,
    int height,
    const std::string& filename
) {
    // Converte da float RGB a unsigned char RGB
    std::vector<unsigned char> byte_data(width * height * 3);
    
    for (size_t i = 0; i < image_data.size(); i++) {
        float value = image_data[i];
        // Clamp e converti a byte
        value = std::max(0.0f, std::min(1.0f, value));
        byte_data[i] = static_cast<unsigned char>(value * 255.0f);
    }
    
    // Usa STB per salvare PNG
    // Nota: Questo richiede l'integrazione con il sistema di I/O esistente di PhotonRender
    std::cout << "[CUDA] Image save to " << filename << " (placeholder - needs STB integration)" << std::endl;
    
    return true; // Placeholder
}

} // namespace gpu
} // namespace photon
