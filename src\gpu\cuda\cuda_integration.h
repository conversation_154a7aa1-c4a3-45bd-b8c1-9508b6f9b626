// src/gpu/cuda/cuda_integration.h
// PhotonRender - CUDA Integration Header
// Interfaccia C++ per integrazione CUDA con PhotonRender

#pragma once

#include <vector>
#include <string>

namespace photon {
namespace gpu {

/**
 * Statistiche di rendering
 */
struct RenderStats {
    int width = 0;
    int height = 0;
    int samples_per_pixel = 0;
    double render_time_ms = 0.0;
    double samples_per_second = 0.0;
    long long total_samples = 0;
};

/**
 * Renderer CUDA per PhotonRender
 * Fornisce accelerazione GPU per ray tracing
 */
class CudaRenderer {
public:
    CudaRenderer();
    ~CudaRenderer();
    
    // Non copiabile
    CudaRenderer(const CudaRenderer&) = delete;
    CudaRenderer& operator=(const CudaRenderer&) = delete;
    
    /**
     * Inizializza il renderer CUDA
     * @return true se l'inizializzazione è riuscita
     */
    bool initialize();
    
    /**
     * Shutdown del renderer CUDA
     */
    void shutdown();
    
    /**
     * Verifica se CUDA è disponibile e inizializzato
     * @return true se CUDA è pronto per il rendering
     */
    bool isAvailable() const;
    
    /**
     * Renderizza una scena di test con sfere
     * @param image_data Buffer output per i dati immagine (RGB float)
     * @param width Larghezza immagine
     * @param height Altezza immagine
     * @param samples_per_pixel Campioni per pixel per anti-aliasing
     * @return true se il rendering è riuscito
     */
    bool renderTestScene(
        std::vector<float>& image_data,
        int width,
        int height,
        int samples_per_pixel = 8
    );
    
    /**
     * Ottieni statistiche dell'ultimo rendering
     * @return Struttura con statistiche di performance
     */
    RenderStats getLastRenderStats() const;
    
    /**
     * Salva immagine in formato PNG
     * @param image_data Dati immagine RGB float
     * @param width Larghezza immagine
     * @param height Altezza immagine
     * @param filename Nome file output
     * @return true se il salvataggio è riuscito
     */
    bool saveImagePNG(
        const std::vector<float>& image_data,
        int width,
        int height,
        const std::string& filename
    );

private:
    bool initialized_;
    RenderStats last_stats_;
};

} // namespace gpu
} // namespace photon
