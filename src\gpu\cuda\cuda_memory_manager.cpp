// src/gpu/cuda/cuda_memory_manager.cpp
// PhotonRender - CUDA Memory Manager Implementation
// Implementazione sistema avanzato gestione memoria GPU

#include "cuda_memory_manager.h"
#include <iostream>
#include <algorithm>
#include <cstring>

namespace photon {
namespace gpu {

// Implementazione TypedMemoryPool
template<typename T>
TypedMemoryPool<T>::TypedMemoryPool(size_t initial_capacity) 
    : total_capacity_(initial_capacity), used_count_(0) {
    
    // Alloca pool iniziale
    void* ptr;
    cudaError_t error = cudaMalloc(&ptr, initial_capacity * sizeof(T));
    if (error == cudaSuccess) {
        blocks_.emplace_back(ptr, initial_capacity * sizeof(T));
    }
}

template<typename T>
TypedMemoryPool<T>::~TypedMemoryPool() {
    std::lock_guard<std::mutex> lock(mutex_);
    for (auto& block : blocks_) {
        if (block.ptr) {
            cudaFree(block.ptr);
        }
    }
}

template<typename T>
T* TypedMemoryPool<T>::allocate(size_t count) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    size_t required_size = count * sizeof(T);
    MemoryBlock* block = findFreeBlock(required_size);
    
    if (!block) {
        expandPool(required_size);
        block = findFreeBlock(required_size);
    }
    
    if (block) {
        block->in_use = true;
        block->ref_count++;
        used_count_ += count;
        return static_cast<T*>(block->ptr);
    }
    
    return nullptr;
}

template<typename T>
void TypedMemoryPool<T>::deallocate(T* ptr) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    for (auto& block : blocks_) {
        if (block.ptr == ptr) {
            block.in_use = false;
            block.ref_count--;
            used_count_ -= block.size / sizeof(T);
            break;
        }
    }
}

template<typename T>
void TypedMemoryPool<T>::expandPool(size_t min_size) {
    size_t new_size = std::max(min_size, total_capacity_ / 2); // Grow by 50%
    
    void* ptr;
    cudaError_t error = cudaMalloc(&ptr, new_size);
    if (error == cudaSuccess) {
        blocks_.emplace_back(ptr, new_size);
        total_capacity_ += new_size / sizeof(T);
    }
}

template<typename T>
MemoryBlock* TypedMemoryPool<T>::findFreeBlock(size_t size) {
    for (auto& block : blocks_) {
        if (!block.in_use && block.size >= size) {
            return &block;
        }
    }
    return nullptr;
}

// Implementazione CudaMemoryManager
CudaMemoryManager& CudaMemoryManager::getInstance() {
    static CudaMemoryManager instance;
    return instance;
}

bool CudaMemoryManager::initialize(size_t initial_pool_size) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (initialized_) {
        return true;
    }
    
    std::cout << "[CUDA Memory] Initializing memory manager with " 
              << (initial_pool_size / 1024 / 1024) << " MB pool..." << std::endl;
    
    pool_size_ = initial_pool_size;
    
    // Alloca pool iniziale
    void* pool_ptr;
    cudaError_t error = cudaMalloc(&pool_ptr, pool_size_);
    if (error != cudaSuccess) {
        std::cerr << "[CUDA Memory ERROR] Failed to allocate initial pool: " 
                  << cudaGetErrorString(error) << std::endl;
        return false;
    }
    
    // Aggiungi al pool
    memory_pool_.emplace_back(pool_ptr, pool_size_);
    
    // Inizializza statistiche
    stats_ = MemoryStats{};
    stats_.total_allocated = pool_size_;
    stats_.pool_size = pool_size_;
    stats_.pool_available = pool_size_;
    
    initialized_ = true;
    
    std::cout << "[CUDA Memory] Memory manager initialized successfully" << std::endl;
    return true;
}

void CudaMemoryManager::shutdown() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!initialized_) {
        return;
    }
    
    std::cout << "[CUDA Memory] Shutting down memory manager..." << std::endl;
    
    // Stampa statistiche finali
    printStats();
    
    // Libera tutto il pool
    for (auto& block : memory_pool_) {
        if (block.ptr) {
            cudaFree(block.ptr);
        }
    }
    
    memory_pool_.clear();
    allocated_blocks_.clear();
    
    initialized_ = false;
    std::cout << "[CUDA Memory] Memory manager shutdown complete" << std::endl;
}

void* CudaMemoryManager::allocate(size_t size, size_t alignment) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!initialized_) {
        std::cerr << "[CUDA Memory ERROR] Manager not initialized" << std::endl;
        return nullptr;
    }
    
    // Allinea la dimensione
    size_t aligned_size = (size + alignment - 1) & ~(alignment - 1);
    
    // Cerca blocco libero nel pool
    MemoryBlock* block = findFreeBlock(aligned_size);
    
    if (!block) {
        // Espandi il pool se necessario
        expandPool(aligned_size);
        block = findFreeBlock(aligned_size);
    }
    
    if (block) {
        block->in_use = true;
        block->ref_count++;
        
        // Aggiorna statistiche
        allocated_blocks_[block->ptr] = aligned_size;
        stats_.current_usage += aligned_size;
        stats_.allocation_count++;
        stats_.pool_hits++;
        
        if (stats_.current_usage > stats_.peak_usage) {
            stats_.peak_usage = stats_.current_usage;
        }
        
        return block->ptr;
    }
    
    // Fallback: allocazione diretta
    void* ptr;
    cudaError_t error = cudaMalloc(&ptr, aligned_size);
    if (error == cudaSuccess) {
        allocated_blocks_[ptr] = aligned_size;
        stats_.current_usage += aligned_size;
        stats_.allocation_count++;
        stats_.pool_misses++;
        
        if (stats_.current_usage > stats_.peak_usage) {
            stats_.peak_usage = stats_.current_usage;
        }
        
        return ptr;
    }
    
    std::cerr << "[CUDA Memory ERROR] Failed to allocate " << size << " bytes: " 
              << cudaGetErrorString(error) << std::endl;
    return nullptr;
}

void CudaMemoryManager::deallocate(void* ptr) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!ptr || !initialized_) {
        return;
    }
    
    auto it = allocated_blocks_.find(ptr);
    if (it == allocated_blocks_.end()) {
        std::cerr << "[CUDA Memory WARNING] Attempting to deallocate unknown pointer" << std::endl;
        return;
    }
    
    size_t size = it->second;
    allocated_blocks_.erase(it);
    
    // Cerca nel pool
    bool found_in_pool = false;
    for (auto& block : memory_pool_) {
        if (block.ptr == ptr) {
            block.in_use = false;
            block.ref_count--;
            found_in_pool = true;
            break;
        }
    }
    
    if (!found_in_pool) {
        // Era allocazione diretta, libera
        cudaFree(ptr);
    }
    
    // Aggiorna statistiche
    stats_.current_usage -= size;
    stats_.deallocation_count++;
}

float* CudaMemoryManager::allocateImageBuffer(int width, int height, int channels) {
    size_t size = width * height * channels * sizeof(float);
    return static_cast<float*>(allocate(size, 256)); // Allineamento 256 byte per performance
}

void CudaMemoryManager::deallocateImageBuffer(float* buffer) {
    deallocate(buffer);
}

void* CudaMemoryManager::allocateGeometryBuffer(size_t size) {
    return allocate(size, 128); // Allineamento 128 byte per geometry
}

void CudaMemoryManager::deallocateGeometryBuffer(void* buffer) {
    deallocate(buffer);
}

cudaArray_t CudaMemoryManager::allocateTexture2D(int width, int height, cudaChannelFormatDesc format) {
    cudaArray_t texture;
    cudaError_t error = cudaMallocArray(&texture, &format, width, height);
    
    if (error == cudaSuccess) {
        size_t texture_size = width * height * 4; // Approssimazione
        stats_.current_usage += texture_size;
        stats_.allocation_count++;
        
        return texture;
    }
    
    std::cerr << "[CUDA Memory ERROR] Failed to allocate texture: " 
              << cudaGetErrorString(error) << std::endl;
    return nullptr;
}

void CudaMemoryManager::deallocateTexture(cudaArray_t texture) {
    if (texture) {
        cudaFreeArray(texture);
        stats_.deallocation_count++;
    }
}

bool CudaMemoryManager::streamToDevice(void* dst, const void* src, size_t size, cudaStream_t stream) {
    cudaError_t error = cudaMemcpyAsync(dst, src, size, cudaMemcpyHostToDevice, stream);
    return error == cudaSuccess;
}

bool CudaMemoryManager::streamFromDevice(void* dst, const void* src, size_t size, cudaStream_t stream) {
    cudaError_t error = cudaMemcpyAsync(dst, src, size, cudaMemcpyDeviceToHost, stream);
    return error == cudaSuccess;
}

MemoryStats CudaMemoryManager::getStats() const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    // Aggiorna statistiche pool
    MemoryStats current_stats = stats_;
    current_stats.pool_available = 0;
    
    for (const auto& block : memory_pool_) {
        if (!block.in_use) {
            current_stats.pool_available += block.size;
        }
    }
    
    return current_stats;
}

void CudaMemoryManager::printStats() const {
    MemoryStats stats = getStats();
    
    std::cout << "\n=== CUDA Memory Statistics ===" << std::endl;
    std::cout << "Total Allocated: " << (stats.total_allocated / 1024 / 1024) << " MB" << std::endl;
    std::cout << "Current Usage: " << (stats.current_usage / 1024 / 1024) << " MB" << std::endl;
    std::cout << "Peak Usage: " << (stats.peak_usage / 1024 / 1024) << " MB" << std::endl;
    std::cout << "Pool Size: " << (stats.pool_size / 1024 / 1024) << " MB" << std::endl;
    std::cout << "Pool Available: " << (stats.pool_available / 1024 / 1024) << " MB" << std::endl;
    std::cout << "Allocations: " << stats.allocation_count << std::endl;
    std::cout << "Deallocations: " << stats.deallocation_count << std::endl;
    std::cout << "Pool Hits: " << stats.pool_hits << std::endl;
    std::cout << "Pool Misses: " << stats.pool_misses << std::endl;
    
    if (stats.allocation_count > 0) {
        float hit_rate = (float)stats.pool_hits / (stats.pool_hits + stats.pool_misses) * 100.0f;
        std::cout << "Pool Hit Rate: " << hit_rate << "%" << std::endl;
    }
    
    std::cout << "===============================" << std::endl;
}

MemoryBlock* CudaMemoryManager::findFreeBlock(size_t size) {
    for (auto& block : memory_pool_) {
        if (!block.in_use && block.size >= size) {
            return &block;
        }
    }
    return nullptr;
}

void CudaMemoryManager::expandPool(size_t additional_size) {
    size_t new_block_size = std::max(additional_size, pool_size_ / 4); // Grow by 25%
    
    void* ptr;
    cudaError_t error = cudaMalloc(&ptr, new_block_size);
    if (error == cudaSuccess) {
        memory_pool_.emplace_back(ptr, new_block_size);
        stats_.total_allocated += new_block_size;
        stats_.pool_size += new_block_size;
        
        std::cout << "[CUDA Memory] Expanded pool by " 
                  << (new_block_size / 1024 / 1024) << " MB" << std::endl;
    }
}

void CudaMemoryManager::garbageCollect() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    // Compatta blocchi liberi adiacenti
    // Implementazione semplificata per ora
    std::cout << "[CUDA Memory] Garbage collection completed" << std::endl;
}

bool CudaMemoryManager::validateMemory() const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    // Verifica integrità dei blocchi
    for (const auto& block : memory_pool_) {
        if (!block.ptr) {
            return false;
        }
    }
    
    return true;
}

} // namespace gpu
} // namespace photon
