// src/gpu/cuda/cuda_memory_manager.h
// PhotonRender - CUDA Memory Manager
// Sistema avanzato di gestione memoria GPU con pooling e ottimizzazioni

#pragma once

#include <cuda_runtime.h>
#include <vector>
#include <unordered_map>
#include <memory>
#include <mutex>

namespace photon {
namespace gpu {

/**
 * Statistiche memoria GPU
 */
struct MemoryStats {
    size_t total_allocated = 0;      // Memoria totale allocata
    size_t peak_usage = 0;           // Picco di utilizzo
    size_t current_usage = 0;        // Utilizzo attuale
    size_t pool_size = 0;            // Dimensione pool
    size_t pool_available = 0;       // Memoria disponibile nel pool
    int allocation_count = 0;        // Numero allocazioni
    int deallocation_count = 0;      // Numero deallocazioni
    int pool_hits = 0;               // Hit del pool
    int pool_misses = 0;             // Miss del pool
};

/**
 * Blocco di memoria GPU
 */
struct MemoryBlock {
    void* ptr = nullptr;             // Puntatore GPU
    size_t size = 0;                 // Dimensione blocco
    bool in_use = false;             // In uso
    int ref_count = 0;               // Reference count
    
    MemoryBlock() = default;
    MemoryBlock(void* p, size_t s) : ptr(p), size(s), in_use(false), ref_count(0) {}
};

/**
 * Pool di memoria GPU per tipo specifico
 */
template<typename T>
class TypedMemoryPool {
public:
    TypedMemoryPool(size_t initial_capacity = 1024);
    ~TypedMemoryPool();
    
    // Alloca memoria del tipo T
    T* allocate(size_t count);
    
    // Dealloca memoria
    void deallocate(T* ptr);
    
    // Ottieni statistiche
    size_t getUsedCount() const { return used_count_; }
    size_t getTotalCapacity() const { return total_capacity_; }
    
private:
    std::vector<MemoryBlock> blocks_;
    size_t total_capacity_;
    size_t used_count_;
    mutable std::mutex mutex_;
    
    void expandPool(size_t min_size);
    MemoryBlock* findFreeBlock(size_t size);
};

/**
 * Manager principale memoria GPU
 */
class CudaMemoryManager {
public:
    static CudaMemoryManager& getInstance();
    
    // Inizializzazione e cleanup
    bool initialize(size_t initial_pool_size = 256 * 1024 * 1024); // 256MB default
    void shutdown();
    
    // Allocazione memoria generica
    void* allocate(size_t size, size_t alignment = 256);
    void deallocate(void* ptr);
    
    // Allocazione typed
    template<typename T>
    T* allocateTyped(size_t count) {
        return static_cast<T*>(allocate(count * sizeof(T), alignof(T)));
    }
    
    // Buffer per immagini (ottimizzato per rendering)
    float* allocateImageBuffer(int width, int height, int channels = 3);
    void deallocateImageBuffer(float* buffer);
    
    // Buffer per geometry (sfere, triangoli, etc.)
    void* allocateGeometryBuffer(size_t size);
    void deallocateGeometryBuffer(void* buffer);
    
    // Texture management
    cudaArray_t allocateTexture2D(int width, int height, cudaChannelFormatDesc format);
    void deallocateTexture(cudaArray_t texture);
    
    // Memory streaming per grandi dataset
    bool streamToDevice(void* dst, const void* src, size_t size, cudaStream_t stream = 0);
    bool streamFromDevice(void* dst, const void* src, size_t size, cudaStream_t stream = 0);
    
    // Statistiche e monitoring
    MemoryStats getStats() const;
    void printStats() const;
    
    // Garbage collection
    void garbageCollect();
    
    // Memory defragmentation
    void defragment();
    
    // Verifica integrità memoria
    bool validateMemory() const;

private:
    CudaMemoryManager() = default;
    ~CudaMemoryManager() = default;
    
    // Non copiabile
    CudaMemoryManager(const CudaMemoryManager&) = delete;
    CudaMemoryManager& operator=(const CudaMemoryManager&) = delete;
    
    bool initialized_ = false;
    mutable std::mutex mutex_;
    
    // Pool di memoria
    std::vector<MemoryBlock> memory_pool_;
    std::unordered_map<void*, size_t> allocated_blocks_;
    
    // Statistiche
    mutable MemoryStats stats_;
    
    // Configurazione
    size_t pool_size_ = 0;
    size_t alignment_ = 256;
    
    // Helper methods
    MemoryBlock* findFreeBlock(size_t size);
    void expandPool(size_t additional_size);
    void updateStats();
    bool isValidPointer(void* ptr) const;
};

/**
 * RAII wrapper per memoria GPU
 */
template<typename T>
class CudaBuffer {
public:
    CudaBuffer() : ptr_(nullptr), size_(0) {}
    
    explicit CudaBuffer(size_t count) : size_(count) {
        ptr_ = CudaMemoryManager::getInstance().allocateTyped<T>(count);
    }
    
    ~CudaBuffer() {
        if (ptr_) {
            CudaMemoryManager::getInstance().deallocate(ptr_);
        }
    }
    
    // Non copiabile, solo movibile
    CudaBuffer(const CudaBuffer&) = delete;
    CudaBuffer& operator=(const CudaBuffer&) = delete;
    
    CudaBuffer(CudaBuffer&& other) noexcept : ptr_(other.ptr_), size_(other.size_) {
        other.ptr_ = nullptr;
        other.size_ = 0;
    }
    
    CudaBuffer& operator=(CudaBuffer&& other) noexcept {
        if (this != &other) {
            if (ptr_) {
                CudaMemoryManager::getInstance().deallocate(ptr_);
            }
            ptr_ = other.ptr_;
            size_ = other.size_;
            other.ptr_ = nullptr;
            other.size_ = 0;
        }
        return *this;
    }
    
    // Accessors
    T* get() const { return ptr_; }
    size_t size() const { return size_; }
    bool empty() const { return ptr_ == nullptr; }
    
    // Resize (può causare riallocazione)
    void resize(size_t new_count) {
        if (new_count != size_) {
            if (ptr_) {
                CudaMemoryManager::getInstance().deallocate(ptr_);
            }
            if (new_count > 0) {
                ptr_ = CudaMemoryManager::getInstance().allocateTyped<T>(new_count);
            } else {
                ptr_ = nullptr;
            }
            size_ = new_count;
        }
    }
    
    // Copy data to/from device
    cudaError_t copyFromHost(const T* host_data, size_t count = 0) {
        if (count == 0) count = size_;
        return cudaMemcpy(ptr_, host_data, count * sizeof(T), cudaMemcpyHostToDevice);
    }
    
    cudaError_t copyToHost(T* host_data, size_t count = 0) const {
        if (count == 0) count = size_;
        return cudaMemcpy(host_data, ptr_, count * sizeof(T), cudaMemcpyDeviceToHost);
    }

private:
    T* ptr_;
    size_t size_;
};

// Typedef per buffer comuni
using FloatBuffer = CudaBuffer<float>;
using IntBuffer = CudaBuffer<int>;
using Vec3Buffer = CudaBuffer<float3>;

} // namespace gpu
} // namespace photon
