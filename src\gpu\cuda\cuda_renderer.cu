// src/gpu/cuda/cuda_renderer.cu
// PhotonRender - CUDA Ray Tracing Kernel
// Implementazione base del renderer CUDA per accelerazione GPU

#include <cuda_runtime.h>
#include <device_launch_parameters.h>
#include <curand_kernel.h>
#include <math.h>
#include <ctime>
#include <cstdio>

// Strutture dati GPU-friendly
struct Vec3 {
    float x, y, z;
    
    __device__ __host__ Vec3() : x(0), y(0), z(0) {}
    __device__ __host__ Vec3(float x_, float y_, float z_) : x(x_), y(y_), z(z_) {}
    
    __device__ __host__ Vec3 operator+(const Vec3& v) const {
        return Vec3(x + v.x, y + v.y, z + v.z);
    }
    
    __device__ __host__ Vec3 operator-(const Vec3& v) const {
        return Vec3(x - v.x, y - v.y, z - v.z);
    }
    
    __device__ __host__ Vec3 operator*(float t) const {
        return Vec3(x * t, y * t, z * t);
    }
    
    __device__ __host__ float dot(const Vec3& v) const {
        return x * v.x + y * v.y + z * v.z;
    }
    
    __device__ __host__ float length() const {
        return sqrtf(x * x + y * y + z * z);
    }
    
    __device__ __host__ Vec3 normalize() const {
        float len = length();
        return len > 0 ? Vec3(x/len, y/len, z/len) : Vec3(0, 0, 0);
    }
};

struct Ray {
    Vec3 origin;
    Vec3 direction;
    
    __device__ __host__ Ray() {}
    __device__ __host__ Ray(const Vec3& o, const Vec3& d) : origin(o), direction(d) {}
    
    __device__ __host__ Vec3 at(float t) const {
        return origin + direction * t;
    }
};

struct Sphere {
    Vec3 center;
    float radius;
    Vec3 color;
    
    __device__ __host__ Sphere() {}
    __device__ __host__ Sphere(const Vec3& c, float r, const Vec3& col) 
        : center(c), radius(r), color(col) {}
};

// Funzione di intersezione ray-sphere
__device__ bool hit_sphere(const Ray& ray, const Sphere& sphere, float t_min, float t_max, float& t_hit) {
    Vec3 oc = ray.origin - sphere.center;
    float a = ray.direction.dot(ray.direction);
    float b = 2.0f * oc.dot(ray.direction);
    float c = oc.dot(oc) - sphere.radius * sphere.radius;
    float discriminant = b * b - 4 * a * c;
    
    if (discriminant < 0) return false;
    
    float sqrt_discriminant = sqrtf(discriminant);
    float root = (-b - sqrt_discriminant) / (2.0f * a);
    
    if (root < t_min || root > t_max) {
        root = (-b + sqrt_discriminant) / (2.0f * a);
        if (root < t_min || root > t_max) {
            return false;
        }
    }
    
    t_hit = root;
    return true;
}

// Kernel CUDA per ray tracing base
__global__ void cuda_render_kernel(
    float* image,           // Buffer immagine output (RGB)
    int width,              // Larghezza immagine
    int height,             // Altezza immagine
    int samples_per_pixel,  // Campioni per pixel
    Sphere* spheres,        // Array di sfere
    int num_spheres,        // Numero di sfere
    unsigned int seed       // Seed per random
) {
    int x = blockIdx.x * blockDim.x + threadIdx.x;
    int y = blockIdx.y * blockDim.y + threadIdx.y;
    
    if (x >= width || y >= height) return;
    
    int pixel_index = (y * width + x) * 3; // RGB
    
    // Inizializza generatore random per questo thread
    curandState rand_state;
    curand_init(seed + y * width + x, 0, 0, &rand_state);
    
    Vec3 color(0, 0, 0);
    
    // Multi-sampling per anti-aliasing
    for (int s = 0; s < samples_per_pixel; s++) {
        // Coordinate pixel con jittering
        float u = (float(x) + curand_uniform(&rand_state)) / float(width);
        float v = (float(y) + curand_uniform(&rand_state)) / float(height);
        
        // Camera semplice
        Vec3 camera_pos(0, 0, 0);
        Vec3 lower_left(-2.0f, -1.0f, -1.0f);
        Vec3 horizontal(4.0f, 0, 0);
        Vec3 vertical(0, 2.0f, 0);
        
        Vec3 direction = lower_left + horizontal * u + vertical * v - camera_pos;
        Ray ray(camera_pos, direction.normalize());
        
        // Ray tracing
        Vec3 sample_color(0.5f, 0.7f, 1.0f); // Background blu
        float closest_t = 1000000.0f;
        int hit_sphere_idx = -1;
        
        // Test intersezione con tutte le sfere
        for (int i = 0; i < num_spheres; i++) {
            float t;
            if (hit_sphere(ray, spheres[i], 0.001f, closest_t, t)) {
                closest_t = t;
                hit_sphere_idx = i;
            }
        }
        
        // Se abbiamo colpito una sfera, usa il suo colore
        if (hit_sphere_idx >= 0) {
            sample_color = spheres[hit_sphere_idx].color;
        }
        
        color = color + sample_color;
    }
    
    // Media dei campioni
    color = color * (1.0f / float(samples_per_pixel));
    
    // Gamma correction
    color.x = sqrtf(color.x);
    color.y = sqrtf(color.y);
    color.z = sqrtf(color.z);
    
    // Scrivi nel buffer immagine
    image[pixel_index + 0] = fminf(color.x, 1.0f);
    image[pixel_index + 1] = fminf(color.y, 1.0f);
    image[pixel_index + 2] = fminf(color.z, 1.0f);
}

// Funzioni C++ per interfaccia con host
extern "C" {
    // Inizializzazione CUDA
    bool cuda_init() {
        int device_count;
        cudaError_t error = cudaGetDeviceCount(&device_count);
        
        if (error != cudaSuccess || device_count == 0) {
            return false;
        }
        
        // Seleziona il primo device
        cudaSetDevice(0);
        
        // Verifica proprietà device
        cudaDeviceProp prop;
        cudaGetDeviceProperties(&prop, 0);
        
        printf("[CUDA] Device: %s\n", prop.name);
        printf("[CUDA] Compute Capability: %d.%d\n", prop.major, prop.minor);
        printf("[CUDA] Global Memory: %.1f GB\n", prop.totalGlobalMem / (1024.0f * 1024.0f * 1024.0f));
        
        return true;
    }
    
    // Cleanup CUDA
    void cuda_cleanup() {
        cudaDeviceReset();
    }

    // Funzione di rendering CUDA
    bool cuda_render(
        float* host_image,      // Buffer immagine host (RGB)
        int width,              // Larghezza immagine
        int height,             // Altezza immagine
        int samples_per_pixel,  // Campioni per pixel
        float* sphere_data,     // Dati sfere [x,y,z,r,r,g,b] per ogni sfera
        int num_spheres         // Numero di sfere
    ) {
        // Calcola dimensioni
        size_t image_size = width * height * 3 * sizeof(float);
        size_t spheres_size = num_spheres * sizeof(Sphere);

        // Alloca memoria GPU
        float* d_image;
        Sphere* d_spheres;

        cudaError_t error;

        error = cudaMalloc(&d_image, image_size);
        if (error != cudaSuccess) {
            printf("[CUDA ERROR] Failed to allocate image memory: %s\n", cudaGetErrorString(error));
            return false;
        }

        error = cudaMalloc(&d_spheres, spheres_size);
        if (error != cudaSuccess) {
            printf("[CUDA ERROR] Failed to allocate spheres memory: %s\n", cudaGetErrorString(error));
            cudaFree(d_image);
            return false;
        }

        // Prepara dati sfere
        Sphere* host_spheres = new Sphere[num_spheres];
        for (int i = 0; i < num_spheres; i++) {
            int idx = i * 7; // x,y,z,r,r,g,b
            host_spheres[i] = Sphere(
                Vec3(sphere_data[idx], sphere_data[idx+1], sphere_data[idx+2]),  // center
                sphere_data[idx+3],                                              // radius
                Vec3(sphere_data[idx+4], sphere_data[idx+5], sphere_data[idx+6]) // color
            );
        }

        // Copia dati su GPU
        error = cudaMemcpy(d_spheres, host_spheres, spheres_size, cudaMemcpyHostToDevice);
        if (error != cudaSuccess) {
            printf("[CUDA ERROR] Failed to copy spheres to device: %s\n", cudaGetErrorString(error));
            delete[] host_spheres;
            cudaFree(d_image);
            cudaFree(d_spheres);
            return false;
        }

        // Configura grid e block
        dim3 block_size(16, 16);
        dim3 grid_size((width + block_size.x - 1) / block_size.x,
                       (height + block_size.y - 1) / block_size.y);

        // Genera seed random
        unsigned int seed = (unsigned int)time(nullptr);

        // Avvia kernel
        cuda_render_kernel<<<grid_size, block_size>>>(
            d_image, width, height, samples_per_pixel, d_spheres, num_spheres, seed
        );

        // Verifica errori kernel
        error = cudaGetLastError();
        if (error != cudaSuccess) {
            printf("[CUDA ERROR] Kernel launch failed: %s\n", cudaGetErrorString(error));
            delete[] host_spheres;
            cudaFree(d_image);
            cudaFree(d_spheres);
            return false;
        }

        // Aspetta completamento
        error = cudaDeviceSynchronize();
        if (error != cudaSuccess) {
            printf("[CUDA ERROR] Device synchronization failed: %s\n", cudaGetErrorString(error));
            delete[] host_spheres;
            cudaFree(d_image);
            cudaFree(d_spheres);
            return false;
        }

        // Copia risultato su host
        error = cudaMemcpy(host_image, d_image, image_size, cudaMemcpyDeviceToHost);
        if (error != cudaSuccess) {
            printf("[CUDA ERROR] Failed to copy image from device: %s\n", cudaGetErrorString(error));
            delete[] host_spheres;
            cudaFree(d_image);
            cudaFree(d_spheres);
            return false;
        }

        // Cleanup
        delete[] host_spheres;
        cudaFree(d_image);
        cudaFree(d_spheres);

        return true;
    }
}
