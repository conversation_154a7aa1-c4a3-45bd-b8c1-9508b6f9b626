// src/gpu/cuda/cuda_renderer.h
// PhotonRender - CUDA Ray Tracing Interface
// Header per interfaccia C++ con kernel CUDA

#pragma once

#ifdef __cplusplus
extern "C" {
#endif

/**
 * Inizializza il sistema CUDA
 * @return true se l'inizializzazione è riuscita, false altrimenti
 */
bool cuda_init();

/**
 * Cleanup del sistema CUDA
 */
void cuda_cleanup();

/**
 * Esegue il rendering CUDA di una scena semplice
 * @param host_image Buffer immagine output (RGB float, width*height*3)
 * @param width Larghezza immagine in pixel
 * @param height Altezza immagine in pixel  
 * @param samples_per_pixel Numero di campioni per pixel (anti-aliasing)
 * @param sphere_data Array di dati sfere [x,y,z,radius,r,g,b] per ogni sfera
 * @param num_spheres Numero di sfere nella scena
 * @return true se il rendering è riuscito, false altrimenti
 */
bool cuda_render(
    float* host_image,
    int width,
    int height,
    int samples_per_pixel,
    float* sphere_data,
    int num_spheres
);

#ifdef __cplusplus
}
#endif
