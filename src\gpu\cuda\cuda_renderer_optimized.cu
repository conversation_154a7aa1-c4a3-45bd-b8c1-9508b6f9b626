// src/gpu/cuda/cuda_renderer_optimized.cu
// PhotonRender - Optimized CUDA Renderer
// Renderer CUDA ottimizzato con memory management avanzato

#include <cuda_runtime.h>
#include <device_launch_parameters.h>
#include <curand_kernel.h>
#include <math.h>
#include <ctime>
#include <cstdio>
#include <chrono>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

// Strutture dati ottimizzate per GPU
struct Vec3 {
    float x, y, z;
    
    __device__ __host__ Vec3() : x(0), y(0), z(0) {}
    __device__ __host__ Vec3(float x_, float y_, float z_) : x(x_), y(y_), z(z_) {}
    
    __device__ __host__ Vec3 operator+(const Vec3& v) const {
        return Vec3(x + v.x, y + v.y, z + v.z);
    }
    
    __device__ __host__ Vec3 operator-(const Vec3& v) const {
        return Vec3(x - v.x, y - v.y, z - v.z);
    }
    
    __device__ __host__ Vec3 operator*(float t) const {
        return Vec3(x * t, y * t, z * t);
    }

    __device__ __host__ Vec3 operator*(const Vec3& v) const {
        return Vec3(x * v.x, y * v.y, z * v.z);
    }
    
    __device__ __host__ float dot(const Vec3& v) const {
        return x * v.x + y * v.y + z * v.z;
    }
    
    __device__ __host__ float length() const {
        return sqrtf(x * x + y * y + z * z);
    }
    
    __device__ __host__ Vec3 normalize() const {
        float len = length();
        return len > 0 ? Vec3(x/len, y/len, z/len) : Vec3(0, 0, 0);
    }
};

struct Ray {
    Vec3 origin;
    Vec3 direction;
    
    __device__ __host__ Ray() {}
    __device__ __host__ Ray(const Vec3& o, const Vec3& d) : origin(o), direction(d) {}
    
    __device__ __host__ Vec3 at(float t) const {
        return origin + direction * t;
    }
};

// Material ottimizzato per GPU
struct Material {
    Vec3 albedo;
    float roughness;
    float metallic;
    float emission;
    
    __device__ __host__ Material() : albedo(0.5f, 0.5f, 0.5f), roughness(0.5f), metallic(0.0f), emission(0.0f) {}
    __device__ __host__ Material(const Vec3& a, float r, float m, float e = 0.0f) 
        : albedo(a), roughness(r), metallic(m), emission(e) {}
};

struct Sphere {
    Vec3 center;
    float radius;
    Material material;
    
    __device__ __host__ Sphere() {}
    __device__ __host__ Sphere(const Vec3& c, float r, const Material& m) 
        : center(c), radius(r), material(m) {}
};

struct HitRecord {
    Vec3 point;
    Vec3 normal;
    float t;
    Material material;
    bool hit;
    
    __device__ HitRecord() : hit(false) {}
};

// Camera ottimizzata
struct Camera {
    Vec3 position;
    Vec3 target;
    Vec3 up;
    float fov;
    float aspect_ratio;
    
    // Cached vectors per performance
    Vec3 u, v, w;
    Vec3 lower_left;
    Vec3 horizontal;
    Vec3 vertical;
    
    __device__ __host__ void updateVectors() {
        w = (position - target).normalize();
        u = up.normalize(); // Semplificato
        v = w; // Semplificato
        
        float viewport_height = 2.0f * tanf(fov * 0.5f);
        float viewport_width = viewport_height * aspect_ratio;
        
        horizontal = Vec3(viewport_width, 0, 0);
        vertical = Vec3(0, viewport_height, 0);
        lower_left = position - horizontal * 0.5f - vertical * 0.5f - Vec3(0, 0, 1.0f);
    }
    
    __device__ Ray getRay(float s, float t) const {
        Vec3 direction = lower_left + horizontal * s + vertical * t - position;
        return Ray(position, direction.normalize());
    }
};

// Intersezione ray-sphere ottimizzata
__device__ bool hit_sphere(const Ray& ray, const Sphere& sphere, float t_min, float t_max, HitRecord& rec) {
    Vec3 oc = ray.origin - sphere.center;
    float a = ray.direction.dot(ray.direction);
    float half_b = oc.dot(ray.direction);
    float c = oc.dot(oc) - sphere.radius * sphere.radius;
    float discriminant = half_b * half_b - a * c;
    
    if (discriminant < 0) return false;
    
    float sqrt_discriminant = sqrtf(discriminant);
    float root = (-half_b - sqrt_discriminant) / a;
    
    if (root < t_min || root > t_max) {
        root = (-half_b + sqrt_discriminant) / a;
        if (root < t_min || root > t_max) {
            return false;
        }
    }
    
    rec.t = root;
    rec.point = ray.at(rec.t);
    Vec3 outward_normal = (rec.point - sphere.center) * (1.0f / sphere.radius);
    rec.normal = outward_normal;
    rec.material = sphere.material;
    rec.hit = true;
    
    return true;
}

// Test intersezione con scena
__device__ bool hit_scene(const Ray& ray, Sphere* spheres, int num_spheres, float t_min, float t_max, HitRecord& rec) {
    HitRecord temp_rec;
    bool hit_anything = false;
    float closest_so_far = t_max;
    
    for (int i = 0; i < num_spheres; i++) {
        if (hit_sphere(ray, spheres[i], t_min, closest_so_far, temp_rec)) {
            hit_anything = true;
            closest_so_far = temp_rec.t;
            rec = temp_rec;
        }
    }
    
    return hit_anything;
}

// Shading PBR semplificato
__device__ Vec3 shade_pbr(const HitRecord& rec, const Vec3& light_dir, const Vec3& view_dir, curandState* rand_state) {
    // Emissive materials
    if (rec.material.emission > 0.0f) {
        return rec.material.albedo * rec.material.emission;
    }
    
    // Lambertian diffuse
    float ndotl = fmaxf(0.0f, rec.normal.dot(light_dir));
    Vec3 diffuse = rec.material.albedo * ndotl;
    
    // Specular (simplified)
    Vec3 reflect_dir = light_dir - rec.normal * (2.0f * light_dir.dot(rec.normal));
    float spec = powf(fmaxf(0.0f, view_dir.dot(reflect_dir)), 32.0f * (1.0f - rec.material.roughness));
    Vec3 specular = Vec3(1.0f, 1.0f, 1.0f) * spec * rec.material.metallic;
    
    // Ambient occlusion semplificato
    float ao = 1.0f - rec.material.roughness * 0.3f;
    
    return (diffuse + specular) * ao;
}

// Kernel ray tracing ottimizzato con multi-resolution support
__global__ void cuda_raytrace_optimized_kernel(
    float* image,               // Buffer immagine RGB
    int width,                  // Larghezza
    int height,                 // Altezza
    int samples_per_pixel,      // Campioni per pixel
    Sphere* spheres,            // Scena
    int num_spheres,            // Numero oggetti
    Camera camera,              // Camera
    Vec3 light_dir,             // Direzione luce
    unsigned int seed,          // Seed random
    int tile_x,                 // Tile offset X (per tiling)
    int tile_y,                 // Tile offset Y
    int tile_width,             // Tile width
    int tile_height,            // Tile height
    float quality_scale         // Scale qualità (1.0 = full, 0.5 = half, etc.)
) {
    int local_x = blockIdx.x * blockDim.x + threadIdx.x;
    int local_y = blockIdx.y * blockDim.y + threadIdx.y;
    
    if (local_x >= tile_width || local_y >= tile_height) return;
    
    int x = tile_x + local_x;
    int y = tile_y + local_y;
    
    if (x >= width || y >= height) return;
    
    int pixel_index = (y * width + x) * 3;
    
    // Adaptive sampling basato su quality scale
    int adaptive_spp = (int)(samples_per_pixel * quality_scale);
    adaptive_spp = max(1, adaptive_spp);
    
    // Inizializza random con seed migliorato
    curandState rand_state;
    curand_init(seed + y * width + x + tile_x * 1000 + tile_y * 1000000, 0, 0, &rand_state);
    
    Vec3 color(0, 0, 0);
    
    // Multi-sampling con jittering
    for (int s = 0; s < adaptive_spp; s++) {
        float u = (float(x) + curand_uniform(&rand_state)) / float(width);
        float v = (float(y) + curand_uniform(&rand_state)) / float(height);
        
        Ray ray = camera.getRay(u, v);
        
        // Ray tracing con bounces multipli
        Vec3 sample_color(0, 0, 0);
        Vec3 attenuation(1, 1, 1);
        
        for (int bounce = 0; bounce < 3; bounce++) { // Max 3 bounces
            HitRecord rec;
            if (hit_scene(ray, spheres, num_spheres, 0.001f, 1000.0f, rec)) {
                Vec3 view_dir = (camera.position - rec.point).normalize();
                Vec3 bounce_color = shade_pbr(rec, light_dir.normalize(), view_dir, &rand_state);
                
                sample_color = sample_color + attenuation * bounce_color;
                
                // Setup per prossimo bounce (simplified)
                if (rec.material.metallic > 0.5f) {
                    Vec3 reflected = ray.direction - rec.normal * (2.0f * ray.direction.dot(rec.normal));
                    ray = Ray(rec.point, reflected);
                    attenuation = attenuation * rec.material.albedo * 0.8f;
                } else {
                    break; // Stop bouncing per diffuse materials
                }
            } else {
                // Background gradient
                float t = 0.5f * (ray.direction.y + 1.0f);
                Vec3 bg = Vec3(1.0f, 1.0f, 1.0f) * (1.0f - t) + Vec3(0.5f, 0.7f, 1.0f) * t;
                sample_color = sample_color + attenuation * bg;
                break;
            }
        }
        
        color = color + sample_color;
    }
    
    // Media e gamma correction
    color = color * (1.0f / float(adaptive_spp));
    color.x = sqrtf(fmaxf(0.0f, color.x));
    color.y = sqrtf(fmaxf(0.0f, color.y));
    color.z = sqrtf(fmaxf(0.0f, color.z));
    
    // Clamp e scrivi
    image[pixel_index + 0] = fminf(color.x, 1.0f);
    image[pixel_index + 1] = fminf(color.y, 1.0f);
    image[pixel_index + 2] = fminf(color.z, 1.0f);
}

// Funzioni C++ per interfaccia
extern "C" {
    
    // Rendering ottimizzato con memory management
    bool cuda_raytrace_optimized(
        float* host_image,
        int width,
        int height,
        int samples_per_pixel,
        float quality_scale,
        bool use_tiling,
        double* render_time_ms
    ) {
        auto start_time = std::chrono::high_resolution_clock::now();
        
        // Usa memory manager per allocazioni
        size_t image_size = width * height * 3 * sizeof(float);
        
        // Nota: Qui dovremmo usare CudaMemoryManager, ma per semplicità usiamo allocazione diretta
        // In una implementazione completa, integreremmo con il memory manager
        float* d_image;
        cudaError_t error = cudaMalloc(&d_image, image_size);
        if (error != cudaSuccess) {
            printf("[CUDA ERROR] Image allocation failed: %s\n", cudaGetErrorString(error));
            return false;
        }
        
        // Crea scena di test ottimizzata
        const int num_spheres = 6;
        Sphere host_spheres[num_spheres] = {
            Sphere(Vec3(-1, 0, -1), 0.5f, Material(Vec3(0.8f, 0.3f, 0.3f), 0.1f, 0.0f)),     // Rosso diffuse
            Sphere(Vec3(1, 0, -1), 0.5f, Material(Vec3(0.8f, 0.8f, 0.9f), 0.0f, 1.0f)),      // Metallo
            Sphere(Vec3(0, -100.5f, -1), 100.0f, Material(Vec3(0.8f, 0.8f, 0.0f), 0.8f, 0.0f)), // Ground
            Sphere(Vec3(0, 1, -1), 0.5f, Material(Vec3(0.3f, 0.3f, 0.8f), 0.2f, 0.0f)),      // Blu
            Sphere(Vec3(-0.5f, 0.5f, -0.5f), 0.3f, Material(Vec3(1.0f, 1.0f, 1.0f), 0.0f, 0.0f, 2.0f)), // Emissive
            Sphere(Vec3(0.5f, -0.3f, -0.8f), 0.2f, Material(Vec3(0.9f, 0.1f, 0.9f), 0.0f, 0.8f)) // Magenta metal
        };
        
        Sphere* d_spheres;
        error = cudaMalloc(&d_spheres, num_spheres * sizeof(Sphere));
        if (error != cudaSuccess) {
            printf("[CUDA ERROR] Spheres allocation failed: %s\n", cudaGetErrorString(error));
            cudaFree(d_image);
            return false;
        }
        
        error = cudaMemcpy(d_spheres, host_spheres, num_spheres * sizeof(Sphere), cudaMemcpyHostToDevice);
        if (error != cudaSuccess) {
            printf("[CUDA ERROR] Spheres copy failed: %s\n", cudaGetErrorString(error));
            cudaFree(d_image);
            cudaFree(d_spheres);
            return false;
        }
        
        // Setup camera ottimizzata
        Camera camera;
        camera.position = Vec3(0, 0, 0);
        camera.target = Vec3(0, 0, -1);
        camera.up = Vec3(0, 1, 0);
        camera.fov = 45.0f * M_PI / 180.0f;
        camera.aspect_ratio = float(width) / float(height);
        camera.updateVectors();
        
        Vec3 light_dir(-0.5f, -1.0f, -0.5f);
        unsigned int seed = (unsigned int)time(nullptr);
        
        if (use_tiling) {
            // Rendering con tiling per grandi immagini
            int tile_size = 128;
            for (int ty = 0; ty < height; ty += tile_size) {
                for (int tx = 0; tx < width; tx += tile_size) {
                    int tw = min(tile_size, width - tx);
                    int th = min(tile_size, height - ty);
                    
                    dim3 block_size(16, 16);
                    dim3 grid_size((tw + block_size.x - 1) / block_size.x,
                                   (th + block_size.y - 1) / block_size.y);
                    
                    cuda_raytrace_optimized_kernel<<<grid_size, block_size>>>(
                        d_image, width, height, samples_per_pixel, d_spheres, num_spheres,
                        camera, light_dir, seed, tx, ty, tw, th, quality_scale
                    );
                }
            }
        } else {
            // Rendering normale
            dim3 block_size(16, 16);
            dim3 grid_size((width + block_size.x - 1) / block_size.x,
                           (height + block_size.y - 1) / block_size.y);
            
            cuda_raytrace_optimized_kernel<<<grid_size, block_size>>>(
                d_image, width, height, samples_per_pixel, d_spheres, num_spheres,
                camera, light_dir, seed, 0, 0, width, height, quality_scale
            );
        }
        
        // Sincronizza
        error = cudaDeviceSynchronize();
        if (error != cudaSuccess) {
            printf("[CUDA ERROR] Kernel execution failed: %s\n", cudaGetErrorString(error));
            cudaFree(d_image);
            cudaFree(d_spheres);
            return false;
        }
        
        auto end_time = std::chrono::high_resolution_clock::now();
        *render_time_ms = std::chrono::duration<double, std::milli>(end_time - start_time).count();
        
        // Copia risultato
        error = cudaMemcpy(host_image, d_image, image_size, cudaMemcpyDeviceToHost);
        if (error != cudaSuccess) {
            printf("[CUDA ERROR] Result copy failed: %s\n", cudaGetErrorString(error));
            cudaFree(d_image);
            cudaFree(d_spheres);
            return false;
        }
        
        // Cleanup
        cudaFree(d_image);
        cudaFree(d_spheres);
        
        return true;
    }
}

// Programma di test per renderer ottimizzato
int main() {
    printf("=== PhotonRender CUDA Optimized Renderer Test ===\n");
    printf("Testing memory optimization and multi-resolution rendering\n");
    printf("======================================================\n");
    
    // Test configurazioni ottimizzate
    struct TestConfig {
        int width, height, spp;
        float quality;
        bool tiling;
        const char* name;
    };
    
    TestConfig configs[] = {
        {256, 256, 8, 1.0f, false, "Baseline Full Quality"},
        {256, 256, 8, 0.5f, false, "Half Quality (Fast Preview)"},
        {512, 512, 8, 1.0f, true, "Large with Tiling"},
        {1024, 1024, 4, 0.25f, true, "4K Preview Mode"},
        {256, 256, 16, 1.0f, false, "High Quality"}
    };
    
    int num_configs = sizeof(configs) / sizeof(configs[0]);
    
    for (int i = 0; i < num_configs; i++) {
        TestConfig& config = configs[i];
        
        printf("\n[TEST %d/%d] %s (%dx%d @ %d SPP, Q=%.2f)\n", 
               i+1, num_configs, config.name, config.width, config.height, config.spp, config.quality);
        
        size_t image_size = config.width * config.height * 3;
        float* image = new float[image_size];
        
        double render_time_ms;
        bool success = cuda_raytrace_optimized(
            image, config.width, config.height, config.spp, 
            config.quality, config.tiling, &render_time_ms
        );
        
        if (success) {
            long long total_samples = (long long)config.width * config.height * config.spp * config.quality;
            double samples_per_second = total_samples / (render_time_ms / 1000.0);
            double mrays_per_second = samples_per_second / 1000000.0;
            
            printf("[SUCCESS] Render completed in %.2f ms\n", render_time_ms);
            printf("[PERF] %.2f Mrays/sec\n", mrays_per_second);
            
            // Confronto con baseline
            if (config.width == 256 && config.height == 256 && config.spp == 8 && config.quality == 1.0f) {
                double cpu_baseline_ms = 25.0;
                double speedup = cpu_baseline_ms / render_time_ms;
                printf("[SPEEDUP] %.2fx vs CPU baseline\n", speedup);
            }
        } else {
            printf("[ERROR] Render failed\n");
        }
        
        delete[] image;
    }
    
    printf("\n=== Optimized Renderer Tests Completed ===\n");
    return 0;
}
