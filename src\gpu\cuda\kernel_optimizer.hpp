// src/gpu/cuda/kernel_optimizer.hpp
// PhotonRender - Professional Rendering Engine for SketchUp
// GPU Kernel Optimization Manager

#pragma once

#include "optimized_kernels.cuh"
#include "../../core/profiling/performance_profiler.hpp"
#include <memory>
#include <vector>
#include <unordered_map>
#include <chrono>

namespace photon {
namespace gpu {

/**
 * @brief Kernel optimization strategies
 */
enum class OptimizationStrategy {
    LATENCY_FOCUSED,        ///< Minimize latency for interactive rendering
    THROUGHPUT_FOCUSED,     ///< Maximize throughput for batch rendering
    BALANCED,               ///< Balance between latency and throughput
    ADAPTIVE,               ///< Adaptive based on workload characteristics
    CUSTOM                  ///< Custom user-defined strategy
};

/**
 * @brief Kernel performance profile
 */
struct KernelProfile {
    std::string kernel_name;
    float execution_time_ms;
    float occupancy_percentage;
    int active_warps;
    int active_blocks;
    size_t shared_memory_used;
    int registers_used;
    float memory_bandwidth_utilization;
    float rt_core_utilization;
    
    // Performance metrics
    float rays_per_second;
    float intersections_per_second;
    float samples_per_second;
    
    // Efficiency metrics
    float efficiency_score;
    float power_efficiency;
    float thermal_efficiency;
    
    void calculateEfficiencyScore();
    std::string toString() const;
};

/**
 * @brief Workload characteristics
 */
struct WorkloadCharacteristics {
    int image_width, image_height;
    int samples_per_pixel;
    int max_path_length;
    float scene_complexity;
    float ray_coherence;
    float material_complexity;
    
    // Dynamic characteristics
    float temporal_coherence;
    float spatial_coherence;
    float cache_hit_ratio;
    
    void analyze(const OptimizedRenderParams& params);
    float getComplexityScore() const;
};

/**
 * @brief Optimization parameters
 */
struct OptimizationParams {
    // Block and grid configuration
    dim3 optimal_block_size;
    dim3 optimal_grid_size;
    size_t shared_memory_per_block;
    int registers_per_thread;
    
    // Wavefront configuration
    WavefrontConfig wavefront_config;
    
    // RT core configuration
    RTCoreConfig rt_config;
    
    // Memory optimization
    bool use_texture_memory;
    bool use_constant_memory;
    bool enable_memory_coalescing;
    int memory_access_pattern;
    
    // Advanced optimizations
    bool enable_warp_specialization;
    bool enable_dynamic_parallelism;
    bool enable_cooperative_groups;
    bool enable_tensor_cores;
    
    void reset();
    void applyStrategy(OptimizationStrategy strategy);
};

/**
 * @brief GPU Kernel Optimizer
 */
class KernelOptimizer {
public:
    /**
     * @brief Constructor
     */
    KernelOptimizer();
    
    /**
     * @brief Destructor
     */
    ~KernelOptimizer();
    
    /**
     * @brief Initialize optimizer
     */
    bool initialize();
    
    /**
     * @brief Shutdown optimizer
     */
    void shutdown();
    
    /**
     * @brief Optimize kernels for given workload
     */
    OptimizationParams optimizeForWorkload(
        const WorkloadCharacteristics& workload,
        OptimizationStrategy strategy = OptimizationStrategy::ADAPTIVE
    );
    
    /**
     * @brief Profile kernel performance
     */
    KernelProfile profileKernel(
        const std::string& kernel_name,
        const OptimizedRenderParams& params,
        int iterations = 10
    );
    
    /**
     * @brief Auto-tune kernel parameters
     */
    OptimizationParams autoTuneKernel(
        const std::string& kernel_name,
        const OptimizedRenderParams& params,
        float target_performance = 0.0f
    );
    
    /**
     * @brief Benchmark RT core utilization
     */
    RTCoreCounters benchmarkRTCores(
        const OptimizedRenderParams& params,
        int duration_ms = 1000
    );
    
    /**
     * @brief Analyze memory access patterns
     */
    struct MemoryAccessAnalysis {
        float coalescing_efficiency;
        float cache_hit_ratio;
        float bandwidth_utilization;
        int memory_transactions;
        std::vector<std::string> optimization_suggestions;
    };
    
    MemoryAccessAnalysis analyzeMemoryAccess(
        const OptimizedRenderParams& params
    );
    
    /**
     * @brief Dynamic load balancing
     */
    void dynamicLoadBalance(
        OptimizedRenderParams& params,
        const std::vector<float>& tile_complexities
    );
    
    /**
     * @brief Adaptive quality scaling
     */
    void adaptiveQualityScaling(
        OptimizedRenderParams& params,
        float target_framerate,
        float current_framerate
    );
    
    /**
     * @brief Get optimization recommendations
     */
    std::vector<std::string> getOptimizationRecommendations(
        const KernelProfile& profile,
        const WorkloadCharacteristics& workload
    );
    
    /**
     * @brief Generate optimization report
     */
    std::string generateOptimizationReport() const;
    
    /**
     * @brief Set optimization strategy
     */
    void setOptimizationStrategy(OptimizationStrategy strategy) {
        m_strategy = strategy;
    }
    
    /**
     * @brief Get current strategy
     */
    OptimizationStrategy getOptimizationStrategy() const {
        return m_strategy;
    }
    
    /**
     * @brief Enable/disable profiling
     */
    void setProfilingEnabled(bool enabled) {
        m_profiling_enabled = enabled;
    }

private:
    // Core data
    bool m_initialized;
    OptimizationStrategy m_strategy;
    bool m_profiling_enabled;
    
    // Device information
    cudaDeviceProp m_device_props;
    RTCoreConfig m_rt_config;
    
    // Performance history
    std::unordered_map<std::string, std::vector<KernelProfile>> m_kernel_profiles;
    std::vector<WorkloadCharacteristics> m_workload_history;
    
    // Optimization cache
    std::unordered_map<size_t, OptimizationParams> m_optimization_cache;
    
    // Internal methods
    void detectDeviceCapabilities();
    void initializeRTCoreConfig();
    
    // Optimization algorithms
    OptimizationParams optimizeLatencyFocused(const WorkloadCharacteristics& workload);
    OptimizationParams optimizeThroughputFocused(const WorkloadCharacteristics& workload);
    OptimizationParams optimizeBalanced(const WorkloadCharacteristics& workload);
    OptimizationParams optimizeAdaptive(const WorkloadCharacteristics& workload);
    
    // Auto-tuning
    struct TuningResult {
        OptimizationParams params;
        float performance_score;
        bool valid;
    };
    
    TuningResult tuneBlockSize(const OptimizedRenderParams& params);
    TuningResult tuneSharedMemory(const OptimizedRenderParams& params);
    TuningResult tuneWavefrontSize(const OptimizedRenderParams& params);
    TuningResult tuneRTCoreUtilization(const OptimizedRenderParams& params);
    
    // Performance analysis
    float calculatePerformanceScore(const KernelProfile& profile);
    float predictPerformance(const OptimizationParams& params, const WorkloadCharacteristics& workload);
    
    // Cache management
    size_t calculateWorkloadHash(const WorkloadCharacteristics& workload);
    void updateOptimizationCache(size_t hash, const OptimizationParams& params);
    bool getCachedOptimization(size_t hash, OptimizationParams& params);
    
    // Profiling utilities
    void startProfiling();
    void stopProfiling();
    KernelProfile collectProfilingData(const std::string& kernel_name);
    
    // Memory analysis
    void analyzeMemoryCoalescing(const OptimizedRenderParams& params, MemoryAccessAnalysis& analysis);
    void analyzeCacheUtilization(const OptimizedRenderParams& params, MemoryAccessAnalysis& analysis);
    void analyzeBandwidthUtilization(const OptimizedRenderParams& params, MemoryAccessAnalysis& analysis);
};

/**
 * @brief Wavefront Path Tracer with optimized kernels
 */
class OptimizedWavefrontRenderer {
public:
    OptimizedWavefrontRenderer();
    ~OptimizedWavefrontRenderer();
    
    /**
     * @brief Initialize renderer
     */
    bool initialize(const RTCoreConfig& config);
    
    /**
     * @brief Render with optimized wavefront path tracing
     */
    bool render(OptimizedRenderParams& params);
    
    /**
     * @brief Set kernel optimizer
     */
    void setKernelOptimizer(std::shared_ptr<KernelOptimizer> optimizer) {
        m_optimizer = optimizer;
    }
    
    /**
     * @brief Get render statistics
     */
    struct RenderStatistics {
        float total_render_time_ms;
        float rays_per_second;
        float rt_core_utilization;
        int total_rays_traced;
        int total_intersections;
        float memory_bandwidth_utilization;
        std::vector<KernelProfile> kernel_profiles;
    };
    
    RenderStatistics getLastRenderStatistics() const {
        return m_last_stats;
    }

private:
    bool m_initialized;
    std::shared_ptr<KernelOptimizer> m_optimizer;
    
    // GPU memory management
    WavefrontData m_wavefront_data;
    curandState* m_rand_states;
    
    // Performance monitoring
    RenderStatistics m_last_stats;
    
    // Internal methods
    bool allocateWavefrontData(const OptimizedRenderParams& params);
    void deallocateWavefrontData();
    bool initializeRandomStates(int num_states);
    
    // Rendering pipeline
    void generatePrimaryRays(const OptimizedRenderParams& params);
    void traceRays(const OptimizedRenderParams& params);
    void evaluateMaterials(const OptimizedRenderParams& params);
    void compactRays(const OptimizedRenderParams& params);
    void accumulateResults(const OptimizedRenderParams& params);
    
    // Performance monitoring
    void updateRenderStatistics(const OptimizedRenderParams& params);
    void profileKernelExecution(const std::string& kernel_name, float execution_time);
};

/**
 * @brief Utility functions for kernel optimization
 */
namespace KernelOptimizationUtils {
    /**
     * @brief Calculate optimal block size for given kernel
     */
    dim3 calculateOptimalBlockSize(
        const cudaDeviceProp& props,
        size_t shared_memory_per_block,
        int registers_per_thread
    );
    
    /**
     * @brief Estimate kernel occupancy
     */
    float estimateOccupancy(
        const cudaDeviceProp& props,
        const dim3& block_size,
        size_t shared_memory_per_block,
        int registers_per_thread
    );
    
    /**
     * @brief Analyze workload characteristics
     */
    WorkloadCharacteristics analyzeWorkload(
        const OptimizedRenderParams& params
    );
    
    /**
     * @brief Generate optimization suggestions
     */
    std::vector<std::string> generateOptimizationSuggestions(
        const KernelProfile& profile,
        const WorkloadCharacteristics& workload
    );
}

} // namespace gpu
} // namespace photon
