// src/gpu/cuda/optimized_kernels.cuh
// PhotonRender - Professional Rendering Engine for SketchUp
// Advanced GPU Kernel Optimization System

#pragma once

#include <cuda_runtime.h>
#include <curand_kernel.h>
#include <cooperative_groups.h>
#include <cub/cub.cuh>
#include "../../core/math/vec3.hpp"
#include "../../core/math/ray.hpp"

namespace photon {
namespace gpu {

// Forward declarations
struct OptimizedRenderParams;
struct RTCoreConfig;
struct WavefrontData;

/**
 * @brief RT Core utilization strategies
 */
enum class RTCoreStrategy {
    BALANCED,           ///< Balanced workload across RT cores
    LATENCY_OPTIMIZED,  ///< Minimize latency for interactive rendering
    THROUGHPUT_OPTIMIZED, ///< Maximize throughput for production rendering
    ADAPTIVE,           ///< Adaptive strategy based on scene complexity
    CUSTOM              ///< Custom strategy with user parameters
};

/**
 * @brief Wavefront path tracing configuration
 */
struct WavefrontConfig {
    int max_path_length = 8;        ///< Maximum path bounces
    int wavefront_size = 1024 * 64; ///< Wavefront size (rays per wave)
    int tile_size = 16;             ///< Tile size for coherent access
    bool use_rt_cores = true;       ///< Enable RT core acceleration
    bool use_tensor_cores = false;  ///< Enable Tensor core denoising
    bool use_shared_memory = true;  ///< Use shared memory optimization
    float ray_coherence_threshold = 0.8f; ///< Ray coherence threshold
    
    // Advanced settings
    bool enable_compaction = true;   ///< Enable ray compaction
    bool enable_sorting = true;      ///< Enable ray sorting by material
    bool enable_streaming = true;    ///< Enable memory streaming
    int compaction_threshold = 256;  ///< Minimum rays for compaction
};

/**
 * @brief RT Core configuration
 */
struct RTCoreConfig {
    int rt_core_count = 36;         ///< Number of RT cores (RTX 4070)
    int sm_count = 36;              ///< Number of streaming multiprocessors
    int max_threads_per_sm = 2048;  ///< Max threads per SM
    int shared_memory_per_sm = 49152; ///< Shared memory per SM (bytes)
    int registers_per_sm = 65536;   ///< Registers per SM
    
    // Occupancy parameters
    float target_occupancy = 0.75f; ///< Target occupancy percentage
    int threads_per_block = 256;    ///< Threads per block
    int blocks_per_sm = 8;          ///< Blocks per SM
    
    // Memory bandwidth
    size_t memory_bandwidth_gbps = 504; ///< Memory bandwidth (GB/s)
    size_t l2_cache_size = 48 * 1024 * 1024; ///< L2 cache size (bytes)
};

/**
 * @brief Optimized render parameters
 */
struct OptimizedRenderParams {
    // Image parameters
    int width, height;
    int samples_per_pixel;
    float* output_buffer;
    
    // Scene data
    void* scene_data;
    size_t scene_data_size;
    
    // Camera parameters
    float3 camera_pos;
    float3 camera_dir;
    float3 camera_up;
    float fov;
    
    // Optimization parameters
    RTCoreStrategy strategy;
    WavefrontConfig wavefront_config;
    RTCoreConfig rt_config;
    
    // Performance monitoring
    bool enable_profiling;
    float* timing_data;
    int* ray_counts;
    float* occupancy_data;
};

/**
 * @brief Wavefront path tracing data
 */
struct WavefrontData {
    // Ray data
    float3* ray_origins;
    float3* ray_directions;
    float3* ray_throughput;
    int* ray_pixel_indices;
    int* ray_path_lengths;
    bool* ray_active;
    
    // Hit data
    float3* hit_positions;
    float3* hit_normals;
    int* hit_material_ids;
    float* hit_distances;
    bool* hit_valid;
    
    // Material data
    float3* material_albedo;
    float* material_roughness;
    float* material_metallic;
    int* material_types;
    
    // Compaction data
    int* compaction_indices;
    int active_ray_count;
    int total_ray_count;
    
    // Memory management
    void* temp_storage;
    size_t temp_storage_bytes;
};

// ============================================================================
// OPTIMIZED KERNEL DECLARATIONS
// ============================================================================

/**
 * @brief Primary ray generation kernel optimized for RT cores
 */
__global__ void generatePrimaryRaysOptimized(
    OptimizedRenderParams params,
    WavefrontData wavefront,
    int tile_x, int tile_y,
    int tile_width, int tile_height
);

/**
 * @brief RT core accelerated intersection kernel
 */
__global__ void intersectRaysRTCore(
    OptimizedRenderParams params,
    WavefrontData wavefront,
    int ray_count
);

/**
 * @brief Material evaluation kernel with shared memory optimization
 */
__global__ void evaluateMaterialsOptimized(
    OptimizedRenderParams params,
    WavefrontData wavefront,
    int ray_count,
    curandState* rand_states
);

/**
 * @brief Ray compaction kernel using CUB primitives
 */
__global__ void compactRaysOptimized(
    WavefrontData wavefront,
    int* d_num_selected_out
);

/**
 * @brief Ray sorting kernel for material coherence
 */
__global__ void sortRaysByMaterial(
    WavefrontData wavefront,
    int ray_count
);

/**
 * @brief Accumulation kernel with atomic operations optimization
 */
__global__ void accumulateResultsOptimized(
    OptimizedRenderParams params,
    WavefrontData wavefront,
    int ray_count
);

/**
 * @brief Adaptive tile size kernel for load balancing
 */
__global__ void adaptiveTileScheduling(
    OptimizedRenderParams params,
    int* tile_sizes,
    float* tile_complexities,
    int num_tiles
);

/**
 * @brief RT core utilization monitoring kernel
 */
__global__ void monitorRTCoreUtilization(
    RTCoreConfig config,
    float* utilization_data,
    int* active_cores
);

// ============================================================================
// DEVICE UTILITY FUNCTIONS
// ============================================================================

/**
 * @brief Optimized ray-sphere intersection using RT cores
 */
__device__ __forceinline__ bool intersectSphereRTCore(
    const float3& ray_origin,
    const float3& ray_direction,
    const float3& sphere_center,
    float sphere_radius,
    float& t_hit
);

/**
 * @brief Optimized ray-triangle intersection using RT cores
 */
__device__ __forceinline__ bool intersectTriangleRTCore(
    const float3& ray_origin,
    const float3& ray_direction,
    const float3& v0, const float3& v1, const float3& v2,
    float& t_hit, float& u, float& v
);

/**
 * @brief Shared memory optimized BRDF evaluation
 */
__device__ __forceinline__ float3 evaluateBRDFShared(
    const float3& wi, const float3& wo, const float3& normal,
    const float3& albedo, float roughness, float metallic
);

/**
 * @brief Warp-level ray coherence analysis
 */
__device__ __forceinline__ float analyzeRayCoherence(
    const float3* ray_directions,
    int warp_size = 32
);

/**
 * @brief Optimized random number generation
 */
__device__ __forceinline__ float3 generateRandomDirectionOptimized(
    curandState* state,
    const float3& normal
);

/**
 * @brief Memory coalescing helper
 */
__device__ __forceinline__ void coalescedMemoryAccess(
    float* global_memory,
    float* shared_memory,
    int thread_id,
    int block_size
);

// ============================================================================
// COOPERATIVE GROUPS OPTIMIZATIONS
// ============================================================================

/**
 * @brief Warp-level reduction for ray statistics
 */
__device__ __forceinline__ float warpReduceSum(float val);

/**
 * @brief Block-level reduction using cooperative groups
 */
__device__ __forceinline__ float blockReduceSum(float val);

/**
 * @brief Grid-level synchronization for wavefront processing
 */
__device__ void gridSynchronize();

// ============================================================================
// PERFORMANCE MONITORING
// ============================================================================

/**
 * @brief RT core performance counters
 */
struct RTCoreCounters {
    unsigned long long rays_processed;
    unsigned long long intersections_found;
    unsigned long long cache_hits;
    unsigned long long cache_misses;
    float utilization_percentage;
    float throughput_mrays_per_sec;
};

/**
 * @brief Kernel performance metrics
 */
struct KernelMetrics {
    float execution_time_ms;
    float occupancy_percentage;
    int active_warps;
    int active_blocks;
    size_t shared_memory_used;
    int registers_used;
    float memory_bandwidth_utilization;
};

/**
 * @brief Update RT core performance counters
 */
__device__ void updateRTCoreCounters(
    RTCoreCounters* counters,
    int rays_this_thread,
    int intersections_this_thread
);

/**
 * @brief Measure kernel performance metrics
 */
__device__ void measureKernelMetrics(
    KernelMetrics* metrics
);

// ============================================================================
// OPTIMIZATION STRATEGIES
// ============================================================================

/**
 * @brief Adaptive block size selection
 */
__host__ dim3 selectOptimalBlockSize(
    const RTCoreConfig& config,
    int problem_size,
    size_t shared_memory_per_block
);

/**
 * @brief Dynamic load balancing
 */
__host__ void dynamicLoadBalancing(
    OptimizedRenderParams& params,
    const float* tile_complexities,
    int num_tiles
);

/**
 * @brief Memory access pattern optimization
 */
__host__ void optimizeMemoryAccess(
    WavefrontData& wavefront,
    const RTCoreConfig& config
);

/**
 * @brief RT core workload distribution
 */
__host__ void distributeWorkloadRTCores(
    const OptimizedRenderParams& params,
    int* workload_per_core
);

} // namespace gpu
} // namespace photon
