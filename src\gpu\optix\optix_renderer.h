// src/gpu/optix/optix_renderer.h
// PhotonRender - OptiX Hardware Ray Tracing Renderer
// Header per integrazione OptiX RT Cores

#pragma once

#ifdef USE_OPTIX
#include <optix.h>
#include <optix_stubs.h>
#include <cuda_runtime.h>
#endif

#include <vector>
#include <memory>
#include <string>

namespace photon {
namespace gpu {

/**
 * Statistiche OptiX rendering
 */
struct OptiXStats {
    double render_time_ms = 0.0;
    double setup_time_ms = 0.0;
    double trace_time_ms = 0.0;
    long long rays_traced = 0;
    double rays_per_second = 0.0;
    double grays_per_second = 0.0;
    int rt_cores_used = 0;
    size_t memory_used = 0;
};

/**
 * Configurazione OptiX
 */
struct OptiXConfig {
    int max_trace_depth = 10;          // Profondità massima ray tracing
    int max_primitives = 1000000;      // Numero massimo primitive
    bool use_motion_blur = false;      // Motion blur support
    bool use_denoising = false;        // AI denoising
    float denoising_strength = 0.5f;   // Forza denoising
    int tile_size = 256;               // Dimensione tile rendering
    bool use_multi_gpu = false;        // Multi-GPU support
};

/**
 * Geometry data per OptiX
 */
struct OptiXGeometry {
    std::vector<float3> vertices;      // Vertici triangoli
    std::vector<uint3> indices;       // Indici triangoli
    std::vector<float3> normals;      // Normali per vertex
    std::vector<float2> texcoords;    // Coordinate texture
    std::vector<int> material_ids;    // ID materiali per triangolo
};

/**
 * Material data per OptiX
 */
struct OptiXMaterial {
    float3 albedo = {0.8f, 0.8f, 0.8f};    // Colore base
    float roughness = 0.5f;                  // Rugosità superficie
    float metallic = 0.0f;                   // Metallicità
    float emission = 0.0f;                   // Emissione
    float ior = 1.5f;                        // Indice rifrazione
    float transmission = 0.0f;               // Trasmissione
    int texture_id = -1;                     // ID texture (-1 = nessuna)
};

/**
 * Camera data per OptiX
 */
struct OptiXCamera {
    float3 position = {0, 0, 0};            // Posizione camera
    float3 target = {0, 0, -1};             // Target camera
    float3 up = {0, 1, 0};                  // Vettore up
    float fov = 45.0f;                      // Field of view (gradi)
    float aspect_ratio = 1.0f;              // Aspect ratio
    float aperture = 0.0f;                  // Apertura per DOF
    float focus_distance = 1.0f;            // Distanza focus
};

/**
 * Light data per OptiX
 */
struct OptiXLight {
    enum Type { DIRECTIONAL, POINT, AREA, ENVIRONMENT };
    
    Type type = DIRECTIONAL;
    float3 position = {0, 10, 0};           // Posizione (point/area)
    float3 direction = {0, -1, 0};          // Direzione (directional)
    float3 color = {1, 1, 1};               // Colore luce
    float intensity = 1.0f;                 // Intensità
    float radius = 1.0f;                    // Raggio (area light)
    int environment_map_id = -1;            // ID environment map
};

#ifdef USE_OPTIX

/**
 * OptiX Renderer principale
 */
class OptiXRenderer {
public:
    OptiXRenderer();
    ~OptiXRenderer();
    
    // Non copiabile
    OptiXRenderer(const OptiXRenderer&) = delete;
    OptiXRenderer& operator=(const OptiXRenderer&) = delete;
    
    /**
     * Inizializza OptiX renderer
     * @param config Configurazione OptiX
     * @return true se inizializzazione riuscita
     */
    bool initialize(const OptiXConfig& config = OptiXConfig{});
    
    /**
     * Shutdown OptiX renderer
     */
    void shutdown();
    
    /**
     * Verifica se OptiX è disponibile
     * @return true se OptiX è pronto
     */
    bool isAvailable() const;
    
    /**
     * Carica geometry nella scena
     * @param geometry Dati geometry
     * @return ID geometry (-1 se errore)
     */
    int loadGeometry(const OptiXGeometry& geometry);
    
    /**
     * Carica materiale
     * @param material Dati materiale
     * @return ID materiale (-1 se errore)
     */
    int loadMaterial(const OptiXMaterial& material);
    
    /**
     * Imposta camera
     * @param camera Configurazione camera
     */
    void setCamera(const OptiXCamera& camera);
    
    /**
     * Aggiunge luce alla scena
     * @param light Configurazione luce
     * @return ID luce (-1 se errore)
     */
    int addLight(const OptiXLight& light);
    
    /**
     * Renderizza scena
     * @param image_data Buffer output immagine (RGB float)
     * @param width Larghezza immagine
     * @param height Altezza immagine
     * @param samples_per_pixel Campioni per pixel
     * @return true se rendering riuscito
     */
    bool render(
        std::vector<float>& image_data,
        int width,
        int height,
        int samples_per_pixel = 16
    );
    
    /**
     * Renderizza con tiling per grandi immagini
     * @param image_data Buffer output immagine
     * @param width Larghezza immagine
     * @param height Altezza immagine
     * @param tile_size Dimensione tile
     * @param samples_per_pixel Campioni per pixel
     * @return true se rendering riuscito
     */
    bool renderTiled(
        std::vector<float>& image_data,
        int width,
        int height,
        int tile_size = 256,
        int samples_per_pixel = 16
    );
    
    /**
     * Ottieni statistiche ultimo rendering
     * @return Statistiche OptiX
     */
    OptiXStats getLastStats() const;
    
    /**
     * Ottieni informazioni RT Cores
     * @return Numero RT Cores disponibili
     */
    int getRTCoresCount() const;
    
    /**
     * Ottieni memoria GPU disponibile
     * @return Memoria disponibile in MB
     */
    size_t getAvailableMemory() const;
    
    /**
     * Abilita/disabilita denoising
     * @param enable True per abilitare
     * @param strength Forza denoising (0.0-1.0)
     */
    void setDenoising(bool enable, float strength = 0.5f);
    
    /**
     * Salva immagine renderizzata
     * @param filename Nome file output
     * @param image_data Dati immagine
     * @param width Larghezza
     * @param height Altezza
     * @return true se salvataggio riuscito
     */
    bool saveImage(
        const std::string& filename,
        const std::vector<float>& image_data,
        int width,
        int height
    );

private:
    bool initialized_ = false;
    OptiXConfig config_;
    OptiXStats last_stats_;
    
    // OptiX objects
    OptixDeviceContext context_ = nullptr;
    OptixModule module_ = nullptr;
    OptixPipeline pipeline_ = nullptr;
    OptixProgramGroup raygen_prog_group_ = nullptr;
    OptixProgramGroup miss_prog_group_ = nullptr;
    OptixProgramGroup hitgroup_prog_group_ = nullptr;
    OptixShaderBindingTable sbt_ = {};
    
    // CUDA objects
    CUcontext cuda_context_ = nullptr;
    CUstream cuda_stream_ = nullptr;
    
    // Scene data
    std::vector<OptiXGeometry> geometries_;
    std::vector<OptiXMaterial> materials_;
    std::vector<OptiXLight> lights_;
    OptiXCamera camera_;
    
    // GPU buffers
    CUdeviceptr d_gas_output_buffer_ = 0;
    CUdeviceptr d_vertices_ = 0;
    CUdeviceptr d_indices_ = 0;
    CUdeviceptr d_materials_ = 0;
    CUdeviceptr d_lights_ = 0;
    
    // Helper methods
    bool initializeOptiX();
    bool createModule();
    bool createPipeline();
    bool createSBT();
    bool buildAccelStructure();
    void updateCamera();
    void updateLights();
    void cleanup();
    
    // Error handling
    static void contextLogCallback(
        unsigned int level,
        const char* tag,
        const char* message,
        void* cbdata
    );
};

#else // !USE_OPTIX

/**
 * Stub OptiX Renderer quando OptiX non è disponibile
 */
class OptiXRenderer {
public:
    OptiXRenderer() = default;
    ~OptiXRenderer() = default;
    
    bool initialize(const OptiXConfig& = OptiXConfig{}) { return false; }
    void shutdown() {}
    bool isAvailable() const { return false; }
    
    int loadGeometry(const OptiXGeometry&) { return -1; }
    int loadMaterial(const OptiXMaterial&) { return -1; }
    void setCamera(const OptiXCamera&) {}
    int addLight(const OptiXLight&) { return -1; }
    
    bool render(std::vector<float>&, int, int, int = 16) { return false; }
    bool renderTiled(std::vector<float>&, int, int, int = 256, int = 16) { return false; }
    
    OptiXStats getLastStats() const { return OptiXStats{}; }
    int getRTCoresCount() const { return 0; }
    size_t getAvailableMemory() const { return 0; }
    void setDenoising(bool, float = 0.5f) {}
    bool saveImage(const std::string&, const std::vector<float>&, int, int) { return false; }
};

#endif // USE_OPTIX

} // namespace gpu
} // namespace photon
