// src/qa/quality_assurance_system.hpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Advanced Quality Assurance System

#pragma once

#include "../core/profiling/performance_profiler.hpp"
#include "../core/memory/advanced_memory_manager.hpp"
#include <memory>
#include <vector>
#include <string>
#include <unordered_map>
#include <chrono>
#include <functional>

namespace photon {
namespace qa {

/**
 * @brief Test categories for organization
 */
enum class TestCategory {
    UNIT,               ///< Unit tests for individual components
    INTEGRATION,        ///< Integration tests for component interaction
    PERFORMANCE,        ///< Performance and benchmark tests
    REGRESSION,         ///< Regression tests for stability
    MEMORY,             ///< Memory leak and usage tests
    GPU,                ///< GPU-specific tests
    STRESS,             ///< Stress and load tests
    VALIDATION,         ///< Output validation tests
    COMPATIBILITY       ///< Platform compatibility tests
};

/**
 * @brief Test severity levels
 */
enum class TestSeverity {
    CRITICAL,           ///< Critical functionality - must pass
    HIGH,               ///< High importance - should pass
    MEDIUM,             ///< Medium importance - nice to pass
    LOW,                ///< Low importance - informational
    INFORMATIONAL       ///< Informational only
};

/**
 * @brief Test result status
 */
enum class TestStatus {
    PASSED,             ///< Test passed successfully
    FAILED,             ///< Test failed
    SKIPPED,            ///< Test was skipped
    TIMEOUT,            ///< Test timed out
    ERROR,              ///< Test encountered an error
    NOT_RUN             ///< Test has not been run yet
};

/**
 * @brief Individual test result
 */
struct TestResult {
    std::string test_name;
    std::string test_description;
    TestCategory category;
    TestSeverity severity;
    TestStatus status;
    
    std::chrono::milliseconds execution_time;
    std::string error_message;
    std::string output_log;
    
    // Performance metrics
    double performance_score;
    size_t memory_usage_bytes;
    double cpu_usage_percent;
    double gpu_usage_percent;
    
    // Validation metrics
    bool output_valid;
    double quality_score;
    std::vector<std::string> warnings;
    
    TestResult(const std::string& name, const std::string& desc, 
               TestCategory cat, TestSeverity sev)
        : test_name(name), test_description(desc), category(cat), severity(sev),
          status(TestStatus::NOT_RUN), execution_time(0), performance_score(0.0),
          memory_usage_bytes(0), cpu_usage_percent(0.0), gpu_usage_percent(0.0),
          output_valid(true), quality_score(1.0) {}
    
    bool isPassed() const { return status == TestStatus::PASSED; }
    bool isCritical() const { return severity == TestSeverity::CRITICAL; }
    std::string getStatusString() const;
    std::string getSeverityString() const;
    std::string getCategoryString() const;
};

/**
 * @brief Test suite configuration
 */
struct TestSuiteConfig {
    bool run_unit_tests = true;
    bool run_integration_tests = true;
    bool run_performance_tests = true;
    bool run_regression_tests = true;
    bool run_memory_tests = true;
    bool run_gpu_tests = true;
    bool run_stress_tests = false;
    bool run_validation_tests = true;
    bool run_compatibility_tests = true;
    
    // Execution settings
    int max_parallel_tests = 4;
    std::chrono::seconds test_timeout{300}; // 5 minutes default
    bool stop_on_first_failure = false;
    bool generate_detailed_reports = true;
    
    // Performance thresholds
    double min_performance_score = 0.8;
    size_t max_memory_usage_mb = 1024;
    double max_cpu_usage_percent = 90.0;
    
    // Output settings
    std::string output_directory = "qa_results";
    bool save_test_artifacts = true;
    bool generate_html_report = true;
    bool generate_junit_xml = true;
};

/**
 * @brief Regression test baseline
 */
struct RegressionBaseline {
    std::string test_name;
    double baseline_performance;
    size_t baseline_memory_usage;
    std::string baseline_output_hash;
    std::chrono::system_clock::time_point baseline_date;
    
    // Tolerance settings
    double performance_tolerance = 0.1; // 10% tolerance
    double memory_tolerance = 0.2;      // 20% tolerance
    bool strict_output_matching = false;
};

/**
 * @brief Test execution context
 */
struct TestContext {
    std::string test_name;
    TestCategory category;
    std::chrono::steady_clock::time_point start_time;
    
    // Resource monitoring
    size_t initial_memory_usage;
    std::unique_ptr<PerformanceProfiler> profiler;
    
    // Output capture
    std::stringstream output_stream;
    std::stringstream error_stream;
    
    TestContext(const std::string& name, TestCategory cat);
    ~TestContext();
    
    void startProfiling();
    void stopProfiling();
    TestResult finalize(TestStatus status, const std::string& error = "");
};

/**
 * @brief Quality Assurance System
 */
class QualityAssuranceSystem {
public:
    /**
     * @brief Constructor
     */
    QualityAssuranceSystem();
    
    /**
     * @brief Destructor
     */
    ~QualityAssuranceSystem();
    
    /**
     * @brief Initialize QA system
     */
    bool initialize(const TestSuiteConfig& config = TestSuiteConfig{});
    
    /**
     * @brief Shutdown QA system
     */
    void shutdown();
    
    /**
     * @brief Register a test function
     */
    void registerTest(const std::string& name, const std::string& description,
                     TestCategory category, TestSeverity severity,
                     std::function<bool(TestContext&)> test_function);
    
    /**
     * @brief Run all registered tests
     */
    bool runAllTests();
    
    /**
     * @brief Run tests by category
     */
    bool runTestsByCategory(TestCategory category);
    
    /**
     * @brief Run specific test
     */
    TestResult runTest(const std::string& test_name);
    
    /**
     * @brief Run regression tests
     */
    bool runRegressionTests();
    
    /**
     * @brief Run performance validation
     */
    bool runPerformanceValidation();
    
    /**
     * @brief Run memory leak detection
     */
    bool runMemoryLeakDetection();
    
    /**
     * @brief Run stress tests
     */
    bool runStressTests();
    
    /**
     * @brief Get test results
     */
    const std::vector<TestResult>& getTestResults() const { return m_test_results; }
    
    /**
     * @brief Get test summary
     */
    struct TestSummary {
        int total_tests;
        int passed_tests;
        int failed_tests;
        int skipped_tests;
        int critical_failures;
        double overall_score;
        std::chrono::milliseconds total_execution_time;
        bool all_critical_passed;
    };
    
    TestSummary getTestSummary() const;
    
    /**
     * @brief Generate test reports
     */
    void generateReports();
    
    /**
     * @brief Generate HTML report
     */
    void generateHTMLReport(const std::string& filename = "qa_report.html");
    
    /**
     * @brief Generate JUnit XML report
     */
    void generateJUnitXMLReport(const std::string& filename = "qa_results.xml");
    
    /**
     * @brief Generate performance report
     */
    void generatePerformanceReport(const std::string& filename = "performance_report.html");
    
    /**
     * @brief Set regression baselines
     */
    void setRegressionBaselines(const std::vector<RegressionBaseline>& baselines);
    
    /**
     * @brief Update regression baselines from current results
     */
    void updateRegressionBaselines();
    
    /**
     * @brief Validate test output
     */
    bool validateTestOutput(const std::string& test_name, const std::string& output);
    
    /**
     * @brief Add custom validation function
     */
    void addCustomValidator(const std::string& test_name, 
                           std::function<bool(const std::string&)> validator);
    
    /**
     * @brief Set configuration
     */
    void setConfiguration(const TestSuiteConfig& config) { m_config = config; }
    
    /**
     * @brief Get configuration
     */
    const TestSuiteConfig& getConfiguration() const { return m_config; }

private:
    // Core data
    bool m_initialized;
    TestSuiteConfig m_config;
    
    // Test registry
    struct RegisteredTest {
        std::string name;
        std::string description;
        TestCategory category;
        TestSeverity severity;
        std::function<bool(TestContext&)> test_function;
    };
    
    std::vector<RegisteredTest> m_registered_tests;
    std::vector<TestResult> m_test_results;
    std::vector<RegressionBaseline> m_regression_baselines;
    
    // Custom validators
    std::unordered_map<std::string, std::function<bool(const std::string&)>> m_custom_validators;
    
    // Internal methods
    void initializeBuiltinTests();
    void registerBuiltinTests();
    
    // Test execution
    TestResult executeTest(const RegisteredTest& test);
    bool checkRegressionBaseline(const TestResult& result);
    
    // Validation
    bool validatePerformance(const TestResult& result);
    bool validateMemoryUsage(const TestResult& result);
    bool validateOutput(const TestResult& result);
    
    // Reporting
    std::string generateTestResultHTML(const TestResult& result);
    std::string generateSummaryHTML(const TestSummary& summary);
    std::string generatePerformanceChartHTML();
    
    // Utilities
    std::string calculateOutputHash(const std::string& output);
    void createOutputDirectory();
    void saveTestArtifacts(const TestResult& result);
};

/**
 * @brief Automated test runner for CI/CD
 */
class AutomatedTestRunner {
public:
    /**
     * @brief Run automated test suite
     */
    static int runAutomatedTests(int argc, char* argv[]);
    
    /**
     * @brief Parse command line arguments
     */
    static TestSuiteConfig parseCommandLineArgs(int argc, char* argv[]);
    
    /**
     * @brief Generate CI/CD compatible exit codes
     */
    static int generateExitCode(const QualityAssuranceSystem::TestSummary& summary);
    
    /**
     * @brief Setup test environment
     */
    static bool setupTestEnvironment();
    
    /**
     * @brief Cleanup test environment
     */
    static void cleanupTestEnvironment();
};

/**
 * @brief Built-in test functions
 */
namespace BuiltinTests {
    bool testMathLibrary(TestContext& context);
    bool testSceneManagement(TestContext& context);
    bool testRenderingPipeline(TestContext& context);
    bool testMemoryManagement(TestContext& context);
    bool testGPUKernels(TestContext& context);
    bool testAdaptiveSampling(TestContext& context);
    bool testPerformanceProfiling(TestContext& context);
    bool testImageIO(TestContext& context);
    bool testMaterialSystem(TestContext& context);
    bool testLightingSystem(TestContext& context);
    
    // Performance tests
    bool performanceTestRayTracing(TestContext& context);
    bool performanceTestMemoryAllocation(TestContext& context);
    bool performanceTestGPUThroughput(TestContext& context);
    
    // Stress tests
    bool stressTestLargeScene(TestContext& context);
    bool stressTestHighSampleCount(TestContext& context);
    bool stressTestMemoryPressure(TestContext& context);
    
    // Regression tests
    bool regressionTestRenderOutput(TestContext& context);
    bool regressionTestPerformance(TestContext& context);
    bool regressionTestMemoryUsage(TestContext& context);
}

// Convenience macros for test registration
#define QA_REGISTER_TEST(qa_system, name, desc, category, severity, function) \
    qa_system.registerTest(name, desc, category, severity, function)

#define QA_REGISTER_UNIT_TEST(qa_system, name, desc, function) \
    QA_REGISTER_TEST(qa_system, name, desc, TestCategory::UNIT, TestSeverity::HIGH, function)

#define QA_REGISTER_PERFORMANCE_TEST(qa_system, name, desc, function) \
    QA_REGISTER_TEST(qa_system, name, desc, TestCategory::PERFORMANCE, TestSeverity::MEDIUM, function)

#define QA_REGISTER_REGRESSION_TEST(qa_system, name, desc, function) \
    QA_REGISTER_TEST(qa_system, name, desc, TestCategory::REGRESSION, TestSeverity::CRITICAL, function)

} // namespace qa
} // namespace photon
