# src/ruby/photon_render/dialog.rb
# Dialog system for PhotonRender

module PhotonRender
  
  module Dialog
    
    # Show render settings dialog
    def self.show_render_settings
      puts "Opening Render Settings dialog"
      
      # Create HTML dialog
      dialog = UI::HtmlDialog.new(
        dialog_title: "PhotonRender - Render Settings",
        preferences_key: "PhotonRender_RenderSettings",
        scrollable: true,
        resizable: true,
        width: 400,
        height: 600,
        left: 200,
        top: 200,
        min_width: 300,
        min_height: 400,
        max_width: 800,
        max_height: 1000,
        style: UI::HtmlDialog::STYLE_DIALOG
      )
      
      # Set HTML content
      dialog.set_html(render_settings_html)
      
      # Add callbacks
      dialog.add_action_callback("apply_settings") do |action_context, settings_json|
        apply_render_settings(JSON.parse(settings_json))
      end
      
      dialog.add_action_callback("start_render") do |action_context|
        dialog.close
        Menu.quick_render
      end
      
      dialog.show
    end
    
    # Show material editor dialog
    def self.show_material_editor
      puts "Opening Material Editor dialog"
      
      dialog = UI::HtmlDialog.new(
        dialog_title: "PhotonRender - Material Editor",
        preferences_key: "PhotonRender_MaterialEditor",
        scrollable: true,
        resizable: true,
        width: 600,
        height: 700,
        style: UI::HtmlDialog::STYLE_DIALOG
      )
      
      # Load existing HTML file
      html_file = File.join(PhotonRender::PLUGIN_PATH, "material_editor.html")
      if File.exist?(html_file)
        dialog.set_file(html_file)
      else
        dialog.set_html(material_editor_html)
      end
      
      dialog.show
    end
    
    # Show material library dialog
    def self.show_material_library
      puts "Opening Material Library dialog"
      
      dialog = UI::HtmlDialog.new(
        dialog_title: "PhotonRender - Material Library",
        preferences_key: "PhotonRender_MaterialLibrary",
        scrollable: true,
        resizable: true,
        width: 500,
        height: 600,
        style: UI::HtmlDialog::STYLE_DIALOG
      )
      
      html_file = File.join(PhotonRender::PLUGIN_PATH, "material_library_browser.html")
      if File.exist?(html_file)
        dialog.set_file(html_file)
      else
        dialog.set_html(material_library_html)
      end
      
      dialog.show
    end
    
    # Show material assignment dialog
    def self.show_material_assignment
      puts "Opening Material Assignment dialog"
      
      dialog = UI::HtmlDialog.new(
        dialog_title: "PhotonRender - Material Assignment",
        preferences_key: "PhotonRender_MaterialAssignment",
        scrollable: true,
        resizable: true,
        width: 450,
        height: 500,
        style: UI::HtmlDialog::STYLE_DIALOG
      )
      
      html_file = File.join(PhotonRender::PLUGIN_PATH, "texture_assignment.html")
      if File.exist?(html_file)
        dialog.set_file(html_file)
      else
        dialog.set_html(material_assignment_html)
      end
      
      dialog.show
    end
    
    # Show render progress dialog
    def self.show_render_progress
      puts "Opening Render Progress dialog"
      
      @progress_dialog = UI::HtmlDialog.new(
        dialog_title: "PhotonRender - Rendering...",
        preferences_key: "PhotonRender_RenderProgress",
        scrollable: false,
        resizable: false,
        width: 400,
        height: 200,
        style: UI::HtmlDialog::STYLE_DIALOG
      )
      
      @progress_dialog.set_html(render_progress_html)
      
      @progress_dialog.add_action_callback("cancel_render") do |action_context|
        PhotonRender.render_manager.stop_render
        @progress_dialog.close
      end
      
      @progress_dialog.show
    end
    
    # Hide render progress dialog
    def self.hide_render_progress
      @progress_dialog.close if @progress_dialog
      @progress_dialog = nil
    end
    
    # Update render progress
    def self.update_progress(progress, stats)
      if @progress_dialog
        @progress_dialog.execute_script("updateProgress(#{progress}, #{stats.to_json})")
      end
    end
    
    # Show preferences dialog
    def self.show_preferences
      puts "Opening Preferences dialog"
      UI.messagebox("Preferences dialog not yet implemented")
    end
    
    # Show other dialogs (stubs)
    def self.show_environment_settings
      puts "Opening Environment Settings dialog"
      UI.messagebox("Environment Settings dialog not yet implemented")
    end
    
    def self.show_sun_sky_settings
      puts "Opening Sun & Sky Settings dialog"
      UI.messagebox("Sun & Sky Settings dialog not yet implemented")
    end
    
    def self.show_camera_settings
      puts "Opening Camera Settings dialog"
      UI.messagebox("Camera Settings dialog not yet implemented")
    end
    
    def self.show_performance_monitor
      puts "Opening Performance Monitor dialog"
      UI.messagebox("Performance Monitor dialog not yet implemented")
    end
    
    def self.show_memory_usage
      puts "Opening Memory Usage dialog"
      UI.messagebox("Memory Usage dialog not yet implemented")
    end
    
    private
    
    # Apply render settings
    def self.apply_render_settings(settings)
      puts "Applying render settings: #{settings}"
      PhotonRender.preferences[:last_render_settings] = settings
      PhotonRender.save_preferences
    end
    
    # HTML content for render settings
    def self.render_settings_html
      <<~HTML
        <!DOCTYPE html>
        <html>
        <head>
          <title>Render Settings</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .setting-group { margin-bottom: 20px; }
            label { display: block; margin-bottom: 5px; font-weight: bold; }
            input, select { width: 100%; padding: 5px; margin-bottom: 10px; }
            button { padding: 10px 20px; margin: 5px; }
            .button-group { text-align: center; margin-top: 20px; }
          </style>
        </head>
        <body>
          <h2>Render Settings</h2>
          
          <div class="setting-group">
            <label>Resolution:</label>
            <select id="resolution">
              <option value="800x600">800 x 600</option>
              <option value="1920x1080" selected>1920 x 1080 (HD)</option>
              <option value="2560x1440">2560 x 1440 (2K)</option>
              <option value="3840x2160">3840 x 2160 (4K)</option>
            </select>
          </div>
          
          <div class="setting-group">
            <label>Quality:</label>
            <select id="quality">
              <option value="low">Low (Fast)</option>
              <option value="medium" selected>Medium</option>
              <option value="high">High</option>
              <option value="ultra">Ultra (Slow)</option>
            </select>
          </div>
          
          <div class="setting-group">
            <label>Samples per Pixel:</label>
            <input type="number" id="samples" value="100" min="1" max="10000">
          </div>
          
          <div class="setting-group">
            <label>Max Bounces:</label>
            <input type="number" id="bounces" value="8" min="1" max="50">
          </div>
          
          <div class="button-group">
            <button onclick="applySettings()">Apply</button>
            <button onclick="startRender()">Render</button>
            <button onclick="window.close()">Cancel</button>
          </div>
          
          <script>
            function applySettings() {
              const settings = {
                resolution: document.getElementById('resolution').value,
                quality: document.getElementById('quality').value,
                samples: parseInt(document.getElementById('samples').value),
                bounces: parseInt(document.getElementById('bounces').value)
              };
              sketchup.apply_settings(JSON.stringify(settings));
            }
            
            function startRender() {
              applySettings();
              sketchup.start_render();
            }
          </script>
        </body>
        </html>
      HTML
    end
    
    # HTML content for material editor (simplified)
    def self.material_editor_html
      <<~HTML
        <!DOCTYPE html>
        <html>
        <head>
          <title>Material Editor</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            h2 { color: #333; }
          </style>
        </head>
        <body>
          <h2>Material Editor</h2>
          <p>Material editor interface would be here.</p>
          <p>This is a placeholder for testing.</p>
        </body>
        </html>
      HTML
    end
    
    # HTML content for material library (simplified)
    def self.material_library_html
      <<~HTML
        <!DOCTYPE html>
        <html>
        <head>
          <title>Material Library</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            h2 { color: #333; }
          </style>
        </head>
        <body>
          <h2>Material Library</h2>
          <p>Material library browser would be here.</p>
          <p>This is a placeholder for testing.</p>
        </body>
        </html>
      HTML
    end
    
    # HTML content for material assignment (simplified)
    def self.material_assignment_html
      <<~HTML
        <!DOCTYPE html>
        <html>
        <head>
          <title>Material Assignment</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            h2 { color: #333; }
          </style>
        </head>
        <body>
          <h2>Material Assignment</h2>
          <p>Material assignment interface would be here.</p>
          <p>This is a placeholder for testing.</p>
        </body>
        </html>
      HTML
    end
    
    # HTML content for render progress
    def self.render_progress_html
      <<~HTML
        <!DOCTYPE html>
        <html>
        <head>
          <title>Rendering Progress</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; text-align: center; }
            .progress-bar { width: 100%; height: 20px; background: #f0f0f0; border-radius: 10px; overflow: hidden; margin: 20px 0; }
            .progress-fill { height: 100%; background: #4CAF50; width: 0%; transition: width 0.3s; }
            button { padding: 10px 20px; margin: 10px; }
          </style>
        </head>
        <body>
          <h2>Rendering in Progress...</h2>
          <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
          </div>
          <p id="progressText">0%</p>
          <button onclick="sketchup.cancel_render()">Cancel</button>
          
          <script>
            function updateProgress(progress, stats) {
              const percent = Math.round(progress * 100);
              document.getElementById('progressFill').style.width = percent + '%';
              document.getElementById('progressText').textContent = percent + '%';
            }
          </script>
        </body>
        </html>
      HTML
    end
    
  end # module Dialog
  
end # module PhotonRender
