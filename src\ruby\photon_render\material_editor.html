<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PhotonRender Material Editor</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1a1a1a;
            color: #e0e0e0;
            overflow: hidden;
            user-select: none;
        }

        .material-editor {
            display: flex;
            height: 100vh;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
        }

        /* Preview Panel */
        .preview-panel {
            flex: 1;
            background: #252525;
            border-right: 1px solid #404040;
            display: flex;
            flex-direction: column;
        }

        .preview-header {
            background: #2a2a2a;
            padding: 15px;
            border-bottom: 1px solid #404040;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .preview-title {
            font-size: 16px;
            font-weight: 600;
            color: #ffffff;
        }

        .preview-controls {
            display: flex;
            gap: 10px;
        }

        .preview-btn {
            background: #404040;
            border: none;
            color: #e0e0e0;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.2s;
        }

        .preview-btn:hover {
            background: #505050;
        }

        .preview-btn.active {
            background: #0078d4;
            color: white;
        }

        .preview-container {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #1e1e1e;
            position: relative;
        }

        .preview-canvas {
            max-width: 100%;
            max-height: 100%;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
            background: #333;
            border: 2px solid #404040;
        }

        .preview-loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #888;
            font-size: 14px;
        }

        .preview-stats {
            background: #2a2a2a;
            padding: 10px 15px;
            border-top: 1px solid #404040;
            font-size: 12px;
            color: #aaa;
            display: flex;
            justify-content: space-between;
        }

        /* Controls Panel */
        .controls-panel {
            width: 350px;
            background: #2a2a2a;
            border-left: 1px solid #404040;
            display: flex;
            flex-direction: column;
            overflow-y: auto;
        }

        .controls-header {
            background: #333;
            padding: 15px;
            border-bottom: 1px solid #404040;
        }

        .controls-title {
            font-size: 16px;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 10px;
        }

        .material-presets {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }

        .preset-btn {
            background: #404040;
            border: none;
            color: #e0e0e0;
            padding: 6px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 11px;
            transition: all 0.2s;
        }

        .preset-btn:hover {
            background: #505050;
            transform: translateY(-1px);
        }

        .preset-btn.active {
            background: #0078d4;
            color: white;
        }

        .controls-content {
            flex: 1;
            padding: 20px;
        }

        .parameter-group {
            margin-bottom: 25px;
        }

        .group-title {
            font-size: 14px;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 15px;
            padding-bottom: 5px;
            border-bottom: 1px solid #404040;
        }

        .parameter {
            margin-bottom: 15px;
        }

        .parameter-label {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-size: 12px;
            color: #ccc;
        }

        .parameter-value {
            background: #1a1a1a;
            color: #0078d4;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 11px;
            min-width: 45px;
            text-align: center;
        }

        .parameter-slider {
            width: 100%;
            height: 6px;
            background: #404040;
            border-radius: 3px;
            outline: none;
            -webkit-appearance: none;
            cursor: pointer;
        }

        .parameter-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 16px;
            height: 16px;
            background: #0078d4;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.2s;
        }

        .parameter-slider::-webkit-slider-thumb:hover {
            background: #106ebe;
            transform: scale(1.1);
        }

        .parameter-slider::-moz-range-thumb {
            width: 16px;
            height: 16px;
            background: #0078d4;
            border-radius: 50%;
            cursor: pointer;
            border: none;
        }

        .color-picker-container {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .color-picker {
            width: 40px;
            height: 30px;
            border: 2px solid #404040;
            border-radius: 4px;
            cursor: pointer;
            background: #333;
        }

        .color-input {
            flex: 1;
            background: #1a1a1a;
            border: 1px solid #404040;
            color: #e0e0e0;
            padding: 6px 8px;
            border-radius: 3px;
            font-size: 11px;
            font-family: monospace;
        }

        .texture-slot {
            background: #1a1a1a;
            border: 2px dashed #404040;
            border-radius: 6px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s;
            margin-top: 8px;
        }

        .texture-slot:hover {
            border-color: #0078d4;
            background: #252525;
        }

        .texture-slot.has-texture {
            border-style: solid;
            border-color: #0078d4;
            background: #252525;
        }

        .texture-preview {
            width: 60px;
            height: 60px;
            background: #333;
            border-radius: 4px;
            margin: 0 auto 8px;
            background-size: cover;
            background-position: center;
        }

        .texture-info {
            font-size: 11px;
            color: #888;
        }

        .action-buttons {
            background: #333;
            padding: 15px;
            border-top: 1px solid #404040;
            display: flex;
            gap: 10px;
        }

        .action-btn {
            flex: 1;
            background: #0078d4;
            border: none;
            color: white;
            padding: 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 600;
            transition: background 0.2s;
        }

        .action-btn:hover {
            background: #106ebe;
        }

        .action-btn.secondary {
            background: #404040;
            color: #e0e0e0;
        }

        .action-btn.secondary:hover {
            background: #505050;
        }

        /* Scrollbar styling */
        .controls-panel::-webkit-scrollbar {
            width: 8px;
        }

        .controls-panel::-webkit-scrollbar-track {
            background: #1a1a1a;
        }

        .controls-panel::-webkit-scrollbar-thumb {
            background: #404040;
            border-radius: 4px;
        }

        .controls-panel::-webkit-scrollbar-thumb:hover {
            background: #505050;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .material-editor {
                flex-direction: column;
            }
            
            .controls-panel {
                width: 100%;
                height: 50vh;
            }
            
            .preview-panel {
                height: 50vh;
            }
        }
    </style>
</head>
<body>
    <div class="material-editor">
        <!-- Preview Panel -->
        <div class="preview-panel">
            <div class="preview-header">
                <div class="preview-title">Material Preview</div>
                <div class="preview-controls">
                    <button class="preview-btn active" data-geometry="sphere">Sphere</button>
                    <button class="preview-btn" data-geometry="cube">Cube</button>
                    <button class="preview-btn" data-geometry="cylinder">Cylinder</button>
                    <button class="preview-btn" data-geometry="torus">Torus</button>
                    <button class="preview-btn" data-geometry="plane">Plane</button>
                </div>
            </div>
            
            <div class="preview-container">
                <canvas class="preview-canvas" id="previewCanvas" width="512" height="512"></canvas>
                <div class="preview-loading" id="previewLoading" style="display: none;">Rendering...</div>
            </div>
            
            <div class="preview-stats">
                <span id="renderTime">Render time: --</span>
                <span id="renderStats">Samples: 16 | Resolution: 512x512</span>
            </div>
        </div>

        <!-- Controls Panel -->
        <div class="controls-panel">
            <div class="controls-header">
                <div class="controls-title">Material Parameters</div>
                <div class="material-presets">
                    <button class="preset-btn active" data-preset="plastic">Plastic</button>
                    <button class="preset-btn" data-preset="metal">Metal</button>
                    <button class="preset-btn" data-preset="glass">Glass</button>
                    <button class="preset-btn" data-preset="wood">Wood</button>
                    <button class="preset-btn" data-preset="fabric">Fabric</button>
                    <button class="preset-btn" data-preset="skin">Skin</button>
                    <button class="preset-btn" data-preset="ceramic">Ceramic</button>
                    <button class="preset-btn" data-preset="rubber">Rubber</button>
                </div>
            </div>
            
            <div class="controls-content">
                <!-- Base Color -->
                <div class="parameter-group">
                    <div class="group-title">Base Color</div>
                    <div class="parameter">
                        <div class="parameter-label">
                            <span>Base Color</span>
                        </div>
                        <div class="color-picker-container">
                            <input type="color" class="color-picker" id="baseColor" value="#cc3333">
                            <input type="text" class="color-input" id="baseColorHex" value="#cc3333">
                        </div>
                        <div class="texture-slot" id="baseColorTexture">
                            <div class="texture-preview"></div>
                            <div class="texture-info">Drop texture here or click to browse</div>
                        </div>
                    </div>
                </div>

                <!-- Material Properties -->
                <div class="parameter-group">
                    <div class="group-title">Material Properties</div>
                    
                    <div class="parameter">
                        <div class="parameter-label">
                            <span>Metallic</span>
                            <span class="parameter-value" id="metallicValue">0.00</span>
                        </div>
                        <input type="range" class="parameter-slider" id="metallic" min="0" max="1" step="0.01" value="0">
                    </div>
                    
                    <div class="parameter">
                        <div class="parameter-label">
                            <span>Roughness</span>
                            <span class="parameter-value" id="roughnessValue">0.50</span>
                        </div>
                        <input type="range" class="parameter-slider" id="roughness" min="0" max="1" step="0.01" value="0.5">
                    </div>
                    
                    <div class="parameter">
                        <div class="parameter-label">
                            <span>Specular</span>
                            <span class="parameter-value" id="specularValue">0.50</span>
                        </div>
                        <input type="range" class="parameter-slider" id="specular" min="0" max="1" step="0.01" value="0.5">
                    </div>
                    
                    <div class="parameter">
                        <div class="parameter-label">
                            <span>Specular Tint</span>
                            <span class="parameter-value" id="specularTintValue">0.00</span>
                        </div>
                        <input type="range" class="parameter-slider" id="specularTint" min="0" max="1" step="0.01" value="0">
                    </div>
                </div>

                <!-- Advanced Properties -->
                <div class="parameter-group">
                    <div class="group-title">Advanced Properties</div>
                    
                    <div class="parameter">
                        <div class="parameter-label">
                            <span>Anisotropic</span>
                            <span class="parameter-value" id="anisotropicValue">0.00</span>
                        </div>
                        <input type="range" class="parameter-slider" id="anisotropic" min="0" max="1" step="0.01" value="0">
                    </div>
                    
                    <div class="parameter">
                        <div class="parameter-label">
                            <span>Sheen</span>
                            <span class="parameter-value" id="sheenValue">0.00</span>
                        </div>
                        <input type="range" class="parameter-slider" id="sheen" min="0" max="1" step="0.01" value="0">
                    </div>
                    
                    <div class="parameter">
                        <div class="parameter-label">
                            <span>Sheen Tint</span>
                            <span class="parameter-value" id="sheenTintValue">0.50</span>
                        </div>
                        <input type="range" class="parameter-slider" id="sheenTint" min="0" max="1" step="0.01" value="0.5">
                    </div>
                    
                    <div class="parameter">
                        <div class="parameter-label">
                            <span>Clearcoat</span>
                            <span class="parameter-value" id="clearcoatValue">0.00</span>
                        </div>
                        <input type="range" class="parameter-slider" id="clearcoat" min="0" max="1" step="0.01" value="0">
                    </div>
                    
                    <div class="parameter">
                        <div class="parameter-label">
                            <span>Clearcoat Gloss</span>
                            <span class="parameter-value" id="clearcoatGlossValue">1.00</span>
                        </div>
                        <input type="range" class="parameter-slider" id="clearcoatGloss" min="0" max="1" step="0.01" value="1">
                    </div>
                </div>

                <!-- Subsurface Scattering -->
                <div class="parameter-group">
                    <div class="group-title">Subsurface Scattering</div>
                    
                    <div class="parameter">
                        <div class="parameter-label">
                            <span>Subsurface</span>
                            <span class="parameter-value" id="subsurfaceValue">0.00</span>
                        </div>
                        <input type="range" class="parameter-slider" id="subsurface" min="0" max="1" step="0.01" value="0">
                    </div>
                </div>
            </div>
            
            <div class="action-buttons">
                <button class="action-btn secondary" id="resetBtn">Reset</button>
                <button class="action-btn secondary" id="saveBtn">Save</button>
                <button class="action-btn" id="applyBtn">Apply</button>
            </div>
        </div>
    </div>

    <script src="material_editor.js"></script>
</body>
</html>
