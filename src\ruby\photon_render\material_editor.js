// src/ruby/photon_render/material_editor.js
// PhotonRender Material Editor - JavaScript Interface
// Sistema di controllo interfaccia per Material Editor

class MaterialEditor {
    constructor() {
        this.currentMaterial = {
            baseColor: [0.8, 0.2, 0.2],
            metallic: 0.0,
            roughness: 0.5,
            specular: 0.5,
            specularTint: 0.0,
            anisotropic: 0.0,
            sheen: 0.0,
            sheenTint: 0.5,
            clearcoat: 0.0,
            clearcoatGloss: 1.0,
            subsurface: 0.0
        };
        
        this.currentGeometry = 'sphere';
        this.currentPreset = 'plastic';
        this.isRendering = false;
        this.renderTimeout = null;
        
        this.initializeEventListeners();
        this.loadPreset('plastic');
        this.updatePreview();
    }
    
    initializeEventListeners() {
        // Geometry buttons
        document.querySelectorAll('[data-geometry]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.setGeometry(e.target.dataset.geometry);
            });
        });
        
        // Preset buttons
        document.querySelectorAll('[data-preset]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.loadPreset(e.target.dataset.preset);
            });
        });
        
        // Parameter sliders
        this.setupSlider('metallic');
        this.setupSlider('roughness');
        this.setupSlider('specular');
        this.setupSlider('specularTint');
        this.setupSlider('anisotropic');
        this.setupSlider('sheen');
        this.setupSlider('sheenTint');
        this.setupSlider('clearcoat');
        this.setupSlider('clearcoatGloss');
        this.setupSlider('subsurface');
        
        // Color picker
        this.setupColorPicker();
        
        // Action buttons
        document.getElementById('resetBtn').addEventListener('click', () => {
            this.resetMaterial();
        });
        
        document.getElementById('saveBtn').addEventListener('click', () => {
            this.saveMaterial();
        });
        
        document.getElementById('applyBtn').addEventListener('click', () => {
            this.applyMaterial();
        });
        
        // Texture slots
        this.setupTextureSlots();
    }
    
    setupSlider(paramName) {
        const slider = document.getElementById(paramName);
        const valueDisplay = document.getElementById(paramName + 'Value');
        
        if (!slider || !valueDisplay) return;
        
        slider.addEventListener('input', (e) => {
            const value = parseFloat(e.target.value);
            this.currentMaterial[paramName] = value;
            valueDisplay.textContent = value.toFixed(2);
            this.schedulePreviewUpdate();
        });
        
        // Initialize value display
        valueDisplay.textContent = this.currentMaterial[paramName].toFixed(2);
        slider.value = this.currentMaterial[paramName];
    }
    
    setupColorPicker() {
        const colorPicker = document.getElementById('baseColor');
        const colorInput = document.getElementById('baseColorHex');
        
        colorPicker.addEventListener('change', (e) => {
            const hex = e.target.value;
            this.setBaseColorFromHex(hex);
            colorInput.value = hex;
            this.schedulePreviewUpdate();
        });
        
        colorInput.addEventListener('change', (e) => {
            const hex = e.target.value;
            if (this.isValidHex(hex)) {
                this.setBaseColorFromHex(hex);
                colorPicker.value = hex;
                this.schedulePreviewUpdate();
            }
        });
    }
    
    setupTextureSlots() {
        const baseColorTexture = document.getElementById('baseColorTexture');
        
        baseColorTexture.addEventListener('click', () => {
            this.openTextureDialog('baseColor');
        });
        
        baseColorTexture.addEventListener('dragover', (e) => {
            e.preventDefault();
            baseColorTexture.style.borderColor = '#0078d4';
        });
        
        baseColorTexture.addEventListener('dragleave', (e) => {
            e.preventDefault();
            baseColorTexture.style.borderColor = '#404040';
        });
        
        baseColorTexture.addEventListener('drop', (e) => {
            e.preventDefault();
            baseColorTexture.style.borderColor = '#404040';
            this.handleTextureDrop(e, 'baseColor');
        });
    }
    
    setGeometry(geometry) {
        if (this.currentGeometry === geometry) return;
        
        // Update button states
        document.querySelectorAll('[data-geometry]').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.geometry === geometry);
        });
        
        this.currentGeometry = geometry;
        this.updatePreview();
        
        console.log(`Geometry changed to: ${geometry}`);
    }
    
    loadPreset(presetName) {
        if (this.currentPreset === presetName) return;
        
        // Update button states
        document.querySelectorAll('[data-preset]').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.preset === presetName);
        });
        
        this.currentPreset = presetName;
        
        // Load preset parameters
        const presets = {
            plastic: {
                baseColor: [0.8, 0.2, 0.2],
                metallic: 0.0,
                roughness: 0.5,
                specular: 0.5,
                specularTint: 0.0,
                anisotropic: 0.0,
                sheen: 0.0,
                sheenTint: 0.5,
                clearcoat: 0.0,
                clearcoatGloss: 1.0,
                subsurface: 0.0
            },
            metal: {
                baseColor: [0.7, 0.7, 0.8],
                metallic: 1.0,
                roughness: 0.1,
                specular: 0.5,
                specularTint: 0.0,
                anisotropic: 0.0,
                sheen: 0.0,
                sheenTint: 0.5,
                clearcoat: 0.0,
                clearcoatGloss: 1.0,
                subsurface: 0.0
            },
            glass: {
                baseColor: [0.9, 0.9, 0.9],
                metallic: 0.0,
                roughness: 0.0,
                specular: 1.0,
                specularTint: 0.0,
                anisotropic: 0.0,
                sheen: 0.0,
                sheenTint: 0.5,
                clearcoat: 1.0,
                clearcoatGloss: 1.0,
                subsurface: 0.0
            },
            wood: {
                baseColor: [0.6, 0.4, 0.2],
                metallic: 0.0,
                roughness: 0.8,
                specular: 0.3,
                specularTint: 0.0,
                anisotropic: 0.5,
                sheen: 0.0,
                sheenTint: 0.5,
                clearcoat: 0.0,
                clearcoatGloss: 1.0,
                subsurface: 0.0
            },
            fabric: {
                baseColor: [0.5, 0.5, 0.5],
                metallic: 0.0,
                roughness: 0.9,
                specular: 0.2,
                specularTint: 0.0,
                anisotropic: 0.0,
                sheen: 0.8,
                sheenTint: 0.5,
                clearcoat: 0.0,
                clearcoatGloss: 1.0,
                subsurface: 0.0
            },
            skin: {
                baseColor: [0.8, 0.6, 0.5],
                metallic: 0.0,
                roughness: 0.4,
                specular: 0.5,
                specularTint: 0.0,
                anisotropic: 0.0,
                sheen: 0.0,
                sheenTint: 0.5,
                clearcoat: 0.0,
                clearcoatGloss: 1.0,
                subsurface: 0.3
            },
            ceramic: {
                baseColor: [0.9, 0.9, 0.9],
                metallic: 0.0,
                roughness: 0.1,
                specular: 0.8,
                specularTint: 0.0,
                anisotropic: 0.0,
                sheen: 0.0,
                sheenTint: 0.5,
                clearcoat: 0.5,
                clearcoatGloss: 1.0,
                subsurface: 0.0
            },
            rubber: {
                baseColor: [0.2, 0.2, 0.2],
                metallic: 0.0,
                roughness: 0.9,
                specular: 0.3,
                specularTint: 0.0,
                anisotropic: 0.0,
                sheen: 0.0,
                sheenTint: 0.5,
                clearcoat: 0.0,
                clearcoatGloss: 1.0,
                subsurface: 0.0
            }
        };
        
        if (presets[presetName]) {
            this.currentMaterial = { ...presets[presetName] };
            this.updateUI();
            this.updatePreview();
        }
        
        console.log(`Preset loaded: ${presetName}`);
    }
    
    updateUI() {
        // Update sliders
        Object.keys(this.currentMaterial).forEach(param => {
            if (param === 'baseColor') return;
            
            const slider = document.getElementById(param);
            const valueDisplay = document.getElementById(param + 'Value');
            
            if (slider && valueDisplay) {
                slider.value = this.currentMaterial[param];
                valueDisplay.textContent = this.currentMaterial[param].toFixed(2);
            }
        });
        
        // Update color picker
        const hex = this.rgbToHex(this.currentMaterial.baseColor);
        document.getElementById('baseColor').value = hex;
        document.getElementById('baseColorHex').value = hex;
    }
    
    schedulePreviewUpdate() {
        if (this.renderTimeout) {
            clearTimeout(this.renderTimeout);
        }
        
        this.renderTimeout = setTimeout(() => {
            this.updatePreview();
        }, 100); // Debounce updates
    }
    
    updatePreview() {
        if (this.isRendering) return;
        
        this.isRendering = true;
        document.getElementById('previewLoading').style.display = 'block';
        
        const startTime = performance.now();
        
        // Simulate rendering call to Ruby backend
        const materialData = {
            material: this.currentMaterial,
            geometry: this.currentGeometry,
            settings: {
                width: 512,
                height: 512,
                samples: 16
            }
        };
        
        // Call Ruby backend
        if (window.sketchup) {
            window.sketchup.renderPreview(JSON.stringify(materialData));
        } else {
            // Fallback for testing
            setTimeout(() => {
                this.onPreviewComplete(null, performance.now() - startTime);
            }, 500);
        }
        
        console.log('Preview update requested:', materialData);
    }
    
    onPreviewComplete(imageData, renderTime) {
        this.isRendering = false;
        document.getElementById('previewLoading').style.display = 'none';
        
        // Update render stats
        document.getElementById('renderTime').textContent = `Render time: ${renderTime.toFixed(0)}ms`;
        
        if (imageData) {
            // Update canvas with new image
            const canvas = document.getElementById('previewCanvas');
            const ctx = canvas.getContext('2d');
            
            const img = new Image();
            img.onload = () => {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
            };
            img.src = imageData;
        }
        
        console.log(`Preview completed in ${renderTime.toFixed(0)}ms`);
    }
    
    setBaseColorFromHex(hex) {
        const rgb = this.hexToRgb(hex);
        if (rgb) {
            this.currentMaterial.baseColor = [rgb.r / 255, rgb.g / 255, rgb.b / 255];
        }
    }
    
    hexToRgb(hex) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        } : null;
    }
    
    rgbToHex(rgb) {
        const r = Math.round(rgb[0] * 255);
        const g = Math.round(rgb[1] * 255);
        const b = Math.round(rgb[2] * 255);
        return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
    }
    
    isValidHex(hex) {
        return /^#[0-9A-F]{6}$/i.test(hex);
    }
    
    openTextureDialog(textureType) {
        if (window.sketchup) {
            window.sketchup.openTextureDialog(textureType);
        } else {
            console.log(`Open texture dialog for: ${textureType}`);
        }
    }
    
    handleTextureDrop(event, textureType) {
        const files = event.dataTransfer.files;
        if (files.length > 0) {
            const file = files[0];
            if (file.type.startsWith('image/')) {
                this.loadTexture(file, textureType);
            }
        }
    }
    
    loadTexture(file, textureType) {
        const reader = new FileReader();
        reader.onload = (e) => {
            const textureSlot = document.getElementById(textureType + 'Texture');
            const preview = textureSlot.querySelector('.texture-preview');
            const info = textureSlot.querySelector('.texture-info');
            
            preview.style.backgroundImage = `url(${e.target.result})`;
            info.textContent = file.name;
            textureSlot.classList.add('has-texture');
            
            this.schedulePreviewUpdate();
        };
        reader.readAsDataURL(file);
    }
    
    resetMaterial() {
        this.loadPreset(this.currentPreset);
    }
    
    saveMaterial() {
        const materialData = {
            name: `Custom_${Date.now()}`,
            parameters: this.currentMaterial
        };
        
        if (window.sketchup) {
            window.sketchup.saveMaterial(JSON.stringify(materialData));
        } else {
            console.log('Save material:', materialData);
        }
    }
    
    applyMaterial() {
        if (window.sketchup) {
            window.sketchup.applyMaterial(JSON.stringify(this.currentMaterial));
        } else {
            console.log('Apply material:', this.currentMaterial);
        }
    }
}

// Initialize material editor when page loads
document.addEventListener('DOMContentLoaded', () => {
    window.materialEditor = new MaterialEditor();
});

// Global functions for Ruby integration
window.onPreviewComplete = (imageData, renderTime) => {
    if (window.materialEditor) {
        window.materialEditor.onPreviewComplete(imageData, renderTime);
    }
};

window.setMaterialParameter = (parameter, value) => {
    if (window.materialEditor) {
        window.materialEditor.currentMaterial[parameter] = value;
        window.materialEditor.updateUI();
        window.materialEditor.schedulePreviewUpdate();
    }
};
