<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PhotonRender Material Export/Import</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1a1a1a;
            color: #e0e0e0;
            overflow: hidden;
        }

        .export-import-panel {
            display: flex;
            height: 100vh;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
        }

        /* Export Panel */
        .export-panel {
            width: 50%;
            background: #252525;
            border-right: 1px solid #404040;
            display: flex;
            flex-direction: column;
        }

        .panel-header {
            background: #2a2a2a;
            padding: 15px;
            border-bottom: 1px solid #404040;
        }

        .panel-title {
            font-size: 16px;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 5px;
        }

        .panel-subtitle {
            font-size: 12px;
            color: #888;
        }

        .panel-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .section {
            margin-bottom: 25px;
        }

        .section-title {
            font-size: 14px;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 1px solid #404040;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-label {
            display: block;
            font-size: 12px;
            color: #ccc;
            margin-bottom: 5px;
        }

        .form-input {
            width: 100%;
            background: #1a1a1a;
            border: 1px solid #404040;
            color: #e0e0e0;
            padding: 8px 10px;
            border-radius: 4px;
            font-size: 12px;
        }

        .form-input:focus {
            outline: none;
            border-color: #0078d4;
        }

        .form-select {
            width: 100%;
            background: #1a1a1a;
            border: 1px solid #404040;
            color: #e0e0e0;
            padding: 8px 10px;
            border-radius: 4px;
            font-size: 12px;
        }

        .form-checkbox {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
        }

        .form-checkbox input[type="checkbox"] {
            width: 16px;
            height: 16px;
            accent-color: #0078d4;
        }

        .form-checkbox label {
            font-size: 12px;
            color: #e0e0e0;
            cursor: pointer;
        }

        .file-drop-zone {
            border: 2px dashed #404040;
            border-radius: 8px;
            padding: 30px;
            text-align: center;
            background: #2a2a2a;
            transition: all 0.2s;
            cursor: pointer;
        }

        .file-drop-zone:hover {
            border-color: #0078d4;
            background: #1e3a5f;
        }

        .file-drop-zone.drag-over {
            border-color: #0078d4;
            background: #1e3a5f;
        }

        .drop-icon {
            font-size: 32px;
            margin-bottom: 10px;
            opacity: 0.7;
        }

        .drop-text {
            font-size: 14px;
            color: #e0e0e0;
            margin-bottom: 5px;
        }

        .drop-subtext {
            font-size: 11px;
            color: #888;
        }

        .material-list {
            background: #2a2a2a;
            border-radius: 6px;
            max-height: 200px;
            overflow-y: auto;
        }

        .material-item {
            padding: 10px;
            border-bottom: 1px solid #404040;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .material-item:last-child {
            border-bottom: none;
        }

        .material-info {
            flex: 1;
        }

        .material-name {
            font-size: 12px;
            color: #ffffff;
            margin-bottom: 2px;
        }

        .material-details {
            font-size: 10px;
            color: #888;
        }

        .material-checkbox {
            margin-left: 10px;
        }

        .format-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
        }

        .format-option {
            background: #2a2a2a;
            border: 2px solid #404040;
            border-radius: 6px;
            padding: 12px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s;
        }

        .format-option:hover {
            border-color: #0078d4;
        }

        .format-option.selected {
            border-color: #0078d4;
            background: #1e3a5f;
        }

        .format-icon {
            font-size: 20px;
            margin-bottom: 5px;
        }

        .format-name {
            font-size: 11px;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 2px;
        }

        .format-desc {
            font-size: 9px;
            color: #888;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: #404040;
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #0078d4, #106ebe);
            width: 0%;
            transition: width 0.3s ease;
        }

        .progress-text {
            font-size: 11px;
            color: #888;
            text-align: center;
        }

        .result-panel {
            background: #2a2a2a;
            border-radius: 6px;
            padding: 15px;
            margin-top: 15px;
            display: none;
        }

        .result-panel.show {
            display: block;
        }

        .result-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 10px;
        }

        .result-icon {
            font-size: 16px;
        }

        .result-icon.success {
            color: #28a745;
        }

        .result-icon.error {
            color: #dc3545;
        }

        .result-title {
            font-size: 13px;
            font-weight: 600;
            color: #ffffff;
        }

        .result-details {
            font-size: 11px;
            color: #888;
            line-height: 1.4;
        }

        .result-files {
            margin-top: 10px;
        }

        .result-file {
            background: #1a1a1a;
            padding: 6px 8px;
            border-radius: 3px;
            margin-bottom: 4px;
            font-size: 10px;
            color: #0078d4;
            font-family: monospace;
        }

        /* Import Panel */
        .import-panel {
            width: 50%;
            background: #252525;
            display: flex;
            flex-direction: column;
        }

        /* Action Bar */
        .action-bar {
            background: #333;
            padding: 15px;
            border-top: 1px solid #404040;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .action-info {
            font-size: 12px;
            color: #888;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
        }

        .action-btn {
            background: #0078d4;
            border: none;
            color: white;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.2s;
        }

        .action-btn:hover {
            background: #106ebe;
        }

        .action-btn:disabled {
            background: #404040;
            color: #888;
            cursor: not-allowed;
        }

        .action-btn.secondary {
            background: #404040;
            color: #e0e0e0;
        }

        .action-btn.secondary:hover {
            background: #505050;
        }

        .action-btn.success {
            background: #28a745;
        }

        .action-btn.success:hover {
            background: #218838;
        }

        /* Scrollbar styling */
        .panel-content::-webkit-scrollbar,
        .material-list::-webkit-scrollbar {
            width: 8px;
        }

        .panel-content::-webkit-scrollbar-track,
        .material-list::-webkit-scrollbar-track {
            background: #1a1a1a;
        }

        .panel-content::-webkit-scrollbar-thumb,
        .material-list::-webkit-scrollbar-thumb {
            background: #404040;
            border-radius: 4px;
        }

        .panel-content::-webkit-scrollbar-thumb:hover,
        .material-list::-webkit-scrollbar-thumb:hover {
            background: #505050;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .export-import-panel {
                flex-direction: column;
            }
            
            .export-panel,
            .import-panel {
                width: 100%;
                height: 50vh;
            }
            
            .format-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="export-import-panel">
        <!-- Export Panel -->
        <div class="export-panel">
            <div class="panel-header">
                <div class="panel-title">📤 Material Export</div>
                <div class="panel-subtitle">Export materials to various formats</div>
            </div>
            
            <div class="panel-content">
                <!-- Material Selection -->
                <div class="section">
                    <div class="section-title">Materials to Export</div>
                    <div class="material-list" id="exportMaterialList">
                        <!-- Materials will be populated here -->
                    </div>
                </div>
                
                <!-- Export Format -->
                <div class="section">
                    <div class="section-title">Export Format</div>
                    <div class="format-grid">
                        <div class="format-option selected" data-format="json">
                            <div class="format-icon">📄</div>
                            <div class="format-name">JSON</div>
                            <div class="format-desc">PhotonRender</div>
                        </div>
                        <div class="format-option" data-format="mtl">
                            <div class="format-icon">🗂️</div>
                            <div class="format-name">MTL</div>
                            <div class="format-desc">Wavefront</div>
                        </div>
                        <div class="format-option" data-format="gltf">
                            <div class="format-icon">🎯</div>
                            <div class="format-name">glTF</div>
                            <div class="format-desc">Khronos</div>
                        </div>
                        <div class="format-option" data-format="obj">
                            <div class="format-icon">📦</div>
                            <div class="format-name">OBJ+MTL</div>
                            <div class="format-desc">Combined</div>
                        </div>
                    </div>
                </div>
                
                <!-- Export Options -->
                <div class="section">
                    <div class="section-title">Export Options</div>
                    
                    <div class="form-group">
                        <label class="form-label">Output Path</label>
                        <input type="text" class="form-input" id="exportPath" placeholder="Choose export location...">
                    </div>
                    
                    <div class="form-checkbox">
                        <input type="checkbox" id="includeTextures" checked>
                        <label for="includeTextures">Include Textures</label>
                    </div>
                    
                    <div class="form-checkbox">
                        <input type="checkbox" id="optimizeTextures">
                        <label for="optimizeTextures">Optimize Textures</label>
                    </div>
                    
                    <div class="form-checkbox">
                        <input type="checkbox" id="useRelativePaths" checked>
                        <label for="useRelativePaths">Use Relative Paths</label>
                    </div>
                    
                    <div class="form-checkbox">
                        <input type="checkbox" id="includeMetadata" checked>
                        <label for="includeMetadata">Include Metadata</label>
                    </div>
                </div>
                
                <!-- Export Progress -->
                <div class="section" id="exportProgressSection" style="display: none;">
                    <div class="section-title">Export Progress</div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="exportProgressFill"></div>
                    </div>
                    <div class="progress-text" id="exportProgressText">Ready to export</div>
                </div>
                
                <!-- Export Result -->
                <div class="result-panel" id="exportResult">
                    <div class="result-header">
                        <div class="result-icon success" id="exportResultIcon">✓</div>
                        <div class="result-title" id="exportResultTitle">Export Successful</div>
                    </div>
                    <div class="result-details" id="exportResultDetails">
                        Materials exported successfully to the specified location.
                    </div>
                    <div class="result-files" id="exportResultFiles">
                        <!-- Exported files will be listed here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Import Panel -->
        <div class="import-panel">
            <div class="panel-header">
                <div class="panel-title">📥 Material Import</div>
                <div class="panel-subtitle">Import materials from external files</div>
            </div>
            
            <div class="panel-content">
                <!-- File Selection -->
                <div class="section">
                    <div class="section-title">Select Files to Import</div>
                    <div class="file-drop-zone" id="importDropZone">
                        <div class="drop-icon">📁</div>
                        <div class="drop-text">Drop material files here</div>
                        <div class="drop-subtext">or click to browse</div>
                        <div class="drop-subtext">Supports: MTL, glTF, JSON, OBJ</div>
                    </div>
                </div>
                
                <!-- Import Format -->
                <div class="section">
                    <div class="section-title">Import Format</div>
                    <div class="form-group">
                        <select class="form-select" id="importFormat">
                            <option value="auto">Auto-detect</option>
                            <option value="mtl">Wavefront MTL</option>
                            <option value="gltf">glTF 2.0</option>
                            <option value="json">PhotonRender JSON</option>
                            <option value="obj">OBJ + MTL</option>
                        </select>
                    </div>
                </div>
                
                <!-- Import Options -->
                <div class="section">
                    <div class="section-title">Import Options</div>
                    
                    <div class="form-checkbox">
                        <input type="checkbox" id="importTextures" checked>
                        <label for="importTextures">Import Textures</label>
                    </div>
                    
                    <div class="form-checkbox">
                        <input type="checkbox" id="copyTextures" checked>
                        <label for="copyTextures">Copy Textures Locally</label>
                    </div>
                    
                    <div class="form-checkbox">
                        <input type="checkbox" id="validateMaterials" checked>
                        <label for="validateMaterials">Validate Materials</label>
                    </div>
                    
                    <div class="form-checkbox">
                        <input type="checkbox" id="convertToDisney" checked>
                        <label for="convertToDisney">Convert to Disney BRDF</label>
                    </div>
                    
                    <div class="form-checkbox">
                        <input type="checkbox" id="overwriteExisting">
                        <label for="overwriteExisting">Overwrite Existing</label>
                    </div>
                </div>
                
                <!-- Import Progress -->
                <div class="section" id="importProgressSection" style="display: none;">
                    <div class="section-title">Import Progress</div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="importProgressFill"></div>
                    </div>
                    <div class="progress-text" id="importProgressText">Ready to import</div>
                </div>
                
                <!-- Import Result -->
                <div class="result-panel" id="importResult">
                    <div class="result-header">
                        <div class="result-icon success" id="importResultIcon">✓</div>
                        <div class="result-title" id="importResultTitle">Import Successful</div>
                    </div>
                    <div class="result-details" id="importResultDetails">
                        Materials imported successfully and added to the library.
                    </div>
                    <div class="result-files" id="importResultFiles">
                        <!-- Imported materials will be listed here -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Action Bar -->
    <div class="action-bar">
        <div class="action-info" id="actionInfo">
            Ready for export/import operations
        </div>
        <div class="action-buttons">
            <button class="action-btn secondary" onclick="exportImport.browseExportPath()">Browse Export</button>
            <button class="action-btn secondary" onclick="exportImport.browseImportFiles()">Browse Import</button>
            <button class="action-btn" onclick="exportImport.startExport()" id="exportBtn">Export Materials</button>
            <button class="action-btn success" onclick="exportImport.startImport()" id="importBtn" disabled>Import Materials</button>
        </div>
    </div>

    <script src="material_export_import.js"></script>
</body>
</html>
