// src/ruby/photon_render/material_export_import.js
// PhotonRender Material Export/Import - JavaScript Interface
// Sistema di controllo interfaccia per Material Export/Import

class MaterialExportImport {
    constructor() {
        this.materials = [];
        this.selectedMaterials = [];
        this.selectedFormat = 'json';
        this.importFiles = [];
        this.isExporting = false;
        this.isImporting = false;
        
        this.initializeEventListeners();
        this.loadMaterials();
    }
    
    initializeEventListeners() {
        // Format selection
        document.querySelectorAll('.format-option').forEach(option => {
            option.addEventListener('click', (e) => {
                this.selectFormat(e.currentTarget.dataset.format);
            });
        });
        
        // File drop zone
        const dropZone = document.getElementById('importDropZone');
        
        dropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropZone.classList.add('drag-over');
        });
        
        dropZone.addEventListener('dragleave', (e) => {
            if (!dropZone.contains(e.relatedTarget)) {
                dropZone.classList.remove('drag-over');
            }
        });
        
        dropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropZone.classList.remove('drag-over');
            this.handleFileDrop(e);
        });
        
        dropZone.addEventListener('click', () => {
            this.browseImportFiles();
        });
        
        // Export path click
        document.getElementById('exportPath').addEventListener('click', () => {
            this.browseExportPath();
        });
    }
    
    loadMaterials() {
        this.updateActionInfo('Loading materials...');
        
        // Request materials from Ruby backend
        if (window.sketchup) {
            window.sketchup.getMaterialsForExport();
        } else {
            // Fallback for testing
            setTimeout(() => {
                this.onMaterialsLoaded(this.generateMockMaterials());
            }, 500);
        }
    }
    
    onMaterialsLoaded(materials) {
        this.materials = materials || [];
        this.selectedMaterials = this.materials.map(m => m.id); // Select all by default
        this.displayMaterials();
        this.updateActionInfo('Ready for export/import operations');
        
        console.log(`Loaded ${this.materials.length} materials for export`);
    }
    
    displayMaterials() {
        const materialList = document.getElementById('exportMaterialList');
        
        if (this.materials.length === 0) {
            materialList.innerHTML = `
                <div style="text-align: center; padding: 20px; color: #888;">
                    <div style="font-size: 24px; margin-bottom: 10px;">📁</div>
                    <div>No materials available</div>
                    <div style="font-size: 11px; margin-top: 5px;">Create materials in the Material Editor first</div>
                </div>
            `;
            return;
        }
        
        materialList.innerHTML = this.materials.map(material => 
            this.createMaterialItem(material)
        ).join('');
        
        // Add event listeners to checkboxes
        this.attachMaterialEventListeners();
    }
    
    createMaterialItem(material) {
        const isSelected = this.selectedMaterials.includes(material.id);
        
        return `
            <div class="material-item">
                <div class="material-info">
                    <div class="material-name">${material.name || 'Unnamed Material'}</div>
                    <div class="material-details">${material.type || 'PBR Material'} • ${material.textureCount || 0} textures</div>
                </div>
                <div class="material-checkbox">
                    <input type="checkbox" ${isSelected ? 'checked' : ''} data-material-id="${material.id}">
                </div>
            </div>
        `;
    }
    
    attachMaterialEventListeners() {
        document.querySelectorAll('.material-checkbox input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                const materialId = e.target.dataset.materialId;
                if (e.target.checked) {
                    if (!this.selectedMaterials.includes(materialId)) {
                        this.selectedMaterials.push(materialId);
                    }
                } else {
                    this.selectedMaterials = this.selectedMaterials.filter(id => id !== materialId);
                }
                
                this.updateExportButton();
            });
        });
    }
    
    selectFormat(format) {
        this.selectedFormat = format;
        
        // Update UI
        document.querySelectorAll('.format-option').forEach(option => {
            option.classList.toggle('selected', option.dataset.format === format);
        });
        
        console.log(`Selected export format: ${format}`);
    }
    
    handleFileDrop(event) {
        const files = Array.from(event.dataTransfer.files);
        this.addImportFiles(files);
    }
    
    addImportFiles(files) {
        const supportedExtensions = ['.mtl', '.gltf', '.glb', '.json', '.obj'];
        
        const validFiles = files.filter(file => {
            const ext = '.' + file.name.split('.').pop().toLowerCase();
            return supportedExtensions.includes(ext);
        });
        
        if (validFiles.length === 0) {
            alert('No supported material files found. Supported formats: MTL, glTF, JSON, OBJ');
            return;
        }
        
        this.importFiles = validFiles;
        this.updateImportDisplay();
        this.updateImportButton();
        
        console.log(`Added ${validFiles.length} files for import:`, validFiles.map(f => f.name));
    }
    
    updateImportDisplay() {
        const dropZone = document.getElementById('importDropZone');
        
        if (this.importFiles.length > 0) {
            dropZone.innerHTML = `
                <div class="drop-icon">📄</div>
                <div class="drop-text">${this.importFiles.length} file(s) selected</div>
                <div class="drop-subtext">${this.importFiles.map(f => f.name).join(', ')}</div>
                <div class="drop-subtext">Click to change selection</div>
            `;
        } else {
            dropZone.innerHTML = `
                <div class="drop-icon">📁</div>
                <div class="drop-text">Drop material files here</div>
                <div class="drop-subtext">or click to browse</div>
                <div class="drop-subtext">Supports: MTL, glTF, JSON, OBJ</div>
            `;
        }
    }
    
    updateExportButton() {
        const exportBtn = document.getElementById('exportBtn');
        exportBtn.disabled = this.selectedMaterials.length === 0 || this.isExporting;
    }
    
    updateImportButton() {
        const importBtn = document.getElementById('importBtn');
        importBtn.disabled = this.importFiles.length === 0 || this.isImporting;
    }
    
    browseExportPath() {
        console.log('Browse export path');
        
        if (window.sketchup) {
            window.sketchup.browseExportPath(this.selectedFormat);
        } else {
            // Mock for testing
            const mockPath = `C:/Users/<USER>/Documents/materials.${this.getFormatExtension(this.selectedFormat)}`;
            document.getElementById('exportPath').value = mockPath;
        }
    }
    
    browseImportFiles() {
        console.log('Browse import files');
        
        if (window.sketchup) {
            window.sketchup.browseImportFiles();
        } else {
            // Mock for testing - create file input
            const input = document.createElement('input');
            input.type = 'file';
            input.multiple = true;
            input.accept = '.mtl,.gltf,.glb,.json,.obj';
            input.onchange = (e) => {
                if (e.target.files.length > 0) {
                    this.addImportFiles(Array.from(e.target.files));
                }
            };
            input.click();
        }
    }
    
    startExport() {
        if (this.selectedMaterials.length === 0) {
            alert('Please select materials to export.');
            return;
        }
        
        const exportPath = document.getElementById('exportPath').value;
        if (!exportPath) {
            alert('Please specify an export path.');
            return;
        }
        
        this.isExporting = true;
        this.updateExportButton();
        this.showExportProgress();
        
        const exportOptions = {
            format: this.selectedFormat,
            outputPath: exportPath,
            materialIds: this.selectedMaterials,
            includeTextures: document.getElementById('includeTextures').checked,
            optimizeTextures: document.getElementById('optimizeTextures').checked,
            useRelativePaths: document.getElementById('useRelativePaths').checked,
            includeMetadata: document.getElementById('includeMetadata').checked
        };
        
        console.log('Starting export with options:', exportOptions);
        
        if (window.sketchup) {
            window.sketchup.exportMaterials(JSON.stringify(exportOptions));
        } else {
            // Mock export for testing
            this.simulateExport(exportOptions);
        }
    }
    
    startImport() {
        if (this.importFiles.length === 0) {
            alert('Please select files to import.');
            return;
        }
        
        this.isImporting = true;
        this.updateImportButton();
        this.showImportProgress();
        
        const importOptions = {
            format: document.getElementById('importFormat').value,
            files: this.importFiles.map(f => f.name), // In real implementation, use file paths
            importTextures: document.getElementById('importTextures').checked,
            copyTextures: document.getElementById('copyTextures').checked,
            validateMaterials: document.getElementById('validateMaterials').checked,
            convertToDisney: document.getElementById('convertToDisney').checked,
            overwriteExisting: document.getElementById('overwriteExisting').checked
        };
        
        console.log('Starting import with options:', importOptions);
        
        if (window.sketchup) {
            window.sketchup.importMaterials(JSON.stringify(importOptions));
        } else {
            // Mock import for testing
            this.simulateImport(importOptions);
        }
    }
    
    showExportProgress() {
        document.getElementById('exportProgressSection').style.display = 'block';
        document.getElementById('exportResult').classList.remove('show');
    }
    
    showImportProgress() {
        document.getElementById('importProgressSection').style.display = 'block';
        document.getElementById('importResult').classList.remove('show');
    }
    
    updateExportProgress(progress, message) {
        document.getElementById('exportProgressFill').style.width = `${progress * 100}%`;
        document.getElementById('exportProgressText').textContent = message;
    }
    
    updateImportProgress(progress, message) {
        document.getElementById('importProgressFill').style.width = `${progress * 100}%`;
        document.getElementById('importProgressText').textContent = message;
    }
    
    onExportComplete(result) {
        this.isExporting = false;
        this.updateExportButton();
        
        document.getElementById('exportProgressSection').style.display = 'none';
        
        const resultPanel = document.getElementById('exportResult');
        const resultIcon = document.getElementById('exportResultIcon');
        const resultTitle = document.getElementById('exportResultTitle');
        const resultDetails = document.getElementById('exportResultDetails');
        const resultFiles = document.getElementById('exportResultFiles');
        
        if (result.success) {
            resultIcon.className = 'result-icon success';
            resultIcon.textContent = '✓';
            resultTitle.textContent = 'Export Successful';
            resultDetails.textContent = `${result.materialCount} materials exported in ${result.exportTime.toFixed(2)}s`;
            
            if (result.exportedFiles && result.exportedFiles.length > 0) {
                resultFiles.innerHTML = result.exportedFiles.map(file => 
                    `<div class="result-file">${file}</div>`
                ).join('');
            }
        } else {
            resultIcon.className = 'result-icon error';
            resultIcon.textContent = '✗';
            resultTitle.textContent = 'Export Failed';
            resultDetails.textContent = result.errors ? result.errors.join(', ') : 'Unknown error occurred';
            resultFiles.innerHTML = '';
        }
        
        resultPanel.classList.add('show');
        this.updateActionInfo(result.success ? 'Export completed successfully' : 'Export failed');
    }
    
    onImportComplete(result) {
        this.isImporting = false;
        this.updateImportButton();
        
        document.getElementById('importProgressSection').style.display = 'none';
        
        const resultPanel = document.getElementById('importResult');
        const resultIcon = document.getElementById('importResultIcon');
        const resultTitle = document.getElementById('importResultTitle');
        const resultDetails = document.getElementById('importResultDetails');
        const resultFiles = document.getElementById('importResultFiles');
        
        if (result.success) {
            resultIcon.className = 'result-icon success';
            resultIcon.textContent = '✓';
            resultTitle.textContent = 'Import Successful';
            resultDetails.textContent = `${result.materialCount} materials imported in ${result.importTime.toFixed(2)}s`;
            
            if (result.materials && result.materials.length > 0) {
                resultFiles.innerHTML = result.materials.map(material => 
                    `<div class="result-file">${material.name}</div>`
                ).join('');
            }
            
            // Refresh materials list
            this.loadMaterials();
        } else {
            resultIcon.className = 'result-icon error';
            resultIcon.textContent = '✗';
            resultTitle.textContent = 'Import Failed';
            resultDetails.textContent = result.errors ? result.errors.join(', ') : 'Unknown error occurred';
            resultFiles.innerHTML = '';
        }
        
        resultPanel.classList.add('show');
        this.updateActionInfo(result.success ? 'Import completed successfully' : 'Import failed');
    }
    
    updateActionInfo(message) {
        document.getElementById('actionInfo').textContent = message;
    }
    
    getFormatExtension(format) {
        const extensions = {
            'json': 'json',
            'mtl': 'mtl',
            'gltf': 'gltf',
            'obj': 'obj'
        };
        return extensions[format] || 'txt';
    }
    
    simulateExport(options) {
        let progress = 0;
        const interval = setInterval(() => {
            progress += 0.1;
            this.updateExportProgress(progress, `Exporting materials... ${Math.round(progress * 100)}%`);
            
            if (progress >= 1.0) {
                clearInterval(interval);
                
                // Simulate export result
                const result = {
                    success: true,
                    materialCount: this.selectedMaterials.length,
                    textureCount: 5,
                    exportTime: 2.3,
                    exportedFiles: [
                        options.outputPath,
                        'textures/material1_diffuse.png',
                        'textures/material1_normal.png'
                    ]
                };
                
                setTimeout(() => {
                    this.onExportComplete(result);
                }, 500);
            }
        }, 200);
    }
    
    simulateImport(options) {
        let progress = 0;
        const interval = setInterval(() => {
            progress += 0.15;
            this.updateImportProgress(progress, `Importing materials... ${Math.round(progress * 100)}%`);
            
            if (progress >= 1.0) {
                clearInterval(interval);
                
                // Simulate import result
                const result = {
                    success: true,
                    materialCount: this.importFiles.length,
                    textureCount: 3,
                    importTime: 1.8,
                    materials: [
                        { name: 'Imported Material 1' },
                        { name: 'Imported Material 2' }
                    ]
                };
                
                setTimeout(() => {
                    this.onImportComplete(result);
                }, 500);
            }
        }, 150);
    }
    
    generateMockMaterials() {
        return [
            {
                id: 'mat_001',
                name: 'Red Plastic',
                type: 'PBR Material',
                textureCount: 2
            },
            {
                id: 'mat_002',
                name: 'Chrome Metal',
                type: 'PBR Material',
                textureCount: 1
            },
            {
                id: 'mat_003',
                name: 'Wood Oak',
                type: 'PBR Material',
                textureCount: 4
            },
            {
                id: 'mat_004',
                name: 'Glass Clear',
                type: 'PBR Material',
                textureCount: 0
            }
        ];
    }
}

// Initialize export/import when page loads
document.addEventListener('DOMContentLoaded', () => {
    window.exportImport = new MaterialExportImport();
});

// Global functions for Ruby integration
window.onMaterialsLoaded = (materials) => {
    if (window.exportImport) {
        window.exportImport.onMaterialsLoaded(materials);
    }
};

window.onExportProgress = (progress, message) => {
    if (window.exportImport) {
        window.exportImport.updateExportProgress(progress, message);
    }
};

window.onImportProgress = (progress, message) => {
    if (window.exportImport) {
        window.exportImport.updateImportProgress(progress, message);
    }
};

window.onExportComplete = (result) => {
    if (window.exportImport) {
        window.exportImport.onExportComplete(result);
    }
};

window.onImportComplete = (result) => {
    if (window.exportImport) {
        window.exportImport.onImportComplete(result);
    }
};

window.onExportPathSelected = (path) => {
    if (window.exportImport) {
        document.getElementById('exportPath').value = path;
    }
};

window.onImportFilesSelected = (files) => {
    if (window.exportImport) {
        // Convert file paths to file objects for display
        const fileObjects = files.map(path => ({
            name: path.split(/[/\\]/).pop(),
            path: path
        }));
        window.exportImport.addImportFiles(fileObjects);
    }
};
