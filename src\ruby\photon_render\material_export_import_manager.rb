# src/ruby/photon_render/material_export_import_manager.rb
# Material export/import management for PhotonRender

module PhotonRender
  
  module MaterialExportImportManager
    
    # Initialize material export/import manager
    def self.initialize
      puts "Initializing Material Export/Import Manager"
      @supported_formats = ["json", "xml", "mtl"]
    end
    
    # Export materials to file
    def self.export_to_file(filename)
      format = detect_format(filename)
      
      case format
      when "json"
        export_to_json(filename)
      when "xml"
        export_to_xml(filename)
      when "mtl"
        export_to_mtl(filename)
      else
        puts "Unsupported export format: #{format}"
        false
      end
    end
    
    # Import materials from file
    def self.import_from_file(filename)
      unless File.exist?(filename)
        puts "File not found: #{filename}"
        return false
      end
      
      format = detect_format(filename)
      
      case format
      when "json"
        import_from_json(filename)
      when "xml"
        import_from_xml(filename)
      when "mtl"
        import_from_mtl(filename)
      else
        puts "Unsupported import format: #{format}"
        false
      end
    end
    
    # Export to JSON format
    def self.export_to_json(filename)
      begin
        materials = MaterialLibraryManager.get_materials
        
        export_data = {
          format_version: "1.0",
          exported_at: Time.now.iso8601,
          exported_by: "PhotonRender #{PhotonRender::PLUGIN_VERSION}",
          material_count: materials.size,
          materials: materials
        }
        
        File.write(filename, JSON.pretty_generate(export_data))
        puts "Materials exported to JSON: #{filename}"
        UI.messagebox("Exported #{materials.size} materials to #{File.basename(filename)}")
        true
      rescue => e
        puts "Error exporting to JSON: #{e.message}"
        UI.messagebox("Error exporting materials: #{e.message}")
        false
      end
    end
    
    # Import from JSON format
    def self.import_from_json(filename)
      begin
        data = JSON.parse(File.read(filename))
        
        # Validate format
        unless data["materials"]
          puts "Invalid JSON format: missing 'materials' section"
          return false
        end
        
        imported_count = 0
        skipped_count = 0
        
        data["materials"].each do |id, material_data|
          # Convert string keys to symbols
          material = {}
          material_data.each { |k, v| material[k.to_sym] = v }
          
          # Validate material
          validation = MaterialValidationManager.validate_material(material)
          
          if validation[:valid]
            MaterialLibraryManager.add_material(id, material)
            imported_count += 1
          else
            puts "Skipping invalid material '#{id}': #{validation[:errors].join(', ')}"
            skipped_count += 1
          end
        end
        
        puts "Imported #{imported_count} materials from JSON, skipped #{skipped_count}"
        UI.messagebox("Imported #{imported_count} materials\nSkipped #{skipped_count} invalid materials")
        true
      rescue => e
        puts "Error importing from JSON: #{e.message}"
        UI.messagebox("Error importing materials: #{e.message}")
        false
      end
    end
    
    # Export to XML format
    def self.export_to_xml(filename)
      begin
        materials = MaterialLibraryManager.get_materials
        
        xml_content = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n"
        xml_content += "<photon_materials version=\"1.0\">\n"
        xml_content += "  <metadata>\n"
        xml_content += "    <exported_at>#{Time.now.iso8601}</exported_at>\n"
        xml_content += "    <exported_by>PhotonRender #{PhotonRender::PLUGIN_VERSION}</exported_by>\n"
        xml_content += "    <material_count>#{materials.size}</material_count>\n"
        xml_content += "  </metadata>\n"
        xml_content += "  <materials>\n"
        
        materials.each do |id, material|
          xml_content += "    <material id=\"#{id}\">\n"
          xml_content += "      <name>#{escape_xml(material[:name])}</name>\n"
          xml_content += "      <type>#{material[:type]}</type>\n"
          xml_content += "      <color r=\"#{material[:color][0]}\" g=\"#{material[:color][1]}\" b=\"#{material[:color][2]}\"/>\n"
          xml_content += "      <roughness>#{material[:roughness] || 0.5}</roughness>\n"
          xml_content += "      <metallic>#{material[:metallic] || 0.0}</metallic>\n"
          xml_content += "      <transmission>#{material[:transmission] || 0.0}</transmission>\n"
          xml_content += "    </material>\n"
        end
        
        xml_content += "  </materials>\n"
        xml_content += "</photon_materials>\n"
        
        File.write(filename, xml_content)
        puts "Materials exported to XML: #{filename}"
        UI.messagebox("Exported #{materials.size} materials to #{File.basename(filename)}")
        true
      rescue => e
        puts "Error exporting to XML: #{e.message}"
        UI.messagebox("Error exporting materials: #{e.message}")
        false
      end
    end
    
    # Import from XML format
    def self.import_from_xml(filename)
      # Simplified XML parsing - in a real implementation, use proper XML parser
      puts "XML import not fully implemented yet"
      UI.messagebox("XML import not yet implemented")
      false
    end
    
    # Export to MTL format (Wavefront OBJ material format)
    def self.export_to_mtl(filename)
      begin
        materials = MaterialLibraryManager.get_materials
        
        mtl_content = "# PhotonRender Material Export\n"
        mtl_content += "# Exported at: #{Time.now}\n"
        mtl_content += "# Material count: #{materials.size}\n\n"
        
        materials.each do |id, material|
          mtl_content += "newmtl #{id}\n"
          mtl_content += "# #{material[:name]}\n"
          
          # Diffuse color
          color = material[:color] || [0.5, 0.5, 0.5]
          mtl_content += "Kd #{color[0]} #{color[1]} #{color[2]}\n"
          
          # Specular color (simplified)
          if material[:metallic] && material[:metallic] > 0.5
            mtl_content += "Ks #{color[0]} #{color[1]} #{color[2]}\n"
          else
            mtl_content += "Ks 0.1 0.1 0.1\n"
          end
          
          # Shininess (from roughness)
          roughness = material[:roughness] || 0.5
          shininess = (1.0 - roughness) * 100
          mtl_content += "Ns #{shininess}\n"
          
          # Transparency
          if material[:transmission]
            mtl_content += "d #{1.0 - material[:transmission]}\n"
          else
            mtl_content += "d 1.0\n"
          end
          
          mtl_content += "\n"
        end
        
        File.write(filename, mtl_content)
        puts "Materials exported to MTL: #{filename}"
        UI.messagebox("Exported #{materials.size} materials to #{File.basename(filename)}")
        true
      rescue => e
        puts "Error exporting to MTL: #{e.message}"
        UI.messagebox("Error exporting materials: #{e.message}")
        false
      end
    end
    
    # Import from MTL format
    def self.import_from_mtl(filename)
      puts "MTL import not fully implemented yet"
      UI.messagebox("MTL import not yet implemented")
      false
    end
    
    # Detect file format from extension
    def self.detect_format(filename)
      File.extname(filename).downcase.gsub('.', '')
    end
    
    # Get supported formats
    def self.get_supported_formats
      @supported_formats
    end
    
    # Escape XML special characters
    def self.escape_xml(text)
      text.to_s.gsub('&', '&amp;').gsub('<', '&lt;').gsub('>', '&gt;').gsub('"', '&quot;').gsub("'", '&apos;')
    end
    
    # Get export statistics
    def self.get_export_statistics
      materials = MaterialLibraryManager.get_materials
      
      {
        total_materials: materials.size,
        supported_formats: @supported_formats,
        last_export: nil # Would track in real implementation
      }
    end
    
  end # module MaterialExportImportManager
  
end # module PhotonRender
