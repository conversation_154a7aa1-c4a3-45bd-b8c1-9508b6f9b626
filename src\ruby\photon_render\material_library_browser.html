<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PhotonRender Material Library</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1a1a1a;
            color: #e0e0e0;
            overflow: hidden;
        }

        .library-browser {
            display: flex;
            height: 100vh;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
        }

        /* Sidebar */
        .sidebar {
            width: 300px;
            background: #252525;
            border-right: 1px solid #404040;
            display: flex;
            flex-direction: column;
        }

        .sidebar-header {
            background: #2a2a2a;
            padding: 20px;
            border-bottom: 1px solid #404040;
        }

        .sidebar-title {
            font-size: 18px;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 15px;
        }

        .search-box {
            width: 100%;
            background: #1a1a1a;
            border: 1px solid #404040;
            color: #e0e0e0;
            padding: 10px 12px;
            border-radius: 6px;
            font-size: 14px;
        }

        .search-box:focus {
            outline: none;
            border-color: #0078d4;
        }

        .sidebar-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .filter-section {
            margin-bottom: 25px;
        }

        .filter-title {
            font-size: 14px;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 12px;
        }

        .category-list {
            list-style: none;
        }

        .category-item {
            padding: 8px 12px;
            margin-bottom: 2px;
            border-radius: 4px;
            cursor: pointer;
            transition: background 0.2s;
            font-size: 13px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .category-item:hover {
            background: #333;
        }

        .category-item.active {
            background: #0078d4;
            color: white;
        }

        .category-count {
            background: #404040;
            color: #ccc;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 11px;
        }

        .category-item.active .category-count {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .tag-list {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
        }

        .tag-item {
            background: #404040;
            color: #e0e0e0;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .tag-item:hover {
            background: #505050;
        }

        .tag-item.active {
            background: #0078d4;
            color: white;
        }

        .stats-section {
            background: #2a2a2a;
            padding: 15px;
            border-radius: 6px;
            margin-top: 20px;
        }

        .stats-title {
            font-size: 12px;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 8px;
        }

        .stats-item {
            display: flex;
            justify-content: space-between;
            font-size: 11px;
            color: #ccc;
            margin-bottom: 4px;
        }

        /* Main Content */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .content-header {
            background: #2a2a2a;
            padding: 20px;
            border-bottom: 1px solid #404040;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .content-title {
            font-size: 16px;
            font-weight: 600;
            color: #ffffff;
        }

        .view-controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .sort-select {
            background: #404040;
            border: none;
            color: #e0e0e0;
            padding: 6px 10px;
            border-radius: 4px;
            font-size: 12px;
        }

        .view-btn {
            background: #404040;
            border: none;
            color: #e0e0e0;
            padding: 8px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.2s;
        }

        .view-btn:hover {
            background: #505050;
        }

        .view-btn.active {
            background: #0078d4;
            color: white;
        }

        .materials-container {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .materials-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
        }

        .materials-list {
            display: none;
        }

        .materials-list.active {
            display: block;
        }

        .materials-grid.list-view {
            display: none;
        }

        .materials-list.list-view {
            display: block;
        }

        .material-card {
            background: #2a2a2a;
            border-radius: 8px;
            overflow: hidden;
            transition: all 0.2s;
            cursor: pointer;
            border: 2px solid transparent;
        }

        .material-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            border-color: #0078d4;
        }

        .material-thumbnail {
            width: 100%;
            height: 150px;
            background: #333;
            background-size: cover;
            background-position: center;
            position: relative;
        }

        .material-rating {
            position: absolute;
            top: 8px;
            right: 8px;
            background: rgba(0, 0, 0, 0.7);
            color: #ffd700;
            padding: 4px 6px;
            border-radius: 4px;
            font-size: 11px;
            display: flex;
            align-items: center;
            gap: 2px;
        }

        .material-info {
            padding: 15px;
        }

        .material-name {
            font-size: 14px;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 5px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .material-category {
            font-size: 11px;
            color: #888;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 8px;
        }

        .material-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
            margin-bottom: 10px;
        }

        .material-tag {
            background: #404040;
            color: #ccc;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 10px;
        }

        .material-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            flex: 1;
            background: #0078d4;
            border: none;
            color: white;
            padding: 6px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            transition: background 0.2s;
        }

        .action-btn:hover {
            background: #106ebe;
        }

        .action-btn.secondary {
            background: #404040;
            color: #e0e0e0;
        }

        .action-btn.secondary:hover {
            background: #505050;
        }

        .action-btn.danger {
            background: #d13438;
        }

        .action-btn.danger:hover {
            background: #b02a2e;
        }

        /* List View */
        .material-list-item {
            background: #2a2a2a;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 15px;
            transition: all 0.2s;
            cursor: pointer;
        }

        .material-list-item:hover {
            background: #333;
            transform: translateX(5px);
        }

        .list-thumbnail {
            width: 60px;
            height: 60px;
            background: #333;
            border-radius: 4px;
            background-size: cover;
            background-position: center;
        }

        .list-info {
            flex: 1;
        }

        .list-name {
            font-size: 14px;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 4px;
        }

        .list-description {
            font-size: 12px;
            color: #888;
            margin-bottom: 6px;
        }

        .list-meta {
            display: flex;
            gap: 15px;
            font-size: 11px;
            color: #666;
        }

        .list-actions {
            display: flex;
            gap: 8px;
        }

        /* Loading State */
        .loading {
            text-align: center;
            padding: 50px;
            color: #888;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #404040;
            border-top: 3px solid #0078d4;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Empty State */
        .empty-state {
            text-align: center;
            padding: 80px 20px;
            color: #888;
        }

        .empty-icon {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.5;
        }

        /* Scrollbar styling */
        .sidebar-content::-webkit-scrollbar,
        .materials-container::-webkit-scrollbar {
            width: 8px;
        }

        .sidebar-content::-webkit-scrollbar-track,
        .materials-container::-webkit-scrollbar-track {
            background: #1a1a1a;
        }

        .sidebar-content::-webkit-scrollbar-thumb,
        .materials-container::-webkit-scrollbar-thumb {
            background: #404040;
            border-radius: 4px;
        }

        .sidebar-content::-webkit-scrollbar-thumb:hover,
        .materials-container::-webkit-scrollbar-thumb:hover {
            background: #505050;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .library-browser {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                height: 40vh;
            }
            
            .materials-grid {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="library-browser">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-title">Material Library</div>
                <input type="text" class="search-box" id="searchBox" placeholder="Search materials...">
            </div>
            
            <div class="sidebar-content">
                <!-- Categories -->
                <div class="filter-section">
                    <div class="filter-title">Categories</div>
                    <ul class="category-list" id="categoryList">
                        <li class="category-item active" data-category="all">
                            <span>All Materials</span>
                            <span class="category-count" id="countAll">0</span>
                        </li>
                        <li class="category-item" data-category="metals">
                            <span>Metals</span>
                            <span class="category-count" id="countMetals">0</span>
                        </li>
                        <li class="category-item" data-category="plastics">
                            <span>Plastics</span>
                            <span class="category-count" id="countPlastics">0</span>
                        </li>
                        <li class="category-item" data-category="glass">
                            <span>Glass</span>
                            <span class="category-count" id="countGlass">0</span>
                        </li>
                        <li class="category-item" data-category="wood">
                            <span>Wood</span>
                            <span class="category-count" id="countWood">0</span>
                        </li>
                        <li class="category-item" data-category="fabric">
                            <span>Fabric</span>
                            <span class="category-count" id="countFabric">0</span>
                        </li>
                        <li class="category-item" data-category="stone">
                            <span>Stone</span>
                            <span class="category-count" id="countStone">0</span>
                        </li>
                        <li class="category-item" data-category="organic">
                            <span>Organic</span>
                            <span class="category-count" id="countOrganic">0</span>
                        </li>
                        <li class="category-item" data-category="custom">
                            <span>Custom</span>
                            <span class="category-count" id="countCustom">0</span>
                        </li>
                    </ul>
                </div>

                <!-- Tags -->
                <div class="filter-section">
                    <div class="filter-title">Tags</div>
                    <div class="tag-list" id="tagList">
                        <span class="tag-item" data-tag="rough">Rough</span>
                        <span class="tag-item" data-tag="smooth">Smooth</span>
                        <span class="tag-item" data-tag="reflective">Reflective</span>
                        <span class="tag-item" data-tag="matte">Matte</span>
                        <span class="tag-item" data-tag="glossy">Glossy</span>
                        <span class="tag-item" data-tag="textured">Textured</span>
                        <span class="tag-item" data-tag="weathered">Weathered</span>
                        <span class="tag-item" data-tag="polished">Polished</span>
                    </div>
                </div>

                <!-- Statistics -->
                <div class="stats-section">
                    <div class="stats-title">Library Statistics</div>
                    <div class="stats-item">
                        <span>Total Materials:</span>
                        <span id="statTotal">0</span>
                    </div>
                    <div class="stats-item">
                        <span>Average Rating:</span>
                        <span id="statRating">0.0</span>
                    </div>
                    <div class="stats-item">
                        <span>Total Usage:</span>
                        <span id="statUsage">0</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <div class="content-header">
                <div class="content-title" id="contentTitle">All Materials</div>
                <div class="view-controls">
                    <select class="sort-select" id="sortSelect">
                        <option value="name">Sort by Name</option>
                        <option value="rating">Sort by Rating</option>
                        <option value="usage">Sort by Usage</option>
                        <option value="date">Sort by Date</option>
                    </select>
                    <button class="view-btn active" id="gridViewBtn" data-view="grid">Grid</button>
                    <button class="view-btn" id="listViewBtn" data-view="list">List</button>
                </div>
            </div>
            
            <div class="materials-container">
                <!-- Loading State -->
                <div class="loading" id="loadingState">
                    <div class="loading-spinner"></div>
                    <div>Loading materials...</div>
                </div>

                <!-- Grid View -->
                <div class="materials-grid" id="materialsGrid">
                    <!-- Material cards will be populated here -->
                </div>

                <!-- List View -->
                <div class="materials-list" id="materialsList">
                    <!-- Material list items will be populated here -->
                </div>

                <!-- Empty State -->
                <div class="empty-state" id="emptyState" style="display: none;">
                    <div class="empty-icon">📦</div>
                    <h3>No materials found</h3>
                    <p>Try adjusting your search criteria or add some materials to your library.</p>
                </div>
            </div>
        </div>
    </div>

    <script src="material_library_browser.js"></script>
</body>
</html>
