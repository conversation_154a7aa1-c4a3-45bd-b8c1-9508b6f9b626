// src/ruby/photon_render/material_library_browser.js
// PhotonRender Material Library Browser - JavaScript Interface
// Sistema di controllo interfaccia per Material Library Browser

class MaterialLibraryBrowser {
    constructor() {
        this.materials = [];
        this.filteredMaterials = [];
        this.currentCategory = 'all';
        this.currentTags = [];
        this.currentSort = 'name';
        this.currentView = 'grid';
        this.searchQuery = '';
        
        this.initializeEventListeners();
        this.loadMaterials();
    }
    
    initializeEventListeners() {
        // Search box
        const searchBox = document.getElementById('searchBox');
        searchBox.addEventListener('input', (e) => {
            this.searchQuery = e.target.value;
            this.filterMaterials();
        });
        
        // Category filters
        document.querySelectorAll('.category-item').forEach(item => {
            item.addEventListener('click', (e) => {
                this.setCategory(e.currentTarget.dataset.category);
            });
        });
        
        // Tag filters
        document.querySelectorAll('.tag-item').forEach(item => {
            item.addEventListener('click', (e) => {
                this.toggleTag(e.currentTarget.dataset.tag);
            });
        });
        
        // Sort control
        const sortSelect = document.getElementById('sortSelect');
        sortSelect.addEventListener('change', (e) => {
            this.currentSort = e.target.value;
            this.sortAndDisplayMaterials();
        });
        
        // View controls
        document.getElementById('gridViewBtn').addEventListener('click', () => {
            this.setView('grid');
        });
        
        document.getElementById('listViewBtn').addEventListener('click', () => {
            this.setView('list');
        });
    }
    
    loadMaterials() {
        this.showLoading(true);
        
        // Request materials from Ruby backend
        const criteria = {
            query: this.searchQuery,
            category: this.currentCategory,
            tags: this.currentTags
        };
        
        if (window.sketchup) {
            window.sketchup.getMaterials(JSON.stringify(criteria));
        } else {
            // Fallback for testing
            setTimeout(() => {
                this.onMaterialsLoaded(this.generateMockMaterials());
            }, 500);
        }
    }
    
    onMaterialsLoaded(materials) {
        this.materials = materials || [];
        this.filterMaterials();
        this.updateStatistics();
        this.showLoading(false);
        
        console.log(`Loaded ${this.materials.length} materials`);
    }
    
    filterMaterials() {
        this.filteredMaterials = this.materials.filter(material => {
            // Category filter
            if (this.currentCategory !== 'all' && material.category !== this.currentCategory) {
                return false;
            }
            
            // Search query filter
            if (this.searchQuery) {
                const query = this.searchQuery.toLowerCase();
                const name = (material.name || '').toLowerCase();
                const description = (material.description || '').toLowerCase();
                
                if (!name.includes(query) && !description.includes(query)) {
                    return false;
                }
            }
            
            // Tags filter
            if (this.currentTags.length > 0) {
                const materialTags = material.tags || [];
                if (!this.currentTags.every(tag => materialTags.includes(tag))) {
                    return false;
                }
            }
            
            return true;
        });
        
        this.sortAndDisplayMaterials();
        this.updateCategoryCounts();
        this.updateContentTitle();
    }
    
    sortAndDisplayMaterials() {
        // Sort materials
        this.filteredMaterials.sort((a, b) => {
            switch (this.currentSort) {
                case 'name':
                    return (a.name || '').localeCompare(b.name || '');
                case 'rating':
                    return (b.rating || 0) - (a.rating || 0);
                case 'usage':
                    return (b.usage_count || 0) - (a.usage_count || 0);
                case 'date':
                    return new Date(b.modified_date || 0) - new Date(a.modified_date || 0);
                default:
                    return 0;
            }
        });
        
        this.displayMaterials();
    }
    
    displayMaterials() {
        const gridContainer = document.getElementById('materialsGrid');
        const listContainer = document.getElementById('materialsList');
        const emptyState = document.getElementById('emptyState');
        
        if (this.filteredMaterials.length === 0) {
            gridContainer.innerHTML = '';
            listContainer.innerHTML = '';
            emptyState.style.display = 'block';
            return;
        }
        
        emptyState.style.display = 'none';
        
        // Generate grid view
        gridContainer.innerHTML = this.filteredMaterials.map(material => 
            this.createMaterialCard(material)
        ).join('');
        
        // Generate list view
        listContainer.innerHTML = this.filteredMaterials.map(material => 
            this.createMaterialListItem(material)
        ).join('');
        
        // Add event listeners to material items
        this.attachMaterialEventListeners();
    }
    
    createMaterialCard(material) {
        const rating = material.rating || 0;
        const stars = '★'.repeat(Math.floor(rating)) + '☆'.repeat(5 - Math.floor(rating));
        const tags = (material.tags || []).slice(0, 3); // Show max 3 tags
        
        return `
            <div class="material-card" data-material-id="${material.id}">
                <div class="material-thumbnail" style="background-image: url('${this.getThumbnailPath(material)}')">
                    <div class="material-rating">
                        <span>${stars}</span>
                        <span>${rating.toFixed(1)}</span>
                    </div>
                </div>
                <div class="material-info">
                    <div class="material-name" title="${material.name || 'Unnamed'}">${material.name || 'Unnamed'}</div>
                    <div class="material-category">${this.getCategoryDisplayName(material.category)}</div>
                    <div class="material-tags">
                        ${tags.map(tag => `<span class="material-tag">${tag}</span>`).join('')}
                    </div>
                    <div class="material-actions">
                        <button class="action-btn" onclick="libraryBrowser.applyMaterial('${material.id}')">Apply</button>
                        <button class="action-btn secondary" onclick="libraryBrowser.previewMaterial('${material.id}')">Preview</button>
                        <button class="action-btn danger" onclick="libraryBrowser.deleteMaterial('${material.id}')">Delete</button>
                    </div>
                </div>
            </div>
        `;
    }
    
    createMaterialListItem(material) {
        const rating = material.rating || 0;
        const stars = '★'.repeat(Math.floor(rating)) + '☆'.repeat(5 - Math.floor(rating));
        
        return `
            <div class="material-list-item" data-material-id="${material.id}">
                <div class="list-thumbnail" style="background-image: url('${this.getThumbnailPath(material)}')"></div>
                <div class="list-info">
                    <div class="list-name">${material.name || 'Unnamed'}</div>
                    <div class="list-description">${material.description || 'No description'}</div>
                    <div class="list-meta">
                        <span>Category: ${this.getCategoryDisplayName(material.category)}</span>
                        <span>Rating: ${stars} ${rating.toFixed(1)}</span>
                        <span>Used: ${material.usage_count || 0} times</span>
                    </div>
                </div>
                <div class="list-actions">
                    <button class="action-btn" onclick="libraryBrowser.applyMaterial('${material.id}')">Apply</button>
                    <button class="action-btn secondary" onclick="libraryBrowser.previewMaterial('${material.id}')">Preview</button>
                    <button class="action-btn danger" onclick="libraryBrowser.deleteMaterial('${material.id}')">Delete</button>
                </div>
            </div>
        `;
    }
    
    setCategory(category) {
        // Update UI
        document.querySelectorAll('.category-item').forEach(item => {
            item.classList.toggle('active', item.dataset.category === category);
        });
        
        this.currentCategory = category;
        this.filterMaterials();
    }
    
    toggleTag(tag) {
        const tagElement = document.querySelector(`[data-tag="${tag}"]`);
        
        if (this.currentTags.includes(tag)) {
            // Remove tag
            this.currentTags = this.currentTags.filter(t => t !== tag);
            tagElement.classList.remove('active');
        } else {
            // Add tag
            this.currentTags.push(tag);
            tagElement.classList.add('active');
        }
        
        this.filterMaterials();
    }
    
    setView(view) {
        this.currentView = view;
        
        // Update view buttons
        document.getElementById('gridViewBtn').classList.toggle('active', view === 'grid');
        document.getElementById('listViewBtn').classList.toggle('active', view === 'list');
        
        // Update containers
        const gridContainer = document.getElementById('materialsGrid');
        const listContainer = document.getElementById('materialsList');
        
        if (view === 'grid') {
            gridContainer.style.display = 'grid';
            listContainer.style.display = 'none';
        } else {
            gridContainer.style.display = 'none';
            listContainer.style.display = 'block';
        }
    }
    
    applyMaterial(materialId) {
        console.log(`Applying material: ${materialId}`);
        
        if (window.sketchup) {
            window.sketchup.applyMaterial(materialId);
        } else {
            console.log(`Mock: Apply material ${materialId}`);
        }
    }
    
    previewMaterial(materialId) {
        console.log(`Previewing material: ${materialId}`);
        
        // TODO: Open material editor with this material
        if (window.sketchup) {
            window.sketchup.previewMaterial(materialId);
        } else {
            console.log(`Mock: Preview material ${materialId}`);
        }
    }
    
    deleteMaterial(materialId) {
        if (confirm('Are you sure you want to delete this material?')) {
            console.log(`Deleting material: ${materialId}`);
            
            if (window.sketchup) {
                window.sketchup.deleteMaterial(materialId);
            } else {
                // Mock deletion
                this.materials = this.materials.filter(m => m.id !== materialId);
                this.filterMaterials();
            }
        }
    }
    
    rateMaterial(materialId, rating) {
        console.log(`Rating material ${materialId}: ${rating}`);
        
        if (window.sketchup) {
            window.sketchup.rateMaterial(JSON.stringify({
                materialId: materialId,
                rating: rating
            }));
        }
        
        // Update local data
        const material = this.materials.find(m => m.id === materialId);
        if (material) {
            material.rating = rating;
            this.displayMaterials();
        }
    }
    
    refreshMaterials() {
        this.loadMaterials();
    }
    
    updateCategoryCounts() {
        const counts = {};
        
        // Initialize counts
        ['all', 'metals', 'plastics', 'glass', 'wood', 'fabric', 'stone', 'organic', 'custom'].forEach(cat => {
            counts[cat] = 0;
        });
        
        // Count materials
        this.materials.forEach(material => {
            counts.all++;
            if (material.category) {
                counts[material.category] = (counts[material.category] || 0) + 1;
            }
        });
        
        // Update UI
        Object.keys(counts).forEach(category => {
            const element = document.getElementById(`count${category.charAt(0).toUpperCase() + category.slice(1)}`);
            if (element) {
                element.textContent = counts[category];
            }
        });
    }
    
    updateContentTitle() {
        const titleElement = document.getElementById('contentTitle');
        let title = 'All Materials';
        
        if (this.currentCategory !== 'all') {
            title = this.getCategoryDisplayName(this.currentCategory);
        }
        
        if (this.searchQuery) {
            title += ` (Search: "${this.searchQuery}")`;
        }
        
        if (this.currentTags.length > 0) {
            title += ` (Tags: ${this.currentTags.join(', ')})`;
        }
        
        title += ` (${this.filteredMaterials.length})`;
        titleElement.textContent = title;
    }
    
    updateStatistics() {
        const totalMaterials = this.materials.length;
        const totalRating = this.materials.reduce((sum, m) => sum + (m.rating || 0), 0);
        const averageRating = totalMaterials > 0 ? (totalRating / totalMaterials) : 0;
        const totalUsage = this.materials.reduce((sum, m) => sum + (m.usage_count || 0), 0);
        
        document.getElementById('statTotal').textContent = totalMaterials;
        document.getElementById('statRating').textContent = averageRating.toFixed(1);
        document.getElementById('statUsage').textContent = totalUsage;
    }
    
    showLoading(show) {
        const loadingState = document.getElementById('loadingState');
        const materialsGrid = document.getElementById('materialsGrid');
        const materialsList = document.getElementById('materialsList');
        
        if (show) {
            loadingState.style.display = 'block';
            materialsGrid.style.display = 'none';
            materialsList.style.display = 'none';
        } else {
            loadingState.style.display = 'none';
            this.setView(this.currentView); // Restore view
        }
    }
    
    attachMaterialEventListeners() {
        // Add double-click to apply material
        document.querySelectorAll('.material-card, .material-list-item').forEach(item => {
            item.addEventListener('dblclick', () => {
                const materialId = item.dataset.materialId;
                this.applyMaterial(materialId);
            });
        });
        
        // Add rating click handlers
        document.querySelectorAll('.material-rating').forEach(rating => {
            rating.addEventListener('click', (e) => {
                e.stopPropagation();
                const materialId = e.target.closest('[data-material-id]').dataset.materialId;
                // TODO: Show rating dialog
                console.log(`Rate material: ${materialId}`);
            });
        });
    }
    
    getThumbnailPath(material) {
        // Return placeholder or actual thumbnail path
        return material.thumbnailPath || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjMzMzIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzY2NiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPk5vIFRodW1ibmFpbDwvdGV4dD48L3N2Zz4=';
    }
    
    getCategoryDisplayName(category) {
        const names = {
            'metals': 'Metals',
            'plastics': 'Plastics',
            'glass': 'Glass',
            'wood': 'Wood',
            'fabric': 'Fabric',
            'stone': 'Stone',
            'organic': 'Organic',
            'automotive': 'Automotive',
            'architectural': 'Architectural',
            'custom': 'Custom'
        };
        return names[category] || 'Unknown';
    }
    
    generateMockMaterials() {
        // Mock data for testing
        return [
            {
                id: 'mat_001',
                name: 'Chrome Metal',
                description: 'Polished chrome metal material',
                category: 'metals',
                tags: ['reflective', 'polished', 'smooth'],
                rating: 4.5,
                usage_count: 25,
                created_date: '2025-01-15',
                modified_date: '2025-01-20'
            },
            {
                id: 'mat_002',
                name: 'Red Plastic',
                description: 'Glossy red plastic material',
                category: 'plastics',
                tags: ['glossy', 'smooth'],
                rating: 3.8,
                usage_count: 12,
                created_date: '2025-01-10',
                modified_date: '2025-01-18'
            },
            {
                id: 'mat_003',
                name: 'Clear Glass',
                description: 'Transparent clear glass',
                category: 'glass',
                tags: ['translucent', 'smooth'],
                rating: 4.2,
                usage_count: 18,
                created_date: '2025-01-12',
                modified_date: '2025-01-19'
            }
        ];
    }
}

// Initialize material library browser when page loads
document.addEventListener('DOMContentLoaded', () => {
    window.libraryBrowser = new MaterialLibraryBrowser();
});

// Global functions for Ruby integration
window.onMaterialsLoaded = (materials) => {
    if (window.libraryBrowser) {
        window.libraryBrowser.onMaterialsLoaded(materials);
    }
};
