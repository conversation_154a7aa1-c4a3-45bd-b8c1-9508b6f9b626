# src/ruby/photon_render/material_library_manager.rb
# Material library management for PhotonRender

module PhotonRender
  
  module MaterialLibraryManager
    
    # Initialize material library
    def self.initialize
      puts "Initializing Material Library Manager"
      @materials = {}
      load_default_materials
    end
    
    # Load default materials
    def self.load_default_materials
      puts "Loading default materials..."
      
      # Add some default materials for testing
      @materials = {
        "white_plastic" => {
          name: "White Plastic",
          type: "plastic",
          color: [0.9, 0.9, 0.9],
          roughness: 0.1,
          metallic: 0.0
        },
        "red_metal" => {
          name: "Red Metal",
          type: "metal",
          color: [0.8, 0.2, 0.2],
          roughness: 0.05,
          metallic: 1.0
        },
        "wood_oak" => {
          name: "Oak Wood",
          type: "wood",
          color: [0.6, 0.4, 0.2],
          roughness: 0.8,
          metallic: 0.0
        },
        "glass_clear" => {
          name: "Clear Glass",
          type: "glass",
          color: [0.9, 0.9, 0.9],
          roughness: 0.0,
          metallic: 0.0,
          transmission: 0.9
        },
        "fabric_cotton" => {
          name: "Cotton Fabric",
          type: "fabric",
          color: [0.7, 0.7, 0.8],
          roughness: 0.9,
          metallic: 0.0
        }
      }
      
      puts "Loaded #{@materials.size} default materials"
    end
    
    # Get all materials
    def self.get_materials
      @materials || {}
    end
    
    # Get material by ID
    def self.get_material(id)
      @materials[id]
    end
    
    # Add material
    def self.add_material(id, material_data)
      @materials[id] = material_data
      puts "Added material: #{id}"
    end
    
    # Remove material
    def self.remove_material(id)
      @materials.delete(id)
      puts "Removed material: #{id}"
    end
    
    # Update material
    def self.update_material(id, material_data)
      if @materials[id]
        @materials[id] = material_data
        puts "Updated material: #{id}"
      else
        puts "Material not found: #{id}"
      end
    end
    
    # Search materials
    def self.search_materials(query)
      results = {}
      @materials.each do |id, material|
        if material[:name].downcase.include?(query.downcase) ||
           material[:type].downcase.include?(query.downcase)
          results[id] = material
        end
      end
      results
    end
    
    # Get materials by type
    def self.get_materials_by_type(type)
      results = {}
      @materials.each do |id, material|
        if material[:type] == type
          results[id] = material
        end
      end
      results
    end
    
    # Export materials to file
    def self.export_to_file(filename)
      begin
        File.write(filename, JSON.pretty_generate(@materials))
        puts "Materials exported to: #{filename}"
        true
      rescue => e
        puts "Error exporting materials: #{e.message}"
        false
      end
    end
    
    # Import materials from file
    def self.import_from_file(filename)
      begin
        data = JSON.parse(File.read(filename))
        data.each do |id, material|
          # Convert string keys to symbols
          material_data = {}
          material.each { |k, v| material_data[k.to_sym] = v }
          @materials[id] = material_data
        end
        puts "Materials imported from: #{filename}"
        true
      rescue => e
        puts "Error importing materials: #{e.message}"
        false
      end
    end
    
    # Validate material data
    def self.validate_material(material_data)
      required_fields = [:name, :type, :color]
      
      required_fields.each do |field|
        unless material_data[field]
          return false, "Missing required field: #{field}"
        end
      end
      
      # Validate color array
      if !material_data[:color].is_a?(Array) || material_data[:color].size != 3
        return false, "Color must be an array of 3 values"
      end
      
      # Validate color values
      material_data[:color].each do |value|
        if !value.is_a?(Numeric) || value < 0 || value > 1
          return false, "Color values must be between 0 and 1"
        end
      end
      
      [true, "Material is valid"]
    end
    
    # Get material statistics
    def self.get_statistics
      stats = {
        total_materials: @materials.size,
        by_type: {}
      }
      
      @materials.each do |id, material|
        type = material[:type]
        stats[:by_type][type] = (stats[:by_type][type] || 0) + 1
      end
      
      stats
    end
    
  end # module MaterialLibraryManager
  
end # module PhotonRender
