<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PhotonRender Material Validation</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1a1a1a;
            color: #e0e0e0;
            overflow: hidden;
        }

        .validation-panel {
            display: flex;
            height: 100vh;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
        }

        /* Validation Results Panel */
        .validation-results {
            width: 400px;
            background: #252525;
            border-right: 1px solid #404040;
            display: flex;
            flex-direction: column;
        }

        .results-header {
            background: #2a2a2a;
            padding: 15px;
            border-bottom: 1px solid #404040;
        }

        .results-title {
            font-size: 16px;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 10px;
        }

        .validation-status {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 10px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            flex-shrink: 0;
        }

        .status-indicator.valid {
            background: #28a745;
        }

        .status-indicator.warning {
            background: #ffc107;
        }

        .status-indicator.error {
            background: #dc3545;
        }

        .status-text {
            font-size: 14px;
            font-weight: 500;
        }

        .validation-summary {
            font-size: 12px;
            color: #888;
            display: flex;
            gap: 15px;
        }

        .summary-item {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .summary-count {
            background: #404040;
            color: #e0e0e0;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 10px;
            min-width: 16px;
            text-align: center;
        }

        .summary-count.error {
            background: #dc3545;
            color: white;
        }

        .summary-count.warning {
            background: #ffc107;
            color: #000;
        }

        .summary-count.info {
            background: #17a2b8;
            color: white;
        }

        .results-content {
            flex: 1;
            overflow-y: auto;
            padding: 15px;
        }

        .issue-category {
            margin-bottom: 20px;
        }

        .category-header {
            font-size: 13px;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 8px;
            padding-bottom: 4px;
            border-bottom: 1px solid #404040;
        }

        .issue-item {
            background: #2a2a2a;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 8px;
            border-left: 3px solid #404040;
            transition: all 0.2s;
        }

        .issue-item:hover {
            background: #333;
        }

        .issue-item.error {
            border-left-color: #dc3545;
        }

        .issue-item.warning {
            border-left-color: #ffc107;
        }

        .issue-item.info {
            border-left-color: #17a2b8;
        }

        .issue-header {
            display: flex;
            justify-content: between;
            align-items: flex-start;
            margin-bottom: 6px;
        }

        .issue-severity {
            font-size: 10px;
            font-weight: 600;
            text-transform: uppercase;
            padding: 2px 6px;
            border-radius: 3px;
            margin-right: 8px;
            flex-shrink: 0;
        }

        .issue-severity.error {
            background: #dc3545;
            color: white;
        }

        .issue-severity.warning {
            background: #ffc107;
            color: #000;
        }

        .issue-severity.info {
            background: #17a2b8;
            color: white;
        }

        .issue-parameter {
            font-size: 11px;
            color: #0078d4;
            font-family: monospace;
            background: rgba(0, 120, 212, 0.1);
            padding: 1px 4px;
            border-radius: 2px;
        }

        .issue-message {
            font-size: 12px;
            color: #e0e0e0;
            margin-bottom: 4px;
            line-height: 1.4;
        }

        .issue-suggestion {
            font-size: 11px;
            color: #888;
            font-style: italic;
            margin-bottom: 6px;
        }

        .issue-values {
            display: flex;
            gap: 10px;
            font-size: 10px;
            color: #666;
        }

        .issue-actions {
            display: flex;
            gap: 6px;
            margin-top: 8px;
        }

        .issue-btn {
            background: #404040;
            border: none;
            color: #e0e0e0;
            padding: 4px 8px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 10px;
            transition: background 0.2s;
        }

        .issue-btn:hover {
            background: #505050;
        }

        .issue-btn.primary {
            background: #0078d4;
            color: white;
        }

        .issue-btn.primary:hover {
            background: #106ebe;
        }

        /* Scores Panel */
        .scores-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .scores-header {
            background: #2a2a2a;
            padding: 15px;
            border-bottom: 1px solid #404040;
        }

        .scores-title {
            font-size: 16px;
            font-weight: 600;
            color: #ffffff;
        }

        .scores-content {
            flex: 1;
            padding: 20px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: 1fr 1fr;
            gap: 20px;
        }

        .score-card {
            background: #2a2a2a;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            border: 2px solid #404040;
            transition: all 0.2s;
        }

        .score-card:hover {
            border-color: #0078d4;
        }

        .score-icon {
            font-size: 32px;
            margin-bottom: 10px;
        }

        .score-title {
            font-size: 14px;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 8px;
        }

        .score-value {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .score-value.excellent {
            color: #28a745;
        }

        .score-value.good {
            color: #ffc107;
        }

        .score-value.poor {
            color: #dc3545;
        }

        .score-description {
            font-size: 11px;
            color: #888;
            line-height: 1.4;
        }

        .score-bar {
            width: 100%;
            height: 6px;
            background: #404040;
            border-radius: 3px;
            margin: 10px 0;
            overflow: hidden;
        }

        .score-fill {
            height: 100%;
            border-radius: 3px;
            transition: width 0.3s ease;
        }

        .score-fill.excellent {
            background: linear-gradient(90deg, #28a745, #20c997);
        }

        .score-fill.good {
            background: linear-gradient(90deg, #ffc107, #fd7e14);
        }

        .score-fill.poor {
            background: linear-gradient(90deg, #dc3545, #e74c3c);
        }

        /* Action Bar */
        .action-bar {
            background: #333;
            padding: 15px;
            border-top: 1px solid #404040;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .validation-info {
            font-size: 12px;
            color: #888;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
        }

        .action-btn {
            background: #0078d4;
            border: none;
            color: white;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.2s;
        }

        .action-btn:hover {
            background: #106ebe;
        }

        .action-btn.secondary {
            background: #404040;
            color: #e0e0e0;
        }

        .action-btn.secondary:hover {
            background: #505050;
        }

        .action-btn.success {
            background: #28a745;
        }

        .action-btn.success:hover {
            background: #218838;
        }

        /* Empty State */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #888;
        }

        .empty-icon {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.5;
        }

        /* Scrollbar styling */
        .results-content::-webkit-scrollbar {
            width: 8px;
        }

        .results-content::-webkit-scrollbar-track {
            background: #1a1a1a;
        }

        .results-content::-webkit-scrollbar-thumb {
            background: #404040;
            border-radius: 4px;
        }

        .results-content::-webkit-scrollbar-thumb:hover {
            background: #505050;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .validation-panel {
                flex-direction: column;
            }
            
            .validation-results {
                width: 100%;
                height: 50vh;
            }
            
            .scores-content {
                grid-template-columns: 1fr;
                grid-template-rows: repeat(4, auto);
            }
        }
    </style>
</head>
<body>
    <div class="validation-panel">
        <!-- Validation Results Panel -->
        <div class="validation-results">
            <div class="results-header">
                <div class="results-title">Validation Results</div>
                <div class="validation-status">
                    <div class="status-indicator valid" id="statusIndicator"></div>
                    <div class="status-text" id="statusText">Material Valid</div>
                </div>
                <div class="validation-summary">
                    <div class="summary-item">
                        <span>Errors:</span>
                        <span class="summary-count error" id="errorCount">0</span>
                    </div>
                    <div class="summary-item">
                        <span>Warnings:</span>
                        <span class="summary-count warning" id="warningCount">0</span>
                    </div>
                    <div class="summary-item">
                        <span>Info:</span>
                        <span class="summary-count info" id="infoCount">0</span>
                    </div>
                </div>
            </div>
            
            <div class="results-content" id="resultsContent">
                <div class="empty-state">
                    <div class="empty-icon">✓</div>
                    <h3>No Issues Found</h3>
                    <p>Your material parameters are valid and follow best practices.</p>
                </div>
            </div>
        </div>

        <!-- Scores Panel -->
        <div class="scores-panel">
            <div class="scores-header">
                <div class="scores-title">Material Quality Scores</div>
            </div>
            
            <div class="scores-content">
                <!-- Energy Conservation Score -->
                <div class="score-card">
                    <div class="score-icon">⚡</div>
                    <div class="score-title">Energy Conservation</div>
                    <div class="score-value excellent" id="energyScore">100%</div>
                    <div class="score-bar">
                        <div class="score-fill excellent" id="energyBar" style="width: 100%"></div>
                    </div>
                    <div class="score-description">
                        Measures how well the material conserves energy according to physical laws.
                    </div>
                </div>

                <!-- Physical Plausibility Score -->
                <div class="score-card">
                    <div class="score-icon">🔬</div>
                    <div class="score-title">Physical Plausibility</div>
                    <div class="score-value excellent" id="plausibilityScore">100%</div>
                    <div class="score-bar">
                        <div class="score-fill excellent" id="plausibilityBar" style="width: 100%"></div>
                    </div>
                    <div class="score-description">
                        Evaluates how realistic the material appears based on physical properties.
                    </div>
                </div>

                <!-- Performance Score -->
                <div class="score-card">
                    <div class="score-icon">🚀</div>
                    <div class="score-title">Performance</div>
                    <div class="score-value excellent" id="performanceScore">100%</div>
                    <div class="score-bar">
                        <div class="score-fill excellent" id="performanceBar" style="width: 100%"></div>
                    </div>
                    <div class="score-description">
                        Indicates the rendering performance impact of the material complexity.
                    </div>
                </div>

                <!-- Overall Score -->
                <div class="score-card">
                    <div class="score-icon">🏆</div>
                    <div class="score-title">Overall Quality</div>
                    <div class="score-value excellent" id="overallScore">100%</div>
                    <div class="score-bar">
                        <div class="score-fill excellent" id="overallBar" style="width: 100%"></div>
                    </div>
                    <div class="score-description">
                        Combined score representing the overall material quality and usability.
                    </div>
                </div>
            </div>
            
            <div class="action-bar">
                <div class="validation-info" id="validationInfo">
                    Last validated: Never
                </div>
                <div class="action-buttons">
                    <button class="action-btn secondary" onclick="validationFeedback.exportReport()">Export Report</button>
                    <button class="action-btn success" onclick="validationFeedback.autoFixIssues()">Auto-Fix Issues</button>
                    <button class="action-btn" onclick="validationFeedback.validateMaterial()">Re-validate</button>
                </div>
            </div>
        </div>
    </div>

    <script src="material_validation_feedback.js"></script>
</body>
</html>
