// src/ruby/photon_render/material_validation_feedback.js
// PhotonRender Material Validation Feedback - JavaScript Interface
// Sistema di controllo interfaccia per Material Validation Feedback

class MaterialValidationFeedback {
    constructor() {
        this.validationResult = null;
        this.autoFixEnabled = true;
        this.realTimeValidation = true;
        
        this.initializeEventListeners();
        this.loadValidationResult();
    }
    
    initializeEventListeners() {
        // Auto-fix toggle
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'f') {
                e.preventDefault();
                this.autoFixIssues();
            }
        });
    }
    
    loadValidationResult() {
        // Request validation result from Ruby backend
        if (window.sketchup) {
            window.sketchup.getValidationResult();
        } else {
            // Fallback for testing
            setTimeout(() => {
                this.onValidationResult(this.generateMockValidationResult());
            }, 500);
        }
    }
    
    onValidationResult(result) {
        this.validationResult = result;
        this.updateValidationDisplay();
        this.updateScoresDisplay();
        this.updateValidationInfo();
        
        console.log('Validation result received:', result);
    }
    
    updateValidationDisplay() {
        if (!this.validationResult) return;
        
        const result = this.validationResult;
        
        // Update status indicator
        const statusIndicator = document.getElementById('statusIndicator');
        const statusText = document.getElementById('statusText');
        
        if (result.hasErrors) {
            statusIndicator.className = 'status-indicator error';
            statusText.textContent = 'Material Invalid';
        } else if (result.hasWarnings) {
            statusIndicator.className = 'status-indicator warning';
            statusText.textContent = 'Material Valid (Warnings)';
        } else {
            statusIndicator.className = 'status-indicator valid';
            statusText.textContent = 'Material Valid';
        }
        
        // Update issue counts
        const errorCount = result.issues.filter(i => i.severity === 'ERROR' || i.severity === 'CRITICAL').length;
        const warningCount = result.issues.filter(i => i.severity === 'WARNING').length;
        const infoCount = result.issues.filter(i => i.severity === 'INFO').length;
        
        document.getElementById('errorCount').textContent = errorCount;
        document.getElementById('warningCount').textContent = warningCount;
        document.getElementById('infoCount').textContent = infoCount;
        
        // Update results content
        this.displayIssues(result.issues);
    }
    
    displayIssues(issues) {
        const resultsContent = document.getElementById('resultsContent');
        
        if (issues.length === 0) {
            resultsContent.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">✓</div>
                    <h3>No Issues Found</h3>
                    <p>Your material parameters are valid and follow best practices.</p>
                </div>
            `;
            return;
        }
        
        // Group issues by category
        const groupedIssues = this.groupIssuesByCategory(issues);
        
        resultsContent.innerHTML = Object.keys(groupedIssues).map(category => 
            this.createCategorySection(category, groupedIssues[category])
        ).join('');
        
        // Add event listeners to issue buttons
        this.attachIssueEventListeners();
    }
    
    groupIssuesByCategory(issues) {
        const grouped = {};
        
        issues.forEach(issue => {
            const category = issue.category || 'Other';
            if (!grouped[category]) {
                grouped[category] = [];
            }
            grouped[category].push(issue);
        });
        
        return grouped;
    }
    
    createCategorySection(category, issues) {
        return `
            <div class="issue-category">
                <div class="category-header">${this.formatCategoryName(category)} (${issues.length})</div>
                ${issues.map(issue => this.createIssueItem(issue)).join('')}
            </div>
        `;
    }
    
    createIssueItem(issue) {
        const severityClass = issue.severity.toLowerCase();
        const autoFixButton = issue.autoFixable ? 
            `<button class="issue-btn primary" onclick="validationFeedback.fixIssue('${issue.parameter}', ${issue.suggestedValue})">Auto-Fix</button>` : '';
        
        return `
            <div class="issue-item ${severityClass}">
                <div class="issue-header">
                    <span class="issue-severity ${severityClass}">${issue.severity}</span>
                    ${issue.parameter ? `<span class="issue-parameter">${issue.parameter}</span>` : ''}
                </div>
                <div class="issue-message">${issue.message}</div>
                ${issue.suggestion ? `<div class="issue-suggestion">💡 ${issue.suggestion}</div>` : ''}
                <div class="issue-values">
                    ${issue.currentValue !== undefined ? `<span>Current: ${issue.currentValue.toFixed(3)}</span>` : ''}
                    ${issue.suggestedValue !== undefined ? `<span>Suggested: ${issue.suggestedValue.toFixed(3)}</span>` : ''}
                </div>
                <div class="issue-actions">
                    ${autoFixButton}
                    <button class="issue-btn" onclick="validationFeedback.highlightParameter('${issue.parameter}')">Highlight</button>
                    <button class="issue-btn" onclick="validationFeedback.learnMore('${issue.category}')">Learn More</button>
                </div>
            </div>
        `;
    }
    
    updateScoresDisplay() {
        if (!this.validationResult) return;
        
        const result = this.validationResult;
        
        // Energy Conservation Score
        this.updateScoreCard('energy', result.energyConservationScore);
        
        // Physical Plausibility Score
        this.updateScoreCard('plausibility', result.physicalPlausibilityScore);
        
        // Performance Score
        this.updateScoreCard('performance', result.performanceScore);
        
        // Overall Score (average of all scores)
        const overallScore = (result.energyConservationScore + result.physicalPlausibilityScore + result.performanceScore) / 3;
        this.updateScoreCard('overall', overallScore);
    }
    
    updateScoreCard(type, score) {
        const percentage = Math.round(score * 100);
        const scoreElement = document.getElementById(`${type}Score`);
        const barElement = document.getElementById(`${type}Bar`);
        
        // Update score text
        scoreElement.textContent = `${percentage}%`;
        
        // Update score class and bar
        let scoreClass = 'excellent';
        let barClass = 'excellent';
        
        if (percentage < 60) {
            scoreClass = 'poor';
            barClass = 'poor';
        } else if (percentage < 80) {
            scoreClass = 'good';
            barClass = 'good';
        }
        
        scoreElement.className = `score-value ${scoreClass}`;
        barElement.className = `score-fill ${barClass}`;
        barElement.style.width = `${percentage}%`;
    }
    
    updateValidationInfo() {
        const validationInfo = document.getElementById('validationInfo');
        const now = new Date();
        validationInfo.textContent = `Last validated: ${now.toLocaleTimeString()}`;
    }
    
    validateMaterial() {
        console.log('Validating material...');
        
        if (window.sketchup) {
            window.sketchup.validateCurrentMaterial();
        } else {
            // Mock validation for testing
            setTimeout(() => {
                this.onValidationResult(this.generateMockValidationResult());
            }, 1000);
        }
        
        // Show loading state
        const resultsContent = document.getElementById('resultsContent');
        resultsContent.innerHTML = `
            <div class="empty-state">
                <div class="empty-icon">⏳</div>
                <h3>Validating Material...</h3>
                <p>Checking parameters and calculating scores.</p>
            </div>
        `;
    }
    
    autoFixIssues() {
        if (!this.validationResult || !this.validationResult.issues) {
            console.log('No issues to fix');
            return;
        }
        
        const autoFixableIssues = this.validationResult.issues.filter(issue => issue.autoFixable);
        
        if (autoFixableIssues.length === 0) {
            alert('No auto-fixable issues found.');
            return;
        }
        
        if (confirm(`Auto-fix ${autoFixableIssues.length} issues?`)) {
            console.log('Auto-fixing issues:', autoFixableIssues);
            
            if (window.sketchup) {
                window.sketchup.autoFixMaterialIssues(JSON.stringify(autoFixableIssues));
            } else {
                // Mock auto-fix for testing
                console.log('Mock: Auto-fixed issues');
                setTimeout(() => {
                    this.validateMaterial();
                }, 500);
            }
        }
    }
    
    fixIssue(parameter, suggestedValue) {
        console.log(`Fixing issue: ${parameter} = ${suggestedValue}`);
        
        if (window.sketchup) {
            window.sketchup.fixMaterialParameter(JSON.stringify({
                parameter: parameter,
                value: suggestedValue
            }));
        } else {
            console.log(`Mock: Fixed ${parameter} to ${suggestedValue}`);
        }
    }
    
    highlightParameter(parameter) {
        console.log(`Highlighting parameter: ${parameter}`);
        
        if (window.sketchup) {
            window.sketchup.highlightParameter(parameter);
        }
    }
    
    learnMore(category) {
        console.log(`Learn more about: ${category}`);
        
        const helpUrls = {
            'ENERGY_CONSERVATION': 'https://docs.photonrender.com/energy-conservation',
            'PARAMETER_RANGE': 'https://docs.photonrender.com/parameter-ranges',
            'PARAMETER_COMBINATION': 'https://docs.photonrender.com/parameter-combinations',
            'PHYSICAL_PLAUSIBILITY': 'https://docs.photonrender.com/physical-plausibility',
            'PERFORMANCE': 'https://docs.photonrender.com/performance-optimization',
            'WORKFLOW': 'https://docs.photonrender.com/workflow-tips'
        };
        
        const url = helpUrls[category] || 'https://docs.photonrender.com/material-validation';
        
        if (window.sketchup) {
            window.sketchup.openHelpUrl(url);
        } else {
            window.open(url, '_blank');
        }
    }
    
    exportReport() {
        if (!this.validationResult) {
            alert('No validation result to export.');
            return;
        }
        
        console.log('Exporting validation report...');
        
        if (window.sketchup) {
            window.sketchup.exportValidationReport(JSON.stringify(this.validationResult));
        } else {
            // Mock export for testing
            const report = this.generateTextReport();
            this.downloadTextFile('material_validation_report.txt', report);
        }
    }
    
    generateTextReport() {
        if (!this.validationResult) return '';
        
        const result = this.validationResult;
        let report = 'PhotonRender Material Validation Report\n';
        report += '=====================================\n\n';
        
        report += `Validation Date: ${new Date().toLocaleString()}\n`;
        report += `Overall Status: ${result.isValid ? 'VALID' : 'INVALID'}\n`;
        report += `Total Issues: ${result.issues.length}\n\n`;
        
        report += 'Scores:\n';
        report += `Energy Conservation: ${Math.round(result.energyConservationScore * 100)}%\n`;
        report += `Physical Plausibility: ${Math.round(result.physicalPlausibilityScore * 100)}%\n`;
        report += `Performance: ${Math.round(result.performanceScore * 100)}%\n\n`;
        
        if (result.issues.length > 0) {
            report += 'Issues Found:\n';
            report += '=============\n\n';
            
            result.issues.forEach((issue, index) => {
                report += `${index + 1}. [${issue.severity}] ${issue.message}\n`;
                if (issue.parameter) report += `   Parameter: ${issue.parameter}\n`;
                if (issue.suggestion) report += `   Suggestion: ${issue.suggestion}\n`;
                if (issue.currentValue !== undefined) report += `   Current Value: ${issue.currentValue}\n`;
                if (issue.suggestedValue !== undefined) report += `   Suggested Value: ${issue.suggestedValue}\n`;
                report += '\n';
            });
        }
        
        return report;
    }
    
    downloadTextFile(filename, content) {
        const element = document.createElement('a');
        element.setAttribute('href', 'data:text/plain;charset=utf-8,' + encodeURIComponent(content));
        element.setAttribute('download', filename);
        element.style.display = 'none';
        document.body.appendChild(element);
        element.click();
        document.body.removeChild(element);
    }
    
    attachIssueEventListeners() {
        // Event listeners are attached via onclick in HTML
        // This method can be used for additional event handling if needed
    }
    
    formatCategoryName(category) {
        return category.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    }
    
    generateMockValidationResult() {
        return {
            isValid: false,
            hasErrors: true,
            hasWarnings: true,
            energyConservationScore: 0.75,
            physicalPlausibilityScore: 0.85,
            performanceScore: 0.90,
            issues: [
                {
                    severity: 'ERROR',
                    category: 'ENERGY_CONSERVATION',
                    parameter: 'metallic',
                    message: 'Material may violate energy conservation',
                    suggestion: 'Reduce metallic or specular values',
                    currentValue: 1.2,
                    suggestedValue: 1.0,
                    autoFixable: true
                },
                {
                    severity: 'WARNING',
                    category: 'PARAMETER_COMBINATION',
                    parameter: 'subsurface+metallic',
                    message: 'Subsurface scattering with metallic materials is physically implausible',
                    suggestion: 'Set either subsurface or metallic to 0',
                    currentValue: 0.5,
                    suggestedValue: 0.0,
                    autoFixable: true
                },
                {
                    severity: 'INFO',
                    category: 'WORKFLOW',
                    parameter: 'preset_suggestion',
                    message: 'This looks like a chrome/mirror material',
                    suggestion: 'Consider using the Chrome preset as starting point',
                    autoFixable: false
                },
                {
                    severity: 'WARNING',
                    category: 'PHYSICAL_PLAUSIBILITY',
                    parameter: 'roughness',
                    message: 'Very low roughness may cause rendering artifacts',
                    suggestion: 'Consider using roughness >= 0.01 for stability',
                    currentValue: 0.001,
                    suggestedValue: 0.01,
                    autoFixable: true
                }
            ]
        };
    }
}

// Initialize validation feedback when page loads
document.addEventListener('DOMContentLoaded', () => {
    window.validationFeedback = new MaterialValidationFeedback();
});

// Global functions for Ruby integration
window.onValidationResult = (result) => {
    if (window.validationFeedback) {
        window.validationFeedback.onValidationResult(result);
    }
};

window.onParameterFixed = (parameter, value) => {
    if (window.validationFeedback) {
        console.log(`Parameter fixed: ${parameter} = ${value}`);
        // Re-validate after fix
        window.validationFeedback.validateMaterial();
    }
};
