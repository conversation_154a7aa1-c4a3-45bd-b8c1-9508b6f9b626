# src/ruby/photon_render/material_validation_manager.rb
# Material validation management for PhotonRender

module PhotonRender
  
  module MaterialValidationManager
    
    # Initialize material validation manager
    def self.initialize
      puts "Initializing Material Validation Manager"
      @validation_rules = setup_validation_rules
    end
    
    # Setup validation rules
    def self.setup_validation_rules
      {
        required_fields: [:name, :type, :color],
        color_range: [0.0, 1.0],
        roughness_range: [0.0, 1.0],
        metallic_range: [0.0, 1.0],
        transmission_range: [0.0, 1.0],
        valid_types: ["plastic", "metal", "glass", "wood", "fabric", "ceramic", "rubber", "skin"]
      }
    end
    
    # Validate single material
    def self.validate_material(material_data)
      errors = []
      warnings = []
      
      # Check required fields
      @validation_rules[:required_fields].each do |field|
        unless material_data[field]
          errors << "Missing required field: #{field}"
        end
      end
      
      # Validate material type
      if material_data[:type] && !@validation_rules[:valid_types].include?(material_data[:type])
        warnings << "Unknown material type: #{material_data[:type]}"
      end
      
      # Validate color
      if material_data[:color]
        if !material_data[:color].is_a?(Array) || material_data[:color].size != 3
          errors << "Color must be an array of 3 values [R, G, B]"
        else
          material_data[:color].each_with_index do |value, index|
            if !value.is_a?(Numeric)
              errors << "Color component #{index} must be numeric"
            elsif value < @validation_rules[:color_range][0] || value > @validation_rules[:color_range][1]
              errors << "Color component #{index} must be between #{@validation_rules[:color_range][0]} and #{@validation_rules[:color_range][1]}"
            end
          end
        end
      end
      
      # Validate roughness
      if material_data[:roughness]
        unless validate_range(material_data[:roughness], @validation_rules[:roughness_range])
          errors << "Roughness must be between #{@validation_rules[:roughness_range][0]} and #{@validation_rules[:roughness_range][1]}"
        end
      end
      
      # Validate metallic
      if material_data[:metallic]
        unless validate_range(material_data[:metallic], @validation_rules[:metallic_range])
          errors << "Metallic must be between #{@validation_rules[:metallic_range][0]} and #{@validation_rules[:metallic_range][1]}"
        end
      end
      
      # Validate transmission
      if material_data[:transmission]
        unless validate_range(material_data[:transmission], @validation_rules[:transmission_range])
          errors << "Transmission must be between #{@validation_rules[:transmission_range][0]} and #{@validation_rules[:transmission_range][1]}"
        end
      end
      
      # Type-specific validations
      case material_data[:type]
      when "metal"
        if material_data[:metallic] && material_data[:metallic] < 0.8
          warnings << "Metal materials should have high metallic values (>= 0.8)"
        end
      when "glass"
        if material_data[:transmission] && material_data[:transmission] < 0.5
          warnings << "Glass materials should have high transmission values (>= 0.5)"
        end
        if material_data[:roughness] && material_data[:roughness] > 0.1
          warnings << "Glass materials typically have low roughness values (<= 0.1)"
        end
      when "plastic"
        if material_data[:metallic] && material_data[:metallic] > 0.1
          warnings << "Plastic materials should have low metallic values (<= 0.1)"
        end
      end
      
      # Energy conservation check
      if material_data[:color] && material_data[:metallic]
        max_color = material_data[:color].max
        if material_data[:metallic] < 1.0 && max_color > 0.9
          warnings << "Very bright non-metallic materials may violate energy conservation"
        end
      end
      
      {
        valid: errors.empty?,
        errors: errors,
        warnings: warnings
      }
    end
    
    # Validate all materials in library
    def self.validate_all_materials
      materials = MaterialLibraryManager.get_materials
      results = {}
      
      materials.each do |id, material|
        results[id] = validate_material(material)
      end
      
      results
    end
    
    # Get validation summary
    def self.get_validation_summary
      results = validate_all_materials
      
      summary = {
        total_materials: results.size,
        valid_materials: 0,
        materials_with_errors: 0,
        materials_with_warnings: 0,
        total_errors: 0,
        total_warnings: 0
      }
      
      results.each do |id, result|
        if result[:valid]
          summary[:valid_materials] += 1
        else
          summary[:materials_with_errors] += 1
        end
        
        if result[:warnings].any?
          summary[:materials_with_warnings] += 1
        end
        
        summary[:total_errors] += result[:errors].size
        summary[:total_warnings] += result[:warnings].size
      end
      
      summary
    end
    
    # Auto-fix common issues
    def self.auto_fix_material(material_data)
      fixed_material = material_data.dup
      fixes_applied = []
      
      # Clamp values to valid ranges
      if fixed_material[:roughness]
        old_value = fixed_material[:roughness]
        fixed_material[:roughness] = clamp_value(fixed_material[:roughness], @validation_rules[:roughness_range])
        if old_value != fixed_material[:roughness]
          fixes_applied << "Clamped roughness from #{old_value} to #{fixed_material[:roughness]}"
        end
      end
      
      if fixed_material[:metallic]
        old_value = fixed_material[:metallic]
        fixed_material[:metallic] = clamp_value(fixed_material[:metallic], @validation_rules[:metallic_range])
        if old_value != fixed_material[:metallic]
          fixes_applied << "Clamped metallic from #{old_value} to #{fixed_material[:metallic]}"
        end
      end
      
      if fixed_material[:transmission]
        old_value = fixed_material[:transmission]
        fixed_material[:transmission] = clamp_value(fixed_material[:transmission], @validation_rules[:transmission_range])
        if old_value != fixed_material[:transmission]
          fixes_applied << "Clamped transmission from #{old_value} to #{fixed_material[:transmission]}"
        end
      end
      
      # Fix color values
      if fixed_material[:color] && fixed_material[:color].is_a?(Array)
        fixed_material[:color] = fixed_material[:color].map.with_index do |value, index|
          if value.is_a?(Numeric)
            old_value = value
            new_value = clamp_value(value, @validation_rules[:color_range])
            if old_value != new_value
              fixes_applied << "Clamped color component #{index} from #{old_value} to #{new_value}"
            end
            new_value
          else
            fixes_applied << "Converted color component #{index} to numeric"
            0.5 # Default value
          end
        end
      end
      
      # Set default type if missing
      unless fixed_material[:type]
        fixed_material[:type] = "plastic"
        fixes_applied << "Set default material type to 'plastic'"
      end
      
      {
        material: fixed_material,
        fixes_applied: fixes_applied
      }
    end
    
    private
    
    # Validate value is within range
    def self.validate_range(value, range)
      value.is_a?(Numeric) && value >= range[0] && value <= range[1]
    end
    
    # Clamp value to range
    def self.clamp_value(value, range)
      [[value, range[0]].max, range[1]].min
    end
    
  end # module MaterialValidationManager
  
end # module PhotonRender
