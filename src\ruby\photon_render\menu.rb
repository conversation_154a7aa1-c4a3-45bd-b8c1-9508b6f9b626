# src/ruby/photon_render/menu.rb
# Menu system for PhotonRender

module PhotonRender
  
  module Menu
    
    # Create menu structure
    def self.create
      # Create main menu
      @main_menu = UI.menu("Plugins").add_submenu("PhotonRender")
      
      # Rendering section
      @main_menu.add_item("Quick Render") { quick_render }
      @main_menu.add_item("Render Settings...") { show_render_settings }
      @main_menu.add_separator
      
      # Scene section
      @main_menu.add_item("Export Scene...") { export_scene }
      @main_menu.add_item("Import Scene...") { import_scene }
      @main_menu.add_separator
      
      # Materials section
      materials_menu = @main_menu.add_submenu("Materials")
      materials_menu.add_item("Material Editor...") { show_material_editor }
      materials_menu.add_item("Material Library...") { show_material_library }
      materials_menu.add_item("Assign Materials...") { show_material_assignment }
      materials_menu.add_separator
      materials_menu.add_item("Import Materials...") { import_materials }
      materials_menu.add_item("Export Materials...") { export_materials }
      
      # Lighting section
      lighting_menu = @main_menu.add_submenu("Lighting")
      lighting_menu.add_item("Add Point Light") { add_point_light }
      lighting_menu.add_item("Add Directional Light") { add_directional_light }
      lighting_menu.add_item("Add Area Light") { add_area_light }
      lighting_menu.add_separator
      lighting_menu.add_item("Environment Settings...") { show_environment_settings }
      lighting_menu.add_item("Sun & Sky...") { show_sun_sky_settings }
      
      # Camera section
      camera_menu = @main_menu.add_submenu("Camera")
      camera_menu.add_item("Camera Settings...") { show_camera_settings }
      camera_menu.add_item("Save Camera View") { save_camera_view }
      camera_menu.add_item("Load Camera View...") { load_camera_view }
      
      # Tools section
      tools_menu = @main_menu.add_submenu("Tools")
      tools_menu.add_item("Viewport Preview") { toggle_viewport_preview }
      tools_menu.add_item("Performance Monitor...") { show_performance_monitor }
      tools_menu.add_item("Memory Usage...") { show_memory_usage }
      tools_menu.add_separator
      tools_menu.add_item("Validate Scene") { validate_scene }
      tools_menu.add_item("Optimize Scene") { optimize_scene }
      
      # Help section
      @main_menu.add_separator
      @main_menu.add_item("Preferences...") { show_preferences }
      @main_menu.add_item("About PhotonRender...") { show_about }
      @main_menu.add_item("Help...") { show_help }
      
      puts "PhotonRender menu created with #{count_menu_items} items"
    end
    
    # Count menu items for verification
    def self.count_menu_items
      # This is a rough count - in real implementation we'd track this properly
      25
    end
    
    # Menu action methods
    def self.quick_render
      puts "Quick Render requested"
      PhotonRender.render_manager.start_render
    end
    
    def self.show_render_settings
      puts "Render Settings dialog requested"
      Dialog.show_render_settings
    end
    
    def self.export_scene
      puts "Export Scene requested"
      filename = UI.savepanel("Export Scene", "", "*.json")
      if filename
        SceneExport.export_to_file(Sketchup.active_model, filename)
      end
    end
    
    def self.import_scene
      puts "Import Scene requested"
      filename = UI.openpanel("Import Scene", "", "*.json")
      if filename
        # SceneImport.import_from_file(filename)
        UI.messagebox("Scene import not yet implemented")
      end
    end
    
    def self.show_material_editor
      puts "Material Editor requested"
      Dialog.show_material_editor
    end
    
    def self.show_material_library
      puts "Material Library requested"
      Dialog.show_material_library
    end
    
    def self.show_material_assignment
      puts "Material Assignment requested"
      Dialog.show_material_assignment
    end
    
    def self.import_materials
      puts "Import Materials requested"
      filename = UI.openpanel("Import Materials", "", "*.json")
      if filename
        MaterialExportImportManager.import_from_file(filename)
      end
    end
    
    def self.export_materials
      puts "Export Materials requested"
      filename = UI.savepanel("Export Materials", "", "*.json")
      if filename
        MaterialExportImportManager.export_to_file(filename)
      end
    end
    
    def self.add_point_light
      puts "Add Point Light requested"
      # LightManager.add_point_light
      UI.messagebox("Light creation not yet implemented")
    end
    
    def self.add_directional_light
      puts "Add Directional Light requested"
      UI.messagebox("Light creation not yet implemented")
    end
    
    def self.add_area_light
      puts "Add Area Light requested"
      UI.messagebox("Light creation not yet implemented")
    end
    
    def self.show_environment_settings
      puts "Environment Settings requested"
      Dialog.show_environment_settings
    end
    
    def self.show_sun_sky_settings
      puts "Sun & Sky Settings requested"
      Dialog.show_sun_sky_settings
    end
    
    def self.show_camera_settings
      puts "Camera Settings requested"
      Dialog.show_camera_settings
    end
    
    def self.save_camera_view
      puts "Save Camera View requested"
      UI.messagebox("Camera save not yet implemented")
    end
    
    def self.load_camera_view
      puts "Load Camera View requested"
      UI.messagebox("Camera load not yet implemented")
    end
    
    def self.toggle_viewport_preview
      puts "Toggle Viewport Preview requested"
      current = PhotonRender.viewport_preview_enabled?
      PhotonRender.preferences[:viewport_preview] = !current
      PhotonRender.save_preferences
      UI.messagebox("Viewport Preview #{!current ? 'enabled' : 'disabled'}")
    end
    
    def self.show_performance_monitor
      puts "Performance Monitor requested"
      Dialog.show_performance_monitor
    end
    
    def self.show_memory_usage
      puts "Memory Usage requested"
      Dialog.show_memory_usage
    end
    
    def self.validate_scene
      puts "Validate Scene requested"
      # SceneValidator.validate(Sketchup.active_model)
      UI.messagebox("Scene validation not yet implemented")
    end
    
    def self.optimize_scene
      puts "Optimize Scene requested"
      UI.messagebox("Scene optimization not yet implemented")
    end
    
    def self.show_preferences
      puts "Preferences requested"
      Dialog.show_preferences
    end
    
    def self.show_about
      puts "About dialog requested"
      UI.messagebox(
        "PhotonRender #{PhotonRender::PLUGIN_VERSION}\n\n" +
        "Professional photorealistic rendering for SketchUp\n\n" +
        "Core loaded: #{PhotonRender.core_loaded?}\n" +
        "Testing mode: #{!PhotonRender.core_loaded?}\n\n" +
        "© 2024 PhotonRender Development Team"
      )
    end
    
    def self.show_help
      puts "Help requested"
      UI.messagebox("Help documentation not yet implemented")
    end
    
  end # module Menu
  
end # module PhotonRender
