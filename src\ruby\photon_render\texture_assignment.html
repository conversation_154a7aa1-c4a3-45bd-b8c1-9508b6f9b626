<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PhotonRender Texture Assignment</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1a1a1a;
            color: #e0e0e0;
            overflow: hidden;
        }

        .texture-assignment {
            display: flex;
            height: 100vh;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
        }

        /* Texture Browser Panel */
        .texture-browser {
            width: 350px;
            background: #252525;
            border-right: 1px solid #404040;
            display: flex;
            flex-direction: column;
        }

        .browser-header {
            background: #2a2a2a;
            padding: 15px;
            border-bottom: 1px solid #404040;
        }

        .browser-title {
            font-size: 16px;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 10px;
        }

        .search-box {
            width: 100%;
            background: #1a1a1a;
            border: 1px solid #404040;
            color: #e0e0e0;
            padding: 8px 10px;
            border-radius: 4px;
            font-size: 12px;
        }

        .search-box:focus {
            outline: none;
            border-color: #0078d4;
        }

        .filter-tabs {
            display: flex;
            margin-top: 10px;
            gap: 5px;
        }

        .filter-tab {
            background: #404040;
            border: none;
            color: #e0e0e0;
            padding: 6px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 11px;
            transition: background 0.2s;
        }

        .filter-tab:hover {
            background: #505050;
        }

        .filter-tab.active {
            background: #0078d4;
            color: white;
        }

        .texture-list {
            flex: 1;
            padding: 15px;
            overflow-y: auto;
        }

        .texture-item {
            background: #2a2a2a;
            border-radius: 6px;
            padding: 10px;
            margin-bottom: 10px;
            cursor: grab;
            transition: all 0.2s;
            border: 2px solid transparent;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .texture-item:hover {
            background: #333;
            transform: translateY(-1px);
        }

        .texture-item:active {
            cursor: grabbing;
        }

        .texture-item.dragging {
            opacity: 0.5;
            transform: rotate(5deg);
        }

        .texture-thumbnail {
            width: 40px;
            height: 40px;
            background: #333;
            border-radius: 4px;
            background-size: cover;
            background-position: center;
            flex-shrink: 0;
        }

        .texture-info {
            flex: 1;
            min-width: 0;
        }

        .texture-name {
            font-size: 12px;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 2px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .texture-details {
            font-size: 10px;
            color: #888;
        }

        /* Main Assignment Panel */
        .assignment-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .assignment-header {
            background: #2a2a2a;
            padding: 15px;
            border-bottom: 1px solid #404040;
        }

        .assignment-title {
            font-size: 16px;
            font-weight: 600;
            color: #ffffff;
        }

        .material-name {
            font-size: 12px;
            color: #888;
            margin-top: 5px;
        }

        .assignment-content {
            flex: 1;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            padding: 20px;
            overflow-y: auto;
        }

        .texture-slot {
            background: #2a2a2a;
            border-radius: 8px;
            overflow: hidden;
            border: 2px dashed #404040;
            transition: all 0.2s;
        }

        .texture-slot.drag-over {
            border-color: #0078d4;
            background: #1e3a5f;
        }

        .texture-slot.assigned {
            border-style: solid;
            border-color: #0078d4;
        }

        .slot-header {
            background: #333;
            padding: 12px 15px;
            border-bottom: 1px solid #404040;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .slot-title {
            font-size: 14px;
            font-weight: 600;
            color: #ffffff;
        }

        .slot-actions {
            display: flex;
            gap: 5px;
        }

        .slot-btn {
            background: #404040;
            border: none;
            color: #e0e0e0;
            padding: 4px 8px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 10px;
            transition: background 0.2s;
        }

        .slot-btn:hover {
            background: #505050;
        }

        .slot-btn.danger {
            background: #d13438;
            color: white;
        }

        .slot-btn.danger:hover {
            background: #b02a2e;
        }

        .slot-content {
            padding: 15px;
            min-height: 200px;
            display: flex;
            flex-direction: column;
        }

        .drop-zone {
            flex: 1;
            border: 2px dashed #555;
            border-radius: 6px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: #888;
            font-size: 12px;
            min-height: 120px;
            transition: all 0.2s;
        }

        .drop-zone.drag-over {
            border-color: #0078d4;
            background: rgba(0, 120, 212, 0.1);
            color: #0078d4;
        }

        .drop-icon {
            font-size: 24px;
            margin-bottom: 8px;
            opacity: 0.5;
        }

        .assigned-texture {
            display: none;
        }

        .assigned-texture.active {
            display: block;
        }

        .texture-preview {
            width: 100%;
            height: 100px;
            background: #333;
            border-radius: 4px;
            background-size: cover;
            background-position: center;
            margin-bottom: 10px;
        }

        .texture-meta {
            font-size: 11px;
            color: #888;
            margin-bottom: 15px;
        }

        .uv-controls {
            margin-top: 15px;
        }

        .control-group {
            margin-bottom: 12px;
        }

        .control-label {
            font-size: 11px;
            color: #ccc;
            margin-bottom: 5px;
            display: block;
        }

        .control-row {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .control-input {
            flex: 1;
            background: #1a1a1a;
            border: 1px solid #404040;
            color: #e0e0e0;
            padding: 4px 6px;
            border-radius: 3px;
            font-size: 11px;
            width: 60px;
        }

        .control-input:focus {
            outline: none;
            border-color: #0078d4;
        }

        .control-slider {
            flex: 2;
            height: 4px;
            background: #404040;
            border-radius: 2px;
            outline: none;
            -webkit-appearance: none;
            cursor: pointer;
        }

        .control-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 12px;
            height: 12px;
            background: #0078d4;
            border-radius: 50%;
            cursor: pointer;
        }

        .control-select {
            background: #1a1a1a;
            border: 1px solid #404040;
            color: #e0e0e0;
            padding: 4px 6px;
            border-radius: 3px;
            font-size: 11px;
        }

        .intensity-control {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 10px;
        }

        .intensity-label {
            font-size: 11px;
            color: #ccc;
            min-width: 50px;
        }

        .intensity-slider {
            flex: 1;
            height: 4px;
            background: #404040;
            border-radius: 2px;
            outline: none;
            -webkit-appearance: none;
            cursor: pointer;
        }

        .intensity-value {
            background: #1a1a1a;
            color: #0078d4;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 10px;
            min-width: 35px;
            text-align: center;
        }

        /* Action Bar */
        .action-bar {
            background: #333;
            padding: 15px;
            border-top: 1px solid #404040;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .action-info {
            font-size: 12px;
            color: #888;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
        }

        .action-btn {
            background: #0078d4;
            border: none;
            color: white;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.2s;
        }

        .action-btn:hover {
            background: #106ebe;
        }

        .action-btn.secondary {
            background: #404040;
            color: #e0e0e0;
        }

        .action-btn.secondary:hover {
            background: #505050;
        }

        /* Scrollbar styling */
        .texture-list::-webkit-scrollbar,
        .assignment-content::-webkit-scrollbar {
            width: 8px;
        }

        .texture-list::-webkit-scrollbar-track,
        .assignment-content::-webkit-scrollbar-track {
            background: #1a1a1a;
        }

        .texture-list::-webkit-scrollbar-thumb,
        .assignment-content::-webkit-scrollbar-thumb {
            background: #404040;
            border-radius: 4px;
        }

        .texture-list::-webkit-scrollbar-thumb:hover,
        .assignment-content::-webkit-scrollbar-thumb:hover {
            background: #505050;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .texture-assignment {
                flex-direction: column;
            }
            
            .texture-browser {
                width: 100%;
                height: 40vh;
            }
            
            .assignment-content {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="texture-assignment">
        <!-- Texture Browser Panel -->
        <div class="texture-browser">
            <div class="browser-header">
                <div class="browser-title">Texture Browser</div>
                <input type="text" class="search-box" id="textureSearch" placeholder="Search textures...">
                <div class="filter-tabs">
                    <button class="filter-tab active" data-filter="all">All</button>
                    <button class="filter-tab" data-filter="recent">Recent</button>
                    <button class="filter-tab" data-filter="hdr">HDR</button>
                    <button class="filter-tab" data-filter="normal">Normal</button>
                </div>
            </div>
            
            <div class="texture-list" id="textureList">
                <!-- Texture items will be populated here -->
            </div>
        </div>

        <!-- Main Assignment Panel -->
        <div class="assignment-panel">
            <div class="assignment-header">
                <div class="assignment-title">Texture Assignment</div>
                <div class="material-name" id="materialName">Current Material: Red Plastic</div>
            </div>
            
            <div class="assignment-content">
                <!-- Diffuse/Base Color Slot -->
                <div class="texture-slot" data-texture-type="diffuse">
                    <div class="slot-header">
                        <div class="slot-title">Base Color</div>
                        <div class="slot-actions">
                            <button class="slot-btn" onclick="textureAssignment.browseTexture('diffuse')">Browse</button>
                            <button class="slot-btn danger" onclick="textureAssignment.clearTexture('diffuse')">Clear</button>
                        </div>
                    </div>
                    <div class="slot-content">
                        <div class="drop-zone">
                            <div class="drop-icon">🖼️</div>
                            <div>Drop texture here or click Browse</div>
                            <div style="font-size: 10px; margin-top: 5px; opacity: 0.7;">Supports: PNG, JPG, TGA, BMP</div>
                        </div>
                        <div class="assigned-texture">
                            <div class="texture-preview"></div>
                            <div class="texture-meta"></div>
                            <div class="uv-controls">
                                <div class="control-group">
                                    <label class="control-label">UV Offset</label>
                                    <div class="control-row">
                                        <input type="number" class="control-input" placeholder="U" step="0.01">
                                        <input type="number" class="control-input" placeholder="V" step="0.01">
                                    </div>
                                </div>
                                <div class="control-group">
                                    <label class="control-label">UV Scale</label>
                                    <div class="control-row">
                                        <input type="number" class="control-input" placeholder="U" step="0.01" value="1.0">
                                        <input type="number" class="control-input" placeholder="V" step="0.01" value="1.0">
                                    </div>
                                </div>
                                <div class="control-group">
                                    <label class="control-label">Rotation</label>
                                    <div class="control-row">
                                        <input type="range" class="control-slider" min="0" max="360" value="0">
                                        <input type="number" class="control-input" placeholder="°" step="1" value="0">
                                    </div>
                                </div>
                                <div class="control-group">
                                    <label class="control-label">Wrap Mode</label>
                                    <div class="control-row">
                                        <select class="control-select">
                                            <option value="repeat">Repeat</option>
                                            <option value="clamp">Clamp</option>
                                            <option value="mirror">Mirror</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="intensity-control">
                                <span class="intensity-label">Intensity:</span>
                                <input type="range" class="intensity-slider" min="0" max="2" step="0.01" value="1">
                                <span class="intensity-value">1.00</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Normal Map Slot -->
                <div class="texture-slot" data-texture-type="normal">
                    <div class="slot-header">
                        <div class="slot-title">Normal Map</div>
                        <div class="slot-actions">
                            <button class="slot-btn" onclick="textureAssignment.browseTexture('normal')">Browse</button>
                            <button class="slot-btn danger" onclick="textureAssignment.clearTexture('normal')">Clear</button>
                        </div>
                    </div>
                    <div class="slot-content">
                        <div class="drop-zone">
                            <div class="drop-icon">🗺️</div>
                            <div>Drop normal map here</div>
                            <div style="font-size: 10px; margin-top: 5px; opacity: 0.7;">Normal maps for surface detail</div>
                        </div>
                        <div class="assigned-texture">
                            <!-- Same structure as diffuse slot -->
                        </div>
                    </div>
                </div>

                <!-- Roughness Map Slot -->
                <div class="texture-slot" data-texture-type="roughness">
                    <div class="slot-header">
                        <div class="slot-title">Roughness</div>
                        <div class="slot-actions">
                            <button class="slot-btn" onclick="textureAssignment.browseTexture('roughness')">Browse</button>
                            <button class="slot-btn danger" onclick="textureAssignment.clearTexture('roughness')">Clear</button>
                        </div>
                    </div>
                    <div class="slot-content">
                        <div class="drop-zone">
                            <div class="drop-icon">⚪</div>
                            <div>Drop roughness map here</div>
                            <div style="font-size: 10px; margin-top: 5px; opacity: 0.7;">Grayscale roughness control</div>
                        </div>
                        <div class="assigned-texture">
                            <!-- Same structure as diffuse slot -->
                        </div>
                    </div>
                </div>

                <!-- Metallic Map Slot -->
                <div class="texture-slot" data-texture-type="metallic">
                    <div class="slot-header">
                        <div class="slot-title">Metallic</div>
                        <div class="slot-actions">
                            <button class="slot-btn" onclick="textureAssignment.browseTexture('metallic')">Browse</button>
                            <button class="slot-btn danger" onclick="textureAssignment.clearTexture('metallic')">Clear</button>
                        </div>
                    </div>
                    <div class="slot-content">
                        <div class="drop-zone">
                            <div class="drop-icon">⚫</div>
                            <div>Drop metallic map here</div>
                            <div style="font-size: 10px; margin-top: 5px; opacity: 0.7;">Metallic/non-metallic mask</div>
                        </div>
                        <div class="assigned-texture">
                            <!-- Same structure as diffuse slot -->
                        </div>
                    </div>
                </div>

                <!-- Emission Map Slot -->
                <div class="texture-slot" data-texture-type="emission">
                    <div class="slot-header">
                        <div class="slot-title">Emission</div>
                        <div class="slot-actions">
                            <button class="slot-btn" onclick="textureAssignment.browseTexture('emission')">Browse</button>
                            <button class="slot-btn danger" onclick="textureAssignment.clearTexture('emission')">Clear</button>
                        </div>
                    </div>
                    <div class="slot-content">
                        <div class="drop-zone">
                            <div class="drop-icon">💡</div>
                            <div>Drop emission map here</div>
                            <div style="font-size: 10px; margin-top: 5px; opacity: 0.7;">Self-illumination texture</div>
                        </div>
                        <div class="assigned-texture">
                            <!-- Same structure as diffuse slot -->
                        </div>
                    </div>
                </div>

                <!-- Opacity Map Slot -->
                <div class="texture-slot" data-texture-type="opacity">
                    <div class="slot-header">
                        <div class="slot-title">Opacity</div>
                        <div class="slot-actions">
                            <button class="slot-btn" onclick="textureAssignment.browseTexture('opacity')">Browse</button>
                            <button class="slot-btn danger" onclick="textureAssignment.clearTexture('opacity')">Clear</button>
                        </div>
                    </div>
                    <div class="slot-content">
                        <div class="drop-zone">
                            <div class="drop-icon">👻</div>
                            <div>Drop opacity map here</div>
                            <div style="font-size: 10px; margin-top: 5px; opacity: 0.7;">Alpha/transparency control</div>
                        </div>
                        <div class="assigned-texture">
                            <!-- Same structure as diffuse slot -->
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="action-bar">
                <div class="action-info" id="actionInfo">
                    Ready to assign textures. Drag and drop or use Browse buttons.
                </div>
                <div class="action-buttons">
                    <button class="action-btn secondary" onclick="textureAssignment.resetAll()">Reset All</button>
                    <button class="action-btn secondary" onclick="textureAssignment.previewMaterial()">Preview</button>
                    <button class="action-btn" onclick="textureAssignment.applyTextures()">Apply</button>
                </div>
            </div>
        </div>
    </div>

    <script src="texture_assignment.js"></script>
</body>
</html>
