// src/ruby/photon_render/texture_assignment.js
// PhotonRender Texture Assignment - JavaScript Interface
// Sistema di controllo interfaccia per Texture Assignment

class TextureAssignment {
    constructor() {
        this.textures = [];
        this.filteredTextures = [];
        this.currentFilter = 'all';
        this.searchQuery = '';
        this.assignments = {};
        this.draggedTexture = null;
        
        this.initializeEventListeners();
        this.loadTextures();
    }
    
    initializeEventListeners() {
        // Search functionality
        const searchBox = document.getElementById('textureSearch');
        searchBox.addEventListener('input', (e) => {
            this.searchQuery = e.target.value;
            this.filterTextures();
        });
        
        // Filter tabs
        document.querySelectorAll('.filter-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                this.setFilter(e.target.dataset.filter);
            });
        });
        
        // Setup drag and drop for texture slots
        this.setupTextureSlots();
        
        // Setup texture list drag functionality
        this.setupTextureDrag();
    }
    
    setupTextureSlots() {
        document.querySelectorAll('.texture-slot').forEach(slot => {
            const textureType = slot.dataset.textureType;
            const dropZone = slot.querySelector('.drop-zone');
            
            // Drag over
            dropZone.addEventListener('dragover', (e) => {
                e.preventDefault();
                dropZone.classList.add('drag-over');
                slot.classList.add('drag-over');
            });
            
            // Drag leave
            dropZone.addEventListener('dragleave', (e) => {
                if (!dropZone.contains(e.relatedTarget)) {
                    dropZone.classList.remove('drag-over');
                    slot.classList.remove('drag-over');
                }
            });
            
            // Drop
            dropZone.addEventListener('drop', (e) => {
                e.preventDefault();
                dropZone.classList.remove('drag-over');
                slot.classList.remove('drag-over');
                
                if (this.draggedTexture) {
                    this.assignTexture(textureType, this.draggedTexture);
                    this.draggedTexture = null;
                } else {
                    // Handle file drop
                    this.handleFileDrop(e, textureType);
                }
            });
            
            // Click to browse
            dropZone.addEventListener('click', () => {
                this.browseTexture(textureType);
            });
            
            // Setup UV controls for this slot
            this.setupUVControls(slot, textureType);
        });
    }
    
    setupTextureDrag() {
        // Will be called when texture list is populated
    }
    
    setupUVControls(slot, textureType) {
        const uvControls = slot.querySelector('.uv-controls');
        if (!uvControls) return;
        
        // Offset controls
        const offsetInputs = uvControls.querySelectorAll('.control-group:nth-child(1) .control-input');
        offsetInputs.forEach((input, index) => {
            input.addEventListener('change', () => {
                this.updateUVTransform(textureType, 'offset', index, parseFloat(input.value) || 0);
            });
        });
        
        // Scale controls
        const scaleInputs = uvControls.querySelectorAll('.control-group:nth-child(2) .control-input');
        scaleInputs.forEach((input, index) => {
            input.addEventListener('change', () => {
                this.updateUVTransform(textureType, 'scale', index, parseFloat(input.value) || 1);
            });
        });
        
        // Rotation controls
        const rotationSlider = uvControls.querySelector('.control-group:nth-child(3) .control-slider');
        const rotationInput = uvControls.querySelector('.control-group:nth-child(3) .control-input');
        
        rotationSlider.addEventListener('input', () => {
            const value = parseFloat(rotationSlider.value);
            rotationInput.value = value;
            this.updateUVTransform(textureType, 'rotation', 0, value);
        });
        
        rotationInput.addEventListener('change', () => {
            const value = parseFloat(rotationInput.value) || 0;
            rotationSlider.value = value;
            this.updateUVTransform(textureType, 'rotation', 0, value);
        });
        
        // Wrap mode
        const wrapSelect = uvControls.querySelector('.control-select');
        wrapSelect.addEventListener('change', () => {
            this.updateWrapMode(textureType, wrapSelect.value);
        });
        
        // Intensity control
        const intensitySlider = slot.querySelector('.intensity-slider');
        const intensityValue = slot.querySelector('.intensity-value');
        
        if (intensitySlider && intensityValue) {
            intensitySlider.addEventListener('input', () => {
                const value = parseFloat(intensitySlider.value);
                intensityValue.textContent = value.toFixed(2);
                this.updateIntensity(textureType, value);
            });
        }
    }
    
    loadTextures() {
        this.updateActionInfo('Loading textures...');
        
        // Request textures from Ruby backend
        if (window.sketchup) {
            window.sketchup.getTextures();
        } else {
            // Fallback for testing
            setTimeout(() => {
                this.onTexturesLoaded(this.generateMockTextures());
            }, 500);
        }
    }
    
    onTexturesLoaded(textures) {
        this.textures = textures || [];
        this.filterTextures();
        this.updateActionInfo('Ready to assign textures. Drag and drop or use Browse buttons.');
        
        console.log(`Loaded ${this.textures.length} textures`);
    }
    
    filterTextures() {
        this.filteredTextures = this.textures.filter(texture => {
            // Search filter
            if (this.searchQuery) {
                const query = this.searchQuery.toLowerCase();
                const name = (texture.name || '').toLowerCase();
                if (!name.includes(query)) {
                    return false;
                }
            }
            
            // Category filter
            switch (this.currentFilter) {
                case 'recent':
                    return texture.isRecent;
                case 'hdr':
                    return texture.isHDR;
                case 'normal':
                    return texture.name && texture.name.toLowerCase().includes('normal');
                default:
                    return true;
            }
        });
        
        this.displayTextures();
    }
    
    displayTextures() {
        const textureList = document.getElementById('textureList');
        
        if (this.filteredTextures.length === 0) {
            textureList.innerHTML = `
                <div style="text-align: center; padding: 40px; color: #888;">
                    <div style="font-size: 24px; margin-bottom: 10px;">📁</div>
                    <div>No textures found</div>
                    <div style="font-size: 11px; margin-top: 5px;">Try adjusting your search or filter</div>
                </div>
            `;
            return;
        }
        
        textureList.innerHTML = this.filteredTextures.map(texture => 
            this.createTextureItem(texture)
        ).join('');
        
        // Setup drag functionality for new items
        this.setupTextureItemDrag();
    }
    
    createTextureItem(texture) {
        return `
            <div class="texture-item" draggable="true" data-texture-id="${texture.id}">
                <div class="texture-thumbnail" style="background-image: url('${this.getThumbnailPath(texture)}')"></div>
                <div class="texture-info">
                    <div class="texture-name">${texture.name || 'Unnamed'}</div>
                    <div class="texture-details">${texture.width}x${texture.height} • ${texture.format.toUpperCase()}</div>
                </div>
            </div>
        `;
    }
    
    setupTextureItemDrag() {
        document.querySelectorAll('.texture-item').forEach(item => {
            item.addEventListener('dragstart', (e) => {
                const textureId = item.dataset.textureId;
                this.draggedTexture = this.textures.find(t => t.id === textureId);
                item.classList.add('dragging');
                
                // Set drag data
                e.dataTransfer.setData('text/plain', textureId);
                e.dataTransfer.effectAllowed = 'copy';
            });
            
            item.addEventListener('dragend', () => {
                item.classList.remove('dragging');
                this.draggedTexture = null;
            });
        });
    }
    
    setFilter(filter) {
        // Update UI
        document.querySelectorAll('.filter-tab').forEach(tab => {
            tab.classList.toggle('active', tab.dataset.filter === filter);
        });
        
        this.currentFilter = filter;
        this.filterTextures();
    }
    
    assignTexture(textureType, texture) {
        console.log(`Assigning texture ${texture.id} to ${textureType}`);
        
        // Store assignment
        this.assignments[textureType] = {
            texture: texture,
            uvTransform: {
                offsetU: 0,
                offsetV: 0,
                scaleU: 1,
                scaleV: 1,
                rotation: 0
            },
            wrapMode: 'repeat',
            intensity: 1.0
        };
        
        // Update UI
        this.updateTextureSlotUI(textureType, texture);
        
        // Notify backend
        if (window.sketchup) {
            window.sketchup.assignTexture(JSON.stringify({
                textureType: textureType,
                textureId: texture.id,
                assignment: this.assignments[textureType]
            }));
        }
        
        this.updateActionInfo(`Assigned ${texture.name} to ${textureType}`);
    }
    
    updateTextureSlotUI(textureType, texture) {
        const slot = document.querySelector(`[data-texture-type="${textureType}"]`);
        if (!slot) return;
        
        const dropZone = slot.querySelector('.drop-zone');
        const assignedTexture = slot.querySelector('.assigned-texture');
        const preview = slot.querySelector('.texture-preview');
        const meta = slot.querySelector('.texture-meta');
        
        // Hide drop zone, show assigned texture
        dropZone.style.display = 'none';
        assignedTexture.classList.add('active');
        slot.classList.add('assigned');
        
        // Update preview
        if (preview) {
            preview.style.backgroundImage = `url('${this.getThumbnailPath(texture)}')`;
        }
        
        // Update metadata
        if (meta) {
            meta.textContent = `${texture.name} • ${texture.width}x${texture.height} • ${texture.format.toUpperCase()}`;
        }
    }
    
    clearTexture(textureType) {
        console.log(`Clearing texture from ${textureType}`);
        
        // Remove assignment
        delete this.assignments[textureType];
        
        // Update UI
        const slot = document.querySelector(`[data-texture-type="${textureType}"]`);
        if (slot) {
            const dropZone = slot.querySelector('.drop-zone');
            const assignedTexture = slot.querySelector('.assigned-texture');
            
            dropZone.style.display = 'flex';
            assignedTexture.classList.remove('active');
            slot.classList.remove('assigned');
        }
        
        // Notify backend
        if (window.sketchup) {
            window.sketchup.clearTexture(textureType);
        }
        
        this.updateActionInfo(`Cleared ${textureType} texture`);
    }
    
    browseTexture(textureType) {
        console.log(`Browse texture for ${textureType}`);
        
        if (window.sketchup) {
            window.sketchup.browseTexture(textureType);
        } else {
            // Fallback for testing
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            input.onchange = (e) => {
                if (e.target.files.length > 0) {
                    this.handleFileSelection(e.target.files[0], textureType);
                }
            };
            input.click();
        }
    }
    
    handleFileDrop(event, textureType) {
        const files = event.dataTransfer.files;
        if (files.length > 0) {
            this.handleFileSelection(files[0], textureType);
        }
    }
    
    handleFileSelection(file, textureType) {
        console.log(`File selected for ${textureType}:`, file.name);
        
        // Create mock texture object
        const texture = {
            id: `file_${Date.now()}`,
            name: file.name.split('.')[0],
            format: file.name.split('.').pop().toLowerCase(),
            width: 1024, // Unknown until loaded
            height: 1024,
            isHDR: ['hdr', 'exr'].includes(file.name.split('.').pop().toLowerCase())
        };
        
        // Load file as data URL for preview
        const reader = new FileReader();
        reader.onload = (e) => {
            texture.dataUrl = e.target.result;
            this.assignTexture(textureType, texture);
        };
        reader.readAsDataURL(file);
        
        // Notify backend to load actual file
        if (window.sketchup) {
            window.sketchup.loadTextureFile(JSON.stringify({
                textureType: textureType,
                filePath: file.path || file.name
            }));
        }
    }
    
    updateUVTransform(textureType, property, index, value) {
        if (!this.assignments[textureType]) return;
        
        const transform = this.assignments[textureType].uvTransform;
        
        switch (property) {
            case 'offset':
                if (index === 0) transform.offsetU = value;
                else transform.offsetV = value;
                break;
            case 'scale':
                if (index === 0) transform.scaleU = value;
                else transform.scaleV = value;
                break;
            case 'rotation':
                transform.rotation = value;
                break;
        }
        
        // Notify backend
        if (window.sketchup) {
            window.sketchup.updateUVTransform(JSON.stringify({
                textureType: textureType,
                transform: transform
            }));
        }
        
        console.log(`Updated ${textureType} UV ${property}:`, transform);
    }
    
    updateWrapMode(textureType, wrapMode) {
        if (!this.assignments[textureType]) return;
        
        this.assignments[textureType].wrapMode = wrapMode;
        
        // Notify backend
        if (window.sketchup) {
            window.sketchup.updateWrapMode(JSON.stringify({
                textureType: textureType,
                wrapMode: wrapMode
            }));
        }
        
        console.log(`Updated ${textureType} wrap mode:`, wrapMode);
    }
    
    updateIntensity(textureType, intensity) {
        if (!this.assignments[textureType]) return;
        
        this.assignments[textureType].intensity = intensity;
        
        // Notify backend
        if (window.sketchup) {
            window.sketchup.updateTextureIntensity(JSON.stringify({
                textureType: textureType,
                intensity: intensity
            }));
        }
        
        console.log(`Updated ${textureType} intensity:`, intensity);
    }
    
    resetAll() {
        if (confirm('Reset all texture assignments?')) {
            Object.keys(this.assignments).forEach(textureType => {
                this.clearTexture(textureType);
            });
            
            this.updateActionInfo('All textures cleared');
        }
    }
    
    previewMaterial() {
        console.log('Preview material with textures:', this.assignments);
        
        if (window.sketchup) {
            window.sketchup.previewMaterialWithTextures(JSON.stringify(this.assignments));
        }
        
        this.updateActionInfo('Generating preview...');
    }
    
    applyTextures() {
        console.log('Apply textures:', this.assignments);
        
        if (window.sketchup) {
            window.sketchup.applyTextureAssignments(JSON.stringify(this.assignments));
        }
        
        this.updateActionInfo('Textures applied successfully');
    }
    
    updateActionInfo(message) {
        const actionInfo = document.getElementById('actionInfo');
        if (actionInfo) {
            actionInfo.textContent = message;
        }
    }
    
    getThumbnailPath(texture) {
        if (texture.dataUrl) {
            return texture.dataUrl;
        }
        
        return texture.thumbnailPath || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjMzMzIi8+Cjx0ZXh0IHg9IjIwIiB5PSIyNCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjEwIiBmaWxsPSIjNjY2IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj5URVg8L3RleHQ+Cjwvc3ZnPgo=';
    }
    
    generateMockTextures() {
        return [
            {
                id: 'tex_001',
                name: 'Brick Wall',
                format: 'jpg',
                width: 1024,
                height: 1024,
                isHDR: false,
                isRecent: true
            },
            {
                id: 'tex_002',
                name: 'Metal Plate',
                format: 'png',
                width: 512,
                height: 512,
                isHDR: false,
                isRecent: true
            },
            {
                id: 'tex_003',
                name: 'Wood Grain',
                format: 'jpg',
                width: 2048,
                height: 2048,
                isHDR: false,
                isRecent: false
            },
            {
                id: 'tex_004',
                name: 'Concrete Normal',
                format: 'png',
                width: 1024,
                height: 1024,
                isHDR: false,
                isRecent: false
            },
            {
                id: 'tex_005',
                name: 'Studio HDRI',
                format: 'hdr',
                width: 4096,
                height: 2048,
                isHDR: true,
                isRecent: false
            }
        ];
    }
}

// Initialize texture assignment when page loads
document.addEventListener('DOMContentLoaded', () => {
    window.textureAssignment = new TextureAssignment();
});

// Global functions for Ruby integration
window.onTexturesLoaded = (textures) => {
    if (window.textureAssignment) {
        window.textureAssignment.onTexturesLoaded(textures);
    }
};

window.onTextureAssigned = (textureType, textureId) => {
    if (window.textureAssignment) {
        const texture = window.textureAssignment.textures.find(t => t.id === textureId);
        if (texture) {
            window.textureAssignment.assignTexture(textureType, texture);
        }
    }
};
