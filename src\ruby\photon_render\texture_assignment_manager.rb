# src/ruby/photon_render/texture_assignment_manager.rb
# Texture assignment management for PhotonRender

module PhotonRender
  
  module TextureAssignmentManager
    
    # Initialize texture assignment manager
    def self.initialize
      puts "Initializing Texture Assignment Manager"
      @assignments = {}
    end
    
    # Assign material to entity
    def self.assign_material(entity, material_id)
      if entity && material_id
        @assignments[entity.entityID] = material_id
        puts "Assigned material '#{material_id}' to entity #{entity.entityID}"
        true
      else
        puts "Invalid entity or material_id"
        false
      end
    end
    
    # Get material assignment for entity
    def self.get_assignment(entity)
      return nil unless entity
      @assignments[entity.entityID]
    end
    
    # Remove assignment
    def self.remove_assignment(entity)
      return false unless entity
      @assignments.delete(entity.entityID)
      puts "Removed material assignment for entity #{entity.entityID}"
      true
    end
    
    # Get all assignments
    def self.get_all_assignments
      @assignments
    end
    
    # Clear all assignments
    def self.clear_all_assignments
      @assignments.clear
      puts "Cleared all material assignments"
    end
    
    # Assign material to selection
    def self.assign_to_selection(material_id)
      model = Sketchup.active_model
      selection = model.selection
      
      if selection.empty?
        UI.messagebox("No entities selected")
        return false
      end
      
      count = 0
      selection.each do |entity|
        if assign_material(entity, material_id)
          count += 1
        end
      end
      
      UI.messagebox("Assigned material to #{count} entities")
      count > 0
    end
    
    # Auto-assign materials based on SketchUp materials
    def self.auto_assign_from_sketchup
      model = Sketchup.active_model
      count = 0
      
      model.entities.each do |entity|
        if entity.respond_to?(:material) && entity.material
          # Try to find matching PhotonRender material
          su_material = entity.material
          pr_material = find_matching_material(su_material)
          
          if pr_material
            assign_material(entity, pr_material)
            count += 1
          end
        end
      end
      
      puts "Auto-assigned #{count} materials from SketchUp"
      count
    end
    
    # Find matching PhotonRender material for SketchUp material
    def self.find_matching_material(su_material)
      return nil unless su_material
      
      # Simple matching by name
      materials = MaterialLibraryManager.get_materials
      
      # First try exact name match
      materials.each do |id, material|
        if material[:name].downcase == su_material.name.downcase
          return id
        end
      end
      
      # Then try partial name match
      materials.each do |id, material|
        if material[:name].downcase.include?(su_material.name.downcase) ||
           su_material.name.downcase.include?(material[:name].downcase)
          return id
        end
      end
      
      # No match found
      nil
    end
    
    # Export assignments to file
    def self.export_assignments(filename)
      begin
        File.write(filename, JSON.pretty_generate(@assignments))
        puts "Assignments exported to: #{filename}"
        true
      rescue => e
        puts "Error exporting assignments: #{e.message}"
        false
      end
    end
    
    # Import assignments from file
    def self.import_assignments(filename)
      begin
        @assignments = JSON.parse(File.read(filename))
        puts "Assignments imported from: #{filename}"
        true
      rescue => e
        puts "Error importing assignments: #{e.message}"
        false
      end
    end
    
    # Get assignment statistics
    def self.get_statistics
      stats = {
        total_assignments: @assignments.size,
        materials_used: @assignments.values.uniq.size,
        most_used_material: nil
      }
      
      # Find most used material
      material_counts = {}
      @assignments.values.each do |material_id|
        material_counts[material_id] = (material_counts[material_id] || 0) + 1
      end
      
      if material_counts.any?
        stats[:most_used_material] = material_counts.max_by { |k, v| v }[0]
      end
      
      stats
    end
    
  end # module TextureAssignmentManager
  
end # module PhotonRender
