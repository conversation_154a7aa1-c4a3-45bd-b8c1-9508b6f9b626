# src/ruby/photon_render/toolbar.rb
# Toolbar system for PhotonRender

module PhotonRender
  
  module Toolbar
    
    # Create toolbar
    def self.create
      @toolbar = UI::Toolbar.new("PhotonRender")
      
      # Quick Render button
      cmd_quick_render = UI::Command.new("Quick Render") {
        Menu.quick_render
      }
      cmd_quick_render.tooltip = "Start quick render with current settings"
      cmd_quick_render.status_bar_text = "Start rendering the current scene"
      cmd_quick_render.small_icon = get_icon_path("render_16.png")
      cmd_quick_render.large_icon = get_icon_path("render_24.png")
      @toolbar.add_item(cmd_quick_render)
      
      # Render Settings button
      cmd_render_settings = UI::Command.new("Render Settings") {
        Menu.show_render_settings
      }
      cmd_render_settings.tooltip = "Open render settings dialog"
      cmd_render_settings.status_bar_text = "Configure rendering parameters"
      cmd_render_settings.small_icon = get_icon_path("settings_16.png")
      cmd_render_settings.large_icon = get_icon_path("settings_24.png")
      @toolbar.add_item(cmd_render_settings)
      
      # Material Editor button
      cmd_material_editor = UI::Command.new("Material Editor") {
        Menu.show_material_editor
      }
      cmd_material_editor.tooltip = "Open material editor"
      cmd_material_editor.status_bar_text = "Edit and create materials"
      cmd_material_editor.small_icon = get_icon_path("material_16.png")
      cmd_material_editor.large_icon = get_icon_path("material_24.png")
      @toolbar.add_item(cmd_material_editor)
      
      # Scene Export button
      cmd_export_scene = UI::Command.new("Export Scene") {
        Menu.export_scene
      }
      cmd_export_scene.tooltip = "Export scene to file"
      cmd_export_scene.status_bar_text = "Export current scene for rendering"
      cmd_export_scene.small_icon = get_icon_path("export_16.png")
      cmd_export_scene.large_icon = get_icon_path("export_24.png")
      @toolbar.add_item(cmd_export_scene)
      
      # Add Light button
      cmd_add_light = UI::Command.new("Add Light") {
        show_light_menu
      }
      cmd_add_light.tooltip = "Add light to scene"
      cmd_add_light.status_bar_text = "Add various types of lights"
      cmd_add_light.small_icon = get_icon_path("light_16.png")
      cmd_add_light.large_icon = get_icon_path("light_24.png")
      @toolbar.add_item(cmd_add_light)
      
      # Camera Settings button
      cmd_camera = UI::Command.new("Camera Settings") {
        Menu.show_camera_settings
      }
      cmd_camera.tooltip = "Camera settings"
      cmd_camera.status_bar_text = "Configure camera parameters"
      cmd_camera.small_icon = get_icon_path("camera_16.png")
      cmd_camera.large_icon = get_icon_path("camera_24.png")
      @toolbar.add_item(cmd_camera)
      
      # Viewport Preview toggle
      cmd_viewport = UI::Command.new("Viewport Preview") {
        Menu.toggle_viewport_preview
      }
      cmd_viewport.tooltip = "Toggle viewport preview"
      cmd_viewport.status_bar_text = "Enable/disable real-time viewport preview"
      cmd_viewport.small_icon = get_icon_path("preview_16.png")
      cmd_viewport.large_icon = get_icon_path("preview_24.png")
      @toolbar.add_item(cmd_viewport)
      
      # Help button
      cmd_help = UI::Command.new("Help") {
        Menu.show_help
      }
      cmd_help.tooltip = "PhotonRender help"
      cmd_help.status_bar_text = "Open PhotonRender documentation"
      cmd_help.small_icon = get_icon_path("help_16.png")
      cmd_help.large_icon = get_icon_path("help_24.png")
      @toolbar.add_item(cmd_help)
      
      # Show toolbar
      @toolbar.show
      
      puts "PhotonRender toolbar created with 8 buttons"
    end
    
    # Get icon path (placeholder - icons would be in plugin directory)
    def self.get_icon_path(icon_name)
      # In a real plugin, icons would be in a subdirectory
      # For testing, we'll use default SketchUp icons or create placeholders
      icon_path = File.join(PhotonRender::PLUGIN_PATH, "icons", icon_name)
      
      # If icon doesn't exist, return nil (SketchUp will use default)
      File.exist?(icon_path) ? icon_path : nil
    end
    
    # Show light menu (context menu for light types)
    def self.show_light_menu
      menu = UI.menu
      menu.add_item("Point Light") { Menu.add_point_light }
      menu.add_item("Directional Light") { Menu.add_directional_light }
      menu.add_item("Area Light") { Menu.add_area_light }
      menu.add_separator
      menu.add_item("Environment Settings...") { Menu.show_environment_settings }
      
      # This would normally show at cursor position
      # For testing, we'll just show the first option
      Menu.add_point_light
    end
    
    # Hide toolbar
    def self.hide
      @toolbar.hide if @toolbar
    end
    
    # Show toolbar
    def self.show
      @toolbar.show if @toolbar
    end
    
    # Get toolbar reference
    def self.toolbar
      @toolbar
    end
    
  end # module Toolbar
  
end # module PhotonRender
