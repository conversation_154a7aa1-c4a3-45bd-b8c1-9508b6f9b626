# src/ruby/photon_render_unified.rb
# Unified PhotonRender plugin for testing

require 'sketchup.rb'
require 'extensions.rb'

module PhotonRender
  
  # Extension information
  PLUGIN_ID = 'photon_render'
  PLUGIN_NAME = 'PhotonRender'
  PLUGIN_VERSION = '0.1.0'
  PLUGIN_DESCRIPTION = 'Professional photorealistic rendering for SketchUp'
  PLUGIN_AUTHOR = 'PhotonRender Team'
  PLUGIN_COPYRIGHT = '© 2024'
  
  # Paths
  PLUGIN_DIR = File.dirname(__FILE__)
  PLUGIN_PATH = File.join(PLUGIN_DIR, 'photon_render')
  
  # Core loaded flag
  @core_loaded = false
  
  def self.core_loaded?
    @core_loaded
  end
  
  # Preferences
  @preferences = {}
  
  def self.preferences
    @preferences
  end
  
  def self.viewport_preview_enabled?
    @preferences[:viewport_preview] || false
  end
  
  def self.auto_save_enabled?
    @preferences[:auto_save_render] || false
  end
  
  # Load preferences
  def self.load_preferences
    @preferences = {
      last_render_settings: Sketchup.read_default(PLUGIN_ID, 'render_settings', {}),
      viewport_preview: Sketchup.read_default(PLUGIN_ID, 'viewport_preview', true),
      auto_save_render: Sketchup.read_default(PLUGIN_ID, 'auto_save', false)
    }
  end
  
  # Save preferences
  def self.save_preferences
    Sketchup.write_default(PLUGIN_ID, 'render_settings', @preferences[:last_render_settings])
    Sketchup.write_default(PLUGIN_ID, 'viewport_preview', @preferences[:viewport_preview])
    Sketchup.write_default(PLUGIN_ID, 'auto_save', @preferences[:auto_save_render])
  end
  
  # Menu module
  module Menu
    def self.create
      # Create main menu
      @main_menu = UI.menu("Plugins").add_submenu("PhotonRender")
      
      # Rendering section
      @main_menu.add_item("Quick Render") { quick_render }
      @main_menu.add_item("Render Settings...") { show_render_settings }
      @main_menu.add_separator
      
      # Scene section
      @main_menu.add_item("Export Scene...") { export_scene }
      @main_menu.add_item("Import Scene...") { import_scene }
      @main_menu.add_separator
      
      # Materials section
      materials_menu = @main_menu.add_submenu("Materials")
      materials_menu.add_item("Material Editor...") { show_material_editor }
      materials_menu.add_item("Material Library...") { show_material_library }
      materials_menu.add_separator
      materials_menu.add_item("Import Materials...") { import_materials }
      materials_menu.add_item("Export Materials...") { export_materials }
      
      # Tools section
      tools_menu = @main_menu.add_submenu("Tools")
      tools_menu.add_item("Viewport Preview") { toggle_viewport_preview }
      tools_menu.add_item("Validate Scene") { validate_scene }
      
      # Help section
      @main_menu.add_separator
      @main_menu.add_item("Preferences...") { show_preferences }
      @main_menu.add_item("About PhotonRender...") { show_about }
      
      puts "PhotonRender menu created"
    end
    
    def self.quick_render
      puts "Quick Render requested"
      UI.messagebox("Quick Render started!\n\nRunning in TESTING MODE\nNo actual rendering will occur.")
    end
    
    def self.show_render_settings
      puts "Render Settings dialog requested"
      UI.messagebox("Render Settings dialog\n\nThis would open the render settings interface.")
    end
    
    def self.export_scene
      puts "Export Scene requested"
      filename = UI.savepanel("Export Scene", "", "*.json")
      if filename
        UI.messagebox("Scene would be exported to:\n#{filename}")
      end
    end
    
    def self.import_scene
      puts "Import Scene requested"
      UI.messagebox("Scene import not yet implemented")
    end
    
    def self.show_material_editor
      puts "Material Editor requested"
      UI.messagebox("Material Editor\n\nThis would open the material editor interface.")
    end
    
    def self.show_material_library
      puts "Material Library requested"
      UI.messagebox("Material Library\n\nThis would show the material library browser.")
    end
    
    def self.import_materials
      puts "Import Materials requested"
      filename = UI.openpanel("Import Materials", "", "*.json")
      if filename
        UI.messagebox("Materials would be imported from:\n#{filename}")
      end
    end
    
    def self.export_materials
      puts "Export Materials requested"
      filename = UI.savepanel("Export Materials", "", "*.json")
      if filename
        UI.messagebox("Materials would be exported to:\n#{filename}")
      end
    end
    
    def self.toggle_viewport_preview
      puts "Toggle Viewport Preview requested"
      current = PhotonRender.viewport_preview_enabled?
      PhotonRender.preferences[:viewport_preview] = !current
      PhotonRender.save_preferences
      UI.messagebox("Viewport Preview #{!current ? 'enabled' : 'disabled'}")
    end
    
    def self.validate_scene
      puts "Validate Scene requested"
      UI.messagebox("Scene validation not yet implemented")
    end
    
    def self.show_preferences
      puts "Preferences requested"
      UI.messagebox("Preferences dialog not yet implemented")
    end
    
    def self.show_about
      puts "About dialog requested"
      UI.messagebox(
        "PhotonRender #{PhotonRender::PLUGIN_VERSION}\n\n" +
        "Professional photorealistic rendering for SketchUp\n\n" +
        "Core loaded: #{PhotonRender.core_loaded?}\n" +
        "Testing mode: #{!PhotonRender.core_loaded?}\n\n" +
        "© 2024 PhotonRender Development Team"
      )
    end
  end
  
  # Toolbar module
  module Toolbar
    def self.create
      @toolbar = UI::Toolbar.new("PhotonRender")
      
      # Quick Render button
      cmd_quick_render = UI::Command.new("Quick Render") {
        Menu.quick_render
      }
      cmd_quick_render.tooltip = "Start quick render with current settings"
      cmd_quick_render.status_bar_text = "Start rendering the current scene"
      @toolbar.add_item(cmd_quick_render)
      
      # Render Settings button
      cmd_render_settings = UI::Command.new("Render Settings") {
        Menu.show_render_settings
      }
      cmd_render_settings.tooltip = "Open render settings dialog"
      cmd_render_settings.status_bar_text = "Configure rendering parameters"
      @toolbar.add_item(cmd_render_settings)
      
      # Material Editor button
      cmd_material_editor = UI::Command.new("Material Editor") {
        Menu.show_material_editor
      }
      cmd_material_editor.tooltip = "Open material editor"
      cmd_material_editor.status_bar_text = "Edit and create materials"
      @toolbar.add_item(cmd_material_editor)
      
      # Export Scene button
      cmd_export_scene = UI::Command.new("Export Scene") {
        Menu.export_scene
      }
      cmd_export_scene.tooltip = "Export scene to file"
      cmd_export_scene.status_bar_text = "Export current scene for rendering"
      @toolbar.add_item(cmd_export_scene)
      
      # About button
      cmd_about = UI::Command.new("About") {
        Menu.show_about
      }
      cmd_about.tooltip = "About PhotonRender"
      cmd_about.status_bar_text = "Show PhotonRender information"
      @toolbar.add_item(cmd_about)
      
      # Show toolbar
      @toolbar.show
      
      puts "PhotonRender toolbar created with 5 buttons"
    end
  end
  
  # Model observer
  class ModelObserver < Sketchup::ModelObserver
    def onTransactionCommit(model)
      puts "Model changed - viewport preview update requested" if PhotonRender.viewport_preview_enabled?
    end
  end
  
  # Plugin initialization
  def self.initialize_plugin
    puts "Initializing PhotonRender plugin..."
    
    # Try to load C++ core (will fail in testing mode)
    begin
      # This would load the C++ extension
      @core_loaded = false # Force testing mode for now
      puts "INFO: Running in TESTING MODE - PhotonCore C++ extension not loaded"
      puts "This is normal for UI testing without full compilation"
    rescue LoadError => e
      puts "INFO: Running in TESTING MODE - #{e.message}"
      @core_loaded = false
    end
    
    # Create menu items
    Menu.create
    puts "Menu created"

    # Create toolbar
    Toolbar.create
    puts "Toolbar created"

    # Register observers
    Sketchup.active_model.add_observer(ModelObserver.new)
    puts "Model observer registered"

    # Load preferences
    load_preferences
    puts "Preferences loaded"

    puts "PhotonRender #{PLUGIN_VERSION} loaded successfully"
    puts "Core loaded: #{core_loaded?}"
    
    # Show welcome message
    show_welcome_message
  end
  
  # Show welcome message
  def self.show_welcome_message
    result = UI.messagebox(
      "PhotonRender #{PLUGIN_VERSION} loaded in TESTING MODE\n\n" +
      "The C++ rendering core is not available, but you can:\n" +
      "• Test the user interface\n" +
      "• Test menu system (15+ commands)\n" +
      "• Test toolbar (5 buttons)\n" +
      "• Export scene geometry\n" +
      "• Configure materials\n\n" +
      "This is normal for development and testing.\n\n" +
      "Continue with testing?", 
      MB_YESNO
    )
    
    if result == IDNO
      puts "User cancelled PhotonRender testing"
    else
      puts "PhotonRender testing mode confirmed by user"
    end
  end

end # module PhotonRender

# Create extension
unless file_loaded?(__FILE__)
  ex = SketchupExtension.new(PhotonRender::PLUGIN_NAME, File.join(File.dirname(__FILE__), 'photon_render_unified'))
  ex.description = PhotonRender::PLUGIN_DESCRIPTION
  ex.version = PhotonRender::PLUGIN_VERSION
  ex.copyright = PhotonRender::PLUGIN_COPYRIGHT
  ex.creator = PhotonRender::PLUGIN_AUTHOR
  Sketchup.register_extension(ex, true)
  file_loaded(__FILE__)
end

# Initialize plugin immediately (since everything is in one file)
PhotonRender.initialize_plugin
