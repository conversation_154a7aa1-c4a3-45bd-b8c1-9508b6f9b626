# tests/CMakeLists.txt
# PhotonRender - Professional Rendering Engine for SketchUp
# Test configuration

cmake_minimum_required(VERSION 3.22)

# GoogleTest is already available from FetchContent in main CMakeLists.txt
# No need to find_package - use modern targets

# Include directories
include_directories(
    ${CMAKE_SOURCE_DIR}/include
    ${CMAKE_SOURCE_DIR}/src
)

# Unit tests
file(GLOB_RECURSE UNIT_TEST_SOURCES "unit/*.cpp")

add_executable(unit_tests ${UNIT_TEST_SOURCES})

target_link_libraries(unit_tests
    photon_core
    gtest
    gtest_main
)

# Add tests to CTest
add_test(NAME UnitTests COMMAND unit_tests)

# Integration tests
file(GLOB_RECURSE INTEGRATION_TEST_SOURCES "integration/*.cpp")

if(INTEGRATION_TEST_SOURCES)
    add_executable(integration_tests ${INTEGRATION_TEST_SOURCES})
    
    target_link_libraries(integration_tests
        photon_core
        gtest
        gtest_main
    )
    
    add_test(NAME IntegrationTests COMMAND integration_tests)
endif()

# Test data
file(COPY scenes DESTINATION ${CMAKE_CURRENT_BINARY_DIR})

# Custom test targets
add_custom_target(run_tests
    COMMAND ${CMAKE_CTEST_COMMAND} --verbose
    DEPENDS unit_tests
    WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
)
