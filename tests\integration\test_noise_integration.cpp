// tests/integration/test_noise_integration.cpp
// PhotonRender - Noise Functions Integration Test
// Tests integration with UV mapping and material system

#include <gtest/gtest.h>
#include <memory>
#include <fstream>
#include "../../src/core/texture/texture.hpp"
#include "../../src/core/texture/uv_mapping.hpp"
#include "../../src/core/math/vec2.hpp"
#include "../../src/core/math/vec3.hpp"
#include "../../src/core/math/color3.hpp"

using namespace photon;

class NoiseIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create different noise textures
        perlinTexture = std::make_unique<NoiseTexture>(NoiseType::PERLIN, 8.0f, 1.0f, FractalParams::defaultParams());
        simplexTexture = std::make_unique<NoiseTexture>(NoiseType::SIMPLEX, 6.0f, 0.8f, FractalParams::defaultParams());
        worleyTexture = std::make_unique<NoiseTexture>(NoiseType::WORLEY, 4.0f, 1.2f, FractalParams::defaultParams());
        
        // Create UV mapping systems
        planarMapping = std::make_unique<UVMapping>(UVMappingMode::PLANAR_XY);
        sphericalMapping = std::make_unique<UVMapping>(UVMappingMode::SPHERICAL);
        cylindricalMapping = std::make_unique<UVMapping>(UVMappingMode::CYLINDRICAL_Y);
    }

    std::unique_ptr<NoiseTexture> perlinTexture;
    std::unique_ptr<NoiseTexture> simplexTexture;
    std::unique_ptr<NoiseTexture> worleyTexture;
    
    std::unique_ptr<UVMapping> planarMapping;
    std::unique_ptr<UVMapping> sphericalMapping;
    std::unique_ptr<UVMapping> cylindricalMapping;
};

// Test 1: UV Mapping Integration
TEST_F(NoiseIntegrationTest, UVMappingIntegration) {
    std::vector<Vec3> testPositions = {
        Vec3(0.0f, 0.0f, 0.0f),
        Vec3(1.0f, 1.0f, 1.0f),
        Vec3(-2.0f, 3.0f, -1.0f),
        Vec3(5.0f, -2.0f, 4.0f)
    };
    
    for (const auto& pos : testPositions) {
        // Generate UV coordinates using different mapping modes
        Vec2 planarUV = planarMapping->generateUV(pos);
        Vec2 sphericalUV = sphericalMapping->generateUV(pos);
        Vec2 cylindricalUV = cylindricalMapping->generateUV(pos);
        
        // Sample noise textures with generated UVs
        float perlinPlanar = perlinTexture->sampleFloat(planarUV);
        float perlinSpherical = perlinTexture->sampleFloat(sphericalUV);
        float perlinCylindrical = perlinTexture->sampleFloat(cylindricalUV);
        
        // All samples should be valid
        EXPECT_GE(perlinPlanar, 0.0f);
        EXPECT_LE(perlinPlanar, 1.0f);
        EXPECT_GE(perlinSpherical, 0.0f);
        EXPECT_LE(perlinSpherical, 1.0f);
        EXPECT_GE(perlinCylindrical, 0.0f);
        EXPECT_LE(perlinCylindrical, 1.0f);
        
        // Different mapping modes should generally produce different results
        bool mappingDifferences = (perlinPlanar != perlinSpherical) || 
                                 (perlinSpherical != perlinCylindrical);
        EXPECT_TRUE(mappingDifferences) << "Different UV mappings should affect noise output";
    }
}

// Test 2: Fractal Noise Complexity
TEST_F(NoiseIntegrationTest, FractalComplexity) {
    // Test different fractal configurations
    std::vector<FractalParams> fractalConfigs = {
        {1, 2.0f, 0.5f, 1.0f},   // Single octave
        {4, 2.0f, 0.5f, 1.0f},   // Standard fractal
        {8, 2.0f, 0.3f, 1.0f},   // High detail
        {3, 3.0f, 0.7f, 2.0f}    // Custom parameters
    };
    
    Vec2 testUV(3.7f, 2.1f);
    
    for (const auto& fractal : fractalConfigs) {
        NoiseTexture fractalNoise(NoiseType::PERLIN, 4.0f, 1.0f, fractal);
        
        float value = fractalNoise.sampleFloat(testUV);
        EXPECT_GE(value, 0.0f) << "Fractal noise should be normalized";
        EXPECT_LE(value, 1.0f) << "Fractal noise should be normalized";
        
        // Higher octave count should generally produce more complex patterns
        // (tested by ensuring the system doesn't crash with high octave counts)
        EXPECT_NO_THROW({
            Color3 color = fractalNoise.sample(testUV);
        }) << "High octave fractal noise should be stable";
    }
}

// Test 3: Texture Wrapping Modes
TEST_F(NoiseIntegrationTest, TextureWrapping) {
    perlinTexture->setWrap(TextureWrap::REPEAT);
    
    // Test coordinates outside [0,1] range
    std::vector<Vec2> wrapTestCoords = {
        Vec2(1.5f, 0.5f),   // U > 1
        Vec2(0.5f, 1.5f),   // V > 1
        Vec2(-0.5f, 0.5f),  // U < 0
        Vec2(0.5f, -0.5f),  // V < 0
        Vec2(2.7f, 3.2f),   // Both > 1
        Vec2(-1.3f, -2.1f)  // Both < 0
    };
    
    for (const auto& coord : wrapTestCoords) {
        EXPECT_NO_THROW({
            float value = perlinTexture->sampleFloat(coord);
            EXPECT_GE(value, 0.0f);
            EXPECT_LE(value, 1.0f);
        }) << "Texture wrapping should handle coordinates outside [0,1]";
    }
    
    // Test different wrap modes
    perlinTexture->setWrap(TextureWrap::CLAMP);
    EXPECT_NO_THROW({
        float clampValue = perlinTexture->sampleFloat(Vec2(1.5f, 1.5f));
    });
    
    perlinTexture->setWrap(TextureWrap::MIRROR);
    EXPECT_NO_THROW({
        float mirrorValue = perlinTexture->sampleFloat(Vec2(1.5f, 1.5f));
    });
}

// Test 4: Multi-Noise Composition
TEST_F(NoiseIntegrationTest, MultiNoiseComposition) {
    Vec2 testUV(1.5f, 2.3f);
    
    // Sample different noise types
    float perlinValue = perlinTexture->sampleFloat(testUV);
    float simplexValue = simplexTexture->sampleFloat(testUV);
    float worleyValue = worleyTexture->sampleFloat(testUV);
    
    // Test composition operations
    float additive = std::clamp(perlinValue + simplexValue * 0.5f, 0.0f, 1.0f);
    float multiplicative = perlinValue * worleyValue;
    float layered = perlinValue * 0.6f + simplexValue * 0.3f + worleyValue * 0.1f;
    
    EXPECT_GE(additive, 0.0f);
    EXPECT_LE(additive, 1.0f);
    EXPECT_GE(multiplicative, 0.0f);
    EXPECT_LE(multiplicative, 1.0f);
    EXPECT_GE(layered, 0.0f);
    EXPECT_LE(layered, 1.0f);
}

// Test 5: Performance with Complex Scenes
TEST_F(NoiseIntegrationTest, ComplexScenePerformance) {
    const int gridSize = 100;
    const float targetTimePerPixel = 5000.0f; // 5μs per pixel
    
    auto start = std::chrono::high_resolution_clock::now();
    
    // Simulate complex scene with multiple noise textures
    for (int y = 0; y < gridSize; ++y) {
        for (int x = 0; x < gridSize; ++x) {
            Vec2 uv(static_cast<float>(x) / gridSize, static_cast<float>(y) / gridSize);
            
            // Sample multiple noise textures (simulating complex material)
            volatile float perlin = perlinTexture->sampleFloat(uv);
            volatile float simplex = simplexTexture->sampleFloat(uv);
            volatile float worley = worleyTexture->sampleFloat(uv);
            
            // Prevent optimization
            (void)perlin;
            (void)simplex;
            (void)worley;
        }
    }
    
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::nanoseconds>(end - start);
    float avgTimePerPixel = static_cast<float>(duration.count()) / (gridSize * gridSize);
    
    EXPECT_LT(avgTimePerPixel, targetTimePerPixel) 
        << "Complex scene performance: " << avgTimePerPixel << "ns per pixel (target: " << targetTimePerPixel << "ns)";
    
    std::cout << "Complex Scene Performance: " << avgTimePerPixel << "ns per pixel\n";
}

// Test 6: Memory Usage Validation
TEST_F(NoiseIntegrationTest, MemoryUsage) {
    // Create multiple noise textures to test memory efficiency
    std::vector<std::unique_ptr<NoiseTexture>> noiseTextures;
    
    for (int i = 0; i < 100; ++i) {
        FractalParams fractal;
        fractal.octaves = 4 + (i % 4);
        fractal.lacunarity = 2.0f + (i % 3) * 0.5f;
        
        noiseTextures.emplace_back(std::make_unique<NoiseTexture>(
            static_cast<NoiseType>(i % 3),
            2.0f + i * 0.1f,
            1.0f,
            fractal
        ));
    }
    
    // Test that all textures work correctly
    Vec2 testUV(0.7f, 0.3f);
    for (const auto& texture : noiseTextures) {
        EXPECT_NO_THROW({
            float value = texture->sampleFloat(testUV);
            EXPECT_GE(value, 0.0f);
            EXPECT_LE(value, 1.0f);
        });
    }
}

// Test 7: Seed Consistency
TEST_F(NoiseIntegrationTest, SeedConsistency) {
    const int seed = 42;
    
    // Create multiple noise textures with same parameters but different seeds
    NoiseTexture noise1(NoiseType::PERLIN, 4.0f, 1.0f, FractalParams::defaultParams());
    NoiseTexture noise2(NoiseType::PERLIN, 4.0f, 1.0f, FractalParams::defaultParams());
    
    noise1.setSeed(seed);
    noise2.setSeed(seed);
    
    // Same seed should produce identical results
    std::vector<Vec2> testCoords = {
        Vec2(0.1f, 0.2f),
        Vec2(1.5f, 2.7f),
        Vec2(-0.3f, 4.1f)
    };
    
    for (const auto& coord : testCoords) {
        float value1 = noise1.sampleFloat(coord);
        float value2 = noise2.sampleFloat(coord);
        EXPECT_FLOAT_EQ(value1, value2) << "Same seed should produce identical noise";
    }
}

// Test 8: Filter Mode Integration
TEST_F(NoiseIntegrationTest, FilterModeIntegration) {
    // Test different texture filtering modes
    perlinTexture->setFilter(TextureFilter::NEAREST);
    float nearestValue = perlinTexture->sampleFloat(Vec2(0.5f, 0.5f));
    
    perlinTexture->setFilter(TextureFilter::BILINEAR);
    float bilinearValue = perlinTexture->sampleFloat(Vec2(0.5f, 0.5f));
    
    // Both should produce valid values
    EXPECT_GE(nearestValue, 0.0f);
    EXPECT_LE(nearestValue, 1.0f);
    EXPECT_GE(bilinearValue, 0.0f);
    EXPECT_LE(bilinearValue, 1.0f);
    
    // For procedural textures, filtering might not make a big difference
    // but the system should handle it gracefully
    EXPECT_NO_THROW({
        Color3 color = perlinTexture->sample(Vec2(0.7f, 0.3f));
    });
}

// Test 9: Edge Case Coordinates
TEST_F(NoiseIntegrationTest, EdgeCaseCoordinates) {
    std::vector<Vec2> edgeCases = {
        Vec2(std::numeric_limits<float>::min(), 0.0f),
        Vec2(std::numeric_limits<float>::max() / 1000.0f, 0.0f),
        Vec2(0.0f, std::numeric_limits<float>::min()),
        Vec2(0.0f, std::numeric_limits<float>::max() / 1000.0f),
        Vec2(1e-6f, 1e-6f),
        Vec2(1e6f, 1e6f)
    };
    
    for (const auto& coord : edgeCases) {
        EXPECT_NO_THROW({
            float value = perlinTexture->sampleFloat(coord);
            EXPECT_GE(value, 0.0f);
            EXPECT_LE(value, 1.0f);
        }) << "Edge case coordinate should be handled: " << coord.x << ", " << coord.y;
    }
}

// Test 10: Integration with UV Transform
TEST_F(NoiseIntegrationTest, UVTransformIntegration) {
    // Create UV transform
    UVTransform transform;
    transform.scale = Vec2(2.0f, 3.0f);
    transform.offset = Vec2(0.5f, 0.25f);
    transform.rotation = 45.0f; // degrees
    
    UVMapping transformMapping(UVMappingMode::VERTEX_UV, transform);
    
    Vec2 originalUV(0.5f, 0.5f);
    Vec2 transformedUV = transformMapping.transformUV(originalUV);
    
    // Sample noise with both original and transformed UVs
    float originalValue = perlinTexture->sampleFloat(originalUV);
    float transformedValue = perlinTexture->sampleFloat(transformedUV);
    
    // Both should be valid
    EXPECT_GE(originalValue, 0.0f);
    EXPECT_LE(originalValue, 1.0f);
    EXPECT_GE(transformedValue, 0.0f);
    EXPECT_LE(transformedValue, 1.0f);
    
    // Transform should generally change the result
    EXPECT_NE(originalValue, transformedValue) << "UV transform should affect noise sampling";
}
