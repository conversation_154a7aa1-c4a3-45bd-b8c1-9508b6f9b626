{"scene": {"name": "Cornell Box", "description": "Classic Cornell Box test scene for path tracing validation"}, "camera": {"type": "perspective", "position": [0.0, 1.0, 3.0], "target": [0.0, 1.0, 0.0], "up": [0.0, 1.0, 0.0], "fov": 45.0, "aspect": 1.0, "near": 0.1, "far": 100.0}, "render_settings": {"width": 512, "height": 512, "samples_per_pixel": 100, "max_bounces": 8, "tile_size": 64, "enable_denoising": false}, "materials": {"white_diffuse": {"type": "diffuse", "albedo": [0.8, 0.8, 0.8], "roughness": 1.0}, "red_diffuse": {"type": "diffuse", "albedo": [0.8, 0.1, 0.1], "roughness": 1.0}, "green_diffuse": {"type": "diffuse", "albedo": [0.1, 0.8, 0.1], "roughness": 1.0}, "area_light": {"type": "emissive", "emission": [15.0, 15.0, 15.0], "albedo": [1.0, 1.0, 1.0]}}, "geometry": [{"name": "floor", "type": "quad", "material": "white_diffuse", "vertices": [[-1.0, 0.0, -1.0], [1.0, 0.0, -1.0], [1.0, 0.0, 1.0], [-1.0, 0.0, 1.0]], "normals": [[0.0, 1.0, 0.0], [0.0, 1.0, 0.0], [0.0, 1.0, 0.0], [0.0, 1.0, 0.0]], "uvs": [[0.0, 0.0], [1.0, 0.0], [1.0, 1.0], [0.0, 1.0]]}, {"name": "ceiling", "type": "quad", "material": "white_diffuse", "vertices": [[-1.0, 2.0, 1.0], [1.0, 2.0, 1.0], [1.0, 2.0, -1.0], [-1.0, 2.0, -1.0]], "normals": [[0.0, -1.0, 0.0], [0.0, -1.0, 0.0], [0.0, -1.0, 0.0], [0.0, -1.0, 0.0]], "uvs": [[0.0, 0.0], [1.0, 0.0], [1.0, 1.0], [0.0, 1.0]]}, {"name": "back_wall", "type": "quad", "material": "white_diffuse", "vertices": [[-1.0, 0.0, -1.0], [-1.0, 2.0, -1.0], [1.0, 2.0, -1.0], [1.0, 0.0, -1.0]], "normals": [[0.0, 0.0, 1.0], [0.0, 0.0, 1.0], [0.0, 0.0, 1.0], [0.0, 0.0, 1.0]], "uvs": [[0.0, 0.0], [0.0, 1.0], [1.0, 1.0], [1.0, 0.0]]}, {"name": "left_wall", "type": "quad", "material": "red_diffuse", "vertices": [[-1.0, 0.0, 1.0], [-1.0, 2.0, 1.0], [-1.0, 2.0, -1.0], [-1.0, 0.0, -1.0]], "normals": [[1.0, 0.0, 0.0], [1.0, 0.0, 0.0], [1.0, 0.0, 0.0], [1.0, 0.0, 0.0]], "uvs": [[0.0, 0.0], [0.0, 1.0], [1.0, 1.0], [1.0, 0.0]]}, {"name": "right_wall", "type": "quad", "material": "green_diffuse", "vertices": [[1.0, 0.0, -1.0], [1.0, 2.0, -1.0], [1.0, 2.0, 1.0], [1.0, 0.0, 1.0]], "normals": [[-1.0, 0.0, 0.0], [-1.0, 0.0, 0.0], [-1.0, 0.0, 0.0], [-1.0, 0.0, 0.0]], "uvs": [[0.0, 0.0], [0.0, 1.0], [1.0, 1.0], [1.0, 0.0]]}, {"name": "area_light", "type": "quad", "material": "area_light", "vertices": [[-0.3, 1.99, -0.3], [0.3, 1.99, -0.3], [0.3, 1.99, 0.3], [-0.3, 1.99, 0.3]], "normals": [[0.0, -1.0, 0.0], [0.0, -1.0, 0.0], [0.0, -1.0, 0.0], [0.0, -1.0, 0.0]], "uvs": [[0.0, 0.0], [1.0, 0.0], [1.0, 1.0], [0.0, 1.0]]}], "lights": [{"name": "ceiling_light", "type": "area", "geometry": "area_light", "emission": [15.0, 15.0, 15.0], "samples": 16}], "environment": {"type": "constant", "color": [0.0, 0.0, 0.0]}}