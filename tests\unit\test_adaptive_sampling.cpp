// tests/unit/test_adaptive_sampling.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Adaptive Sampling System Tests

#include <gtest/gtest.h>
#include "../../src/core/sampler/adaptive_sampler.hpp"
#include "../../src/core/math/vec3.hpp"
#include <vector>
#include <random>

using namespace photon;

class AdaptiveSamplingTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Standard test parameters
        params.enabled = true;
        params.convergenceThreshold = 0.01f;
        params.varianceThreshold = 0.05f;
        params.minSamples = 4;
        params.maxSamples = 64;
        params.checkInterval = 4;
        params.useVarianceAnalysis = true;
        params.useNoiseAnalysis = true;
        
        // Initialize sampler
        sampler.initialize(testWidth, testHeight, params);
    }
    
    void TearDown() override {
        sampler.reset();
    }
    
    // Test data
    static constexpr int testWidth = 32;
    static constexpr int testHeight = 32;
    AdaptiveSampler sampler;
    AdaptiveSamplingParams params;
    std::mt19937 rng{42}; // Fixed seed for reproducible tests
};

// Test 1: Basic Initialization
TEST_F(AdaptiveSamplingTest, BasicInitialization) {
    EXPECT_EQ(sampler.getCurrentSampleCount(0, 0), 0);
    EXPECT_FALSE(sampler.hasConverged(0, 0));
    EXPECT_EQ(sampler.getConvergenceProgress(), 0.0f);
    EXPECT_EQ(sampler.getAverageSamplesPerPixel(), 0.0f);
}

// Test 2: Pixel Variance Statistics
TEST_F(AdaptiveSamplingTest, PixelVarianceStats) {
    PixelVarianceStats stats;
    
    // Test initial state
    EXPECT_EQ(stats.sampleCount, 0);
    EXPECT_FALSE(stats.converged);
    EXPECT_EQ(stats.mean, Color3(0.0f, 0.0f, 0.0f));
    
    // Add samples
    stats.updateWithSample(Color3(1.0f, 0.5f, 0.2f));
    EXPECT_EQ(stats.sampleCount, 1);
    EXPECT_EQ(stats.mean, Color3(1.0f, 0.5f, 0.2f));
    
    stats.updateWithSample(Color3(0.8f, 0.6f, 0.3f));
    EXPECT_EQ(stats.sampleCount, 2);
    
    // Check mean calculation
    Color3 expectedMean = (Color3(1.0f, 0.5f, 0.2f) + Color3(0.8f, 0.6f, 0.3f)) / 2.0f;
    EXPECT_NEAR(stats.mean.r, expectedMean.r, 1e-6f);
    EXPECT_NEAR(stats.mean.g, expectedMean.g, 1e-6f);
    EXPECT_NEAR(stats.mean.b, expectedMean.b, 1e-6f);
}

// Test 3: Convergence Detection
TEST_F(AdaptiveSamplingTest, ConvergenceDetection) {
    int x = 5, y = 5;
    
    // Add consistent samples (should converge quickly)
    Color3 consistentColor(0.5f, 0.5f, 0.5f);
    for (int i = 0; i < 16; i++) {
        // Add small noise to make it realistic
        std::normal_distribution<float> noise(0.0f, 0.001f);
        Color3 sample = consistentColor + Color3(noise(rng), noise(rng), noise(rng));
        
        bool needsMore = sampler.updatePixel(x, y, sample);
        
        if (i >= params.minSamples - 1 && (i + 1) % params.checkInterval == 0) {
            // Should converge with consistent samples
            if (sampler.hasConverged(x, y)) {
                EXPECT_LT(i, params.maxSamples);
                break;
            }
        }
    }
    
    EXPECT_TRUE(sampler.hasConverged(x, y));
}

// Test 4: High Variance Samples
TEST_F(AdaptiveSamplingTest, HighVarianceSamples) {
    int x = 10, y = 10;
    
    // Add highly variable samples (should not converge quickly)
    std::uniform_real_distribution<float> dist(0.0f, 1.0f);
    
    for (int i = 0; i < params.maxSamples; i++) {
        Color3 sample(dist(rng), dist(rng), dist(rng));
        bool needsMore = sampler.updatePixel(x, y, sample);
        
        if (i >= params.minSamples - 1 && (i + 1) % params.checkInterval == 0) {
            // High variance should delay convergence
            if (i < params.maxSamples / 2) {
                EXPECT_FALSE(sampler.hasConverged(x, y));
            }
        }
    }
}

// Test 5: Noise Analysis
TEST_F(AdaptiveSamplingTest, NoiseAnalysis) {
    NoiseAnalysis noise;
    
    // Test initial state
    EXPECT_EQ(noise.noiseLevel, 0.0f);
    EXPECT_EQ(noise.samplesForTarget, 0);
    EXPECT_FALSE(noise.isConverging);
    
    // Update with decreasing variance (converging)
    noise.updateNoise(1.0f, 4);
    float firstNoise = noise.noiseLevel;
    EXPECT_GT(firstNoise, 0.0f);
    
    noise.updateNoise(0.5f, 8);
    EXPECT_LT(noise.noiseLevel, firstNoise);
    EXPECT_TRUE(noise.isConverging);
    EXPECT_GT(noise.noiseReduction, 0.0f);
}

// Test 6: Optimal Sample Count Estimation
TEST_F(AdaptiveSamplingTest, OptimalSampleCount) {
    int x = 15, y = 15;
    
    // Add some samples
    for (int i = 0; i < 8; i++) {
        Color3 sample(0.5f + 0.1f * std::sin(i), 0.5f, 0.5f);
        sampler.updatePixel(x, y, sample);
    }
    
    int optimalCount = sampler.getOptimalSampleCount(x, y);
    int currentCount = sampler.getCurrentSampleCount(x, y);
    
    EXPECT_GE(optimalCount, params.minSamples);
    EXPECT_LE(optimalCount, params.maxSamples);
    EXPECT_EQ(currentCount, 8);
}

// Test 7: Spatial Coherence
TEST_F(AdaptiveSamplingTest, SpatialCoherence) {
    // Enable spatial adaptation
    params.spatialAdaptation = true;
    params.spatialRadius = 1;
    sampler.setParameters(params);
    
    // Add similar samples to neighboring pixels
    Color3 baseColor(0.7f, 0.3f, 0.1f);
    for (int y = 5; y <= 7; y++) {
        for (int x = 5; x <= 7; x++) {
            for (int s = 0; s < 8; s++) {
                Color3 sample = baseColor + Color3(0.01f * s, 0.01f * s, 0.01f * s);
                sampler.updatePixel(x, y, sample);
            }
        }
    }
    
    // Center pixel should benefit from spatial coherence
    const PixelVarianceStats& centerStats = sampler.getPixelStats(6, 6);
    EXPECT_GT(centerStats.sampleCount, 0);
}

// Test 8: Performance Metrics
TEST_F(AdaptiveSamplingTest, PerformanceMetrics) {
    // Add samples to multiple pixels
    for (int y = 0; y < 8; y++) {
        for (int x = 0; x < 8; x++) {
            // Vary convergence difficulty
            int targetSamples = params.minSamples + (x + y) % 8;
            
            for (int s = 0; s < targetSamples; s++) {
                Color3 sample(0.5f, 0.5f, 0.5f);
                sampler.updatePixel(x, y, sample);
            }
        }
    }
    
    float progress = sampler.getConvergenceProgress();
    float avgSPP = sampler.getAverageSamplesPerPixel();
    float efficiency = sampler.getEfficiency();
    
    EXPECT_GE(progress, 0.0f);
    EXPECT_LE(progress, 1.0f);
    EXPECT_GT(avgSPP, 0.0f);
    EXPECT_GT(efficiency, 0.0f);
}

// Test 9: Parameter Updates
TEST_F(AdaptiveSamplingTest, ParameterUpdates) {
    AdaptiveSamplingParams newParams = params;
    newParams.convergenceThreshold = 0.005f; // Stricter threshold
    newParams.maxSamples = 128;
    
    sampler.setParameters(newParams);
    
    const AdaptiveSamplingParams& currentParams = sampler.getParameters();
    EXPECT_EQ(currentParams.convergenceThreshold, 0.005f);
    EXPECT_EQ(currentParams.maxSamples, 128);
}

// Test 10: Edge Cases
TEST_F(AdaptiveSamplingTest, EdgeCases) {
    // Test invalid pixel coordinates
    EXPECT_FALSE(sampler.updatePixel(-1, 0, Color3(1.0f, 1.0f, 1.0f)));
    EXPECT_FALSE(sampler.updatePixel(0, -1, Color3(1.0f, 1.0f, 1.0f)));
    EXPECT_FALSE(sampler.updatePixel(testWidth, 0, Color3(1.0f, 1.0f, 1.0f)));
    EXPECT_FALSE(sampler.updatePixel(0, testHeight, Color3(1.0f, 1.0f, 1.0f)));
    
    // Test with zero/extreme colors
    EXPECT_TRUE(sampler.updatePixel(0, 0, Color3(0.0f, 0.0f, 0.0f)));
    EXPECT_TRUE(sampler.updatePixel(1, 1, Color3(1000.0f, 1000.0f, 1000.0f)));
    
    // Test reset functionality
    sampler.updatePixel(2, 2, Color3(0.5f, 0.5f, 0.5f));
    EXPECT_GT(sampler.getCurrentSampleCount(2, 2), 0);
    
    sampler.reset();
    EXPECT_EQ(sampler.getCurrentSampleCount(2, 2), 0);
    EXPECT_EQ(sampler.getConvergenceProgress(), 0.0f);
}

// Test 11: Utility Functions
TEST_F(AdaptiveSamplingTest, UtilityFunctions) {
    // Test optimal sample calculation
    int optimal = AdaptiveSamplingUtils::calculateOptimalSamples(0.1f, 0.01f, 16);
    EXPECT_GT(optimal, 16); // Should need more samples for lower variance
    
    // Test convergence rate estimation
    std::vector<float> convergingSamples = {1.0f, 0.5f, 0.25f, 0.125f, 0.0625f};
    float rate = AdaptiveSamplingUtils::estimateConvergenceRate(convergingSamples);
    EXPECT_GT(rate, 0.0f); // Should detect convergence
    
    // Test noise reduction calculation
    float reduction = AdaptiveSamplingUtils::calculateNoiseReduction(1.0f, 0.5f);
    EXPECT_NEAR(reduction, 0.5f, 1e-6f);
}

// Performance benchmark test
TEST_F(AdaptiveSamplingTest, PerformanceBenchmark) {
    auto start = std::chrono::high_resolution_clock::now();
    
    // Simulate realistic rendering workload
    for (int y = 0; y < testHeight; y++) {
        for (int x = 0; x < testWidth; x++) {
            for (int s = 0; s < 32; s++) {
                Color3 sample(
                    0.5f + 0.1f * std::sin(x * 0.1f + s * 0.01f),
                    0.5f + 0.1f * std::cos(y * 0.1f + s * 0.01f),
                    0.5f
                );
                sampler.updatePixel(x, y, sample);
                
                if (sampler.hasConverged(x, y)) break;
            }
        }
    }
    
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
    
    // Performance should be reasonable (< 100ms for this test)
    EXPECT_LT(duration.count(), 100000);
    
    std::cout << "Adaptive sampling performance: " << duration.count() << " microseconds" << std::endl;
    std::cout << "Average SPP: " << sampler.getAverageSamplesPerPixel() << std::endl;
    std::cout << "Efficiency: " << sampler.getEfficiency() << "x" << std::endl;
    std::cout << "Convergence: " << (sampler.getConvergenceProgress() * 100.0f) << "%" << std::endl;
}
