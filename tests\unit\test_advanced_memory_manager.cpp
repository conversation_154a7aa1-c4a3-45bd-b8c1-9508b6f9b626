// tests/unit/test_advanced_memory_manager.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Advanced Memory Management System Tests

#include <gtest/gtest.h>
#include "../../src/core/memory/advanced_memory_manager.hpp"
#include <vector>
#include <thread>
#include <chrono>

using namespace photon;

class AdvancedMemoryManagerTest : public ::testing::Test {
protected:
    void SetUp() override {
        manager = &AdvancedMemoryManager::getInstance();
        manager->initialize();
    }
    
    void TearDown() override {
        manager->shutdown();
    }
    
    AdvancedMemoryManager* manager;
};

// Test 1: Basic Initialization
TEST_F(AdvancedMemoryManagerTest, BasicInitialization) {
    auto stats = manager->getStatistics();
    EXPECT_EQ(stats.allocation_count, 0);
    EXPECT_EQ(stats.current_usage, 0);
    
    auto pool_stats = manager->getPoolStatistics();
    EXPECT_GT(pool_stats.size(), 0);
    
    // Check that all expected pools are created
    EXPECT_TRUE(pool_stats.find(PoolType::SMALL_OBJECTS) != pool_stats.end());
    EXPECT_TRUE(pool_stats.find(PoolType::MEDIUM_OBJECTS) != pool_stats.end());
    EXPECT_TRUE(pool_stats.find(PoolType::LARGE_OBJECTS) != pool_stats.end());
    EXPECT_TRUE(pool_stats.find(PoolType::TEXTURE_DATA) != pool_stats.end());
}

// Test 2: Basic Allocation and Deallocation
TEST_F(AdvancedMemoryManagerTest, BasicAllocation) {
    // Test small allocation
    void* ptr1 = manager->allocate(256, 16, "test_small");
    EXPECT_NE(ptr1, nullptr);
    
    // Test medium allocation
    void* ptr2 = manager->allocate(32 * 1024, 16, "test_medium");
    EXPECT_NE(ptr2, nullptr);
    
    // Test large allocation
    void* ptr3 = manager->allocate(512 * 1024, 16, "test_large");
    EXPECT_NE(ptr3, nullptr);
    
    // Check statistics
    auto stats = manager->getStatistics();
    EXPECT_EQ(stats.allocation_count, 3);
    EXPECT_GT(stats.current_usage, 0);
    
    // Deallocate
    manager->deallocate(ptr1);
    manager->deallocate(ptr2);
    manager->deallocate(ptr3);
    
    stats = manager->getStatistics();
    EXPECT_EQ(stats.deallocation_count, 3);
}

// Test 3: Typed Allocation
TEST_F(AdvancedMemoryManagerTest, TypedAllocation) {
    // Allocate array of integers
    int* int_array = manager->allocateTyped<int>(100, "int_array");
    EXPECT_NE(int_array, nullptr);
    
    // Test writing to allocated memory
    for (int i = 0; i < 100; i++) {
        int_array[i] = i;
    }
    
    // Verify data
    for (int i = 0; i < 100; i++) {
        EXPECT_EQ(int_array[i], i);
    }
    
    // Allocate array of floats
    float* float_array = manager->allocateTyped<float>(50, "float_array");
    EXPECT_NE(float_array, nullptr);
    
    // Deallocate
    manager->deallocateTyped(int_array);
    manager->deallocateTyped(float_array);
    
    auto stats = manager->getStatistics();
    EXPECT_EQ(stats.allocation_count, 2);
    EXPECT_EQ(stats.deallocation_count, 2);
}

// Test 4: Pool-specific Allocation
TEST_F(AdvancedMemoryManagerTest, PoolSpecificAllocation) {
    // Allocate from texture pool
    void* texture_ptr = manager->allocateFromPool(PoolType::TEXTURE_DATA, 1024 * 1024, 256);
    EXPECT_NE(texture_ptr, nullptr);
    
    // Allocate from geometry pool
    void* geometry_ptr = manager->allocateFromPool(PoolType::GEOMETRY_DATA, 512 * 1024, 128);
    EXPECT_NE(geometry_ptr, nullptr);
    
    // Check pool statistics
    auto pool_stats = manager->getPoolStatistics();
    auto texture_stats = pool_stats[PoolType::TEXTURE_DATA];
    auto geometry_stats = pool_stats[PoolType::GEOMETRY_DATA];
    
    EXPECT_GT(texture_stats.allocation_count, 0);
    EXPECT_GT(geometry_stats.allocation_count, 0);
    
    // Deallocate
    manager->deallocate(texture_ptr);
    manager->deallocate(geometry_ptr);
}

// Test 5: Memory Guard RAII
TEST_F(AdvancedMemoryManagerTest, MemoryGuard) {
    {
        // Create memory guard
        auto guard = PHOTON_MEMORY_GUARD(int, 1000);
        EXPECT_NE(guard.get(), nullptr);
        EXPECT_EQ(guard.size(), 1000);
        
        // Test array access
        for (size_t i = 0; i < guard.size(); i++) {
            guard[i] = static_cast<int>(i);
        }
        
        // Verify data
        for (size_t i = 0; i < guard.size(); i++) {
            EXPECT_EQ(guard[i], static_cast<int>(i));
        }
        
        // Memory should be automatically freed when guard goes out of scope
    }
    
    // Check that deallocation happened
    auto stats = manager->getStatistics();
    EXPECT_GT(stats.deallocation_count, 0);
}

// Test 6: Large Allocation Handling
TEST_F(AdvancedMemoryManagerTest, LargeAllocations) {
    std::vector<void*> large_ptrs;
    
    // Allocate several large blocks
    for (int i = 0; i < 5; i++) {
        void* ptr = manager->allocate(2 * 1024 * 1024, 16, "large_block_" + std::to_string(i));
        EXPECT_NE(ptr, nullptr);
        large_ptrs.push_back(ptr);
    }
    
    auto stats = manager->getStatistics();
    EXPECT_EQ(stats.allocation_count, 5);
    EXPECT_GT(stats.current_usage, 10 * 1024 * 1024); // Should be > 10MB
    
    // Deallocate all
    for (void* ptr : large_ptrs) {
        manager->deallocate(ptr);
    }
    
    stats = manager->getStatistics();
    EXPECT_EQ(stats.deallocation_count, 5);
}

// Test 7: Garbage Collection
TEST_F(AdvancedMemoryManagerTest, GarbageCollection) {
    // Allocate and deallocate many small objects
    for (int i = 0; i < 100; i++) {
        void* ptr = manager->allocate(1024, 16, "temp_object");
        if (i % 2 == 0) {
            manager->deallocate(ptr); // Deallocate every other allocation
        }
    }
    
    auto stats_before = manager->getStatistics();
    
    // Run garbage collection
    size_t reclaimed = manager->runGarbageCollection();
    
    auto stats_after = manager->getStatistics();
    
    // GC should have run (reclaimed might be 0 if no expired blocks)
    EXPECT_GE(reclaimed, 0);
    EXPECT_GT(stats_after.gc_runs, stats_before.gc_runs);
}

// Test 8: Memory Statistics and Reporting
TEST_F(AdvancedMemoryManagerTest, StatisticsAndReporting) {
    // Allocate various sizes to generate statistics
    std::vector<void*> ptrs;
    
    ptrs.push_back(manager->allocate(512, 16, "small"));
    ptrs.push_back(manager->allocate(16 * 1024, 16, "medium"));
    ptrs.push_back(manager->allocate(256 * 1024, 16, "large"));
    
    auto stats = manager->getStatistics();
    EXPECT_EQ(stats.allocation_count, 3);
    EXPECT_GT(stats.current_usage, 0);
    EXPECT_GT(stats.total_allocated, 0);
    
    // Test hit ratio calculation
    double hit_ratio = stats.getHitRatio();
    EXPECT_GE(hit_ratio, 0.0);
    EXPECT_LE(hit_ratio, 1.0);
    
    // Generate memory report
    std::string report = manager->generateMemoryReport();
    EXPECT_FALSE(report.empty());
    EXPECT_TRUE(report.find("Advanced Memory Manager Report") != std::string::npos);
    EXPECT_TRUE(report.find("Global Statistics") != std::string::npos);
    EXPECT_TRUE(report.find("Pool Statistics") != std::string::npos);
    
    // Cleanup
    for (void* ptr : ptrs) {
        manager->deallocate(ptr);
    }
}

// Test 9: Thread Safety
TEST_F(AdvancedMemoryManagerTest, ThreadSafety) {
    const int num_threads = 4;
    const int allocations_per_thread = 100;
    std::vector<std::thread> threads;
    std::vector<std::vector<void*>> thread_ptrs(num_threads);
    
    // Launch threads that allocate memory
    for (int t = 0; t < num_threads; t++) {
        threads.emplace_back([this, t, allocations_per_thread, &thread_ptrs]() {
            for (int i = 0; i < allocations_per_thread; i++) {
                size_t size = 1024 + (i % 1024); // Variable sizes
                void* ptr = manager->allocate(size, 16, "thread_" + std::to_string(t));
                if (ptr) {
                    thread_ptrs[t].push_back(ptr);
                    
                    // Write some data to verify memory integrity
                    memset(ptr, t + i, size);
                }
                
                // Occasionally deallocate some memory
                if (i % 10 == 0 && !thread_ptrs[t].empty()) {
                    manager->deallocate(thread_ptrs[t].back());
                    thread_ptrs[t].pop_back();
                }
            }
        });
    }
    
    // Wait for all threads to complete
    for (auto& thread : threads) {
        thread.join();
    }
    
    // Cleanup remaining allocations
    for (int t = 0; t < num_threads; t++) {
        for (void* ptr : thread_ptrs[t]) {
            manager->deallocate(ptr);
        }
    }
    
    auto stats = manager->getStatistics();
    EXPECT_GT(stats.allocation_count, 0);
    EXPECT_GT(stats.deallocation_count, 0);
}

// Test 10: Memory Pool Configuration
TEST_F(AdvancedMemoryManagerTest, PoolConfiguration) {
    // Test pool configuration
    PoolConfiguration config(8192, 16, 256, 2, true, std::chrono::milliseconds(1000));
    
    manager->configurePool(PoolType::TEMPORARY, config);
    
    // Allocate from configured pool
    void* ptr = manager->allocateFromPool(PoolType::TEMPORARY, 4096, 16);
    EXPECT_NE(ptr, nullptr);
    
    manager->deallocate(ptr);
}

// Test 11: Allocation Strategy
TEST_F(AdvancedMemoryManagerTest, AllocationStrategy) {
    // Test different allocation strategies
    manager->setAllocationStrategy(AllocationStrategy::POOL_BASED);
    
    void* ptr1 = manager->allocate(1024, 16, "pool_based");
    EXPECT_NE(ptr1, nullptr);
    
    manager->setAllocationStrategy(AllocationStrategy::HYBRID);
    
    void* ptr2 = manager->allocate(1024, 16, "hybrid");
    EXPECT_NE(ptr2, nullptr);
    
    manager->deallocate(ptr1);
    manager->deallocate(ptr2);
}

// Test 12: Edge Cases and Error Handling
TEST_F(AdvancedMemoryManagerTest, EdgeCases) {
    // Test zero-size allocation
    void* ptr1 = manager->allocate(0, 16, "zero_size");
    EXPECT_EQ(ptr1, nullptr);
    
    // Test null pointer deallocation
    manager->deallocate(nullptr); // Should not crash
    
    // Test double deallocation
    void* ptr2 = manager->allocate(1024, 16, "double_free_test");
    EXPECT_NE(ptr2, nullptr);
    
    manager->deallocate(ptr2);
    manager->deallocate(ptr2); // Should not crash
    
    // Test very large allocation
    void* ptr3 = manager->allocate(SIZE_MAX, 16, "huge_allocation");
    // This might return nullptr depending on system memory
    if (ptr3) {
        manager->deallocate(ptr3);
    }
}

// Performance benchmark test
TEST_F(AdvancedMemoryManagerTest, PerformanceBenchmark) {
    auto start = std::chrono::high_resolution_clock::now();
    
    const int num_allocations = 10000;
    std::vector<void*> ptrs;
    ptrs.reserve(num_allocations);
    
    // Allocation benchmark
    for (int i = 0; i < num_allocations; i++) {
        size_t size = 64 + (i % 1024); // Variable sizes 64-1088 bytes
        void* ptr = manager->allocate(size, 16, "benchmark");
        if (ptr) {
            ptrs.push_back(ptr);
        }
    }
    
    auto mid = std::chrono::high_resolution_clock::now();
    
    // Deallocation benchmark
    for (void* ptr : ptrs) {
        manager->deallocate(ptr);
    }
    
    auto end = std::chrono::high_resolution_clock::now();
    
    auto alloc_time = std::chrono::duration_cast<std::chrono::microseconds>(mid - start);
    auto dealloc_time = std::chrono::duration_cast<std::chrono::microseconds>(end - mid);
    auto total_time = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
    
    std::cout << "Memory Manager Performance:" << std::endl;
    std::cout << "  Allocations: " << num_allocations << std::endl;
    std::cout << "  Allocation time: " << alloc_time.count() << " μs" << std::endl;
    std::cout << "  Deallocation time: " << dealloc_time.count() << " μs" << std::endl;
    std::cout << "  Total time: " << total_time.count() << " μs" << std::endl;
    std::cout << "  Avg per allocation: " << (total_time.count() / num_allocations) << " μs" << std::endl;
    
    // Performance should be reasonable
    EXPECT_LT(total_time.count(), 1000000); // Less than 1 second for 10k allocations
    
    auto stats = manager->getStatistics();
    std::cout << "  Hit ratio: " << (stats.getHitRatio() * 100.0) << "%" << std::endl;
    std::cout << "  Peak usage: " << (stats.peak_usage / 1024 / 1024) << " MB" << std::endl;
}
