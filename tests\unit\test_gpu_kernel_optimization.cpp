// tests/unit/test_gpu_kernel_optimization.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// GPU Kernel Optimization System Tests

#include <gtest/gtest.h>

#ifdef CUDA_ENABLED
#include "../../src/gpu/cuda/kernel_optimizer.hpp"
#include "../../src/gpu/cuda/optimized_kernels.cuh"
#include <cuda_runtime.h>
#include <vector>
#include <chrono>

using namespace photon::gpu;

class GPUKernelOptimizationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Check if CUDA is available
        int device_count;
        cudaError_t error = cudaGetDeviceCount(&device_count);
        
        if (error != cudaSuccess || device_count == 0) {
            GTEST_SKIP() << "CUDA not available, skipping GPU tests";
        }
        
        // Initialize CUDA device
        cudaSetDevice(0);
        cudaGetDeviceProperties(&device_props, 0);
        
        // Initialize optimizer
        optimizer = std::make_unique<KernelOptimizer>();
        optimizer->initialize();
        
        // Setup test parameters
        setupTestParameters();
    }
    
    void TearDown() override {
        if (optimizer) {
            optimizer->shutdown();
        }
        
        // Cleanup CUDA
        cudaDeviceReset();
    }
    
    void setupTestParameters() {
        // Setup basic render parameters
        test_params.width = 512;
        test_params.height = 512;
        test_params.samples_per_pixel = 16;
        
        // Camera setup
        test_params.camera_pos = make_float3(0.0f, 0.0f, 5.0f);
        test_params.camera_dir = make_float3(0.0f, 0.0f, -1.0f);
        test_params.camera_up = make_float3(0.0f, 1.0f, 0.0f);
        test_params.fov = 45.0f;
        
        // Optimization setup
        test_params.strategy = RTCoreStrategy::ADAPTIVE;
        test_params.enable_profiling = true;
        
        // Wavefront configuration
        test_params.wavefront_config.max_path_length = 8;
        test_params.wavefront_config.wavefront_size = 1024 * 32;
        test_params.wavefront_config.tile_size = 16;
        test_params.wavefront_config.use_rt_cores = true;
        
        // RT core configuration
        test_params.rt_config.rt_core_count = device_props.multiProcessorCount;
        test_params.rt_config.sm_count = device_props.multiProcessorCount;
        test_params.rt_config.max_threads_per_sm = device_props.maxThreadsPerMultiProcessor;
        test_params.rt_config.shared_memory_per_sm = device_props.sharedMemPerMultiprocessor;
    }
    
    cudaDeviceProp device_props;
    std::unique_ptr<KernelOptimizer> optimizer;
    OptimizedRenderParams test_params;
};

// Test 1: Basic Initialization
TEST_F(GPUKernelOptimizationTest, BasicInitialization) {
    EXPECT_TRUE(optimizer != nullptr);
    EXPECT_EQ(optimizer->getOptimizationStrategy(), OptimizationStrategy::ADAPTIVE);
    
    // Test device detection
    EXPECT_GT(device_props.multiProcessorCount, 0);
    EXPECT_GT(device_props.maxThreadsPerMultiProcessor, 0);
    EXPECT_GT(device_props.sharedMemPerMultiprocessor, 0);
}

// Test 2: Workload Characteristics Analysis
TEST_F(GPUKernelOptimizationTest, WorkloadAnalysis) {
    WorkloadCharacteristics workload;
    workload.analyze(test_params);
    
    EXPECT_EQ(workload.image_width, test_params.width);
    EXPECT_EQ(workload.image_height, test_params.height);
    EXPECT_EQ(workload.samples_per_pixel, test_params.samples_per_pixel);
    EXPECT_EQ(workload.max_path_length, test_params.wavefront_config.max_path_length);
    
    float complexity = workload.getComplexityScore();
    EXPECT_GE(complexity, 0.0f);
    EXPECT_LE(complexity, 10.0f);
}

// Test 3: Optimization Strategy Selection
TEST_F(GPUKernelOptimizationTest, OptimizationStrategies) {
    WorkloadCharacteristics workload;
    workload.analyze(test_params);
    
    // Test different optimization strategies
    std::vector<OptimizationStrategy> strategies = {
        OptimizationStrategy::LATENCY_FOCUSED,
        OptimizationStrategy::THROUGHPUT_FOCUSED,
        OptimizationStrategy::BALANCED,
        OptimizationStrategy::ADAPTIVE
    };
    
    for (auto strategy : strategies) {
        OptimizationParams params = optimizer->optimizeForWorkload(workload, strategy);
        
        // Verify optimization parameters are reasonable
        EXPECT_GT(params.optimal_block_size.x, 0);
        EXPECT_LE(params.optimal_block_size.x, 1024);
        EXPECT_GT(params.optimal_grid_size.x, 0);
        EXPECT_GE(params.shared_memory_per_block, 0);
        EXPECT_LE(params.shared_memory_per_block, device_props.sharedMemPerBlock);
    }
}

// Test 4: Block Size Optimization
TEST_F(GPUKernelOptimizationTest, BlockSizeOptimization) {
    // Test optimal block size calculation
    dim3 block_size = KernelOptimizationUtils::calculateOptimalBlockSize(
        device_props, 1024, 32);
    
    EXPECT_GT(block_size.x, 0);
    EXPECT_LE(block_size.x * block_size.y * block_size.z, 1024);
    
    // Test occupancy estimation
    float occupancy = KernelOptimizationUtils::estimateOccupancy(
        device_props, block_size, 1024, 32);
    
    EXPECT_GE(occupancy, 0.0f);
    EXPECT_LE(occupancy, 1.0f);
}

// Test 5: Memory Access Analysis
TEST_F(GPUKernelOptimizationTest, MemoryAccessAnalysis) {
    auto analysis = optimizer->analyzeMemoryAccess(test_params);
    
    EXPECT_GE(analysis.coalescing_efficiency, 0.0f);
    EXPECT_LE(analysis.coalescing_efficiency, 1.0f);
    EXPECT_GE(analysis.cache_hit_ratio, 0.0f);
    EXPECT_LE(analysis.cache_hit_ratio, 1.0f);
    EXPECT_GE(analysis.bandwidth_utilization, 0.0f);
    EXPECT_LE(analysis.bandwidth_utilization, 1.0f);
    EXPECT_GE(analysis.memory_transactions, 0);
    
    // Should have optimization suggestions
    EXPECT_FALSE(analysis.optimization_suggestions.empty());
}

// Test 6: RT Core Benchmarking
TEST_F(GPUKernelOptimizationTest, RTCoreBenchmarking) {
    RTCoreCounters counters = optimizer->benchmarkRTCores(test_params, 100);
    
    EXPECT_GE(counters.rays_processed, 0);
    EXPECT_GE(counters.intersections_found, 0);
    EXPECT_GE(counters.cache_hits, 0);
    EXPECT_GE(counters.cache_misses, 0);
    EXPECT_GE(counters.utilization_percentage, 0.0f);
    EXPECT_LE(counters.utilization_percentage, 100.0f);
    EXPECT_GE(counters.throughput_mrays_per_sec, 0.0f);
}

// Test 7: Kernel Profiling
TEST_F(GPUKernelOptimizationTest, KernelProfiling) {
    // Profile a simple kernel
    KernelProfile profile = optimizer->profileKernel(
        "generatePrimaryRaysOptimized", test_params, 5);
    
    EXPECT_FALSE(profile.kernel_name.empty());
    EXPECT_GT(profile.execution_time_ms, 0.0f);
    EXPECT_GE(profile.occupancy_percentage, 0.0f);
    EXPECT_LE(profile.occupancy_percentage, 100.0f);
    EXPECT_GE(profile.active_warps, 0);
    EXPECT_GE(profile.active_blocks, 0);
    EXPECT_GE(profile.efficiency_score, 0.0f);
    EXPECT_LE(profile.efficiency_score, 1.0f);
}

// Test 8: Auto-tuning
TEST_F(GPUKernelOptimizationTest, AutoTuning) {
    OptimizationParams tuned_params = optimizer->autoTuneKernel(
        "intersectRaysRTCore", test_params, 1000.0f);
    
    // Verify tuned parameters are reasonable
    EXPECT_GT(tuned_params.optimal_block_size.x, 0);
    EXPECT_LE(tuned_params.optimal_block_size.x, 1024);
    EXPECT_GE(tuned_params.shared_memory_per_block, 0);
    EXPECT_LE(tuned_params.shared_memory_per_block, device_props.sharedMemPerBlock);
    
    // Wavefront configuration should be optimized
    EXPECT_GT(tuned_params.wavefront_config.wavefront_size, 0);
    EXPECT_LE(tuned_params.wavefront_config.max_path_length, 32);
}

// Test 9: Dynamic Load Balancing
TEST_F(GPUKernelOptimizationTest, DynamicLoadBalancing) {
    // Create test tile complexities
    std::vector<float> tile_complexities = {
        1.0f, 2.5f, 0.8f, 3.2f, 1.5f, 0.5f, 2.8f, 1.2f
    };
    
    OptimizedRenderParams params = test_params;
    int original_wavefront_size = params.wavefront_config.wavefront_size;
    
    optimizer->dynamicLoadBalance(params, tile_complexities);
    
    // Wavefront size should be adjusted based on complexity variance
    EXPECT_NE(params.wavefront_config.wavefront_size, original_wavefront_size);
    EXPECT_GT(params.wavefront_config.wavefront_size, 0);
}

// Test 10: Adaptive Quality Scaling
TEST_F(GPUKernelOptimizationTest, AdaptiveQualityScaling) {
    OptimizedRenderParams params = test_params;
    int original_spp = params.samples_per_pixel;
    
    // Test scaling down for low framerate
    optimizer->adaptiveQualityScaling(params, 60.0f, 30.0f);
    EXPECT_LE(params.samples_per_pixel, original_spp);
    
    // Reset and test scaling up for high framerate
    params = test_params;
    optimizer->adaptiveQualityScaling(params, 30.0f, 60.0f);
    EXPECT_GE(params.samples_per_pixel, original_spp);
}

// Test 11: Optimization Recommendations
TEST_F(GPUKernelOptimizationTest, OptimizationRecommendations) {
    // Create a test kernel profile
    KernelProfile profile;
    profile.kernel_name = "test_kernel";
    profile.execution_time_ms = 10.0f;
    profile.occupancy_percentage = 50.0f;
    profile.memory_bandwidth_utilization = 0.3f;
    profile.rt_core_utilization = 0.6f;
    
    WorkloadCharacteristics workload;
    workload.analyze(test_params);
    
    auto recommendations = optimizer->getOptimizationRecommendations(profile, workload);
    
    EXPECT_FALSE(recommendations.empty());
    
    // Should contain relevant optimization suggestions
    bool found_relevant_suggestion = false;
    for (const auto& rec : recommendations) {
        if (rec.find("occupancy") != std::string::npos ||
            rec.find("memory") != std::string::npos ||
            rec.find("RT core") != std::string::npos) {
            found_relevant_suggestion = true;
            break;
        }
    }
    EXPECT_TRUE(found_relevant_suggestion);
}

// Test 12: Optimization Report Generation
TEST_F(GPUKernelOptimizationTest, OptimizationReporting) {
    // Run some optimizations to generate data
    WorkloadCharacteristics workload;
    workload.analyze(test_params);
    
    optimizer->optimizeForWorkload(workload, OptimizationStrategy::BALANCED);
    optimizer->profileKernel("test_kernel", test_params, 3);
    
    std::string report = optimizer->generateOptimizationReport();
    
    EXPECT_FALSE(report.empty());
    EXPECT_TRUE(report.find("Optimization Report") != std::string::npos);
    EXPECT_TRUE(report.find("Performance") != std::string::npos);
}

// Test 13: Wavefront Renderer Integration
TEST_F(GPUKernelOptimizationTest, WavefrontRendererIntegration) {
    OptimizedWavefrontRenderer renderer;
    
    // Initialize with RT core config
    RTCoreConfig config;
    config.rt_core_count = device_props.multiProcessorCount;
    config.sm_count = device_props.multiProcessorCount;
    config.max_threads_per_sm = device_props.maxThreadsPerMultiProcessor;
    
    EXPECT_TRUE(renderer.initialize(config));
    
    // Set optimizer
    renderer.setKernelOptimizer(optimizer);
    
    // Test render (this might fail without proper scene data, but should not crash)
    bool render_result = renderer.render(test_params);
    
    // Get statistics (should be available even if render failed)
    auto stats = renderer.getLastRenderStatistics();
    EXPECT_GE(stats.total_render_time_ms, 0.0f);
}

// Performance benchmark test
TEST_F(GPUKernelOptimizationTest, PerformanceBenchmark) {
    auto start = std::chrono::high_resolution_clock::now();
    
    // Benchmark optimization process
    WorkloadCharacteristics workload;
    workload.analyze(test_params);
    
    OptimizationParams params = optimizer->optimizeForWorkload(workload, OptimizationStrategy::ADAPTIVE);
    
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
    
    std::cout << "GPU Kernel Optimization Performance:" << std::endl;
    std::cout << "  Optimization time: " << duration.count() << " ms" << std::endl;
    std::cout << "  Optimal block size: " << params.optimal_block_size.x << "x" << params.optimal_block_size.y << std::endl;
    std::cout << "  Wavefront size: " << params.wavefront_config.wavefront_size << std::endl;
    std::cout << "  RT cores enabled: " << (params.wavefront_config.use_rt_cores ? "Yes" : "No") << std::endl;
    
    // Optimization should be fast (< 100ms)
    EXPECT_LT(duration.count(), 100);
    
    // Verify optimization results
    EXPECT_GT(params.optimal_block_size.x, 0);
    EXPECT_GT(params.wavefront_config.wavefront_size, 0);
}

#else

// Dummy test when CUDA is not available
TEST(GPUKernelOptimizationTest, CUDANotAvailable) {
    GTEST_SKIP() << "CUDA not enabled, skipping GPU kernel optimization tests";
}

#endif // CUDA_ENABLED
