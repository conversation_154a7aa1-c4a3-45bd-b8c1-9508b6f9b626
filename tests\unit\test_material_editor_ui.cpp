// tests/unit/test_material_editor_ui.cpp
// PhotonRender - Material Editor UI Unit Tests
// Test suite per l'interfaccia Material Editor

#include <gtest/gtest.h>
#include <fstream>
#include <string>
#include <regex>

class MaterialEditorUITest : public ::testing::Test {
protected:
    void SetUp() override {
        // Paths to UI files
        m_htmlPath = "src/ruby/photon_render/material_editor.html";
        m_jsPath = "src/ruby/photon_render/material_editor.js";
        m_rubyPath = "src/ruby/photon_render/dialog.rb";
    }
    
    std::string readFile(const std::string& path) {
        std::ifstream file(path);
        if (!file.is_open()) {
            return "";
        }
        
        std::string content((std::istreambuf_iterator<char>(file)),
                           std::istreambuf_iterator<char>());
        return content;
    }
    
    bool fileExists(const std::string& path) {
        std::ifstream file(path);
        return file.good();
    }
    
    int countOccurrences(const std::string& text, const std::string& pattern) {
        std::regex regex_pattern(pattern);
        auto begin = std::sregex_iterator(text.begin(), text.end(), regex_pattern);
        auto end = std::sregex_iterator();
        return std::distance(begin, end);
    }
    
    std::string m_htmlPath;
    std::string m_jsPath;
    std::string m_rubyPath;
};

// Test 1: File Existence
TEST_F(MaterialEditorUITest, FileExistence) {
    // Check if all UI files exist
    EXPECT_TRUE(fileExists(m_htmlPath)) << "HTML file not found: " << m_htmlPath;
    EXPECT_TRUE(fileExists(m_jsPath)) << "JavaScript file not found: " << m_jsPath;
    EXPECT_TRUE(fileExists(m_rubyPath)) << "Ruby file not found: " << m_rubyPath;
}

// Test 2: HTML Structure Validation
TEST_F(MaterialEditorUITest, HTMLStructureValidation) {
    std::string htmlContent = readFile(m_htmlPath);
    ASSERT_FALSE(htmlContent.empty()) << "HTML file is empty or unreadable";
    
    // Check for essential HTML elements
    EXPECT_TRUE(htmlContent.find("<!DOCTYPE html>") != std::string::npos);
    EXPECT_TRUE(htmlContent.find("<html") != std::string::npos);
    EXPECT_TRUE(htmlContent.find("<head>") != std::string::npos);
    EXPECT_TRUE(htmlContent.find("<body>") != std::string::npos);
    EXPECT_TRUE(htmlContent.find("</html>") != std::string::npos);
    
    // Check for Material Editor specific elements
    EXPECT_TRUE(htmlContent.find("material-editor") != std::string::npos);
    EXPECT_TRUE(htmlContent.find("preview-panel") != std::string::npos);
    EXPECT_TRUE(htmlContent.find("controls-panel") != std::string::npos);
    EXPECT_TRUE(htmlContent.find("preview-canvas") != std::string::npos);
    
    // Check for parameter controls
    EXPECT_TRUE(htmlContent.find("metallic") != std::string::npos);
    EXPECT_TRUE(htmlContent.find("roughness") != std::string::npos);
    EXPECT_TRUE(htmlContent.find("specular") != std::string::npos);
    EXPECT_TRUE(htmlContent.find("baseColor") != std::string::npos);
    
    // Check for preset buttons
    EXPECT_TRUE(htmlContent.find("data-preset=\"plastic\"") != std::string::npos);
    EXPECT_TRUE(htmlContent.find("data-preset=\"metal\"") != std::string::npos);
    EXPECT_TRUE(htmlContent.find("data-preset=\"glass\"") != std::string::npos);
    
    // Check for geometry buttons
    EXPECT_TRUE(htmlContent.find("data-geometry=\"sphere\"") != std::string::npos);
    EXPECT_TRUE(htmlContent.find("data-geometry=\"cube\"") != std::string::npos);
    EXPECT_TRUE(htmlContent.find("data-geometry=\"cylinder\"") != std::string::npos);
}

// Test 3: CSS Styling Validation
TEST_F(MaterialEditorUITest, CSSValidation) {
    std::string htmlContent = readFile(m_htmlPath);
    ASSERT_FALSE(htmlContent.empty());
    
    // Check for CSS styles
    EXPECT_TRUE(htmlContent.find("<style>") != std::string::npos);
    EXPECT_TRUE(htmlContent.find("</style>") != std::string::npos);
    
    // Check for dark theme colors
    EXPECT_TRUE(htmlContent.find("#1a1a1a") != std::string::npos);  // Dark background
    EXPECT_TRUE(htmlContent.find("#e0e0e0") != std::string::npos);  // Light text
    EXPECT_TRUE(htmlContent.find("#0078d4") != std::string::npos);  // Accent color
    
    // Check for responsive design
    EXPECT_TRUE(htmlContent.find("@media") != std::string::npos);
    EXPECT_TRUE(htmlContent.find("flex") != std::string::npos);
    
    // Check for hover effects
    EXPECT_TRUE(htmlContent.find(":hover") != std::string::npos);
}

// Test 4: JavaScript Structure Validation
TEST_F(MaterialEditorUITest, JavaScriptValidation) {
    std::string jsContent = readFile(m_jsPath);
    ASSERT_FALSE(jsContent.empty()) << "JavaScript file is empty or unreadable";
    
    // Check for MaterialEditor class
    EXPECT_TRUE(jsContent.find("class MaterialEditor") != std::string::npos);
    EXPECT_TRUE(jsContent.find("constructor()") != std::string::npos);
    
    // Check for essential methods
    EXPECT_TRUE(jsContent.find("initializeEventListeners") != std::string::npos);
    EXPECT_TRUE(jsContent.find("setupSlider") != std::string::npos);
    EXPECT_TRUE(jsContent.find("setupColorPicker") != std::string::npos);
    EXPECT_TRUE(jsContent.find("loadPreset") != std::string::npos);
    EXPECT_TRUE(jsContent.find("updatePreview") != std::string::npos);
    
    // Check for material presets
    EXPECT_TRUE(jsContent.find("plastic:") != std::string::npos);
    EXPECT_TRUE(jsContent.find("metal:") != std::string::npos);
    EXPECT_TRUE(jsContent.find("glass:") != std::string::npos);
    EXPECT_TRUE(jsContent.find("wood:") != std::string::npos);
    EXPECT_TRUE(jsContent.find("fabric:") != std::string::npos);
    EXPECT_TRUE(jsContent.find("skin:") != std::string::npos);
    EXPECT_TRUE(jsContent.find("ceramic:") != std::string::npos);
    EXPECT_TRUE(jsContent.find("rubber:") != std::string::npos);
    
    // Check for Disney BRDF parameters
    EXPECT_TRUE(jsContent.find("baseColor") != std::string::npos);
    EXPECT_TRUE(jsContent.find("metallic") != std::string::npos);
    EXPECT_TRUE(jsContent.find("roughness") != std::string::npos);
    EXPECT_TRUE(jsContent.find("specular") != std::string::npos);
    EXPECT_TRUE(jsContent.find("specularTint") != std::string::npos);
    EXPECT_TRUE(jsContent.find("anisotropic") != std::string::npos);
    EXPECT_TRUE(jsContent.find("sheen") != std::string::npos);
    EXPECT_TRUE(jsContent.find("clearcoat") != std::string::npos);
    EXPECT_TRUE(jsContent.find("subsurface") != std::string::npos);
}

// Test 5: Ruby Integration Validation
TEST_F(MaterialEditorUITest, RubyIntegrationValidation) {
    std::string rubyContent = readFile(m_rubyPath);
    ASSERT_FALSE(rubyContent.empty()) << "Ruby file is empty or unreadable";
    
    // Check for material editor methods
    EXPECT_TRUE(rubyContent.find("show_material_editor") != std::string::npos);
    EXPECT_TRUE(rubyContent.find("setup_material_editor_callbacks") != std::string::npos);
    
    // Check for callback methods
    EXPECT_TRUE(rubyContent.find("renderPreview") != std::string::npos);
    EXPECT_TRUE(rubyContent.find("openTextureDialog") != std::string::npos);
    EXPECT_TRUE(rubyContent.find("saveMaterial") != std::string::npos);
    EXPECT_TRUE(rubyContent.find("applyMaterial") != std::string::npos);
    
    // Check for helper methods
    EXPECT_TRUE(rubyContent.find("create_preview_renderer") != std::string::npos);
    EXPECT_TRUE(rubyContent.find("update_preview_material") != std::string::npos);
    EXPECT_TRUE(rubyContent.find("update_preview_geometry") != std::string::npos);
    EXPECT_TRUE(rubyContent.find("render_preview_async") != std::string::npos);
    
    // Check for error handling
    EXPECT_TRUE(rubyContent.find("rescue =>") != std::string::npos);
    EXPECT_TRUE(rubyContent.find("puts \"Error") != std::string::npos);
}

// Test 6: Parameter Count Validation
TEST_F(MaterialEditorUITest, ParameterCountValidation) {
    std::string htmlContent = readFile(m_htmlPath);
    std::string jsContent = readFile(m_jsPath);
    
    // Count Disney BRDF parameters in HTML
    int htmlSliders = countOccurrences(htmlContent, "parameter-slider");
    EXPECT_GE(htmlSliders, 10) << "Expected at least 10 parameter sliders in HTML";
    
    // Count material presets in HTML
    int htmlPresets = countOccurrences(htmlContent, "data-preset=");
    EXPECT_GE(htmlPresets, 8) << "Expected at least 8 material presets in HTML";
    
    // Count geometry options in HTML
    int htmlGeometries = countOccurrences(htmlContent, "data-geometry=");
    EXPECT_GE(htmlGeometries, 5) << "Expected at least 5 geometry options in HTML";
    
    // Count presets in JavaScript
    int jsPresets = countOccurrences(jsContent, "\\w+:\\s*\\{");
    EXPECT_GE(jsPresets, 8) << "Expected at least 8 preset definitions in JavaScript";
}

// Test 7: Color Picker Validation
TEST_F(MaterialEditorUITest, ColorPickerValidation) {
    std::string htmlContent = readFile(m_htmlPath);
    std::string jsContent = readFile(m_jsPath);
    
    // Check for color picker elements in HTML
    EXPECT_TRUE(htmlContent.find("type=\"color\"") != std::string::npos);
    EXPECT_TRUE(htmlContent.find("color-picker") != std::string::npos);
    EXPECT_TRUE(htmlContent.find("color-input") != std::string::npos);
    
    // Check for color conversion functions in JavaScript
    EXPECT_TRUE(jsContent.find("hexToRgb") != std::string::npos);
    EXPECT_TRUE(jsContent.find("rgbToHex") != std::string::npos);
    EXPECT_TRUE(jsContent.find("isValidHex") != std::string::npos);
    EXPECT_TRUE(jsContent.find("setBaseColorFromHex") != std::string::npos);
}

// Test 8: Texture Support Validation
TEST_F(MaterialEditorUITest, TextureSupportValidation) {
    std::string htmlContent = readFile(m_htmlPath);
    std::string jsContent = readFile(m_jsPath);
    
    // Check for texture slots in HTML
    EXPECT_TRUE(htmlContent.find("texture-slot") != std::string::npos);
    EXPECT_TRUE(htmlContent.find("texture-preview") != std::string::npos);
    EXPECT_TRUE(htmlContent.find("Drop texture here") != std::string::npos);
    
    // Check for drag & drop support in JavaScript
    EXPECT_TRUE(jsContent.find("dragover") != std::string::npos);
    EXPECT_TRUE(jsContent.find("dragleave") != std::string::npos);
    EXPECT_TRUE(jsContent.find("drop") != std::string::npos);
    EXPECT_TRUE(jsContent.find("handleTextureDrop") != std::string::npos);
    EXPECT_TRUE(jsContent.find("loadTexture") != std::string::npos);
}

// Test 9: Responsive Design Validation
TEST_F(MaterialEditorUITest, ResponsiveDesignValidation) {
    std::string htmlContent = readFile(m_htmlPath);
    
    // Check for responsive CSS
    EXPECT_TRUE(htmlContent.find("@media (max-width: 768px)") != std::string::npos);
    EXPECT_TRUE(htmlContent.find("flex-direction: column") != std::string::npos);
    
    // Check for flexible layout
    EXPECT_TRUE(htmlContent.find("flex: 1") != std::string::npos);
    EXPECT_TRUE(htmlContent.find("display: flex") != std::string::npos);
}

// Test 10: Integration Points Validation
TEST_F(MaterialEditorUITest, IntegrationPointsValidation) {
    std::string jsContent = readFile(m_jsPath);
    std::string rubyContent = readFile(m_rubyPath);
    
    // Check for SketchUp integration in JavaScript
    EXPECT_TRUE(jsContent.find("window.sketchup") != std::string::npos);
    EXPECT_TRUE(jsContent.find("renderPreview") != std::string::npos);
    EXPECT_TRUE(jsContent.find("openTextureDialog") != std::string::npos);
    
    // Check for WebDialog integration in Ruby
    EXPECT_TRUE(rubyContent.find("UI::WebDialog") != std::string::npos);
    EXPECT_TRUE(rubyContent.find("add_action_callback") != std::string::npos);
    EXPECT_TRUE(rubyContent.find("execute_script") != std::string::npos);
    
    // Check for JSON communication
    EXPECT_TRUE(jsContent.find("JSON.stringify") != std::string::npos);
    EXPECT_TRUE(rubyContent.find("JSON.parse") != std::string::npos);
}

// Performance targets for Material Editor UI:
// - HTML file size: < 50KB
// - JavaScript file size: < 100KB
// - Load time: < 500ms
// - Parameter update response: < 50ms
// - Preview update trigger: < 100ms debounce
