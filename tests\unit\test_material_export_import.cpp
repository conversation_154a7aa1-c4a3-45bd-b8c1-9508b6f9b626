// tests/unit/test_material_export_import.cpp
// PhotonRender - Material Export/Import System Unit Tests
// Test suite per il sistema di export/import materiali

#include <gtest/gtest.h>
#include "../../src/core/material/material_exporter.hpp"
#include "../../src/core/material/material_importer.hpp"
#include "../../src/core/material/material.hpp"
#include <filesystem>
#include <fstream>

using namespace photon;

class MaterialExportImportTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create temporary test directory
        m_testDirectory = std::filesystem::temp_directory_path() / "photon_test_export_import";
        std::filesystem::remove_all(m_testDirectory);
        std::filesystem::create_directories(m_testDirectory);
        
        // Create test materials
        createTestMaterials();
        
        // Initialize exporter and importer
        m_exporter = std::make_unique<MaterialExporter>();
        m_importer = std::make_unique<MaterialImporter>();
    }
    
    void TearDown() override {
        // Cleanup test directory
        std::filesystem::remove_all(m_testDirectory);
        
        m_exporter.reset();
        m_importer.reset();
    }
    
    void createTestMaterials() {
        // Create test PBR material
        auto pbrMaterial = std::make_shared<PBRMaterial>();
        pbrMaterial->setName("Test PBR Material");
        
        DisneyBRDFParams params;
        params.baseColor = Color3(0.8f, 0.2f, 0.2f);
        params.metallic = 0.0f;
        params.roughness = 0.5f;
        params.specular = 0.5f;
        
        pbrMaterial->setDisneyParams(params);
        m_testMaterials.push_back(pbrMaterial);
        
        // Create test metallic material
        auto metallicMaterial = std::make_shared<PBRMaterial>();
        metallicMaterial->setName("Test Metallic Material");
        
        DisneyBRDFParams metallicParams;
        metallicParams.baseColor = Color3(0.9f, 0.9f, 0.9f);
        metallicParams.metallic = 1.0f;
        metallicParams.roughness = 0.1f;
        metallicParams.specular = 0.9f;
        
        metallicMaterial->setDisneyParams(metallicParams);
        m_testMaterials.push_back(metallicMaterial);
    }
    
    std::filesystem::path m_testDirectory;
    std::vector<std::shared_ptr<Material>> m_testMaterials;
    std::unique_ptr<MaterialExporter> m_exporter;
    std::unique_ptr<MaterialImporter> m_importer;
};

// Test 1: Exporter Initialization
TEST_F(MaterialExportImportTest, ExporterInitialization) {
    EXPECT_NE(m_exporter, nullptr);
    
    // Test supported formats
    auto formats = m_exporter->getSupportedFormats();
    EXPECT_FALSE(formats.empty());
    EXPECT_TRUE(std::find(formats.begin(), formats.end(), ExportFormat::JSON) != formats.end());
    EXPECT_TRUE(std::find(formats.begin(), formats.end(), ExportFormat::MTL) != formats.end());
    EXPECT_TRUE(std::find(formats.begin(), formats.end(), ExportFormat::GLTF) != formats.end());
}

// Test 2: Importer Initialization
TEST_F(MaterialExportImportTest, ImporterInitialization) {
    EXPECT_NE(m_importer, nullptr);
    
    // Test supported formats
    auto formats = m_importer->getSupportedFormats();
    EXPECT_FALSE(formats.empty());
    EXPECT_TRUE(std::find(formats.begin(), formats.end(), ImportFormat::JSON) != formats.end());
    EXPECT_TRUE(std::find(formats.begin(), formats.end(), ImportFormat::MTL) != formats.end());
    EXPECT_TRUE(std::find(formats.begin(), formats.end(), ImportFormat::GLTF) != formats.end());
}

// Test 3: Export to JSON Format
TEST_F(MaterialExportImportTest, ExportToJSON) {
    std::string outputPath = (m_testDirectory / "test_materials.json").string();
    
    ExportOptions options;
    options.format = ExportFormat::JSON;
    options.outputPath = outputPath;
    options.includeTextures = false;
    
    auto result = m_exporter->exportMaterials(m_testMaterials, options);
    
    EXPECT_TRUE(result.success);
    EXPECT_EQ(result.materialCount, m_testMaterials.size());
    EXPECT_TRUE(std::filesystem::exists(outputPath));
    EXPECT_FALSE(result.exportedFiles.empty());
    EXPECT_GT(result.exportTime, 0.0);
}

// Test 4: Export to MTL Format
TEST_F(MaterialExportImportTest, ExportToMTL) {
    std::string outputPath = (m_testDirectory / "test_materials.mtl").string();
    
    ExportOptions options;
    options.format = ExportFormat::MTL;
    options.outputPath = outputPath;
    options.includeTextures = false;
    
    auto result = m_exporter->exportMaterials(m_testMaterials, options);
    
    EXPECT_TRUE(result.success);
    EXPECT_EQ(result.materialCount, m_testMaterials.size());
    EXPECT_TRUE(std::filesystem::exists(outputPath));
    
    // Check MTL file content
    std::ifstream file(outputPath);
    std::string content((std::istreambuf_iterator<char>(file)),
                        std::istreambuf_iterator<char>());
    
    EXPECT_TRUE(content.find("newmtl") != std::string::npos);
    EXPECT_TRUE(content.find("Test PBR Material") != std::string::npos);
}

// Test 5: Export to glTF Format
TEST_F(MaterialExportImportTest, ExportToGLTF) {
    std::string outputPath = (m_testDirectory / "test_materials.gltf").string();
    
    ExportOptions options;
    options.format = ExportFormat::GLTF;
    options.outputPath = outputPath;
    options.includeTextures = false;
    
    auto result = m_exporter->exportMaterials(m_testMaterials, options);
    
    EXPECT_TRUE(result.success);
    EXPECT_EQ(result.materialCount, m_testMaterials.size());
    EXPECT_TRUE(std::filesystem::exists(outputPath));
    
    // Check glTF file content
    std::ifstream file(outputPath);
    std::string content((std::istreambuf_iterator<char>(file)),
                        std::istreambuf_iterator<char>());
    
    EXPECT_TRUE(content.find("\"materials\"") != std::string::npos);
    EXPECT_TRUE(content.find("pbrMetallicRoughness") != std::string::npos);
}

// Test 6: Export to OBJ+MTL Format
TEST_F(MaterialExportImportTest, ExportToOBJMTL) {
    std::string outputPath = (m_testDirectory / "test_materials.obj").string();
    
    ExportOptions options;
    options.format = ExportFormat::OBJ_MTL;
    options.outputPath = outputPath;
    options.includeTextures = false;
    
    auto result = m_exporter->exportMaterials(m_testMaterials, options);
    
    EXPECT_TRUE(result.success);
    EXPECT_EQ(result.materialCount, m_testMaterials.size());
    EXPECT_TRUE(std::filesystem::exists(outputPath));
    
    // Check that both OBJ and MTL files were created
    std::string mtlPath = (m_testDirectory / "test_materials.mtl").string();
    EXPECT_TRUE(std::filesystem::exists(mtlPath));
    EXPECT_EQ(result.exportedFiles.size(), 2);
}

// Test 7: Import from JSON Format
TEST_F(MaterialExportImportTest, ImportFromJSON) {
    // First export to JSON
    std::string exportPath = (m_testDirectory / "export_test.json").string();
    
    ExportOptions exportOptions;
    exportOptions.format = ExportFormat::JSON;
    exportOptions.outputPath = exportPath;
    exportOptions.includeTextures = false;
    
    auto exportResult = m_exporter->exportMaterials(m_testMaterials, exportOptions);
    ASSERT_TRUE(exportResult.success);
    
    // Then import from JSON
    ImportOptions importOptions;
    importOptions.format = ImportFormat::JSON;
    importOptions.inputPath = exportPath;
    importOptions.importTextures = false;
    
    auto importResult = m_importer->importMaterials(exportPath, importOptions);
    
    EXPECT_TRUE(importResult.success);
    EXPECT_EQ(importResult.materialCount, m_testMaterials.size());
    EXPECT_FALSE(importResult.materials.empty());
    EXPECT_GT(importResult.importTime, 0.0);
}

// Test 8: Import from MTL Format
TEST_F(MaterialExportImportTest, ImportFromMTL) {
    // Create a test MTL file
    std::string mtlPath = (m_testDirectory / "test_import.mtl").string();
    
    std::ofstream mtlFile(mtlPath);
    mtlFile << "# Test MTL file\n";
    mtlFile << "newmtl TestMaterial1\n";
    mtlFile << "Ka 0.1 0.1 0.1\n";
    mtlFile << "Kd 0.8 0.2 0.2\n";
    mtlFile << "Ks 0.5 0.5 0.5\n";
    mtlFile << "Ns 100\n";
    mtlFile << "\n";
    mtlFile << "newmtl TestMaterial2\n";
    mtlFile << "Ka 0.1 0.1 0.1\n";
    mtlFile << "Kd 0.2 0.8 0.2\n";
    mtlFile << "Ks 0.7 0.7 0.7\n";
    mtlFile << "Ns 200\n";
    mtlFile.close();
    
    // Import from MTL
    ImportOptions importOptions;
    importOptions.format = ImportFormat::MTL;
    importOptions.inputPath = mtlPath;
    importOptions.importTextures = false;
    
    auto importResult = m_importer->importMaterials(mtlPath, importOptions);
    
    EXPECT_TRUE(importResult.success);
    EXPECT_EQ(importResult.materialCount, 2);
    EXPECT_FALSE(importResult.materials.empty());
}

// Test 9: Format Detection
TEST_F(MaterialExportImportTest, FormatDetection) {
    // Test JSON detection
    std::string jsonPath = (m_testDirectory / "test.json").string();
    EXPECT_EQ(m_importer->detectFormat(jsonPath), ImportFormat::JSON);
    
    // Test MTL detection
    std::string mtlPath = (m_testDirectory / "test.mtl").string();
    EXPECT_EQ(m_importer->detectFormat(mtlPath), ImportFormat::MTL);
    
    // Test glTF detection
    std::string gltfPath = (m_testDirectory / "test.gltf").string();
    EXPECT_EQ(m_importer->detectFormat(gltfPath), ImportFormat::GLTF);
    
    // Test OBJ detection
    std::string objPath = (m_testDirectory / "test.obj").string();
    EXPECT_EQ(m_importer->detectFormat(objPath), ImportFormat::OBJ_MTL);
}

// Test 10: Export Options Validation
TEST_F(MaterialExportImportTest, ExportOptionsValidation) {
    ExportOptions validOptions;
    validOptions.format = ExportFormat::JSON;
    validOptions.outputPath = (m_testDirectory / "valid.json").string();
    validOptions.maxTextureSize = 1024;
    validOptions.textureQuality = 0.8f;
    
    auto result = m_exporter->exportMaterials(m_testMaterials, validOptions);
    EXPECT_TRUE(result.success);
    
    // Test invalid options
    ExportOptions invalidOptions;
    invalidOptions.format = ExportFormat::JSON;
    invalidOptions.outputPath = ""; // Empty path
    
    auto invalidResult = m_exporter->exportMaterials(m_testMaterials, invalidOptions);
    EXPECT_FALSE(invalidResult.success);
    EXPECT_FALSE(invalidResult.errors.empty());
}

// Test 11: Import Options Validation
TEST_F(MaterialExportImportTest, ImportOptionsValidation) {
    // Create a valid test file first
    std::string testPath = (m_testDirectory / "test.json").string();
    std::ofstream testFile(testPath);
    testFile << "{\"materials\": []}";
    testFile.close();
    
    ImportOptions validOptions;
    validOptions.format = ImportFormat::JSON;
    validOptions.inputPath = testPath;
    
    auto result = m_importer->importMaterials(testPath, validOptions);
    EXPECT_TRUE(result.success);
    
    // Test invalid options
    ImportOptions invalidOptions;
    invalidOptions.format = ImportFormat::JSON;
    invalidOptions.inputPath = "nonexistent.json";
    
    auto invalidResult = m_importer->importMaterials("nonexistent.json", invalidOptions);
    EXPECT_FALSE(invalidResult.success);
    EXPECT_FALSE(invalidResult.errors.empty());
}

// Test 12: Round-trip Export/Import
TEST_F(MaterialExportImportTest, RoundTripExportImport) {
    // Export materials to JSON
    std::string exportPath = (m_testDirectory / "roundtrip.json").string();
    
    ExportOptions exportOptions;
    exportOptions.format = ExportFormat::JSON;
    exportOptions.outputPath = exportPath;
    exportOptions.includeTextures = false;
    
    auto exportResult = m_exporter->exportMaterials(m_testMaterials, exportOptions);
    ASSERT_TRUE(exportResult.success);
    
    // Import materials back
    ImportOptions importOptions;
    importOptions.format = ImportFormat::JSON;
    importOptions.inputPath = exportPath;
    importOptions.importTextures = false;
    
    auto importResult = m_importer->importMaterials(exportPath, importOptions);
    ASSERT_TRUE(importResult.success);
    
    // Verify material count matches
    EXPECT_EQ(importResult.materialCount, m_testMaterials.size());
    
    // Verify material names are preserved
    bool foundPBRMaterial = false;
    bool foundMetallicMaterial = false;
    
    for (const auto& material : importResult.materials) {
        auto materialName = material->getName();
        if (materialName == "Test PBR Material") {
            foundPBRMaterial = true;
        } else if (materialName == "Test Metallic Material") {
            foundMetallicMaterial = true;
        }
    }
    
    EXPECT_TRUE(foundPBRMaterial);
    EXPECT_TRUE(foundMetallicMaterial);
}

// Test 13: Progress Callback
TEST_F(MaterialExportImportTest, ProgressCallback) {
    bool exportCallbackTriggered = false;
    float lastExportProgress = 0.0f;
    std::string lastExportMessage;
    
    // Set export progress callback
    m_exporter->setProgressCallback([&](float progress, const std::string& message) {
        exportCallbackTriggered = true;
        lastExportProgress = progress;
        lastExportMessage = message;
    });
    
    std::string exportPath = (m_testDirectory / "progress_test.json").string();
    
    ExportOptions exportOptions;
    exportOptions.format = ExportFormat::JSON;
    exportOptions.outputPath = exportPath;
    
    auto exportResult = m_exporter->exportMaterials(m_testMaterials, exportOptions);
    EXPECT_TRUE(exportResult.success);
    EXPECT_TRUE(exportCallbackTriggered);
    EXPECT_GE(lastExportProgress, 0.0f);
    EXPECT_LE(lastExportProgress, 1.0f);
    EXPECT_FALSE(lastExportMessage.empty());
    
    // Test import progress callback
    bool importCallbackTriggered = false;
    float lastImportProgress = 0.0f;
    std::string lastImportMessage;
    
    m_importer->setProgressCallback([&](float progress, const std::string& message) {
        importCallbackTriggered = true;
        lastImportProgress = progress;
        lastImportMessage = message;
    });
    
    ImportOptions importOptions;
    importOptions.format = ImportFormat::JSON;
    importOptions.inputPath = exportPath;
    
    auto importResult = m_importer->importMaterials(exportPath, importOptions);
    EXPECT_TRUE(importResult.success);
    EXPECT_TRUE(importCallbackTriggered);
    EXPECT_GE(lastImportProgress, 0.0f);
    EXPECT_LE(lastImportProgress, 1.0f);
    EXPECT_FALSE(lastImportMessage.empty());
}

// Test 14: Export Statistics
TEST_F(MaterialExportImportTest, ExportStatistics) {
    // Initial statistics
    std::string initialStats = m_exporter->getExportStats();
    EXPECT_FALSE(initialStats.empty());
    
    // Perform some exports
    std::string exportPath1 = (m_testDirectory / "stats_test1.json").string();
    std::string exportPath2 = (m_testDirectory / "stats_test2.mtl").string();
    
    ExportOptions options1;
    options1.format = ExportFormat::JSON;
    options1.outputPath = exportPath1;
    
    ExportOptions options2;
    options2.format = ExportFormat::MTL;
    options2.outputPath = exportPath2;
    
    m_exporter->exportMaterials(m_testMaterials, options1);
    m_exporter->exportMaterials(m_testMaterials, options2);
    
    // Check updated statistics
    std::string updatedStats = m_exporter->getExportStats();
    EXPECT_FALSE(updatedStats.empty());
    EXPECT_TRUE(updatedStats.find("Total Exports: 2") != std::string::npos);
}

// Test 15: Error Handling
TEST_F(MaterialExportImportTest, ErrorHandling) {
    // Test export with invalid path
    ExportOptions invalidExportOptions;
    invalidExportOptions.format = ExportFormat::JSON;
    invalidExportOptions.outputPath = "/invalid/path/test.json";
    
    auto exportResult = m_exporter->exportMaterials(m_testMaterials, invalidExportOptions);
    EXPECT_FALSE(exportResult.success);
    EXPECT_FALSE(exportResult.errors.empty());
    
    // Test import with non-existent file
    ImportOptions invalidImportOptions;
    invalidImportOptions.format = ImportFormat::JSON;
    invalidImportOptions.inputPath = "nonexistent.json";
    
    auto importResult = m_importer->importMaterials("nonexistent.json", invalidImportOptions);
    EXPECT_FALSE(importResult.success);
    EXPECT_FALSE(importResult.errors.empty());
    
    // Test export with empty material list
    std::vector<std::shared_ptr<Material>> emptyMaterials;
    
    ExportOptions emptyOptions;
    emptyOptions.format = ExportFormat::JSON;
    emptyOptions.outputPath = (m_testDirectory / "empty.json").string();
    
    auto emptyResult = m_exporter->exportMaterials(emptyMaterials, emptyOptions);
    EXPECT_FALSE(emptyResult.success);
    EXPECT_FALSE(emptyResult.errors.empty());
}

// Performance targets for Material Export/Import:
// - JSON export: < 100ms per material
// - MTL export: < 50ms per material
// - glTF export: < 150ms per material
// - JSON import: < 200ms per material
// - MTL import: < 100ms per material
// - Format detection: < 1ms per file
