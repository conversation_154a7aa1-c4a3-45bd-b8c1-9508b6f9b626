// tests/unit/test_material_library.cpp
// PhotonRender - Material Library System Unit Tests
// Test suite per il sistema di gestione libreria materiali

#include <gtest/gtest.h>
#include "../../src/core/material/material_library.hpp"
#include "../../src/core/material/disney_brdf.hpp"
#include <filesystem>
#include <fstream>

using namespace photon;

class MaterialLibraryTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create temporary test directory
        m_testLibraryPath = std::filesystem::temp_directory_path() / "photon_test_library";
        std::filesystem::remove_all(m_testLibraryPath);
        std::filesystem::create_directories(m_testLibraryPath);
        
        // Create test materials
        m_testMaterial1 = std::make_shared<PBRMaterial>(
            DisneyMaterialPresets::createPlastic(Color3(0.8f, 0.2f, 0.2f))
        );
        
        m_testMaterial2 = std::make_shared<PBRMaterial>(
            DisneyMaterialPresets::createMetal(Color3(0.7f, 0.7f, 0.8f))
        );
        
        // Create test metadata
        m_testMetadata1 = MaterialMetadata{
            .id = "test_plastic_001",
            .name = "Red Plastic",
            .description = "Glossy red plastic material",
            .author = "Test User",
            .version = "1.0",
            .category = MaterialCategory::PLASTICS,
            .tags = {MaterialTag::GLOSSY, MaterialTag::SMOOTH},
            .rating = 4.5f
        };
        
        m_testMetadata2 = MaterialMetadata{
            .id = "test_metal_001",
            .name = "Chrome Metal",
            .description = "Polished chrome metal",
            .author = "Test User",
            .version = "1.0",
            .category = MaterialCategory::METALS,
            .tags = {MaterialTag::REFLECTIVE, MaterialTag::POLISHED},
            .rating = 4.2f
        };
    }
    
    void TearDown() override {
        // Cleanup test directory
        std::filesystem::remove_all(m_testLibraryPath);
    }
    
    std::filesystem::path m_testLibraryPath;
    std::shared_ptr<PBRMaterial> m_testMaterial1;
    std::shared_ptr<PBRMaterial> m_testMaterial2;
    MaterialMetadata m_testMetadata1;
    MaterialMetadata m_testMetadata2;
};

// Test 1: Library Initialization
TEST_F(MaterialLibraryTest, LibraryInitialization) {
    MaterialLibrary library;
    
    // Test initialization
    EXPECT_TRUE(library.initialize(m_testLibraryPath.string()));
    
    // Check if directories were created
    EXPECT_TRUE(std::filesystem::exists(m_testLibraryPath / "materials"));
    EXPECT_TRUE(std::filesystem::exists(m_testLibraryPath / "thumbnails"));
    EXPECT_TRUE(std::filesystem::exists(m_testLibraryPath / "previews"));
    EXPECT_TRUE(std::filesystem::exists(m_testLibraryPath / "textures"));
    
    // Test initial state
    EXPECT_EQ(library.getMaterialCount(), 0);
    
    library.shutdown();
}

// Test 2: Add Material
TEST_F(MaterialLibraryTest, AddMaterial) {
    MaterialLibrary library;
    EXPECT_TRUE(library.initialize(m_testLibraryPath.string()));
    
    // Add material
    std::string materialId = library.addMaterial(m_testMaterial1, m_testMetadata1);
    EXPECT_FALSE(materialId.empty());
    EXPECT_EQ(library.getMaterialCount(), 1);
    
    // Verify material was added
    auto entry = library.getMaterial(materialId);
    EXPECT_NE(entry, nullptr);
    EXPECT_EQ(entry->metadata.name, "Red Plastic");
    EXPECT_EQ(entry->metadata.category, MaterialCategory::PLASTICS);
    EXPECT_EQ(entry->metadata.rating, 4.5f);
    
    library.shutdown();
}

// Test 3: Remove Material
TEST_F(MaterialLibraryTest, RemoveMaterial) {
    MaterialLibrary library;
    EXPECT_TRUE(library.initialize(m_testLibraryPath.string()));
    
    // Add material
    std::string materialId = library.addMaterial(m_testMaterial1, m_testMetadata1);
    EXPECT_EQ(library.getMaterialCount(), 1);
    
    // Remove material
    EXPECT_TRUE(library.removeMaterial(materialId));
    EXPECT_EQ(library.getMaterialCount(), 0);
    
    // Verify material was removed
    auto entry = library.getMaterial(materialId);
    EXPECT_EQ(entry, nullptr);
    
    library.shutdown();
}

// Test 4: Search Materials
TEST_F(MaterialLibraryTest, SearchMaterials) {
    MaterialLibrary library;
    EXPECT_TRUE(library.initialize(m_testLibraryPath.string()));
    
    // Add test materials
    std::string id1 = library.addMaterial(m_testMaterial1, m_testMetadata1);
    std::string id2 = library.addMaterial(m_testMaterial2, m_testMetadata2);
    EXPECT_EQ(library.getMaterialCount(), 2);
    
    // Test search by name
    MaterialSearchCriteria criteria;
    criteria.nameFilter = "plastic";
    auto results = library.searchMaterials(criteria);
    EXPECT_EQ(results.size(), 1);
    EXPECT_EQ(results[0], id1);
    
    // Test search by category
    criteria = MaterialSearchCriteria{};
    criteria.category = MaterialCategory::METALS;
    results = library.searchMaterials(criteria);
    EXPECT_EQ(results.size(), 1);
    EXPECT_EQ(results[0], id2);
    
    // Test search by tag
    criteria = MaterialSearchCriteria{};
    criteria.requiredTags = {MaterialTag::REFLECTIVE};
    results = library.searchMaterials(criteria);
    EXPECT_EQ(results.size(), 1);
    EXPECT_EQ(results[0], id2);
    
    // Test search by rating
    criteria = MaterialSearchCriteria{};
    criteria.minRating = 4.3f;
    results = library.searchMaterials(criteria);
    EXPECT_EQ(results.size(), 1);
    EXPECT_EQ(results[0], id1);
    
    library.shutdown();
}

// Test 5: Category Management
TEST_F(MaterialLibraryTest, CategoryManagement) {
    MaterialLibrary library;
    EXPECT_TRUE(library.initialize(m_testLibraryPath.string()));
    
    // Add materials to different categories
    library.addMaterial(m_testMaterial1, m_testMetadata1); // PLASTICS
    library.addMaterial(m_testMaterial2, m_testMetadata2); // METALS
    
    // Test get materials by category
    auto plasticMaterials = library.getMaterialsByCategory(MaterialCategory::PLASTICS);
    EXPECT_EQ(plasticMaterials.size(), 1);
    
    auto metalMaterials = library.getMaterialsByCategory(MaterialCategory::METALS);
    EXPECT_EQ(metalMaterials.size(), 1);
    
    auto glassMaterials = library.getMaterialsByCategory(MaterialCategory::GLASS);
    EXPECT_EQ(glassMaterials.size(), 0);
    
    // Test category counts
    EXPECT_EQ(library.getMaterialCountByCategory(MaterialCategory::PLASTICS), 1);
    EXPECT_EQ(library.getMaterialCountByCategory(MaterialCategory::METALS), 1);
    EXPECT_EQ(library.getMaterialCountByCategory(MaterialCategory::GLASS), 0);
    
    library.shutdown();
}

// Test 6: Rating System
TEST_F(MaterialLibraryTest, RatingSystem) {
    MaterialLibrary library;
    EXPECT_TRUE(library.initialize(m_testLibraryPath.string()));
    
    // Add material
    std::string materialId = library.addMaterial(m_testMaterial1, m_testMetadata1);
    
    // Test rating update
    library.setMaterialRating(materialId, 3.5f);
    
    auto entry = library.getMaterial(materialId);
    EXPECT_NE(entry, nullptr);
    EXPECT_FLOAT_EQ(entry->metadata.rating, 3.5f);
    
    // Test rating bounds
    library.setMaterialRating(materialId, -1.0f); // Should clamp to 0
    entry = library.getMaterial(materialId);
    EXPECT_FLOAT_EQ(entry->metadata.rating, 0.0f);
    
    library.setMaterialRating(materialId, 6.0f); // Should clamp to 5
    entry = library.getMaterial(materialId);
    EXPECT_FLOAT_EQ(entry->metadata.rating, 5.0f);
    
    library.shutdown();
}

// Test 7: Usage Tracking
TEST_F(MaterialLibraryTest, UsageTracking) {
    MaterialLibrary library;
    EXPECT_TRUE(library.initialize(m_testLibraryPath.string()));
    
    // Add material
    std::string materialId = library.addMaterial(m_testMaterial1, m_testMetadata1);
    
    // Test initial usage count
    auto entry = library.getMaterial(materialId);
    EXPECT_EQ(entry->metadata.downloadCount, 0);
    
    // Increment usage count
    library.incrementUsageCount(materialId);
    library.incrementUsageCount(materialId);
    library.incrementUsageCount(materialId);
    
    entry = library.getMaterial(materialId);
    EXPECT_EQ(entry->metadata.downloadCount, 3);
    
    library.shutdown();
}

// Test 8: Recent Materials
TEST_F(MaterialLibraryTest, RecentMaterials) {
    MaterialLibrary library;
    EXPECT_TRUE(library.initialize(m_testLibraryPath.string()));
    
    // Add materials
    std::string id1 = library.addMaterial(m_testMaterial1, m_testMetadata1);
    std::string id2 = library.addMaterial(m_testMaterial2, m_testMetadata2);
    
    // Access materials in specific order
    library.getMaterial(id1);
    library.getMaterial(id2);
    library.getMaterial(id1); // Access id1 again
    
    // Check recent materials order
    auto recent = library.getRecentMaterials(5);
    EXPECT_GE(recent.size(), 2);
    EXPECT_EQ(recent[0], id1); // Most recently accessed
    EXPECT_EQ(recent[1], id2);
    
    library.shutdown();
}

// Test 9: Popular Materials
TEST_F(MaterialLibraryTest, PopularMaterials) {
    MaterialLibrary library;
    EXPECT_TRUE(library.initialize(m_testLibraryPath.string()));
    
    // Add materials
    std::string id1 = library.addMaterial(m_testMaterial1, m_testMetadata1);
    std::string id2 = library.addMaterial(m_testMaterial2, m_testMetadata2);
    
    // Set different usage counts
    library.incrementUsageCount(id1); // 1 use
    library.incrementUsageCount(id2); // 1 use
    library.incrementUsageCount(id2); // 2 uses
    library.incrementUsageCount(id2); // 3 uses
    
    // Check popular materials order
    auto popular = library.getPopularMaterials(5);
    EXPECT_GE(popular.size(), 2);
    EXPECT_EQ(popular[0], id2); // Most used (3 times)
    EXPECT_EQ(popular[1], id1); // Less used (1 time)
    
    library.shutdown();
}

// Test 10: Library Statistics
TEST_F(MaterialLibraryTest, LibraryStatistics) {
    MaterialLibrary library;
    EXPECT_TRUE(library.initialize(m_testLibraryPath.string()));
    
    // Add materials
    library.addMaterial(m_testMaterial1, m_testMetadata1); // Rating 4.5
    library.addMaterial(m_testMaterial2, m_testMetadata2); // Rating 4.2
    
    // Get statistics
    std::string stats = library.getLibraryStats();
    
    // Check that stats contain expected information
    EXPECT_TRUE(stats.find("Total materials: 2") != std::string::npos);
    EXPECT_TRUE(stats.find("plastics: 1") != std::string::npos);
    EXPECT_TRUE(stats.find("metals: 1") != std::string::npos);
    
    library.shutdown();
}

// Test 11: Save and Load Library
TEST_F(MaterialLibraryTest, SaveAndLoadLibrary) {
    {
        // Create and populate library
        MaterialLibrary library;
        EXPECT_TRUE(library.initialize(m_testLibraryPath.string()));
        
        std::string id1 = library.addMaterial(m_testMaterial1, m_testMetadata1);
        std::string id2 = library.addMaterial(m_testMaterial2, m_testMetadata2);
        
        EXPECT_EQ(library.getMaterialCount(), 2);
        
        // Save library
        EXPECT_TRUE(library.saveLibrary());
        library.shutdown();
    }
    
    {
        // Load library in new instance
        MaterialLibrary library;
        EXPECT_TRUE(library.initialize(m_testLibraryPath.string()));
        
        // Check that materials were loaded
        EXPECT_EQ(library.getMaterialCount(), 2);
        
        // Verify specific materials exist
        auto allIds = library.getAllMaterialIds();
        EXPECT_EQ(allIds.size(), 2);
        
        library.shutdown();
    }
}

// Test 12: Thumbnail Generation
TEST_F(MaterialLibraryTest, ThumbnailGeneration) {
    MaterialLibrary library;
    EXPECT_TRUE(library.initialize(m_testLibraryPath.string()));
    
    // Add material
    std::string materialId = library.addMaterial(m_testMaterial1, m_testMetadata1);
    
    // Generate thumbnail (may fail if preview system not available)
    bool thumbnailGenerated = library.generateThumbnail(materialId, 128);
    
    // Check if thumbnail file exists (if generation succeeded)
    if (thumbnailGenerated) {
        std::string thumbnailPath = m_testLibraryPath.string() + "/thumbnails/" + materialId + ".png";
        EXPECT_TRUE(std::filesystem::exists(thumbnailPath));
    }
    
    library.shutdown();
}

// Test 13: Error Handling
TEST_F(MaterialLibraryTest, ErrorHandling) {
    MaterialLibrary library;
    
    // Test operations without initialization
    EXPECT_EQ(library.addMaterial(m_testMaterial1, m_testMetadata1), "");
    EXPECT_FALSE(library.removeMaterial("invalid_id"));
    EXPECT_EQ(library.getMaterial("invalid_id"), nullptr);
    
    // Initialize library
    EXPECT_TRUE(library.initialize(m_testLibraryPath.string()));
    
    // Test invalid operations
    EXPECT_FALSE(library.removeMaterial("nonexistent_id"));
    EXPECT_EQ(library.getMaterial("nonexistent_id"), nullptr);
    
    // Test adding null material
    EXPECT_EQ(library.addMaterial(nullptr, m_testMetadata1), "");
    
    library.shutdown();
}

// Performance targets for Material Library:
// - Library initialization: < 1 second for 1000 materials
// - Material search: < 100ms for 1000 materials
// - Thumbnail generation: < 500ms per material
// - Library save/load: < 2 seconds for 1000 materials
