// tests/unit/test_material_validation.cpp
// PhotonRender - Material Validation System Unit Tests
// Test suite per il sistema di validazione materiali

#include <gtest/gtest.h>
#include "../../src/core/material/material_validator.hpp"
#include "../../src/core/material/disney_brdf.hpp"

using namespace photon;

class MaterialValidationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create validator with default configuration
        m_validator = std::make_unique<MaterialValidator>();
        
        // Create test Disney BRDF parameters
        m_validParams = DisneyBRDFParams{
            .baseColor = Color3(0.8f, 0.2f, 0.2f),
            .metallic = 0.0f,
            .roughness = 0.5f,
            .specular = 0.5f,
            .specularTint = 0.0f,
            .anisotropic = 0.0f,
            .sheen = 0.0f,
            .sheenTint = 0.5f,
            .clearcoat = 0.0f,
            .clearcoatGloss = 1.0f,
            .subsurface = 0.0f
        };
        
        m_invalidParams = m_validParams;
        m_invalidParams.metallic = 1.5f; // Invalid range
        m_invalidParams.roughness = -0.1f; // Invalid range
    }
    
    void TearDown() override {
        m_validator.reset();
    }
    
    std::unique_ptr<MaterialValidator> m_validator;
    DisneyBRDFParams m_validParams;
    DisneyBRDFParams m_invalidParams;
};

// Test 1: Validator Initialization
TEST_F(MaterialValidationTest, ValidatorInitialization) {
    EXPECT_NE(m_validator, nullptr);
    
    // Test default configuration
    auto config = m_validator->getConfig();
    EXPECT_TRUE(config.enableEnergyConservation);
    EXPECT_TRUE(config.enableParameterRange);
    EXPECT_TRUE(config.enableParameterCombination);
    EXPECT_TRUE(config.enablePhysicalPlausibility);
    EXPECT_TRUE(config.enablePerformanceWarnings);
    EXPECT_FALSE(config.strictMode);
    EXPECT_FALSE(config.autoFix);
}

// Test 2: Valid Material Validation
TEST_F(MaterialValidationTest, ValidMaterialValidation) {
    auto result = m_validator->validateDisneyBRDF(m_validParams);
    
    EXPECT_TRUE(result.isValid);
    EXPECT_FALSE(result.hasErrors());
    EXPECT_GE(result.energyConservationScore, 0.9f);
    EXPECT_GE(result.physicalPlausibilityScore, 0.9f);
    EXPECT_GE(result.performanceScore, 0.8f);
}

// Test 3: Parameter Range Validation
TEST_F(MaterialValidationTest, ParameterRangeValidation) {
    auto result = m_validator->validateDisneyBRDF(m_invalidParams);
    
    EXPECT_FALSE(result.isValid);
    EXPECT_TRUE(result.hasErrors());
    
    // Check for range errors
    auto rangeIssues = result.getIssuesByCategory(ValidationCategory::PARAMETER_RANGE);
    EXPECT_GE(rangeIssues.size(), 2); // metallic and roughness out of range
    
    // Check specific parameter issues
    bool foundMetallicIssue = false;
    bool foundRoughnessIssue = false;
    
    for (const auto& issue : rangeIssues) {
        if (issue.parameter == "metallic") {
            foundMetallicIssue = true;
            EXPECT_EQ(issue.severity, ValidationSeverity::ERROR);
            EXPECT_TRUE(issue.autoFixable);
        }
        if (issue.parameter == "roughness") {
            foundRoughnessIssue = true;
            EXPECT_EQ(issue.severity, ValidationSeverity::ERROR);
            EXPECT_TRUE(issue.autoFixable);
        }
    }
    
    EXPECT_TRUE(foundMetallicIssue);
    EXPECT_TRUE(foundRoughnessIssue);
}

// Test 4: Energy Conservation Validation
TEST_F(MaterialValidationTest, EnergyConservationValidation) {
    // Create parameters that violate energy conservation
    DisneyBRDFParams energyViolatingParams = m_validParams;
    energyViolatingParams.metallic = 0.8f;
    energyViolatingParams.specular = 0.9f;
    energyViolatingParams.clearcoat = 0.5f;
    
    auto result = m_validator->validateDisneyBRDF(energyViolatingParams);
    
    // Should have energy conservation warnings
    auto energyIssues = result.getIssuesByCategory(ValidationCategory::ENERGY_CONSERVATION);
    EXPECT_GT(energyIssues.size(), 0);
    
    // Energy conservation score should be lower
    EXPECT_LT(result.energyConservationScore, 0.9f);
}

// Test 5: Parameter Combination Validation
TEST_F(MaterialValidationTest, ParameterCombinationValidation) {
    // Create parameters with incompatible combinations
    DisneyBRDFParams incompatibleParams = m_validParams;
    incompatibleParams.metallic = 0.8f;
    incompatibleParams.subsurface = 0.5f; // Metallic + subsurface is incompatible
    
    auto result = m_validator->validateDisneyBRDF(incompatibleParams);
    
    // Should have parameter combination warnings
    auto combinationIssues = result.getIssuesByCategory(ValidationCategory::PARAMETER_COMBINATION);
    EXPECT_GT(combinationIssues.size(), 0);
    
    // Check for specific incompatibility
    bool foundSubsurfaceMetallicIssue = false;
    for (const auto& issue : combinationIssues) {
        if (issue.parameter.find("subsurface") != std::string::npos &&
            issue.parameter.find("metallic") != std::string::npos) {
            foundSubsurfaceMetallicIssue = true;
            EXPECT_EQ(issue.severity, ValidationSeverity::WARNING);
        }
    }
    EXPECT_TRUE(foundSubsurfaceMetallicIssue);
}

// Test 6: Physical Plausibility Validation
TEST_F(MaterialValidationTest, PhysicalPlausibilityValidation) {
    // Create physically implausible parameters
    DisneyBRDFParams implausibleParams = m_validParams;
    implausibleParams.roughness = 0.001f; // Perfect mirror (rare in nature)
    implausibleParams.specular = 0.9f;
    
    auto result = m_validator->validateDisneyBRDF(implausibleParams);
    
    // Should have physical plausibility warnings
    auto plausibilityIssues = result.getIssuesByCategory(ValidationCategory::PHYSICAL_PLAUSIBILITY);
    EXPECT_GT(plausibilityIssues.size(), 0);
    
    // Physical plausibility score should be lower
    EXPECT_LT(result.physicalPlausibilityScore, 1.0f);
}

// Test 7: Performance Validation
TEST_F(MaterialValidationTest, PerformanceValidation) {
    // Create performance-heavy parameters
    DisneyBRDFParams heavyParams = m_validParams;
    heavyParams.subsurface = 0.8f; // Expensive subsurface scattering
    heavyParams.clearcoat = 0.7f;  // Additional reflection layer
    heavyParams.anisotropic = 0.6f; // Anisotropic reflection
    
    auto result = m_validator->validateDisneyBRDF(heavyParams);
    
    // Should have performance warnings
    auto performanceIssues = result.getIssuesByCategory(ValidationCategory::PERFORMANCE);
    EXPECT_GT(performanceIssues.size(), 0);
    
    // Performance score should be lower
    EXPECT_LT(result.performanceScore, 0.9f);
}

// Test 8: Auto-Fix Functionality
TEST_F(MaterialValidationTest, AutoFixFunctionality) {
    auto result = m_validator->validateDisneyBRDF(m_invalidParams);
    EXPECT_FALSE(result.isValid);
    
    // Get auto-fixable issues
    auto autoFixableIssues = result.getAutoFixableIssues();
    EXPECT_GT(autoFixableIssues.size(), 0);
    
    // Apply auto-fixes
    DisneyBRDFParams fixedParams = m_invalidParams;
    int fixedCount = m_validator->autoFixIssues(fixedParams, autoFixableIssues);
    EXPECT_GT(fixedCount, 0);
    
    // Validate fixed parameters
    auto fixedResult = m_validator->validateDisneyBRDF(fixedParams);
    EXPECT_TRUE(fixedResult.isValid);
    EXPECT_FALSE(fixedResult.hasErrors());
}

// Test 9: Energy Conservation Score Calculation
TEST_F(MaterialValidationTest, EnergyConservationScoreCalculation) {
    // Perfect energy conservation
    float score1 = m_validator->calculateEnergyConservationScore(m_validParams);
    EXPECT_FLOAT_EQ(score1, 1.0f);
    
    // Energy violation
    DisneyBRDFParams violatingParams = m_validParams;
    violatingParams.metallic = 1.0f;
    violatingParams.specular = 1.0f;
    
    float score2 = m_validator->calculateEnergyConservationScore(violatingParams);
    EXPECT_LT(score2, 1.0f);
    EXPECT_GE(score2, 0.0f);
}

// Test 10: Physical Plausibility Score Calculation
TEST_F(MaterialValidationTest, PhysicalPlausibilityScoreCalculation) {
    // Physically plausible parameters
    float score1 = m_validator->calculatePhysicalPlausibilityScore(m_validParams);
    EXPECT_FLOAT_EQ(score1, 1.0f);
    
    // Physically implausible parameters
    DisneyBRDFParams implausibleParams = m_validParams;
    implausibleParams.subsurface = 0.5f;
    implausibleParams.metallic = 0.5f; // Incompatible combination
    implausibleParams.roughness = 0.001f; // Perfect mirror
    
    float score2 = m_validator->calculatePhysicalPlausibilityScore(implausibleParams);
    EXPECT_LT(score2, 1.0f);
    EXPECT_GE(score2, 0.0f);
}

// Test 11: Performance Score Calculation
TEST_F(MaterialValidationTest, PerformanceScoreCalculation) {
    // Simple material (good performance)
    float score1 = m_validator->calculatePerformanceScore(m_validParams);
    EXPECT_GE(score1, 0.8f);
    
    // Complex material (poor performance)
    DisneyBRDFParams complexParams = m_validParams;
    complexParams.subsurface = 0.8f;
    complexParams.clearcoat = 0.7f;
    complexParams.anisotropic = 0.6f;
    complexParams.sheen = 0.5f;
    complexParams.roughness = 0.01f;
    
    float score2 = m_validator->calculatePerformanceScore(complexParams);
    EXPECT_LT(score2, score1);
    EXPECT_GE(score2, 0.0f);
}

// Test 12: Validation Configuration
TEST_F(MaterialValidationTest, ValidationConfiguration) {
    // Test configuration changes
    ValidationConfig config = m_validator->getConfig();
    config.enableEnergyConservation = false;
    config.enableParameterRange = false;
    config.autoFix = true;
    config.strictMode = true;
    
    m_validator->setConfig(config);
    
    auto newConfig = m_validator->getConfig();
    EXPECT_FALSE(newConfig.enableEnergyConservation);
    EXPECT_FALSE(newConfig.enableParameterRange);
    EXPECT_TRUE(newConfig.autoFix);
    EXPECT_TRUE(newConfig.strictMode);
    
    // Validation with disabled checks should have fewer issues
    auto result = m_validator->validateDisneyBRDF(m_invalidParams);
    auto energyIssues = result.getIssuesByCategory(ValidationCategory::ENERGY_CONSERVATION);
    auto rangeIssues = result.getIssuesByCategory(ValidationCategory::PARAMETER_RANGE);
    
    EXPECT_EQ(energyIssues.size(), 0); // Energy conservation disabled
    EXPECT_EQ(rangeIssues.size(), 0);  // Parameter range disabled
}

// Test 13: Validation Statistics
TEST_F(MaterialValidationTest, ValidationStatistics) {
    // Initial statistics
    std::string initialStats = m_validator->getValidationStats();
    EXPECT_FALSE(initialStats.empty());
    
    // Perform some validations
    m_validator->validateDisneyBRDF(m_validParams);
    m_validator->validateDisneyBRDF(m_invalidParams);
    
    // Check updated statistics
    std::string updatedStats = m_validator->getValidationStats();
    EXPECT_FALSE(updatedStats.empty());
    EXPECT_TRUE(updatedStats.find("Total Validations: 2") != std::string::npos);
}

// Test 14: Workflow Suggestions
TEST_F(MaterialValidationTest, WorkflowSuggestions) {
    // Chrome-like material
    DisneyBRDFParams chromeParams = m_validParams;
    chromeParams.metallic = 0.95f;
    chromeParams.roughness = 0.05f;
    
    auto result = m_validator->validateDisneyBRDF(chromeParams);
    auto workflowIssues = result.getIssuesByCategory(ValidationCategory::WORKFLOW);
    
    // Should suggest chrome preset
    bool foundChromePresetSuggestion = false;
    for (const auto& issue : workflowIssues) {
        if (issue.message.find("chrome") != std::string::npos ||
            issue.message.find("mirror") != std::string::npos) {
            foundChromePresetSuggestion = true;
            EXPECT_EQ(issue.severity, ValidationSeverity::INFO);
        }
    }
    EXPECT_TRUE(foundChromePresetSuggestion);
}

// Test 15: Real-time Validation Callback
TEST_F(MaterialValidationTest, RealTimeValidationCallback) {
    bool callbackTriggered = false;
    ValidationResult callbackResult;
    
    // Set validation callback
    m_validator->setValidationCallback([&](const ValidationResult& result) {
        callbackTriggered = true;
        callbackResult = result;
    });
    
    // Enable real-time validation
    m_validator->setRealTimeValidation(true);
    
    // Perform validation
    auto result = m_validator->validateDisneyBRDF(m_validParams);
    
    // Check if callback was triggered
    EXPECT_TRUE(callbackTriggered);
    EXPECT_EQ(callbackResult.isValid, result.isValid);
    EXPECT_EQ(callbackResult.issues.size(), result.issues.size());
}

// Performance targets for Material Validation:
// - Parameter validation: < 1ms per parameter
// - Energy conservation check: < 5ms per material
// - Full material validation: < 10ms per material
// - Auto-fix application: < 5ms per fix
// - Score calculation: < 2ms per score
