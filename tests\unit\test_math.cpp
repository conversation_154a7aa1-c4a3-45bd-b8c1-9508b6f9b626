// tests/unit/test_math.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Unit tests for math library

#include <gtest/gtest.h>
#include "../../src/core/math/vec3.hpp"
#include "../../src/core/math/ray.hpp"
#include "../../src/core/math/matrix4.hpp"

using namespace photon;

class MathTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Setup test data
    }
    
    void TearDown() override {
        // Cleanup
    }
};

// Vec3 Tests
TEST_F(MathTest, Vec3DefaultConstructor) {
    Vec3 v;
    EXPECT_FLOAT_EQ(v.x, 0.0f);
    EXPECT_FLOAT_EQ(v.y, 0.0f);
    EXPECT_FLOAT_EQ(v.z, 0.0f);
}

TEST_F(MathTest, Vec3ParameterConstructor) {
    Vec3 v(1.0f, 2.0f, 3.0f);
    EXPECT_FLOAT_EQ(v.x, 1.0f);
    EXPECT_FLOAT_EQ(v.y, 2.0f);
    EXPECT_FLOAT_EQ(v.z, 3.0f);
}

TEST_F(MathTest, Vec3Addition) {
    Vec3 a(1.0f, 2.0f, 3.0f);
    Vec3 b(4.0f, 5.0f, 6.0f);
    Vec3 c = a + b;
    
    EXPECT_FLOAT_EQ(c.x, 5.0f);
    EXPECT_FLOAT_EQ(c.y, 7.0f);
    EXPECT_FLOAT_EQ(c.z, 9.0f);
}

TEST_F(MathTest, Vec3DotProduct) {
    Vec3 a(1.0f, 0.0f, 0.0f);
    Vec3 b(0.0f, 1.0f, 0.0f);
    
    EXPECT_FLOAT_EQ(a.dot(b), 0.0f);
    EXPECT_FLOAT_EQ(a.dot(a), 1.0f);
}

TEST_F(MathTest, Vec3CrossProduct) {
    Vec3 a(1.0f, 0.0f, 0.0f);
    Vec3 b(0.0f, 1.0f, 0.0f);
    Vec3 c = a.cross(b);
    
    EXPECT_FLOAT_EQ(c.x, 0.0f);
    EXPECT_FLOAT_EQ(c.y, 0.0f);
    EXPECT_FLOAT_EQ(c.z, 1.0f);
}

TEST_F(MathTest, Vec3Length) {
    Vec3 v(3.0f, 4.0f, 0.0f);
    EXPECT_FLOAT_EQ(v.length(), 5.0f);
    EXPECT_FLOAT_EQ(v.lengthSquared(), 25.0f);
}

TEST_F(MathTest, Vec3Normalization) {
    Vec3 v(3.0f, 4.0f, 0.0f);
    Vec3 n = v.normalized();
    
    EXPECT_FLOAT_EQ(n.length(), 1.0f);
    EXPECT_FLOAT_EQ(n.x, 0.6f);
    EXPECT_FLOAT_EQ(n.y, 0.8f);
    EXPECT_FLOAT_EQ(n.z, 0.0f);
}

// Ray Tests
TEST_F(MathTest, RayDefaultConstructor) {
    Ray ray;
    EXPECT_EQ(ray.o, Point3(0));
    EXPECT_EQ(ray.d, Vec3(0, 0, 1));
}

TEST_F(MathTest, RayParameterConstructor) {
    Point3 origin(1, 2, 3);
    Vec3 direction(0, 0, 1);
    Ray ray(origin, direction);
    
    EXPECT_EQ(ray.o, origin);
    EXPECT_EQ(ray.d, direction);
}

TEST_F(MathTest, RayEvaluation) {
    Ray ray(Point3(0, 0, 0), Vec3(1, 0, 0));
    Point3 p = ray.at(5.0f);
    
    EXPECT_FLOAT_EQ(p.x, 5.0f);
    EXPECT_FLOAT_EQ(p.y, 0.0f);
    EXPECT_FLOAT_EQ(p.z, 0.0f);
}

// Matrix4 Tests
TEST_F(MathTest, Matrix4Identity) {
    Matrix4 m = Matrix4::identity();
    
    for (int i = 0; i < 4; ++i) {
        for (int j = 0; j < 4; ++j) {
            if (i == j) {
                EXPECT_FLOAT_EQ(m(i, j), 1.0f);
            } else {
                EXPECT_FLOAT_EQ(m(i, j), 0.0f);
            }
        }
    }
}

TEST_F(MathTest, Matrix4Translation) {
    Matrix4 m = Matrix4::translation(1, 2, 3);
    Point3 p(0, 0, 0);
    Point3 transformed = m.transformPoint(p);
    
    EXPECT_FLOAT_EQ(transformed.x, 1.0f);
    EXPECT_FLOAT_EQ(transformed.y, 2.0f);
    EXPECT_FLOAT_EQ(transformed.z, 3.0f);
}

TEST_F(MathTest, Matrix4Scale) {
    Matrix4 m = Matrix4::scale(2, 3, 4);
    Point3 p(1, 1, 1);
    Point3 transformed = m.transformPoint(p);
    
    EXPECT_FLOAT_EQ(transformed.x, 2.0f);
    EXPECT_FLOAT_EQ(transformed.y, 3.0f);
    EXPECT_FLOAT_EQ(transformed.z, 4.0f);
}

TEST_F(MathTest, Matrix4Multiplication) {
    Matrix4 a = Matrix4::translation(1, 0, 0);
    Matrix4 b = Matrix4::scale(2, 2, 2);
    Matrix4 c = a * b;
    
    Point3 p(1, 1, 1);
    Point3 transformed = c.transformPoint(p);
    
    EXPECT_FLOAT_EQ(transformed.x, 3.0f); // (1*2) + 1
    EXPECT_FLOAT_EQ(transformed.y, 2.0f); // 1*2
    EXPECT_FLOAT_EQ(transformed.z, 2.0f); // 1*2
}

TEST_F(MathTest, Matrix4Inverse) {
    Matrix4 m = Matrix4::translation(1, 2, 3);
    Matrix4 inv = m.inverse();
    Matrix4 identity = m * inv;
    
    // Check if m * inv = identity (within tolerance)
    for (int i = 0; i < 4; ++i) {
        for (int j = 0; j < 4; ++j) {
            if (i == j) {
                EXPECT_NEAR(identity(i, j), 1.0f, 1e-5f);
            } else {
                EXPECT_NEAR(identity(i, j), 0.0f, 1e-5f);
            }
        }
    }
}

TEST_F(MathTest, Matrix4Determinant) {
    Matrix4 identity = Matrix4::identity();
    EXPECT_FLOAT_EQ(identity.determinant(), 1.0f);
    
    Matrix4 scale = Matrix4::scale(2, 3, 4);
    EXPECT_FLOAT_EQ(scale.determinant(), 24.0f); // 2*3*4*1
}

// Integration test
TEST_F(MathTest, TransformationChain) {
    // Test a chain of transformations: translate -> rotate -> scale
    Matrix4 translation = Matrix4::translation(1, 0, 0);
    Matrix4 rotation = Matrix4::rotationZ(M_PI / 2); // 90 degrees
    Matrix4 scale = Matrix4::scale(2, 2, 2);
    
    Matrix4 combined = scale * rotation * translation;
    
    Point3 p(0, 0, 0);
    Point3 result = combined.transformPoint(p);
    
    // After translation: (1, 0, 0)
    // After rotation: (0, 1, 0) 
    // After scale: (0, 2, 0)
    EXPECT_NEAR(result.x, 0.0f, 1e-5f);
    EXPECT_NEAR(result.y, 2.0f, 1e-5f);
    EXPECT_NEAR(result.z, 0.0f, 1e-5f);
}
