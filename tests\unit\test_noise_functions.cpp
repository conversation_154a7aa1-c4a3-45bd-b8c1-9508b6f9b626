// tests/unit/test_noise_functions.cpp
// PhotonRender - Advanced Noise Functions Test Suite
// Tests for Perlin, Simplex, and Worley noise implementations

#include <gtest/gtest.h>
#include <chrono>
#include <vector>
#include <cmath>
#include "../../src/core/texture/texture.hpp"
#include "../../src/core/math/vec2.hpp"
#include "../../src/core/math/color3.hpp"

using namespace photon;

class NoiseTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create noise textures for testing
        perlinNoise = std::make_unique<NoiseTexture>(NoiseType::PERLIN, 4.0f, 1.0f, FractalParams::defaultParams());
        simplexNoise = std::make_unique<NoiseTexture>(NoiseType::SIMPLEX, 4.0f, 1.0f, FractalParams::defaultParams());
        worleyNoise = std::make_unique<NoiseTexture>(NoiseType::WORLEY, 4.0f, 1.0f, FractalParams::defaultParams());
        
        // Test coordinates
        testCoords = {
            Vec2(0.0f, 0.0f),
            Vec2(0.5f, 0.5f),
            Vec2(1.0f, 1.0f),
            Vec2(2.5f, 3.7f),
            Vec2(-1.2f, 4.8f),
            Vec2(10.0f, -5.0f)
        };
    }

    std::unique_ptr<NoiseTexture> perlinNoise;
    std::unique_ptr<NoiseTexture> simplexNoise;
    std::unique_ptr<NoiseTexture> worleyNoise;
    std::vector<Vec2> testCoords;
};

// Test 1: Basic Noise Function Validation
TEST_F(NoiseTest, BasicNoiseValidation) {
    for (const auto& coord : testCoords) {
        // Test Perlin noise
        float perlinValue = perlinNoise->sampleFloat(coord);
        EXPECT_GE(perlinValue, 0.0f) << "Perlin noise should be normalized to [0,1]";
        EXPECT_LE(perlinValue, 1.0f) << "Perlin noise should be normalized to [0,1]";
        
        // Test Simplex noise
        float simplexValue = simplexNoise->sampleFloat(coord);
        EXPECT_GE(simplexValue, 0.0f) << "Simplex noise should be normalized to [0,1]";
        EXPECT_LE(simplexValue, 1.0f) << "Simplex noise should be normalized to [0,1]";
        
        // Test Worley noise
        float worleyValue = worleyNoise->sampleFloat(coord);
        EXPECT_GE(worleyValue, 0.0f) << "Worley noise should be normalized to [0,1]";
        EXPECT_LE(worleyValue, 1.0f) << "Worley noise should be normalized to [0,1]";
    }
}

// Test 2: Noise Continuity (adjacent samples should be similar)
TEST_F(NoiseTest, NoiseContinuity) {
    const float epsilon = 0.001f;
    const float maxDifference = 0.5f; // Reasonable threshold for continuity
    
    for (const auto& coord : testCoords) {
        Vec2 adjacent = coord + Vec2(epsilon, epsilon);
        
        // Test Perlin continuity
        float perlin1 = perlinNoise->sampleFloat(coord);
        float perlin2 = perlinNoise->sampleFloat(adjacent);
        float perlinDiff = std::abs(perlin1 - perlin2);
        EXPECT_LT(perlinDiff, maxDifference) << "Perlin noise should be continuous";
        
        // Test Simplex continuity
        float simplex1 = simplexNoise->sampleFloat(coord);
        float simplex2 = simplexNoise->sampleFloat(adjacent);
        float simplexDiff = std::abs(simplex1 - simplex2);
        EXPECT_LT(simplexDiff, maxDifference) << "Simplex noise should be continuous";
    }
}

// Test 3: Noise Determinism (same input should give same output)
TEST_F(NoiseTest, NoiseDeterminism) {
    for (const auto& coord : testCoords) {
        // Test multiple samples at same coordinate
        float perlin1 = perlinNoise->sampleFloat(coord);
        float perlin2 = perlinNoise->sampleFloat(coord);
        EXPECT_FLOAT_EQ(perlin1, perlin2) << "Perlin noise should be deterministic";
        
        float simplex1 = simplexNoise->sampleFloat(coord);
        float simplex2 = simplexNoise->sampleFloat(coord);
        EXPECT_FLOAT_EQ(simplex1, simplex2) << "Simplex noise should be deterministic";
        
        float worley1 = worleyNoise->sampleFloat(coord);
        float worley2 = worleyNoise->sampleFloat(coord);
        EXPECT_FLOAT_EQ(worley1, worley2) << "Worley noise should be deterministic";
    }
}

// Test 4: Fractal Parameters
TEST_F(NoiseTest, FractalParameters) {
    FractalParams fractal;
    fractal.octaves = 6;
    fractal.lacunarity = 2.5f;
    fractal.persistence = 0.4f;
    fractal.scale = 2.0f;
    
    NoiseTexture fractalNoise(NoiseType::PERLIN, 2.0f, 1.0f, fractal);
    
    // Test that fractal parameters affect output
    float value1 = fractalNoise.sampleFloat(Vec2(1.0f, 1.0f));
    float value2 = perlinNoise->sampleFloat(Vec2(1.0f, 1.0f));
    
    // Values should be different due to different fractal parameters
    EXPECT_NE(value1, value2) << "Fractal parameters should affect noise output";
}

// Test 5: Seed Variation
TEST_F(NoiseTest, SeedVariation) {
    NoiseTexture noise1(NoiseType::PERLIN, 4.0f, 1.0f, FractalParams::defaultParams());
    NoiseTexture noise2(NoiseType::PERLIN, 4.0f, 1.0f, FractalParams::defaultParams());
    
    noise1.setSeed(12345);
    noise2.setSeed(54321);
    
    Vec2 testCoord(2.5f, 3.7f);
    float value1 = noise1.sampleFloat(testCoord);
    float value2 = noise2.sampleFloat(testCoord);
    
    EXPECT_NE(value1, value2) << "Different seeds should produce different noise";
}

// Test 6: Performance Benchmark
TEST_F(NoiseTest, PerformanceBenchmark) {
    const int numSamples = 10000;
    const float targetTimePerSample = 1000.0f; // 1000ns = 1μs per sample
    
    std::vector<Vec2> coords;
    coords.reserve(numSamples);
    for (int i = 0; i < numSamples; ++i) {
        coords.emplace_back(
            static_cast<float>(i % 100) * 0.1f,
            static_cast<float>(i / 100) * 0.1f
        );
    }
    
    // Benchmark Perlin noise
    auto start = std::chrono::high_resolution_clock::now();
    for (const auto& coord : coords) {
        volatile float value = perlinNoise->sampleFloat(coord);
        (void)value; // Prevent optimization
    }
    auto end = std::chrono::high_resolution_clock::now();
    
    auto duration = std::chrono::duration_cast<std::chrono::nanoseconds>(end - start);
    float avgTimePerSample = static_cast<float>(duration.count()) / numSamples;
    
    EXPECT_LT(avgTimePerSample, targetTimePerSample) 
        << "Perlin noise performance: " << avgTimePerSample << "ns per sample (target: " << targetTimePerSample << "ns)";
    
    std::cout << "Performance Results:\n";
    std::cout << "  Perlin Noise: " << avgTimePerSample << "ns per sample\n";
}

// Test 7: Color Output Validation
TEST_F(NoiseTest, ColorOutput) {
    for (const auto& coord : testCoords) {
        Color3 color = perlinNoise->sample(coord);
        
        // Color should be grayscale (R=G=B) for noise texture
        EXPECT_FLOAT_EQ(color.r, color.g) << "Noise texture should produce grayscale";
        EXPECT_FLOAT_EQ(color.g, color.b) << "Noise texture should produce grayscale";
        
        // Color components should be in valid range
        EXPECT_GE(color.r, 0.0f) << "Color components should be >= 0";
        EXPECT_LE(color.r, 1.0f) << "Color components should be <= 1";
    }
}

// Test 8: Edge Cases
TEST_F(NoiseTest, EdgeCases) {
    std::vector<Vec2> edgeCases = {
        Vec2(0.0f, 0.0f),           // Origin
        Vec2(1000.0f, 1000.0f),     // Large coordinates
        Vec2(-1000.0f, -1000.0f),   // Large negative coordinates
        Vec2(0.000001f, 0.000001f), // Very small coordinates
        Vec2(3.14159f, 2.71828f)    // Irrational coordinates
    };
    
    for (const auto& coord : edgeCases) {
        // All noise functions should handle edge cases gracefully
        EXPECT_NO_THROW({
            float perlin = perlinNoise->sampleFloat(coord);
            float simplex = simplexNoise->sampleFloat(coord);
            float worley = worleyNoise->sampleFloat(coord);
            
            // Values should still be in valid range
            EXPECT_GE(perlin, 0.0f);
            EXPECT_LE(perlin, 1.0f);
            EXPECT_GE(simplex, 0.0f);
            EXPECT_LE(simplex, 1.0f);
            EXPECT_GE(worley, 0.0f);
            EXPECT_LE(worley, 1.0f);
        }) << "Noise functions should handle edge case: " << coord.x << ", " << coord.y;
    }
}

// Test 9: Noise Type Differences
TEST_F(NoiseTest, NoiseTypeDifferences) {
    Vec2 testCoord(5.5f, 7.3f);
    
    float perlinValue = perlinNoise->sampleFloat(testCoord);
    float simplexValue = simplexNoise->sampleFloat(testCoord);
    float worleyValue = worleyNoise->sampleFloat(testCoord);
    
    // Different noise types should generally produce different values
    // (though they could occasionally be the same by chance)
    bool allDifferent = (perlinValue != simplexValue) || 
                       (simplexValue != worleyValue) || 
                       (perlinValue != worleyValue);
    
    EXPECT_TRUE(allDifferent) << "Different noise types should generally produce different values";
}

// Test 10: Amplitude and Frequency Effects
TEST_F(NoiseTest, AmplitudeFrequencyEffects) {
    NoiseTexture lowFreq(NoiseType::PERLIN, 1.0f, 1.0f, FractalParams::defaultParams());
    NoiseTexture highFreq(NoiseType::PERLIN, 8.0f, 1.0f, FractalParams::defaultParams());
    NoiseTexture lowAmp(NoiseType::PERLIN, 4.0f, 0.5f, FractalParams::defaultParams());
    
    Vec2 testCoord(2.0f, 3.0f);
    
    float normalValue = perlinNoise->sampleFloat(testCoord);
    float lowFreqValue = lowFreq.sampleFloat(testCoord);
    float highFreqValue = highFreq.sampleFloat(testCoord);
    float lowAmpValue = lowAmp.sampleFloat(testCoord);
    
    // Different parameters should affect the output
    EXPECT_NE(normalValue, lowFreqValue) << "Frequency should affect noise output";
    EXPECT_NE(normalValue, highFreqValue) << "Frequency should affect noise output";
    EXPECT_NE(normalValue, lowAmpValue) << "Amplitude should affect noise output";
}
