// tests/unit/test_preview_system.cpp
// PhotonRender - Preview System Unit Tests
// Test suite per il sistema di preview real-time

#include <gtest/gtest.h>
#include "../../src/core/preview/preview_renderer.hpp"
#include "../../src/core/preview/preview_scene.hpp"
#include "../../src/core/preview/preview_camera.hpp"
#include "../../src/core/material/material.hpp"
#include "../../src/core/material/disney_brdf.hpp"
#include <memory>
#include <chrono>

using namespace photon;

class PreviewSystemTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create test materials
        m_plasticMaterial = std::make_shared<PBRMaterial>(
            DisneyMaterialPresets::createPlastic(Color3(0.8f, 0.2f, 0.2f))
        );
        
        m_metalMaterial = std::make_shared<PBRMaterial>(
            DisneyMaterialPresets::createMetal(Color3(0.7f, 0.7f, 0.8f))
        );
        
        // Create preview settings
        m_settings = PreviewSettings{
            .width = 256,
            .height = 256,
            .samples = 4,  // Low samples for fast testing
            .maxDepth = 4,
            .geometry = PreviewGeometry::SPHERE,
            .lighting = PreviewLighting::STUDIO,
            .enableDenoising = false,  // Disable for testing
            .enableToneMapping = true,
            .exposure = 1.0f,
            .gamma = 2.2f,
            .cameraDistance = 3.0f,
            .cameraAngleX = 0.0f,
            .cameraAngleY = 0.0f,
            .fov = 45.0f
        };
    }
    
    void TearDown() override {
        // Cleanup handled by smart pointers
    }
    
    std::shared_ptr<PBRMaterial> m_plasticMaterial;
    std::shared_ptr<PBRMaterial> m_metalMaterial;
    PreviewSettings m_settings;
};

// Test 1: PreviewRenderer Initialization
TEST_F(PreviewSystemTest, PreviewRendererInitialization) {
    PreviewRenderer renderer;
    
    // Test initialization
    EXPECT_TRUE(renderer.initialize(m_settings));
    
    // Test settings
    const auto& settings = renderer.getSettings();
    EXPECT_EQ(settings.width, 256);
    EXPECT_EQ(settings.height, 256);
    EXPECT_EQ(settings.samples, 4);
    EXPECT_EQ(settings.maxDepth, 4);
    
    // Test shutdown
    renderer.shutdown();
}

// Test 2: PreviewScene Geometry Creation
TEST_F(PreviewSystemTest, PreviewSceneGeometry) {
    PreviewScene scene;
    
    // Test initialization
    EXPECT_TRUE(scene.initialize(PreviewGeometry::SPHERE));
    
    // Test geometry switching
    scene.createGeometry(PreviewGeometry::CUBE);
    EXPECT_EQ(scene.getCurrentGeometry(), PreviewGeometry::CUBE);
    
    scene.createGeometry(PreviewGeometry::CYLINDER);
    EXPECT_EQ(scene.getCurrentGeometry(), PreviewGeometry::CYLINDER);
    
    scene.createGeometry(PreviewGeometry::TORUS);
    EXPECT_EQ(scene.getCurrentGeometry(), PreviewGeometry::TORUS);
    
    scene.createGeometry(PreviewGeometry::PLANE);
    EXPECT_EQ(scene.getCurrentGeometry(), PreviewGeometry::PLANE);
}

// Test 3: PreviewScene Lighting Setup
TEST_F(PreviewSystemTest, PreviewSceneLighting) {
    PreviewScene scene;
    EXPECT_TRUE(scene.initialize(PreviewGeometry::SPHERE));
    
    // Test lighting presets
    scene.setupLighting(PreviewLighting::STUDIO);
    EXPECT_EQ(scene.getCurrentLighting(), PreviewLighting::STUDIO);
    
    scene.setupLighting(PreviewLighting::OUTDOOR);
    EXPECT_EQ(scene.getCurrentLighting(), PreviewLighting::OUTDOOR);
    
    scene.setupLighting(PreviewLighting::INDOOR);
    EXPECT_EQ(scene.getCurrentLighting(), PreviewLighting::INDOOR);
    
    scene.setupLighting(PreviewLighting::DRAMATIC);
    EXPECT_EQ(scene.getCurrentLighting(), PreviewLighting::DRAMATIC);
    
    scene.setupLighting(PreviewLighting::SOFT);
    EXPECT_EQ(scene.getCurrentLighting(), PreviewLighting::SOFT);
    
    // Test lighting intensity
    scene.setLightingIntensity(2.0f);
    scene.setLightingIntensity(0.5f);
    scene.setLightingIntensity(0.0f);  // Should clamp to 0
}

// Test 4: PreviewCamera Controls
TEST_F(PreviewSystemTest, PreviewCameraControls) {
    PreviewCamera camera;
    
    // Test initialization
    EXPECT_TRUE(camera.initialize(m_settings));
    
    // Test position updates
    camera.updatePosition(5.0f, 30.0f, 45.0f);
    EXPECT_FLOAT_EQ(camera.getDistance(), 5.0f);
    EXPECT_FLOAT_EQ(camera.getAngleX(), 30.0f);
    EXPECT_FLOAT_EQ(camera.getAngleY(), 45.0f);
    
    // Test constraints
    camera.setConstraints(1.0f, 10.0f, -80.0f, 80.0f);
    camera.updatePosition(0.5f, -100.0f, 0.0f);  // Should be constrained
    EXPECT_GE(camera.getDistance(), 1.0f);
    EXPECT_GE(camera.getAngleX(), -80.0f);
    
    // Test reset
    camera.reset();
    EXPECT_FLOAT_EQ(camera.getDistance(), 3.0f);
    EXPECT_FLOAT_EQ(camera.getAngleX(), 0.0f);
    EXPECT_FLOAT_EQ(camera.getAngleY(), 0.0f);
}

// Test 5: Material Assignment
TEST_F(PreviewSystemTest, MaterialAssignment) {
    PreviewRenderer renderer;
    EXPECT_TRUE(renderer.initialize(m_settings));
    
    // Test material assignment
    renderer.setMaterial(m_plasticMaterial);
    renderer.setMaterial(m_metalMaterial);
    
    // Test geometry with material
    renderer.setGeometry(PreviewGeometry::SPHERE);
    renderer.setGeometry(PreviewGeometry::CUBE);
    
    renderer.shutdown();
}

// Test 6: Preview Rendering
TEST_F(PreviewSystemTest, PreviewRendering) {
    PreviewRenderer renderer;
    EXPECT_TRUE(renderer.initialize(m_settings));
    
    // Set material and render
    renderer.setMaterial(m_plasticMaterial);
    
    auto startTime = std::chrono::high_resolution_clock::now();
    auto image = renderer.render();
    auto endTime = std::chrono::high_resolution_clock::now();
    
    // Verify render result
    EXPECT_NE(image, nullptr);
    if (image) {
        EXPECT_EQ(image->getWidth(), 256);
        EXPECT_EQ(image->getHeight(), 256);
    }
    
    // Check render time (should be reasonable for preview)
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
    std::cout << "Preview render time: " << duration.count() << "ms" << std::endl;
    EXPECT_LT(duration.count(), 5000);  // Should render in under 5 seconds
    
    renderer.shutdown();
}

// Test 7: Settings Updates
TEST_F(PreviewSystemTest, SettingsUpdates) {
    PreviewRenderer renderer;
    EXPECT_TRUE(renderer.initialize(m_settings));
    
    // Test resolution change
    PreviewSettings newSettings = m_settings;
    newSettings.width = 128;
    newSettings.height = 128;
    renderer.updateSettings(newSettings);
    
    const auto& settings = renderer.getSettings();
    EXPECT_EQ(settings.width, 128);
    EXPECT_EQ(settings.height, 128);
    
    // Test samples change
    newSettings.samples = 8;
    renderer.updateSettings(newSettings);
    EXPECT_EQ(renderer.getSettings().samples, 8);
    
    // Test geometry change
    renderer.setGeometry(PreviewGeometry::TORUS);
    EXPECT_EQ(renderer.getSettings().geometry, PreviewGeometry::TORUS);
    
    // Test lighting change
    renderer.setLighting(PreviewLighting::DRAMATIC);
    EXPECT_EQ(renderer.getSettings().lighting, PreviewLighting::DRAMATIC);
    
    renderer.shutdown();
}

// Test 8: Camera Animation
TEST_F(PreviewSystemTest, CameraAnimation) {
    PreviewCamera camera;
    EXPECT_TRUE(camera.initialize(m_settings));
    
    // Start animation
    camera.animateTo(5.0f, 30.0f, 90.0f, 1.0f);
    EXPECT_TRUE(camera.isAnimating());
    
    // Update animation
    bool animating = camera.updateAnimation(0.5f);  // Half way
    EXPECT_TRUE(animating);
    
    // Check intermediate values
    float distance = camera.getDistance();
    EXPECT_GT(distance, 3.0f);  // Should be between start and end
    EXPECT_LT(distance, 5.0f);
    
    // Complete animation
    animating = camera.updateAnimation(0.6f);  // Complete
    EXPECT_FALSE(animating);
    EXPECT_FALSE(camera.isAnimating());
    
    // Check final values
    EXPECT_FLOAT_EQ(camera.getDistance(), 5.0f);
    EXPECT_FLOAT_EQ(camera.getAngleX(), 30.0f);
    EXPECT_FLOAT_EQ(camera.getAngleY(), 90.0f);
}

// Test 9: Performance Benchmarks
TEST_F(PreviewSystemTest, PerformanceBenchmarks) {
    PreviewRenderer renderer;
    EXPECT_TRUE(renderer.initialize(m_settings));
    renderer.setMaterial(m_plasticMaterial);
    
    // Benchmark different geometries
    std::vector<PreviewGeometry> geometries = {
        PreviewGeometry::SPHERE,
        PreviewGeometry::CUBE,
        PreviewGeometry::CYLINDER,
        PreviewGeometry::TORUS
    };
    
    for (auto geometry : geometries) {
        renderer.setGeometry(geometry);
        
        auto startTime = std::chrono::high_resolution_clock::now();
        auto image = renderer.render();
        auto endTime = std::chrono::high_resolution_clock::now();
        
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
        std::cout << "Geometry " << static_cast<int>(geometry) 
                  << " render time: " << duration.count() << "ms" << std::endl;
        
        EXPECT_NE(image, nullptr);
        EXPECT_LT(duration.count(), 3000);  // Should be fast for preview
    }
    
    renderer.shutdown();
}

// Test 10: Error Handling
TEST_F(PreviewSystemTest, ErrorHandling) {
    PreviewRenderer renderer;
    
    // Test render without initialization
    auto image = renderer.render();
    EXPECT_EQ(image, nullptr);
    
    // Test invalid settings
    PreviewSettings invalidSettings = m_settings;
    invalidSettings.width = 0;
    invalidSettings.height = 0;
    EXPECT_FALSE(renderer.initialize(invalidSettings));
    
    // Test double initialization
    EXPECT_TRUE(renderer.initialize(m_settings));
    EXPECT_TRUE(renderer.initialize(m_settings));  // Should handle gracefully
    
    renderer.shutdown();
}

// Performance target: Preview renders should complete in under 1 second for 256x256@4spp
// Quality target: Preview should accurately represent material appearance
// Memory target: Preview system should use less than 100MB memory
