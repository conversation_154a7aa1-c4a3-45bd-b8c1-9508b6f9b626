// tests/unit/test_quality_assurance_system.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Quality Assurance System Tests

#include <gtest/gtest.h>
#include "../../src/qa/quality_assurance_system.hpp"
#include <chrono>
#include <thread>

using namespace photon::qa;

class QualityAssuranceTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Setup test configuration
        config.run_unit_tests = true;
        config.run_integration_tests = true;
        config.run_performance_tests = true;
        config.run_regression_tests = true;
        config.run_memory_tests = true;
        config.run_gpu_tests = true;
        config.run_stress_tests = false; // Skip stress tests for unit testing
        config.run_validation_tests = true;
        config.run_compatibility_tests = true;
        
        config.max_parallel_tests = 1; // Sequential for predictable testing
        config.test_timeout = std::chrono::seconds(30);
        config.stop_on_first_failure = false;
        config.generate_detailed_reports = false; // Skip for unit tests
        config.output_directory = "test_qa_output";
        
        // Initialize QA system
        qa_system = std::make_unique<QualityAssuranceSystem>();
        qa_system->initialize(config);
    }
    
    void TearDown() override {
        if (qa_system) {
            qa_system->shutdown();
        }
    }
    
    TestSuiteConfig config;
    std::unique_ptr<QualityAssuranceSystem> qa_system;
};

// Test 1: Basic Initialization
TEST_F(QualityAssuranceTest, BasicInitialization) {
    EXPECT_TRUE(qa_system != nullptr);
    
    // Test configuration
    const auto& qa_config = qa_system->getConfiguration();
    EXPECT_TRUE(qa_config.run_unit_tests);
    EXPECT_TRUE(qa_config.run_integration_tests);
    EXPECT_EQ(qa_config.max_parallel_tests, 1);
    EXPECT_EQ(qa_config.output_directory, "test_qa_output");
}

// Test 2: Test Registration
TEST_F(QualityAssuranceTest, TestRegistration) {
    // Register a simple test
    bool test_executed = false;
    
    qa_system->registerTest(
        "simple_test",
        "A simple test for registration",
        TestCategory::UNIT,
        TestSeverity::HIGH,
        [&test_executed](TestContext& context) -> bool {
            test_executed = true;
            context.output_stream << "Simple test executed\n";
            return true;
        }
    );
    
    // Run the test
    TestResult result = qa_system->runTest("simple_test");
    
    EXPECT_TRUE(test_executed);
    EXPECT_EQ(result.status, TestStatus::PASSED);
    EXPECT_EQ(result.test_name, "simple_test");
    EXPECT_EQ(result.category, TestCategory::UNIT);
    EXPECT_EQ(result.severity, TestSeverity::HIGH);
}

// Test 3: Test Execution with Failure
TEST_F(QualityAssuranceTest, TestExecutionFailure) {
    qa_system->registerTest(
        "failing_test",
        "A test that always fails",
        TestCategory::UNIT,
        TestSeverity::MEDIUM,
        [](TestContext& context) -> bool {
            context.output_stream << "This test is designed to fail\n";
            return false;
        }
    );
    
    TestResult result = qa_system->runTest("failing_test");
    
    EXPECT_EQ(result.status, TestStatus::FAILED);
    EXPECT_EQ(result.test_name, "failing_test");
    EXPECT_FALSE(result.output_log.empty());
}

// Test 4: Test Execution with Exception
TEST_F(QualityAssuranceTest, TestExecutionException) {
    qa_system->registerTest(
        "exception_test",
        "A test that throws an exception",
        TestCategory::UNIT,
        TestSeverity::LOW,
        [](TestContext& context) -> bool {
            context.output_stream << "About to throw exception\n";
            throw std::runtime_error("Test exception");
            return true;
        }
    );
    
    TestResult result = qa_system->runTest("exception_test");
    
    EXPECT_EQ(result.status, TestStatus::ERROR);
    EXPECT_FALSE(result.error_message.empty());
    EXPECT_TRUE(result.error_message.find("Test exception") != std::string::npos);
}

// Test 5: Test Timeout
TEST_F(QualityAssuranceTest, TestTimeout) {
    // Set short timeout for this test
    TestSuiteConfig timeout_config = config;
    timeout_config.test_timeout = std::chrono::milliseconds(100);
    
    auto timeout_qa = std::make_unique<QualityAssuranceSystem>();
    timeout_qa->initialize(timeout_config);
    
    timeout_qa->registerTest(
        "timeout_test",
        "A test that takes too long",
        TestCategory::UNIT,
        TestSeverity::LOW,
        [](TestContext& context) -> bool {
            context.output_stream << "Starting long operation\n";
            std::this_thread::sleep_for(std::chrono::milliseconds(200));
            return true;
        }
    );
    
    TestResult result = timeout_qa->runTest("timeout_test");
    
    EXPECT_EQ(result.status, TestStatus::TIMEOUT);
    EXPECT_TRUE(result.error_message.find("timed out") != std::string::npos);
    
    timeout_qa->shutdown();
}

// Test 6: Performance Measurement
TEST_F(QualityAssuranceTest, PerformanceMeasurement) {
    qa_system->registerTest(
        "performance_test",
        "A test that measures performance",
        TestCategory::PERFORMANCE,
        TestSeverity::MEDIUM,
        [](TestContext& context) -> bool {
            context.startProfiling();
            
            // Simulate some work
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
            
            context.stopProfiling();
            context.output_stream << "Performance test completed\n";
            return true;
        }
    );
    
    TestResult result = qa_system->runTest("performance_test");
    
    EXPECT_EQ(result.status, TestStatus::PASSED);
    EXPECT_GT(result.execution_time.count(), 8); // Should be at least 8ms
    EXPECT_LT(result.execution_time.count(), 50); // Should be less than 50ms
}

// Test 7: Memory Usage Tracking
TEST_F(QualityAssuranceTest, MemoryUsageTracking) {
    qa_system->registerTest(
        "memory_test",
        "A test that allocates memory",
        TestCategory::MEMORY,
        TestSeverity::HIGH,
        [](TestContext& context) -> bool {
            // Allocate some memory
            std::vector<int> large_vector(10000, 42);
            
            context.output_stream << "Allocated vector with " << large_vector.size() << " elements\n";
            
            // Use the memory to prevent optimization
            volatile int sum = 0;
            for (int val : large_vector) {
                sum += val;
            }
            
            return true;
        }
    );
    
    TestResult result = qa_system->runTest("memory_test");
    
    EXPECT_EQ(result.status, TestStatus::PASSED);
    // Memory usage should be tracked (though exact amount may vary)
    EXPECT_GE(result.memory_usage_bytes, 0);
}

// Test 8: Test Categories
TEST_F(QualityAssuranceTest, TestCategories) {
    // Register tests in different categories
    std::vector<TestCategory> categories = {
        TestCategory::UNIT,
        TestCategory::INTEGRATION,
        TestCategory::PERFORMANCE,
        TestCategory::REGRESSION,
        TestCategory::MEMORY,
        TestCategory::GPU,
        TestCategory::VALIDATION
    };
    
    for (size_t i = 0; i < categories.size(); i++) {
        std::string test_name = "category_test_" + std::to_string(i);
        qa_system->registerTest(
            test_name,
            "Test for category " + std::to_string(i),
            categories[i],
            TestSeverity::MEDIUM,
            [](TestContext& context) -> bool {
                context.output_stream << "Category test executed\n";
                return true;
            }
        );
    }
    
    // Run tests by category
    bool unit_result = qa_system->runTestsByCategory(TestCategory::UNIT);
    EXPECT_TRUE(unit_result);
    
    bool performance_result = qa_system->runTestsByCategory(TestCategory::PERFORMANCE);
    EXPECT_TRUE(performance_result);
}

// Test 9: Test Summary Generation
TEST_F(QualityAssuranceTest, TestSummaryGeneration) {
    // Register a mix of passing and failing tests
    qa_system->registerTest("pass_test_1", "Passing test 1", TestCategory::UNIT, TestSeverity::HIGH,
        [](TestContext&) { return true; });
    qa_system->registerTest("pass_test_2", "Passing test 2", TestCategory::UNIT, TestSeverity::MEDIUM,
        [](TestContext&) { return true; });
    qa_system->registerTest("fail_test_1", "Failing test 1", TestCategory::UNIT, TestSeverity::LOW,
        [](TestContext&) { return false; });
    qa_system->registerTest("critical_test", "Critical test", TestCategory::UNIT, TestSeverity::CRITICAL,
        [](TestContext&) { return true; });
    
    // Run all tests
    bool overall_result = qa_system->runAllTests();
    
    // Get summary
    auto summary = qa_system->getTestSummary();
    
    EXPECT_EQ(summary.total_tests, 4);
    EXPECT_EQ(summary.passed_tests, 3);
    EXPECT_EQ(summary.failed_tests, 1);
    EXPECT_EQ(summary.critical_failures, 0);
    EXPECT_TRUE(summary.all_critical_passed);
    EXPECT_GT(summary.overall_score, 0.5); // Should be > 50%
    EXPECT_LT(summary.overall_score, 1.0); // Should be < 100% due to failure
}

// Test 10: Critical Test Failure Detection
TEST_F(QualityAssuranceTest, CriticalTestFailureDetection) {
    qa_system->registerTest("critical_fail", "Critical failing test", TestCategory::UNIT, TestSeverity::CRITICAL,
        [](TestContext&) { return false; });
    qa_system->registerTest("normal_pass", "Normal passing test", TestCategory::UNIT, TestSeverity::HIGH,
        [](TestContext&) { return true; });
    
    bool overall_result = qa_system->runAllTests();
    auto summary = qa_system->getTestSummary();
    
    EXPECT_FALSE(overall_result); // Should fail due to critical failure
    EXPECT_EQ(summary.critical_failures, 1);
    EXPECT_FALSE(summary.all_critical_passed);
}

// Test 11: Custom Validation
TEST_F(QualityAssuranceTest, CustomValidation) {
    // Add custom validator
    qa_system->addCustomValidator("validated_test", [](const std::string& output) -> bool {
        return output.find("VALIDATION_MARKER") != std::string::npos;
    });
    
    // Register test that produces expected output
    qa_system->registerTest("validated_test", "Test with custom validation", TestCategory::VALIDATION, TestSeverity::HIGH,
        [](TestContext& context) -> bool {
            context.output_stream << "Test output with VALIDATION_MARKER\n";
            return true;
        });
    
    TestResult result = qa_system->runTest("validated_test");
    
    EXPECT_EQ(result.status, TestStatus::PASSED);
    EXPECT_TRUE(result.output_valid);
}

// Test 12: Built-in Tests Execution
TEST_F(QualityAssuranceTest, BuiltinTestsExecution) {
    // The QA system should have built-in tests registered
    bool result = qa_system->runAllTests();
    
    // Get results
    const auto& test_results = qa_system->getTestResults();
    
    // Should have some built-in tests
    EXPECT_GT(test_results.size(), 0);
    
    // Check that we have tests from different categories
    bool has_unit = false, has_integration = false, has_performance = false;
    
    for (const auto& test_result : test_results) {
        switch (test_result.category) {
            case TestCategory::UNIT: has_unit = true; break;
            case TestCategory::INTEGRATION: has_integration = true; break;
            case TestCategory::PERFORMANCE: has_performance = true; break;
            default: break;
        }
    }
    
    EXPECT_TRUE(has_unit);
    EXPECT_TRUE(has_integration);
    EXPECT_TRUE(has_performance);
}

// Test 13: Report Generation
TEST_F(QualityAssuranceTest, ReportGeneration) {
    // Register a simple test
    qa_system->registerTest("report_test", "Test for report generation", TestCategory::UNIT, TestSeverity::HIGH,
        [](TestContext& context) -> bool {
            context.output_stream << "Report test executed successfully\n";
            return true;
        });
    
    // Run tests
    qa_system->runAllTests();
    
    // Generate reports (this should not crash)
    qa_system->generateReports();
    
    // Check if HTML report was generated
    std::ifstream html_file(config.output_directory + "/qa_report.html");
    EXPECT_TRUE(html_file.good());
    html_file.close();
}

// Test 14: Automated Test Runner
TEST_F(QualityAssuranceTest, AutomatedTestRunner) {
    // Test command line parsing
    const char* argv[] = {"test_program", "--no-stress", "--parallel", "2", "--output", "custom_output"};
    int argc = sizeof(argv) / sizeof(argv[0]);
    
    TestSuiteConfig parsed_config = AutomatedTestRunner::parseCommandLineArgs(argc, const_cast<char**>(argv));
    
    EXPECT_FALSE(parsed_config.run_stress_tests);
    EXPECT_EQ(parsed_config.max_parallel_tests, 2);
    EXPECT_EQ(parsed_config.output_directory, "custom_output");
}

// Performance benchmark test
TEST_F(QualityAssuranceTest, PerformanceBenchmark) {
    auto start = std::chrono::high_resolution_clock::now();
    
    // Register multiple tests
    for (int i = 0; i < 10; i++) {
        std::string test_name = "benchmark_test_" + std::to_string(i);
        qa_system->registerTest(test_name, "Benchmark test " + std::to_string(i), 
                                TestCategory::UNIT, TestSeverity::MEDIUM,
            [i](TestContext& context) -> bool {
                context.output_stream << "Benchmark test " << i << " executed\n";
                return true;
            });
    }
    
    // Run all tests
    bool result = qa_system->runAllTests();
    
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
    
    std::cout << "QA System Performance:" << std::endl;
    std::cout << "  Total execution time: " << duration.count() << " ms" << std::endl;
    std::cout << "  Tests executed: " << qa_system->getTestResults().size() << std::endl;
    std::cout << "  Average time per test: " << (duration.count() / qa_system->getTestResults().size()) << " ms" << std::endl;
    
    EXPECT_TRUE(result);
    EXPECT_LT(duration.count(), 5000); // Should complete within 5 seconds
    
    auto summary = qa_system->getTestSummary();
    EXPECT_GT(summary.total_tests, 10); // Should have built-in tests + our benchmark tests
}

// Test status and category string conversion
TEST_F(QualityAssuranceTest, StatusAndCategoryStrings) {
    TestResult result("test", "description", TestCategory::UNIT, TestSeverity::HIGH);
    
    // Test status strings
    result.status = TestStatus::PASSED;
    EXPECT_EQ(result.getStatusString(), "PASSED");
    
    result.status = TestStatus::FAILED;
    EXPECT_EQ(result.getStatusString(), "FAILED");
    
    result.status = TestStatus::TIMEOUT;
    EXPECT_EQ(result.getStatusString(), "TIMEOUT");
    
    // Test category strings
    EXPECT_EQ(result.getCategoryString(), "UNIT");
    
    // Test severity strings
    EXPECT_EQ(result.getSeverityString(), "HIGH");
    
    // Test helper methods
    result.severity = TestSeverity::CRITICAL;
    EXPECT_TRUE(result.isCritical());
    
    result.status = TestStatus::PASSED;
    EXPECT_TRUE(result.isPassed());
}
