// tests/unit/test_texture_assignment.cpp
// PhotonRender - Texture Assignment Interface Unit Tests
// Test suite per il sistema di assegnazione texture

#include <gtest/gtest.h>
#include "../../src/core/texture/texture_manager.hpp"
#include <filesystem>
#include <fstream>

using namespace photon;

class TextureAssignmentTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create temporary test directory
        m_testLibraryPath = std::filesystem::temp_directory_path() / "photon_test_textures";
        std::filesystem::remove_all(m_testLibraryPath);
        std::filesystem::create_directories(m_testLibraryPath);
        
        // Create test texture files
        createTestTextureFile("test_diffuse.png", 512, 512);
        createTestTextureFile("test_normal.png", 1024, 1024);
        createTestTextureFile("test_roughness.jpg", 256, 256);
        createTestTextureFile("test_metallic.tga", 512, 512);
        createTestTextureFile("test_emission.hdr", 2048, 1024);
    }
    
    void TearDown() override {
        // Cleanup test directory
        std::filesystem::remove_all(m_testLibraryPath);
    }
    
    void createTestTextureFile(const std::string& filename, int width, int height) {
        std::string filePath = (m_testLibraryPath / filename).string();
        
        // Create a simple test file (not a real image, just for testing)
        std::ofstream file(filePath, std::ios::binary);
        file << "TEST_TEXTURE_" << width << "x" << height;
        file.close();
    }
    
    std::filesystem::path m_testLibraryPath;
};

// Test 1: TextureManager Initialization
TEST_F(TextureAssignmentTest, TextureManagerInitialization) {
    TextureManager manager;
    
    // Test initialization
    EXPECT_TRUE(manager.initialize(m_testLibraryPath.string(), 256));
    
    // Check if directories were created
    EXPECT_TRUE(std::filesystem::exists(m_testLibraryPath / "thumbnails"));
    EXPECT_TRUE(std::filesystem::exists(m_testLibraryPath / "cache"));
    
    manager.shutdown();
}

// Test 2: Texture Loading
TEST_F(TextureAssignmentTest, TextureLoading) {
    TextureManager manager;
    EXPECT_TRUE(manager.initialize(m_testLibraryPath.string()));
    
    // Test loading existing texture file
    std::string textureFile = (m_testLibraryPath / "test_diffuse.png").string();
    std::string textureId = manager.loadTexture(textureFile);
    
    // Note: This will fail because we're not loading real images
    // In a real test, we would use actual image files
    // EXPECT_FALSE(textureId.empty());
    
    // Test loading non-existent file
    std::string invalidFile = (m_testLibraryPath / "nonexistent.png").string();
    std::string invalidId = manager.loadTexture(invalidFile);
    EXPECT_TRUE(invalidId.empty());
    
    manager.shutdown();
}

// Test 3: Texture Search
TEST_F(TextureAssignmentTest, TextureSearch) {
    TextureManager manager;
    EXPECT_TRUE(manager.initialize(m_testLibraryPath.string()));
    
    // Test search with empty criteria
    TextureSearchCriteria criteria;
    auto results = manager.searchTextures(criteria);
    EXPECT_TRUE(results.empty()); // No textures loaded yet
    
    // Test search by name filter
    criteria.nameFilter = "diffuse";
    results = manager.searchTextures(criteria);
    EXPECT_TRUE(results.empty()); // No textures loaded yet
    
    // Test search by format
    criteria = TextureSearchCriteria{};
    criteria.formats = {"png", "jpg"};
    results = manager.searchTextures(criteria);
    EXPECT_TRUE(results.empty()); // No textures loaded yet
    
    manager.shutdown();
}

// Test 4: UV Transform
TEST_F(TextureAssignmentTest, UVTransform) {
    UVTransform transform;
    
    // Test default values
    EXPECT_EQ(transform.offset, Vec2(0.0f, 0.0f));
    EXPECT_EQ(transform.scale, Vec2(1.0f, 1.0f));
    EXPECT_FLOAT_EQ(transform.rotation, 0.0f);
    EXPECT_EQ(transform.pivot, Vec2(0.5f, 0.5f));
    
    // Test transform application
    Vec2 testUV(0.5f, 0.5f);
    Vec2 transformedUV = transform.transform(testUV);
    EXPECT_EQ(transformedUV, testUV); // Should be unchanged with identity transform
    
    // Test with offset
    transform.offset = Vec2(0.1f, 0.2f);
    transformedUV = transform.transform(testUV);
    EXPECT_FLOAT_EQ(transformedUV.x, 0.6f);
    EXPECT_FLOAT_EQ(transformedUV.y, 0.7f);
    
    // Test with scale
    transform.reset();
    transform.scale = Vec2(2.0f, 0.5f);
    transformedUV = transform.transform(testUV);
    // With pivot at (0.5, 0.5), scaling should not change the center point
    EXPECT_FLOAT_EQ(transformedUV.x, 0.5f);
    EXPECT_FLOAT_EQ(transformedUV.y, 0.5f);
    
    // Test reset
    transform.reset();
    EXPECT_EQ(transform.offset, Vec2(0.0f, 0.0f));
    EXPECT_EQ(transform.scale, Vec2(1.0f, 1.0f));
    EXPECT_FLOAT_EQ(transform.rotation, 0.0f);
}

// Test 5: Texture Assignment Creation
TEST_F(TextureAssignmentTest, TextureAssignmentCreation) {
    TextureManager manager;
    EXPECT_TRUE(manager.initialize(m_testLibraryPath.string()));
    
    // Test creating assignment
    std::string textureId = "test_texture_001";
    TextureType type = TextureType::DIFFUSE;
    
    TextureAssignment assignment = manager.createAssignment(textureId, type);
    
    EXPECT_EQ(assignment.textureId, textureId);
    EXPECT_EQ(assignment.type, type);
    EXPECT_EQ(assignment.filter, TextureFilter::LINEAR);
    EXPECT_EQ(assignment.wrapU, TextureWrap::REPEAT);
    EXPECT_EQ(assignment.wrapV, TextureWrap::REPEAT);
    EXPECT_FLOAT_EQ(assignment.intensity, 1.0f);
    EXPECT_TRUE(assignment.enabled);
    
    manager.shutdown();
}

// Test 6: Texture Assignment Validation
TEST_F(TextureAssignmentTest, TextureAssignmentValidation) {
    TextureManager manager;
    EXPECT_TRUE(manager.initialize(m_testLibraryPath.string()));
    
    // Create valid assignment
    TextureAssignment assignment = manager.createAssignment("test_id", TextureType::DIFFUSE);
    
    // Should fail validation because texture doesn't exist
    EXPECT_FALSE(manager.validateAssignment(assignment));
    
    // Test invalid intensity
    assignment.intensity = -1.0f;
    EXPECT_FALSE(manager.validateAssignment(assignment));
    
    assignment.intensity = 15.0f; // Too high
    EXPECT_FALSE(manager.validateAssignment(assignment));
    
    // Test invalid scale
    assignment.intensity = 1.0f; // Reset to valid
    assignment.uvTransform.scale.x = 0.0f; // Invalid scale
    EXPECT_FALSE(manager.validateAssignment(assignment));
    
    assignment.uvTransform.scale.x = -1.0f; // Negative scale
    EXPECT_FALSE(manager.validateAssignment(assignment));
    
    manager.shutdown();
}

// Test 7: Supported Formats
TEST_F(TextureAssignmentTest, SupportedFormats) {
    TextureManager manager;
    
    auto formats = manager.getSupportedFormats();
    EXPECT_FALSE(formats.empty());
    
    // Check for common formats
    EXPECT_TRUE(std::find(formats.begin(), formats.end(), "png") != formats.end());
    EXPECT_TRUE(std::find(formats.begin(), formats.end(), "jpg") != formats.end());
    EXPECT_TRUE(std::find(formats.begin(), formats.end(), "jpeg") != formats.end());
    EXPECT_TRUE(std::find(formats.begin(), formats.end(), "bmp") != formats.end());
    EXPECT_TRUE(std::find(formats.begin(), formats.end(), "tga") != formats.end());
    EXPECT_TRUE(std::find(formats.begin(), formats.end(), "hdr") != formats.end());
    EXPECT_TRUE(std::find(formats.begin(), formats.end(), "exr") != formats.end());
    
    // Test format checking
    EXPECT_TRUE(manager.isFormatSupported("png"));
    EXPECT_TRUE(manager.isFormatSupported("PNG")); // Case insensitive
    EXPECT_TRUE(manager.isFormatSupported("jpg"));
    EXPECT_TRUE(manager.isFormatSupported("hdr"));
    EXPECT_FALSE(manager.isFormatSupported("xyz")); // Unsupported format
}

// Test 8: Texture Type String Conversion
TEST_F(TextureAssignmentTest, TextureTypeStringConversion) {
    // Test type to string conversion
    EXPECT_EQ(TextureManager::getTextureTypeString(TextureType::DIFFUSE), "diffuse");
    EXPECT_EQ(TextureManager::getTextureTypeString(TextureType::NORMAL), "normal");
    EXPECT_EQ(TextureManager::getTextureTypeString(TextureType::ROUGHNESS), "roughness");
    EXPECT_EQ(TextureManager::getTextureTypeString(TextureType::METALLIC), "metallic");
    EXPECT_EQ(TextureManager::getTextureTypeString(TextureType::SPECULAR), "specular");
    EXPECT_EQ(TextureManager::getTextureTypeString(TextureType::EMISSION), "emission");
    EXPECT_EQ(TextureManager::getTextureTypeString(TextureType::OPACITY), "opacity");
    EXPECT_EQ(TextureManager::getTextureTypeString(TextureType::DISPLACEMENT), "displacement");
    EXPECT_EQ(TextureManager::getTextureTypeString(TextureType::AMBIENT_OCCLUSION), "ambient_occlusion");
    EXPECT_EQ(TextureManager::getTextureTypeString(TextureType::ENVIRONMENT), "environment");
    
    // Test string to type conversion
    EXPECT_EQ(TextureManager::parseTextureType("diffuse"), TextureType::DIFFUSE);
    EXPECT_EQ(TextureManager::parseTextureType("normal"), TextureType::NORMAL);
    EXPECT_EQ(TextureManager::parseTextureType("roughness"), TextureType::ROUGHNESS);
    EXPECT_EQ(TextureManager::parseTextureType("metallic"), TextureType::METALLIC);
    EXPECT_EQ(TextureManager::parseTextureType("emission"), TextureType::EMISSION);
    EXPECT_EQ(TextureManager::parseTextureType("invalid"), TextureType::DIFFUSE); // Default
}

// Test 9: Cache Management
TEST_F(TextureAssignmentTest, CacheManagement) {
    TextureManager manager;
    EXPECT_TRUE(manager.initialize(m_testLibraryPath.string(), 1)); // 1 MB cache
    
    // Test cache size setting
    manager.setCacheSize(2); // 2 MB
    
    // Test cache stats
    std::string stats = manager.getCacheStats();
    EXPECT_FALSE(stats.empty());
    EXPECT_TRUE(stats.find("Texture Cache Statistics") != std::string::npos);
    EXPECT_TRUE(stats.find("Cached textures: 0") != std::string::npos);
    
    // Test cache clearing
    manager.clearCache(false); // Clear all
    manager.clearCache(true);  // Keep recent
    
    manager.shutdown();
}

// Test 10: Recent Textures Tracking
TEST_F(TextureAssignmentTest, RecentTexturesTracking) {
    TextureManager manager;
    EXPECT_TRUE(manager.initialize(m_testLibraryPath.string()));
    
    // Test getting recent textures when empty
    auto recent = manager.getRecentTextures(5);
    EXPECT_TRUE(recent.empty());
    
    // Test getting all texture IDs when empty
    auto allIds = manager.getAllTextureIds();
    EXPECT_TRUE(allIds.empty());
    
    manager.shutdown();
}

// Test 11: Progress Callback
TEST_F(TextureAssignmentTest, ProgressCallback) {
    TextureManager manager;
    
    bool callbackCalled = false;
    std::string lastMessage;
    float lastProgress = 0.0f;
    
    // Set progress callback
    manager.setProgressCallback([&](float progress, const std::string& message) {
        callbackCalled = true;
        lastProgress = progress;
        lastMessage = message;
    });
    
    EXPECT_TRUE(manager.initialize(m_testLibraryPath.string()));
    
    // Progress callback should have been called during initialization
    // (though it might not be called if no textures are loaded)
    
    manager.shutdown();
}

// Test 12: Error Handling
TEST_F(TextureAssignmentTest, ErrorHandling) {
    TextureManager manager;
    
    // Test operations without initialization
    EXPECT_EQ(manager.loadTexture("test.png"), "");
    EXPECT_EQ(manager.getTexture("invalid_id"), nullptr);
    EXPECT_FALSE(manager.removeTexture("invalid_id"));
    
    // Initialize manager
    EXPECT_TRUE(manager.initialize(m_testLibraryPath.string()));
    
    // Test invalid operations
    EXPECT_EQ(manager.getTexture("nonexistent_id"), nullptr);
    EXPECT_FALSE(manager.removeTexture("nonexistent_id"));
    EXPECT_FALSE(manager.generateThumbnail("nonexistent_id"));
    
    // Test invalid file paths
    EXPECT_EQ(manager.loadTexture(""), "");
    EXPECT_EQ(manager.loadTexture("/nonexistent/path/texture.png"), "");
    
    manager.shutdown();
}

// Performance targets for Texture Assignment:
// - Texture loading: < 500ms per texture
// - UV transform calculation: < 1ms per transform
// - Texture search: < 100ms for 1000 textures
// - Cache operations: < 50ms for cache management
// - Assignment validation: < 10ms per assignment
